using System;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.UI.Components.Button;
using Sirenix.OdinInspector;
using UnityEngine;

namespace b100SDK.Scripts.UI.Components.SubPanel
{
    public class TabButton : BhMonoBehavior
    {
        [SerializeField]
        private int type;
        
        [Space]
        [SerializeField]
        private BhButton button;


        private Action<int> _onClick;
        

        private void OnEnable()
        {
            button.onClick.AddListener(OnClick);
        }
        
        void OnDisable()
        {
            button.onClick.RemoveListener(OnClick);
        }

        public void Init(Action<int> onClick)
        {
            _onClick = onClick;
        }


        void OnClick()
        {
            _onClick?.Invoke(type);
        }

        [Button]
        public virtual void UpdateStatusSelect(bool isSelected)
        {
            //TODO: Update status select
            
        }
    }
}