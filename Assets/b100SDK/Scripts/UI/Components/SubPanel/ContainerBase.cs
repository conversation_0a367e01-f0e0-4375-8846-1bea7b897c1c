using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities;
using UnityEngine;

namespace b100SDK.Scripts.UI.Components.SubPanel
{
    [RequireComponent(typeof(ContainerAnim))]
    public abstract class ContainerBase : BhMonoBehavior
    {
        [SerializeField]
        protected int type;

        public Canvas root;

        [SerializeField]
        private ContainerAnim containerAnim;

        private bool _isShowing;

        private AnimHomeDirection _direction;

        public bool IsShowing
        {
            get => _isShowing;
            set
            {
                _isShowing = value;
            }
        }

        protected virtual void Awake()
        {
            containerAnim.Setup(this);
            Hide();
        }

        public virtual void Show(AnimHomeDirection direction)
        {
            containerAnim.animIn = direction == AnimHomeDirection.Left
                ? ContainerAnim.AnimInType.FromLeft
                : ContainerAnim.AnimInType.FromRight;
            
            gameObject.SetActiveWithChecker(true);
            
            containerAnim.StartAnimIn();

            _isShowing = true;
        }

        public virtual void Hide()
        {
            gameObject.SetActiveWithChecker(false);
        }

        public virtual void Close(AnimHomeDirection direction)
        {
            _isShowing = false;
            containerAnim.animOut = direction == AnimHomeDirection.Left
                ? ContainerAnim.AnimOutType.ToLeft
                : ContainerAnim.AnimOutType.ToRight;
            
            containerAnim.StartAnimOut();
        }

        private void Reset()
        {
            containerAnim = GetComponent<ContainerAnim>();
        }
    }

    public enum AnimHomeDirection
    {
        Left,
        Right,
    }
}