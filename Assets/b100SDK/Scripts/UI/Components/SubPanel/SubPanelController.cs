using System;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities;
using UnityEngine;

namespace b100SDK.Scripts.UI.Components.SubPanel
{
    public class SubPanelController : BhMonoBehavior
    {
        [SerializeField]
        private SerializedDictionary<int, ButtonPanelPair> buttonPanelMap;

        private int _currentIndex;
        private bool _isInited;

        protected Action onChangeHomeType;

        public virtual void Init()
        {
            foreach (var item in buttonPanelMap)
            {
                item.Value.tabButton.Init(SelectPanelType);
            }
        }

        
        public void SelectPanelType(int type)
        {
            //Debug.Log("Select Home Type: " + type);
            if (type == _currentIndex && _isInited)
                return;
        
            _isInited = true;
        
            buttonPanelMap[type].tabButton.transform.SetAsLastSibling();
        
            foreach (var item in buttonPanelMap)
            {
                item.Value.tabButton.UpdateStatusSelect(item.Key == type);
            }

            if (_currentIndex > type)
            {
                if (buttonPanelMap[_currentIndex].container.IsShowing)
                {
                    buttonPanelMap[_currentIndex].container.Close(AnimHomeDirection.Right);
                }
                
                buttonPanelMap[type].container.Show(AnimHomeDirection.Left);
            }
            else
            {
                if (buttonPanelMap[_currentIndex].container.IsShowing)
                {
                    buttonPanelMap[_currentIndex].container.Close(AnimHomeDirection.Left);
                }
                
                buttonPanelMap[type].container.Show(AnimHomeDirection.Right);
            }
        
            _currentIndex = type;
            
            onChangeHomeType?.Invoke();
        }
        
        
        [Serializable]
        public class ButtonPanelPair
        {
            public TabButton tabButton;
            public ContainerBase container;
        }
    }
}