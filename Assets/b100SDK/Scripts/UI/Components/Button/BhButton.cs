using b100SDK.Scripts.Utilities;
using UnityEngine;

namespace b100SDK.Scripts.UI.Components.Button
{
    public class BhButton : UnityEngine.UI.Button
    {
        public ButtonAnim buttonAnim;
    
        public void Show(bool instant = false)
        {
            gameObject.SetActiveWithChecker(true);

            if (!instant && buttonAnim)
                buttonAnim.ShowAnim();
        }

        public void Hide(bool instant = false)
        {
            if (instant || !buttonAnim)
                gameObject.SetActiveWithChecker(false);
            else
                buttonAnim.HideAnim(() => gameObject.SetActive(false));
        }

        [ContextMenu("Setup")]
        void Setup()
        {
            buttonAnim = GetComponent<ButtonAnim>();
        }
    }
}