using System;
using System.Collections.Generic;
using System.Linq;
using b100SDK.Scripts.UI.Components.Anim.HideAnim;
using b100SDK.Scripts.UI.Components.Anim.ShowAnim;
using Sirenix.OdinInspector;
using UnityEngine;

namespace b100SDK.Scripts.UI.Components.Panel
{
    public class PanelAnim : MonoBehaviour
    {
        private List<IShowAnimatable> _showAnimations;
        private List<IHideAnimatable> _hideAnimations;


        public bool HasAnimIn => _showAnimations.Count > 0;
        public bool HasAnimOut => _hideAnimations.Count > 0;

        public void Setup()
        {
            _showAnimations = GetComponents<IShowAnimatable>().ToList();
            _hideAnimations = GetComponents<IHideAnimatable>().ToList();
        }

        [Button]
        public void StartAnimIn(Action onComplete = null)
        {
            for (var index = 0; index < _showAnimations.Count; index++)
            {
                var showAnimation = _showAnimations[index];
                
                showAnimation.Execute(index == _showAnimations.Count - 1 ? onComplete : null);
            }
        }


        [Button]
        public void StartAnimOut(Action onComplete = null)
        {
            for (var index = 0; index < _hideAnimations.Count; index++)
            {
                var hideAnimation = _hideAnimations[index];
                
                hideAnimation.Execute(index == _hideAnimations.Count - 1 ? onComplete : null);
            }
        }
    }
}