using System.Collections.Generic;
using b100SDK.Scripts.Base;
using EnhancedUI.EnhancedScroller;
using UnityEngine;
using UnityEngine.Events;

namespace b100SDK.Scripts.UI.Components.Drop_Down
{
    public class DropDownScroller : Bh<PERSON>onoBehavior, IEnhancedScrollerDelegate
    {
        [SerializeField]
        private EnhancedScroller scroller;

        [SerializeField]
        private DropDownCellView dropDownCellView;

        [SerializeField]
        private float cellSize = 100f;


        private List<DropDownData> _data = new();


        [HideInInspector]
        public UnityEvent<DropDownData> OnValueChanged = new();



        public void Init(List<DropDownData> dropDownData)
        {
            if (_data.Count > 0)
            {
                for (int i = 0; i < _data.Count; i++)
                {
                    _data[i].onChangeStatus = null;
                }
            }
            
            _data = dropDownData;
            
            scroller.Delegate = this;
            
            scroller.ReloadData();
        }
        

        /*
        private void Start()
        {
            scroller.Delegate = this;

            LoadData();
        }

        private void LoadData()
        {
            _data.Clear();
            
            //TODO: Init data
            
            
            scroller.ReloadData();
        }*/

        public void SelectItem(int id)
        {
            if (id < 0 || id >= _data.Count)
            {
                return;
            }
            else
            {
                DropDownCellView cellView = scroller.GetCellViewAtDataIndex(id) as DropDownCellView;
                
                SelectItem(cellView);
            }
        }

        void SelectItem(DropDownCellView cellView)
        {
            if (cellView == null)
            {
                return;
            }
            else
            {
                var selectedItem = (cellView as DropDownCellView).Id;

                for (int i = 0; i < _data.Count; i++)
                {
                    _data[i].IsSelected = (selectedItem == i);
                }
                
                OnValueChanged?.Invoke(_data[selectedItem]);
            }
        }




        #region Scroller Handler

        
        public int GetNumberOfCells(EnhancedScroller scroller)
        {
            return _data.Count;
        }

        public float GetCellViewSize(EnhancedScroller scroller, int dataIndex)
        {
            return cellSize;
        }

        public EnhancedScrollerCellView GetCellView(EnhancedScroller scroller, int dataIndex, int cellIndex)
        {
            DropDownCellView cellView = scroller.GetCellView(dropDownCellView) as DropDownCellView;
            
            cellView.name = "Drop Down Cell View " + dataIndex;

            cellView.selected = SelectItem;
            
            cellView.SetData(_data[dataIndex]);

            return cellView;
        }
        
        #endregion
    }
}