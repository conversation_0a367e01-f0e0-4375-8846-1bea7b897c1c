using System.Collections.Generic;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.UI.Components.Button;
using DG.Tweening;
using Sirenix.OdinInspector;
using TMPro;
using UnityEngine;

namespace b100SDK.Scripts.UI.Components.Drop_Down
{
    public class BhDropDown : BhMonoBehavior
    {
        [SerializeField]
        private TMP_Text defaultOptionText;

        [SerializeField]
        private BhButton switchButton;
        
        [SerializeField]
        private RectTransform dropDownRectTransform;
        
        [SerializeField]
        private RectTransform scrollerRectTransform;
        
        [SerializeField]
        private DropDownScroller dropDownScroller;
        
        
        [Space(20)]
        [SerializeField]
        private float collapseSize = 150f;
        
        [SerializeField]
        private float expandSize = 750f;

        [SerializeField]
        private float collapseDuration = 0.2f;
        
        [SerializeField]
        private float expandDuration = 0.2f;
        
        private bool _isExpanded = false;

        private void OnEnable()
        {
            switchButton.onClick.AddListener(ToggleDropDown);
            dropDownScroller.OnValueChanged.AddListener(OnValueChanged);
        }
        
        private void OnDisable()
        {
            switchButton.onClick.RemoveListener(ToggleDropDown);
            dropDownScroller.OnValueChanged.RemoveListener(OnValueChanged);
        }


        private void Start()
        {
            List<DropDownData> testData = new();
            
            testData.Add(new DropDownData()
            {
                optionId = 0, optionName = "OPTION 1", IsSelected = false,
            });
            
            testData.Add(new DropDownData()
            {
                optionId = 1, optionName = "OPTION 2", IsSelected = false,
            });
            
            testData.Add(new DropDownData()
            {
                optionId = 2, optionName = "OPTION 3", IsSelected = false,
            });
            
            testData.Add(new DropDownData()
            {
                optionId = 3, optionName = "OPTION 4", IsSelected = false,
            });
            
            testData.Add(new DropDownData()
            {
                optionId = 4, optionName = "OPTION 5", IsSelected = false,
            });
            
            dropDownScroller.Init(testData);


            dropDownScroller.SelectItem(0);
            
            Hide(true);
            _isExpanded = false;
        }


        [Button]
        public void Show(bool instant = false)
        {
            dropDownRectTransform.DOKill(true);
            
            var currentSize = dropDownRectTransform.sizeDelta;
            var targetSize = new Vector2(currentSize.x, expandSize);
            
            var scrollerSize = scrollerRectTransform.sizeDelta;
            var targetScrollerSize = new Vector2(scrollerSize.x, expandSize - collapseSize);
            
            if (instant)
            {
                dropDownRectTransform.sizeDelta = targetSize;        
                scrollerRectTransform.sizeDelta = targetScrollerSize;
            }
            else
            {
                dropDownRectTransform.DOSizeDelta(targetSize, expandDuration).SetTarget(dropDownRectTransform);
                scrollerRectTransform.DOSizeDelta(targetScrollerSize, expandDuration).SetTarget(dropDownRectTransform);
            }
        }

        [Button]
        public void Hide(bool instant = false)
        {
            dropDownRectTransform.DOKill(true);
            
            var currentSize = dropDownRectTransform.sizeDelta;
            var targetSize = new Vector2(currentSize.x, collapseSize);
            
            var scrollerSize = scrollerRectTransform.sizeDelta;
            var targetScrollerSize = new Vector2(scrollerSize.x, 0f);

            if (instant)
            {
                dropDownRectTransform.sizeDelta = targetSize;
                scrollerRectTransform.sizeDelta = targetScrollerSize;
                return;
            }
            
            dropDownRectTransform.DOSizeDelta(targetSize, collapseDuration).SetTarget(dropDownRectTransform);
            scrollerRectTransform.DOSizeDelta(targetScrollerSize, collapseDuration).SetTarget(dropDownRectTransform);
        }


        void OnValueChanged(DropDownData value)
        {
            Debug.Log("OnValueChanged: " + value.optionId);
            
            defaultOptionText.text = value.optionName;
        }

        void ToggleDropDown()
        {
            _isExpanded = !_isExpanded;
            
            if (_isExpanded)
            {
                Show();
            }
            else
            {
                Hide();
            }
        }
    }
}