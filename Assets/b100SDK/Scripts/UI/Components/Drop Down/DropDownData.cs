using System;

namespace b100SDK.Scripts.UI.Components.Drop_Down
{
    
    public delegate void DropDownChangeStatusDelegate(bool isSelected);
    
    public class DropDownData
    {
        public int optionId;
        public string optionName;

        public DropDownChangeStatusDelegate onChangeStatus;
        
        private bool _isSelected;

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    onChangeStatus?.Invoke(_isSelected);
                }
            }
        }
    }
}