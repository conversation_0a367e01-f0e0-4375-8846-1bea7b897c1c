using System;
using b100SDK.Scripts.UI.Components.Button;
using b100SDK.Scripts.Utilities;
using EnhancedUI.EnhancedScroller;
using TMPro;
using UnityEngine;

namespace b100SDK.Scripts.UI.Components.Drop_Down
{
    public delegate void SelectedDelegate(DropDownCellView cellView);
    
    public class DropDownCellView : EnhancedScrollerCellView
    {
        public SelectedDelegate selected;
        
        
        [SerializeField]
        private BhButton button;
        
        [SerializeField]
        private TMP_Text optionText;

        [SerializeField]
        private GameObject tickGameObject;

        private DropDownData _data;

        public int Id => _data.optionId;

        private void OnEnable()
        {
            button.onClick.AddListener(OnSelect);
        }
        
        private void OnDisable()
        {
            button.onClick.RemoveListener(OnSelect);

            if (_data != null)
            {
                _data.onChangeStatus -= UpdateStatus;
            }
        }


        public void SetData(DropDownData data)
        {
            if (_data != null)
            {
                _data.onChangeStatus -= UpdateStatus;
            }
            
            _data = data;
            
            optionText.text = _data.optionName;
            
            _data.onChangeStatus += UpdateStatus;
            
            UpdateStatus(_data.IsSelected);
        }

        void OnSelect()
        {
            selected?.Invoke(this);
        }

        void UpdateStatus(bool isSelected)
        {
            tickGameObject.SetActiveWithChecker(_data.IsSelected);
        }
    }
}