using System;
using System.Collections;
using b100SDK.Scripts.EventHandler;
using DG.Tweening;
using Sirenix.OdinInspector;
using UnityEngine;
using Random = UnityEngine.Random;

namespace b100SDK.Scripts.UI.Components.ResourcesClaim
{
    public class ResourceClaimFx : MonoBehaviour
    {
        [Header("Events Listener")]
        [SerializeField]
        private IntEventChannelSO OnClaimResourceDefault;
        
        [Header("Events Broadcast")]
        [SerializeField]
        private IntEventChannelSO OnClaimResourceComplete;
        
        [Header("Fx")]
        [SerializeField] private GameObject resourceIconPrefab;
        [SerializeField] private Transform resourceIcon, spawnTrans, maxRdTrans, minRdTrans;
        
        
        [SerializeField]
        private bool autoHidden = false;
        
        [SerializeField, ShowIf(nameof(autoHidden))]
        private CanvasGroup canvasGroup;

        [SerializeField, ShowIf(nameof(autoHidden))]
        private float delayHidden = 5f;        
        
        [SerializeField, ShowIf(nameof(autoHidden))]
        private float hiddenDuration = 1f;

        [SerializeField, ShowIf(nameof(autoHidden))]
        private GameObject container;



        private Vector3 _maxRdPos, _minRdPos;

        private void OnEnable()
        {
            OnClaimResourceDefault.OnEventRaised += ClaimResourceDefault;
            
            if (autoHidden)
            {
                canvasGroup.alpha = 0;
                container.SetActive(false);
            }

        }
        
        private void OnDisable()
        {
            OnClaimResourceDefault.OnEventRaised -= ClaimResourceDefault;
        }

        void CalculateSpawnBounds()
        {
            _maxRdPos = maxRdTrans.position;
            _minRdPos = minRdTrans.position;
        }

        [Button]
        void ClaimResourceDefault(int value)
        {
            ClaimResource(value);
        }

        [Button]
        public void ClaimResource(int value, Transform spawn = null, int loops = 10, float interval = .05f)
        {
            container.SetActive(true);
            canvasGroup.alpha = 1f;

            
            Vector3 spawnOffset = Vector3.zero;
            if (spawn)
                spawnOffset = spawn.position - spawnTrans.position;
                
            StartCoroutine(ClaimResourceCoroutine(value, spawnOffset, loops, interval));
        }
        
        public IEnumerator ClaimResourceCoroutine(int value, Vector3 spawnOffset, int loops, float interval)
        {
            int val = Math.DivRem(value, loops, out int remain);
            
            for (int i = 0; i < loops; i++)
            {
                SpawnResource(i == loops - 1 ? val + remain : val, spawnOffset);
                yield return new WaitForSeconds(interval);
            }
            
            Hidden();
        }
        
        void SpawnResource(int value, Vector3 spawnOffset)
        {
            CalculateSpawnBounds();
        
            Vector3 rdPos = spawnOffset + new Vector3(Random.Range(_minRdPos.x, _maxRdPos.x),
                Random.Range(_minRdPos.y, _maxRdPos.y),
                Random.Range(_minRdPos.z, _maxRdPos.z));

            Vector3 spawnPos = spawnTrans.position + spawnOffset;
            Transform spawnedResource = Instantiate(resourceIconPrefab, spawnPos, Quaternion.identity, transform).transform;

            spawnedResource.localScale = Vector3.zero;
            spawnedResource.DOScale(Vector3.one, .3f).SetEase(Ease.OutExpo);
            spawnedResource.DOMove(rdPos, .5f).SetEase(Ease.OutExpo).OnComplete(() =>
            {
                spawnedResource.DOMove(resourceIcon.position, .3f).SetEase(Ease.InExpo).SetDelay(.1f).OnComplete(() =>
                {
                    //TODO: Haptics
                    OnClaimResourceComplete.RaiseEvent(value);
                    
                    DOTween.Kill(resourceIcon);
                    resourceIcon.DOScale(Vector3.one * 1.1f, .05f).OnComplete(() =>
                    {
                        resourceIcon.DOScale(Vector3.one, .05f);
                    });

                    spawnedResource.DOScale(Vector3.zero, .1f).OnComplete(() =>
                    {
                        Destroy(spawnedResource.gameObject);
                    });
                });
            });
        }
        
        void Hidden()
        {
            if (!autoHidden)
                return;

            canvasGroup.DOKill();

            DOVirtual.DelayedCall(delayHidden, () =>
            {
                canvasGroup.DOFade(0, hiddenDuration).OnComplete(() =>
                {
                    container.SetActive(false);
                }).SetTarget(canvasGroup);
            }).SetTarget(canvasGroup);

        }

    }
}