using b100SDK.Scripts.EventHandler;
using b100SDK.Scripts.Utilities.Extensions;
using DG.Tweening;
using Sirenix.OdinInspector;
using TMPro;
using UnityEngine;

namespace b100SDK.Scripts.UI.Components.ResourcesClaim
{
    public class ResourcesTween : MonoBehaviour
    {
        [Header("Events")]
        [SerializeField]
        private IntResultEventChannelSO OnGetCurrentResource;
        
        [SerializeField]
        private BoolEventChannelSO OnChangeResource;
        
        [SerializeField] private TMP_Text resourceText;

        [Header("Effects")] 
        [SerializeField] private Transform startTransform;
        [SerializeField] private RectTransform resourceChangeEffect;
        [SerializeField] private CanvasGroup resourceChangeCanvasGroup;
        [SerializeField] private float resourceChangeDuration = 1f;
        [SerializeField] private TMP_Text resourceChangeText;
    
        private int _currentMoney;

        private void OnEnable()
        {
            _currentMoney = OnGetCurrentResource.RaiseEvent();   
            resourceText.text = _currentMoney.ToFormatString();

            OnChangeResource.OnEventRaised += UpdateMoney;
        }

        private void OnDisable()
        {
            OnChangeResource.OnEventRaised -= UpdateMoney;
        }

        void UpdateMoney(bool success)
        {
            DOTween.Kill(this);
            if (success)
            {
                int tmp = _currentMoney;
                DOTween.To(() => tmp, UpdateMoneyText, OnGetCurrentResource.RaiseEvent(), .2f)
                    .SetEase(Ease.Linear).SetTarget(this);
                
                if (tmp > OnGetCurrentResource.RaiseEvent()) StartAnimMoneyChange(tmp -  OnGetCurrentResource.RaiseEvent());
            }
            else
            {
                // Error anim
            }
        }

        void UpdateMoneyText(int money)
        {
            _currentMoney = money;
            resourceText.text = money.ToFormatString();
        }

        [Button]
        void StartAnimMoneyChange(int value)
        {
            resourceChangeEffect.DOKill(true);
            resourceChangeEffect.gameObject.SetActive(true);
            
            resourceChangeText.text = " - " + value;

            resourceChangeEffect.localPosition = startTransform.localPosition;

            resourceChangeEffect.transform.DOLocalMoveY(30f, resourceChangeDuration);
            DOVirtual.Float(1f, 0f, resourceChangeDuration, x => resourceChangeCanvasGroup.alpha = x)
                .OnComplete(() => resourceChangeEffect.gameObject.SetActive(false)).SetTarget(resourceChangeEffect);
        }
    }
}