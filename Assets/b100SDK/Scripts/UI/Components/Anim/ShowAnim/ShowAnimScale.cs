using System;
using b100SDK.Scripts.Base;
using DG.Tweening;
using Sirenix.OdinInspector;
using UnityEngine;

namespace b100SDK.Scripts.UI.Components.Anim.ShowAnim
{
    public class ShowAnimScale : MonoBehaviour, IShowAnimatable
    {          
        [SerializeField]
        private float showAnimTime = .35f;
        
        [SerializeField]
        public float delayShowAnim = 0f;
        
        [SerializeField]
        private float initScale = 0;

        [SerializeField]
        private bool separateAxisShowAnim;
        [SerializeField, HideIf(nameof(separateAxisShowAnim))]
        private EasingType easingType = EasingType.OutBack;
        
        [ShowIf(nameof(UseShowAnimCurve))]
        public AnimationCurve showAnimCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        
        [SerializeField]
        [ShowIf(nameof(separateAxisShowAnim))]
        private EasingType showAnimXAxisEasingType = EasingType.OutBack;

        [SerializeField]
        [ShowIf(nameof(UseXAxisShowAnimCurve))]
        private AnimationCurve showAnimXAxisCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);

        [SerializeField]
        [ShowIf(nameof(separateAxisShowAnim))]
        private EasingType showAnimYAxisEasingType = EasingType.OutBack;

        [SerializeField]
        [ShowIf(nameof(UseYAxisShowAnimCurve))]
        private AnimationCurve showAnimYAxisCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        
        [Space]
        [SerializeField]
        private bool unscaleTime = true;

        private bool UseShowAnimCurve
        {
            get
            {
                if (separateAxisShowAnim)
                    return false;

                return easingType == EasingType.Custom;
            }
        }

        private bool UseXAxisShowAnimCurve =>
            separateAxisShowAnim && showAnimXAxisEasingType == EasingType.Custom;

        private bool UseYAxisShowAnimCurve =>
            separateAxisShowAnim && showAnimYAxisEasingType == EasingType.Custom;
        
        public void Execute(Action onComplete = null)
        {
            transform.localScale = Vector3.one * initScale;

            if (separateAxisShowAnim)
            {
                Ease dotweenXEase = (Ease)showAnimXAxisEasingType;
                Ease dotweenYEase = (Ease)showAnimYAxisEasingType;

                var xTween = transform.DOScaleX(1, showAnimTime)
                    .SetUpdate(unscaleTime)
                    .OnComplete(() => onComplete?.Invoke())
                    .SetTarget(transform)
                    .SetDelay(delayShowAnim);

                if (UseXAxisShowAnimCurve)
                    xTween.SetEase(showAnimXAxisCurve);
                else
                    xTween.SetEase(dotweenXEase);

                var yTween = transform.DOScaleY(1, showAnimTime)
                    .SetUpdate(unscaleTime)
                    .SetTarget(transform)
                    .SetDelay(delayShowAnim);

                if (UseYAxisShowAnimCurve)
                    yTween.SetEase(showAnimYAxisCurve);
                else
                    yTween.SetEase(dotweenYEase);
            }
            else
            {
                var tween = transform.DOScale(1, showAnimTime)
                    .SetUpdate(unscaleTime)
                    .OnComplete(() => onComplete?.Invoke())
                    .SetTarget(transform)
                    .SetDelay(delayShowAnim);

                if (UseShowAnimCurve)
                    tween.SetEase(showAnimCurve);
                else
                    tween.SetEase((Ease) easingType);
            }
        }

    }
}