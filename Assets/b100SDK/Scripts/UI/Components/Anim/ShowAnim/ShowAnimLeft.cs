using System;
using b100SDK.Scripts.Base;
using DG.Tweening;
using Sirenix.OdinInspector;
using UnityEngine;

namespace b100SDK.Scripts.UI.Components.Anim.ShowAnim
{
    [RequireComponent(typeof(RectTransform))]
    public class ShowAnimLeft : MonoBehaviour, IShowAnimatable
    {
        [Space]
        [Header("Components")]
        [SerializeField]
        private RectTransform rectTransform;
        
        [SerializeField]
        private RectTransform container;
        
        [Space]
        [Header("Configs")]
        [SerializeField]
        private float showAnimTime = .35f;
        
        [SerializeField]
        private float delayShowAnim = 0f;

        [SerializeField]
        private EasingType easingType = EasingType.OutBack;
        
        [ShowIf(nameof(UseShowAnimCurve))]
        [SerializeField]
        private AnimationCurve showAnimCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        
        
        [Space]
        [SerializeField]
        private bool unscaleTime = true;

        private bool UseShowAnimCurve
        {
            get
            {
                return easingType == EasingType.Custom;
            }
        }

        
        public void Execute(Action onComplete = null)
        {
            if (!container)
                return;
            
            if (!rectTransform) 
                return;
            
            Rect rootRect = container.rect;
            Rect popupRect = rectTransform.rect;

            float offset = rootRect.width / 2 + popupRect.width / 2;

            rectTransform.localPosition += Vector3.left * offset;

            var tween = rectTransform.DOLocalMoveX(offset, showAnimTime)
                .SetDelay(delayShowAnim)
                .SetUpdate(unscaleTime)
                .SetRelative(true)
                .OnComplete(() => onComplete?.Invoke())
                .SetTarget(rectTransform);

            if (UseShowAnimCurve)
                tween.SetEase(showAnimCurve);
            else
                tween.SetEase((Ease) easingType);
        }


#if UNITY_EDITOR
        private void Reset()
        {
            rectTransform = GetComponent<RectTransform>();

            var animRight = GetComponent<ShowAnimRight>();
            
            if (animRight)
            {
                DestroyImmediate(animRight);
            }
        }
#endif
    }
}