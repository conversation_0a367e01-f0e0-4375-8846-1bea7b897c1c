using System;
using b100SDK.Scripts.Base;
using DG.Tweening;
using Sirenix.OdinInspector;
using UnityEngine;

namespace b100SDK.Scripts.UI.Components.Anim.HideAnim
{
    [RequireComponent(typeof(RectTransform))]
    public class HideAnimRight : MonoBehaviour, IHideAnimatable
    {
        [Space]
        [Header("Components")]
        [SerializeField]
        private RectTransform rectTransform;
        
        [SerializeField]
        private RectTransform container;
        
        [Space]
        [Header("Configs")]
        [SerializeField]
        private float hideAnimTime = .35f;
        
        [SerializeField]
        private float delayHideAnim = 0f;

        [SerializeField]
        private EasingType easingType = EasingType.OutBack;
        
        [ShowIf(nameof(UseHideAnimCurve))]
        [SerializeField]
        private AnimationCurve hideAnimCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        
        
        [Space]
        [SerializeField]
        private bool unscaleTime = true;

        private bool UseHideAnimCurve
        {
            get
            {
                return easingType == EasingType.Custom;
            }
        }

        
        public void Execute(Action onComplete = null)
        {
            if (!container)
                return;
            
            if (!rectTransform)
                return;
            
            Rect rootRect = container.rect;
            Rect popupRect = rectTransform.rect;

            float offset = rootRect.width / 2 + popupRect.width / 2;

            var tween = rectTransform.DOLocalMoveX(offset, hideAnimTime)
                .SetDelay(delayHideAnim)
                .SetUpdate(unscaleTime)
                .SetRelative(true)
                .OnComplete(() => onComplete?.Invoke())
                .SetTarget(rectTransform);

            if (UseHideAnimCurve)
                tween.SetEase(hideAnimCurve);
            else
                tween.SetEase((Ease) easingType);
        }


#if UNITY_EDITOR
        private void Reset()
        {
            rectTransform = GetComponent<RectTransform>();

            var anim = GetComponent<HideAnimLeft>();
            
            if (anim)
            {
                DestroyImmediate(anim);
            }
        }
#endif
    }
}