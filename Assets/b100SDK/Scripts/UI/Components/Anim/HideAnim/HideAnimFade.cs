using System;
using b100SDK.Scripts.Base;
using DG.Tweening;
using Sirenix.OdinInspector;
using UnityEngine;

namespace b100SDK.Scripts.UI.Components.Anim.HideAnim
{
    [RequireComponent(typeof(CanvasGroup))]
    public class HideAnimFade : MonoBehaviour, IHideAnimatable
    {
        [Space]
        [Header("Components")]
        [SerializeField]
        private CanvasGroup canvasGroup;
        
        [Space]
        [Header("Configs")]
        [SerializeField]
        private float showAnimTime = .35f;
        
        [SerializeField]
        private EasingType easingType = EasingType.InBack;
        
        [SerializeField, ShowIf(nameof(UseShowAnimCurve))]
        private AnimationCurve showAnimCurve;
        
        [Space]
        [SerializeField] 
        private bool unscaleTime = true;
        
        private bool UseShowAnimCurve => easingType == EasingType.Custom;
        
        public void Execute(Action onComplete = null)
        {
            if (!canvasGroup)
                return;
            
            canvasGroup.DOKill();

            var tween = canvasGroup.DOFade(0f, showAnimTime)
                .SetUpdate(unscaleTime)
                .OnComplete(() => onComplete?.Invoke())
                .SetTarget(canvasGroup);

            if (UseShowAnimCurve)
                tween.SetEase(showAnimCurve);
            else
                tween.SetEase((Ease)easingType);
            
        }


#if UNITY_EDITOR
        private void Reset()
        {
            canvasGroup = GetComponent<CanvasGroup>();
        }
#endif
    }
}