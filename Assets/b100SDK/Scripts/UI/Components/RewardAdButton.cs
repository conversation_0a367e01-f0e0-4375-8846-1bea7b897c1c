using System.Collections;
using b100SDK.Scripts.Ads;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.UI.Components.Button;
using b100SDK.Scripts.UI.Panel;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

namespace b100SDK.Scripts.UI.Components
{
    public class RewardAdButton :  BhMonoBehavior
    {
        [SerializeField]
        private BhButton button;

        [SerializeField]
        private Image backgroundImage;

        [SerializeField]
        private Sprite bgAdReady;
        
        [SerializeField]
        private Sprite bgAdLoading;

        public UnityEvent onCompleteAd = new();


        private void OnEnable()
        {
            button.onClick.AddListener(HandleClickEvent);
            SetupStatusByAd();
        }

        private void OnDisable()
        {
            button.onClick.RemoveListener(HandleClickEvent);
            StopAllCoroutines();
        }

        public void Show()
        {
            button.Show();
        }
        
        public void Hide()
        {
            button.Hide();
        }


        void SetupStatusByAd()
        {
            if (AdManager.CanShowRewardAd())
            {
                backgroundImage.sprite = bgAdReady;
            }
            else
            {
                backgroundImage.sprite = bgAdLoading;
                
                StopAllCoroutines();
                StartCoroutine(GetStatusRewardAd());
            }


            IEnumerator GetStatusRewardAd()
            {
                while (!AdManager.CanShowRewardAd())
                {
                    yield return null;
                }

                backgroundImage.sprite = bgAdReady;
            }
        }


        void HandleClickEvent()
        {
            if (AdManager.CanShowRewardAd())
            {
                AdManager.ShowRewardedVideoAd(() =>
                {
                    onCompleteAd?.Invoke();
                });
            }
            else
            {
                PopupNotification.Show(GameConst.AD_NOT_AVAILABLE);
            }
        }
        

        private void Reset()
        {
            button = GetComponent<BhButton>();
            backgroundImage = GetComponentInChildren<Image>();
        }
    }
}