using b100SDK.Scripts.Base;
using DG.Tweening;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.UI;

namespace b100SDK.Scripts.UI
{
    public class UILoading : BhMonoBehavior
    {
        [SerializeField]
        private Slider progressBar;

        private void OnEnable()
        {
            progressBar.value = 0f;

            if (Evm)
            {
                Evm.OnChangeProgressLoading.AddListener(Progress);
            }
        }

        private void OnDisable()
        {
            if (Evm)
            {
                Evm.OnChangeProgressLoading.RemoveListener(Progress);
            }
        }

        [Button]
        void Progress(float progress, float speed)
        {
            progressBar.DOKill();
            progressBar.DOValue(progress, speed)
                .SetSpeedBased(true)
                .SetEase(Ease.Linear)
                .SetTarget(progressBar);
        }
    }
}