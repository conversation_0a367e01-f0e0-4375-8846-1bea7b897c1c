using b100SDK.Scripts.Audio;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.UI.Components.Panel;
using b100SDK.Scripts.Utilities;
using Sirenix.OdinInspector;
using UnityEngine;

namespace b100SDK.Scripts.UI
{
    public class UIPanel : BhMonoBehavior
    {
        protected GUIManager gui;
        protected bool isInited;
        
        [Header("Common")]
        public PanelAnim panelAnimator;
        [SerializeField]
        private SoundType openSound = SoundType.OpenPopup;
        [SerializeField]
        private SoundType closeSound = SoundType.ClosePopup;

        [SerializeField]
        private bool hasMaxTimeExist = false;

        [SerializeField, ShowIf(nameof(hasMaxTimeExist))]
        private float maxTimeExist = 300f;

        public virtual UIPanelType GetId()
        {
            return UIPanelType.None;
        }

        public virtual bool HasMaxTimeExist()
        {
            return hasMaxTimeExist;
        }

        public virtual float GetMaxTimeExist()
        {
            return maxTimeExist;
        }

        private void Awake()
        {
            gui = GUIManager.Instance;
            if (panelAnimator)
                panelAnimator.Setup();
        }

        public virtual void OnAppear()
        {
            if (isInited)
                return;
            isInited = true;
            
            if (panelAnimator)
                panelAnimator.StartAnimIn();

            RegisterEvent();
            AudioManager.Shot(openSound);
        }

        public virtual void OnDisappear()
        {
            if (panelAnimator)
                panelAnimator.StartAnimOut();

            UnregisterEvent();
            AudioManager.Shot(closeSound);
            isInited = false;
        }

        public virtual void Close()
        {
            OnDisappear();
            
            if (panelAnimator == null || !panelAnimator.HasAnimOut)
            {
                Hide();
            }
            
            gui.Dismiss(this);
            gui.CheckPopupQueue();
        }

        public virtual void Hide()
        {
            gameObject.SetActiveWithChecker(false);
        }

        protected virtual void RegisterEvent()
        {
        }

        protected virtual void UnregisterEvent()
        {
        }

#if UNITY_EDITOR
        private void Reset()
        {
            panelAnimator = GetComponent<PanelAnim>();
        }
#endif
    }
}