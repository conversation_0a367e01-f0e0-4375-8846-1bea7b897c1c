namespace b100SDK.Scripts.UI.Panel
{
    public class MainScreen : UIPanel
    {
        public static MainScreen Instance { get; private set; }

        public override UIPanelType GetId()
        {
            return UIPanelType.MainScreen;
        }

        public static void Show()
        {
            var newInstance = (MainScreen) GUIManager.Instance.NewPanel(UIPanelType.MainScreen);
            Instance = newInstance;
            newInstance.OnAppear();
        }

        public override void OnAppear()
        {
            if (isInited)
                return;

            base.OnAppear();

            Init();
        }

        private void Init()
        {

        }
        protected override void RegisterEvent()
        {
            base.RegisterEvent();
        }

        protected override void UnregisterEvent()
        {
            base.UnregisterEvent();
        }

        public override void OnDisappear()
        {
            base.OnDisappear();
            Instance = null;
        }

        public override void Close()
        {
            base.Close();
        }
    }
}