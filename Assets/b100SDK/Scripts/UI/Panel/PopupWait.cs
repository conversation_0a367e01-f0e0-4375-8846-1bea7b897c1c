using b100SDK.Scripts.UI;
using b100SDK.Scripts.UI.Components.Button;
using GenerateData.Scripts;
using TMPro;
using UnityEngine;

namespace b100SDK.Scripts.UI.Panel
{
    public class PopupWait : UIPanel
    {
        [SerializeField]
        private BhButton cancelButton;

        [SerializeField]
        private TMP_Text levelCount;
        
        public static PopupWait Instance { get; private set; }
    
        public override UIPanelType GetId()
        {
            return UIPanelType.PopupWait;
        }
    
        public static void Show()
        {
            var newInstance = (PopupWait) GUIManager.Instance.NewPanel(UIPanelType.PopupWait);
            Instance = newInstance;
            newInstance.OnAppear();
        }
    
        public override void OnAppear()
        {
            if (isInited)
                return;
    
            base.OnAppear();
    
            Init();
        }
        
        public override void OnDisappear()
        {
            base.OnDisappear();
            Instance = null;
        }

        protected override void RegisterEvent()
        {
            base.RegisterEvent();
            cancelButton.onClick.AddListener(Cancel);
            
            Evm.OnUpdateLevelCreatedCount.AddListener(UpdateLevelCount);
        }
    
        protected override void UnregisterEvent()
        {
            base.UnregisterEvent();
            cancelButton.onClick.RemoveListener(Cancel);
            
            Evm.OnUpdateLevelCreatedCount.RemoveListener(UpdateLevelCount);
        }
    
        private void Init()
        {
        }

        void Cancel()
        {
            if (LevelEditor.Instance)
            {
                LevelEditor.Instance.CancelCreate();
            }
        }
        
        void UpdateLevelCount(int count)
        {
            levelCount.text = "Level Created: " + count;
        }
    }
}