using System;
using b100SDK.Scripts.UI.Components.Button;
using TMPro;
using UnityEngine;

namespace b100SDK.Scripts.UI.Panel
{
    public class PopupConsent : UIPanel
    {
        [SerializeField] private TMP_Text title;
        [SerializeField] private BhButton btnAgree, btnCancel;
    
        public static PopupConsent Instance { get; private set; }

        public override UIPanelType GetId()
        {
            return UIPanelType.PopupConsent;
        }

        private Action _onConsentUpdated;
    
        public static void Show(Action onConsentUpdated)
        {
            var newInstance = (PopupConsent) GUIManager.Instance.NewPanel(UIPanelType.PopupConsent);
            Instance = newInstance;
            newInstance.OnAppear(onConsentUpdated);
        }

        public void OnAppear(Action onConsentUpdated)
        {
            if (isInited)
                return;

            base.OnAppear();

            Init(onConsentUpdated);
        }

        private void Init(Action onConsentUpdated)
        {
            _onConsentUpdated = onConsentUpdated;
        }

        public void OnConsentUpdate(bool agree)
        {
        
            Gm.data.user.isConsentUpdated = agree;

            if (!agree)
            {
                Application.Quit();
                return;
            }
        
            _onConsentUpdated?.Invoke();
            Close();
        }

        protected override void RegisterEvent()
        {
            base.RegisterEvent();
        
            btnAgree.onClick.AddListener(() => OnConsentUpdate(true));
            btnCancel.onClick.AddListener(() => OnConsentUpdate(false));
        }

        protected override void UnregisterEvent()
        {
            base.UnregisterEvent();
        
            btnAgree.onClick.RemoveListener(() => OnConsentUpdate(true));
            btnCancel.onClick.RemoveListener(() => OnConsentUpdate(false));
        }

        public override void OnDisappear()
        {
            base.OnDisappear();
            Instance = null;
        }
    }
}