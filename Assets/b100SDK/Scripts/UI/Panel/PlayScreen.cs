namespace b100SDK.Scripts.UI.Panel
{
    public class PlayScreen : UIPanel
    {
        public static PlayScreen Instance { get; private set; }
        public override UIPanelType GetId()
        {
            return UIPanelType.PlayScreen;
        }

        public static void Show()
        {
            var newInstance = (PlayScreen) GUIManager.Instance.NewPanel(UIPanelType.PlayScreen);
            Instance = newInstance;
            newInstance.OnAppear();
        }

        public override void OnAppear()
        {
            base.OnAppear();
            Init();
        }

        private void Init()
        {
        }

        public override void OnDisappear()
        {
            base.OnDisappear();
            Instance = null;
        }

        protected override void RegisterEvent()
        {
            base.RegisterEvent();
        }

        protected override void UnregisterEvent()
        {
            base.UnregisterEvent();
        }
    }
}