#region

using b100SDK.Scripts.Base;
using b100SDK.Scripts.Vibration;
using UnityEngine;
using UnityEngine.UI;

#endregion

namespace b100SDK.Scripts.UI.Panel
{
    public class PopupRate : UIPanel
    {
        private int _starCount = 4;

        [SerializeField]
        private Image[] imgStar;

        [SerializeField]
        private Sprite[] sprStar;

        public override UIPanelType GetId()
        {
            return UIPanelType.PopupRate;
        }

        public static void Show()
        {
            var newInstance = (PopupRate) GUIManager.Instance.NewPanel(UIPanelType.PopupRate);
            newInstance.OnAppear();
        }

        public override void OnAppear()
        {
            if (isInited)
                return;

            base.OnAppear();

            _starCount = 4;
            SetStar();
        }

        private void SetStar()
        {
            for (var i = 0; i < 5; i++)
                if (i <= _starCount)
                    imgStar[i].sprite = sprStar[0];
                else
                    imgStar[i].sprite = sprStar[1];
        }

        public void OnClickRate(int index)
        {
            BhVibrate.Haptic(BhHapticTypes.SoftImpact);
            _starCount = index;
            SetStar();
        }

        public void OnConfirmRate()
        {
            Close();

            if (_starCount < 4)
            {
                PopupNotification.Show(GameConst.FEEDBACK_THANKS);
            }
            else
            {
#if UNITY_ANDROID
                Application.OpenURL(@"https://play.google.com/store/apps/details?id=" + Cfg.gameSetting.packageName);
#elif UNITY_IOS
        if (!Device.RequestStoreReview())
        {
            Application.OpenURL(@"https://apps.apple.com/us/app/id" + GameManager.Instance.GameSetting.AppstoreID);
        }
#else
            Debug.Log("Rated in store!");
#endif
            }

            Gm.data.user.rated = true;
            Database.SaveData();
        }

        public void OnCancelRate()
        {
            Close();
        }
    }
}