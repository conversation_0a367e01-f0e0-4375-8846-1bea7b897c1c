namespace b100SDK.Scripts.UI.Panel
{
    public class PopupSetting : UIPanel
    {
        public static PopupSetting Instance { get; private set; }
    
        public override UIPanelType GetId()
        {
            return UIPanelType.PopupSetting;
        }
    
        public static void Show()
        {
            var newInstance = (PopupSetting) GUIManager.Instance.NewPanel(UIPanelType.PopupSetting);
            Instance = newInstance;
            newInstance.OnAppear();
        }
    
        public override void OnAppear()
        {
            if (isInited)
                return;
    
            base.OnAppear();
    
            Init();
        }
        
        public override void OnDisappear()
        {
            base.OnDisappear();
            Instance = null;
        }

        protected override void RegisterEvent()
        {
            base.RegisterEvent();
        }
    
        protected override void UnregisterEvent()
        {
            base.UnregisterEvent();
        }
    
        private void Init()
        {
        }
    }
}