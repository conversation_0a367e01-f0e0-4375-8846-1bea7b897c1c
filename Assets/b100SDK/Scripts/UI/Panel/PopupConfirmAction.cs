using System;
using b100SDK.Scripts.UI;
using b100SDK.Scripts.UI.Components.Button;
using TMPro;
using UnityEngine;

namespace b100SDK.Scripts.UI.Panel
{
    public class PopupConfirmAction : UIPanel
    {
        [SerializeField]
        private BhButton closeButton;
        
        [SerializeField]
        private BhButton confirmButton;

        [SerializeField]
        private TMP_Text messageText;
        
        private Action _onConfirmAction;
        
        public static PopupConfirmAction Instance { get; private set; }
    
        public override UIPanelType GetId()
        {
            return UIPanelType.PopupConfirmAction;
        }
    
        public static void Show(string message, Action onConfirmAction)
        {
            var newInstance = (PopupConfirmAction) GUIManager.Instance.NewPanel(UIPanelType.PopupConfirmAction);
            Instance = newInstance;
            newInstance.OnAppear(message, onConfirmAction);
        }
    
        void OnAppear(string message, Action onConfirmAction)
        {
            if (isInited)
                return;
    
            base.OnAppear();
    
            Init();
            
            messageText.text = message;
            _onConfirmAction = onConfirmAction;
        }
        
        public override void OnDisappear()
        {
            base.OnDisappear();
            Instance = null;
        }

        protected override void RegisterEvent()
        {
            base.RegisterEvent();
            closeButton.onClick.AddListener(Close);
            confirmButton.onClick.AddListener(Confirm);
        }
    
        protected override void UnregisterEvent()
        {
            base.UnregisterEvent();
            closeButton.onClick.RemoveListener(Close);
            confirmButton.onClick.RemoveListener(Confirm);
        }
    
        private void Init()
        {
        }

        void Confirm()
        {
            _onConfirmAction?.Invoke();
            Close();
        }
    }
}