namespace b100SDK.Scripts.UI.Panel
{
    public class PopupNoInternet : UIPanel
    {
        public static PopupNoInternet Instance { get; private set; }
    
        public override UIPanelType GetId()
        {
            return UIPanelType.PopupNoInternet;
        }
    
        public static void Show()
        {
            var newInstance = (PopupNoInternet) GUIManager.Instance.NewPanel(UIPanelType.PopupNoInternet);
            Instance = newInstance;
            newInstance.OnAppear();
        }
    
        public override void OnAppear()
        {
            if (isInited)
                return;
    
            base.OnAppear();
    
            Init();
        }
        
        public override void OnDisappear()
        {
            base.OnDisappear();
            Instance = null;
        }

        protected override void RegisterEvent()
        {
            base.RegisterEvent();
        }
    
        protected override void UnregisterEvent()
        {
            base.UnregisterEvent();
        }
    
        private void Init()
        {
        }
    }
}