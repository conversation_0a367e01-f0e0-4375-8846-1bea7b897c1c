#region

using DG.Tweening;
using TMPro;

#endregion

namespace b100SDK.Scripts.UI.Panel
{
    public class PopupNotification : UIPanel
    {
        public TMP_Text txtMessage;

        public override UIPanelType GetId()
        {
            return UIPanelType.PopupNotification;
        }

        public static void Show(string message, float duration = 3f)
        {
            var newInstance = (PopupNotification) GUIManager.Instance.NewPanel(UIPanelType.PopupNotification);
            newInstance.OnAppear(message, duration);
        }

        private void OnAppear(string message, float duration)
        {
            base.OnAppear();
            txtMessage.text = message;

            txtMessage.DOKill();
            DOVirtual.DelayedCall(duration, () => Close()).SetTarget(txtMessage);
        }
    }
}