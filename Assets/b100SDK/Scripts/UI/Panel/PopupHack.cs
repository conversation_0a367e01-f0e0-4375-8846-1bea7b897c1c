using b100SDK.Scripts.Ads;
using b100SDK.Scripts.IAP;
using b100SDK.Scripts.UI.Components;
using b100SDK.Scripts.UI.Components.Button;
using UnityEngine;

namespace b100SDK.Scripts.UI.Panel
{
    public class PopupHack : UIPanel
    {
        [Space]
        [SerializeField]
        private BhButton closeButton;
    
        [Head<PERSON>("Ads")]
        [SerializeField]
        private SwitchButton switchButtonAds;
    
        [<PERSON><PERSON>("IAP")]
        [SerializeField]
        private SwitchButton switchButtonIap;
    
        public static PopupHack Instance { get; private set; }

        public override UIPanelType GetId()
        {
            return UIPanelType.PopupHack;
        }

        public static void Show()
        {
            var newInstance = (PopupHack) GUIManager.Instance.NewPanel(UIPanelType.PopupHack);
            Instance = newInstance;
            newInstance.OnAppear();
        }

        public override void OnAppear()
        {
            if (isInited)
                return;

            base.OnAppear();

            Init();
        }

        private void Init()
        {
            switchButtonAds.Init(AdManager.Instance.IsDebugAds, SetStatusAds);
            switchButtonIap.Init(IAPManager.Instance.IsDebugIAP, SetStatusIAP);
        }

        protected override void RegisterEvent()
        {
            base.RegisterEvent();
            closeButton.onClick.AddListener(Close);
        }

        protected override void UnregisterEvent()
        {
            base.UnregisterEvent();
            closeButton.onClick.RemoveListener(Close);
        }

        public override void OnDisappear()
        {
            base.OnDisappear();
            Instance = null;
        }

        void SetStatusAds(bool statusAds)
        {
            AdManager.Instance.IsDebugAds = statusAds;
        }

        void SetStatusIAP(bool statusIAP)
        {
            IAPManager.Instance.IsDebugIAP = !statusIAP;
        }
    }
}