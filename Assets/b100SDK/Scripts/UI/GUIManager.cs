using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.DesignPatterns;
using b100SDK.Scripts.UI.Panel;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Extensions;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace b100SDK.Scripts.UI
{
    public class GUIManager : Singleton<GUIManager>
    {
        private readonly Dictionary<UIPanelType, UIPanel> _initiedPanelMap = new Dictionary<UIPanelType, UIPanel>();
        
        [SerializeField]
        private SerializedDictionary<UIPanelType, float> _existTimeLeftMap = new();

        private readonly Queue<Action> _queuePopup = new Queue<Action>();

        private readonly List<UIPanel> _showingNotifications = new List<UIPanel>();

        private readonly List<UIPanel> _showingPopups = new List<UIPanel>();

        private Stack<UIPanel> _screenStack = new Stack<UIPanel>();

        [SerializeField]
        private Canvas layerLoading;

        [SerializeField]
        private Canvas layerNotify;

        [SerializeField]
        private Canvas layerPopup;

        [SerializeField]
        private Canvas layerScreen;

        [SerializeField]
        private Camera mainCamera;

        [SerializeField]
        private Canvas root;

        protected override void Awake()
        {
            base.Awake();
            ClearGui();
        }

        public void ReloadCamera()
        {
            if (mainCamera == null)
            {
                mainCamera = Camera.main;
            }

            root.worldCamera = mainCamera;
        }

        public void Init()
        {
            ReloadCamera();

            Evm.OnStartLoadScene.AddListener(StartLoading);
            Evm.OnFinishLoadScene.AddListener(FinishLoading);
            Evm.OnFinishLoadScene.AddListener(ReloadCamera);

            Evm.OnEverySecondTick.AddListener(AutoRemovePanel);
        }

        public UIPanel NewPanel(UIPanelType id)
        {
            var type = id.ToString().GetPanelType();

            UIPanel newPanel = null;
            if (_initiedPanelMap.ContainsKey(id))
            {
                newPanel = _initiedPanelMap[id];

                AddPanelToAutoRemovePanelMap(newPanel);
            }
            else
            {
                newPanel = Instantiate(GetPrefab(id), GetRootByType(type).transform);
                
                _initiedPanelMap.TryAdd(id, newPanel);
                
                AddPanelToAutoRemovePanelMap(newPanel);
            }

            if (type == PanelType.Popup)
            {
                if (_showingPopups.Contains(newPanel))
                {
                    _showingPopups.Remove(newPanel);
                }

                _showingPopups.Add(newPanel);
            }
            else if (type == PanelType.Notification)
            {
                if (!_showingNotifications.Contains(newPanel))
                {
                    _showingNotifications.Add(newPanel);
                }
            }
            else
            {
                var currentScreen = GetCurrentScreen();
                if (currentScreen != null && currentScreen.GetId() != id && currentScreen.gameObject.activeSelf)
                {
                    currentScreen.Close();
                }

                if (_screenStack.Contains(newPanel))
                {
                    _screenStack = MakeElementToTopStack(newPanel, _screenStack);
                }
                else
                {
                    _screenStack.Push(newPanel);
                }
            }

            newPanel.transform.SetAsLastSibling();
            newPanel.gameObject.SetActiveWithChecker(true);

            return newPanel;
        }
        
        
        

        public UIPanel GetCurrentScreen()
        {
            if (_screenStack.Count == 0)
                return null;

            return _screenStack.Peek();
        }

        public void GoBackLastScreen()
        {
            _screenStack.Pop().Close();

            if (GetCurrentScreen() == null || GetCurrentScreen().GetId() == UIPanelType.MainScreen)
            {
                MainScreen.Show();
            }
            else
            {
                var newPanel = NewPanel(GetCurrentScreen().GetId());
                newPanel.OnAppear();
            }
        }

        public void ClearGui()
        {
            foreach (var pair in _initiedPanelMap)
            {
                pair.Value.Hide();
            }

            DestroyChildren(layerScreen.transform);
            DestroyChildren(layerPopup.transform);
            DestroyChildren(layerNotify.transform);
            
            _initiedPanelMap.Clear();
            _existTimeLeftMap.Clear();
        }

        public void Dismiss(UIPanel panel)
        {
            _showingPopups.Remove(panel);
            _showingNotifications.Remove(panel);

            //Show main screen if hide all screen
            if (GetCurrentScreen().GetId().Equals(panel.GetId()) && GetCurrentScreen().GetId() != UIPanelType.MainScreen)
            {
                MainScreen.Show();
            }
        }

        public void DismissTopPopup()
        {
            var topPanel = GetTopPopup();
            if (topPanel == null)
                return;

            topPanel.Close();
            Dismiss(topPanel);
        }

        public void DismissPanelById(UIPanelType id)
        {
            var panel = GetPanel(id);
            if (panel == null)
                return;

            panel.Close();
            Dismiss(panel);
        }


        public void CheckPopupQueue()
        {
            if (_showingPopups.Count == 0 && _queuePopup.Count > 0)
            {
                if (_queuePopup.Peek() != null)
                {
                    _queuePopup.Dequeue().Invoke();
                }
            }
        }

        public void AddPopupQueue(Action action)
        {
            _queuePopup.Enqueue(action);

            CheckPopupQueue();
        }


        #region Optimize


        void AddPanelToAutoRemovePanelMap(UIPanel panel)
        {
            if (!panel.HasMaxTimeExist())
                return;

            if (_existTimeLeftMap.ContainsKey(panel.GetId()))
            {
                _existTimeLeftMap[panel.GetId()] = panel.GetMaxTimeExist();
            }
            else
            {
                _existTimeLeftMap.TryAdd(panel.GetId(), panel.GetMaxTimeExist());
            }
        }
        
        void AutoRemovePanel()
        {
            var allKey = _existTimeLeftMap.Keys.ToList();
            foreach (var uiPanelType in allKey)
            {
                if (_initiedPanelMap[uiPanelType].gameObject.activeSelf)
                {
                    continue;
                }

                _existTimeLeftMap[uiPanelType]--;
                
                if (_existTimeLeftMap[uiPanelType] <= 0)
                {
                    RemovePanel(uiPanelType);
                }
            }
        }

        public void RemovePanel(UIPanelType uiPanelType)
        {
            if (!_initiedPanelMap.ContainsKey(uiPanelType))
            {
                return;
            }
            
            var panel = _initiedPanelMap[uiPanelType];

            if (!panel)
            {
                return;
            }

            if (panel.gameObject.activeSelf)
            {
                Dismiss(panel);
            }
            
            _initiedPanelMap.Remove(uiPanelType);
            
            _existTimeLeftMap.Remove(uiPanelType);
            
            Destroy(panel.gameObject);
        }


        public void RemovePanel(List<UIPanelType> panelTypes)
        {
            for (var i = 0; i < panelTypes.Count; i++)
            {
                RemovePanel(panelTypes[i]);
            }
        }
        

        #endregion
        
        
        
        
        #region Loading
        public GameObject loadingUi;

        void StartLoading()
        {
            loadingUi.SetActiveWithChecker(true);
        }

        void FinishLoading()
        {
            loadingUi.SetActiveWithChecker(false);
        }
        #endregion
        
        


        #region Utilities
        UIPanel GetTopPopup()
        {
            if (_showingPopups.Count == 0)
                return null;

            return _showingPopups.GetLast();
        }

        private UIPanel GetPrefab(UIPanelType id)
        {
            return Cfg.uiConfig.GetPanel(id);
        }

        private Canvas GetRootByType(PanelType type)
        {
            switch (type)
            {
                case PanelType.Screen:
                    return layerScreen;
                case PanelType.Popup:
                    return layerPopup;
                case PanelType.Notification:
                    return layerNotify;
                case PanelType.Loading:
                    return layerLoading;
            }

            return null;
        }

        void DestroyChildren(Transform transform)
        {
            var totalChild = transform.childCount;

            if (totalChild == 0)
                return;

            for (var i = totalChild - 1; i >= 0; i--)
                Destroy(transform.GetChild(i).gameObject);
        }

        UIPanel GetPanel(UIPanelType type)
        {
            if (_initiedPanelMap.ContainsKey(type))
                return _initiedPanelMap[type];

            return null;
        }

        Stack<UIPanel> MakeElementToTopStack(UIPanel objectTop, Stack<UIPanel> stack)
        {
            var extraPanel = stack.ToArray();
            for (var i = 0; i < extraPanel.Length; i++)
                if (extraPanel[i] == objectTop)
                {
                    for (var ii = i; ii > 0; ii--)
                        extraPanel[ii] = extraPanel[ii - 1];

                    extraPanel[0] = objectTop;
                }

            Array.Reverse(extraPanel);
            return new Stack<UIPanel>(extraPanel);
        }
        #endregion
    }
}