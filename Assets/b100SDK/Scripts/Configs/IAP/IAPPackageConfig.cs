using UnityEngine;
using UnityEngine.Purchasing;

namespace b100SDK.Scripts.Configs.IAP
{
    [CreateAssetMenu(menuName = "b100 SDK/Configs/IAP/IAP Package Config", fileName = "IAP Package Config")]
    public class IAPPackageConfig : ScriptableObject
    {
        public IAPPackageType packageType;
        public string packageId;
        public ProductType productType;
        public int maxTimesBuyCount = -1;
        public string packageName;
        public float defaultPrice = 4.99f;

        public SubscriptionGroupType groupType = SubscriptionGroupType.None;
    }
}