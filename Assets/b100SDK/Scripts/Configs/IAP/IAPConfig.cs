using System.Collections.Generic;
using System.Linq;
using b100SDK.Scripts.IAP;
using b100SDK.Scripts.Utilities.Tool;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.Purchasing;
#if UNITY_EDITOR
#endif


namespace b100SDK.Scripts.Configs.IAP
{
    [CreateAssetMenu(menuName = "b100 SDK/Configs/IAP/IAP Config", fileName = "IAP Config")]
    public class IAPConfig : ScriptableObject
    {
        [TableList(NumberOfItemsPerPage = 10, ShowPaging = true, DrawScrollView = false, ShowIndexLabels = false)]
        public List<IAPPackageConfig> configs;
        
        [PropertySpace(20)]
        //These proration modes are the ones recommended by the Google Play Store, but you may want to use different modes for your specific situation.
        //https://developer.android.com/google/play/billing/subscriptions#proration-recommendations
        public GooglePlayProrationMode upgradeSubscriptionProrationMode = GooglePlayProrationMode.ImmediateAndChargeProratedPrice;
        public GooglePlayProrationMode downgradeSubscriptionProrationMode = GooglePlayProrationMode.Deferred;

        [PropertySpace(20)]
        public bool isDebugIAP;
        
        public IAPPackageConfig GetData(IAPPackageType type) => configs.FirstOrDefault(x => x.packageType == type);
        
        public IAPPackageConfig GetData(string packageId) => configs.FirstOrDefault(x => x.packageId == packageId);
        
        
        
        


#if UNITY_EDITOR
        [PropertySpace(20)]
        [FolderPath]
        public string path;

        [PropertySpace(10)]
        [Button(ButtonSizes.Medium)]
        public void GetConfig()
        {
            var allConfigs = ConfigTool.GetConfigs<IAPPackageConfig>(path);
            if (allConfigs != null && allConfigs.Count > 0)
            {
                allConfigs.Sort((a, b) => a.packageType.CompareTo(b.packageType));
            }

            if (allConfigs != null)
            {
                configs = allConfigs;
            }
            else
            {
                configs.Clear();
            }
            
        }
        
        [Button(ButtonSizes.Large, ButtonStyle.Box), PropertySpace(20, 20), GUIColor(0.40f, 0.8f, .1f)]
        void CreateOrSync()
        {
            IAPCreator.OpenWindow();
        }
#endif
    }
}