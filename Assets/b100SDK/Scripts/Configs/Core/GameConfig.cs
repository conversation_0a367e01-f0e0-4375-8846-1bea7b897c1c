#region

using Sirenix.OdinInspector;
using UnityEngine;

#endregion

namespace b100SDK.Scripts.Configs.Core
{
    [CreateAssetMenu(fileName = "Game Config", menuName = "b100 SDK/Configs/Core/Game Config")]
    public class GameConfig : ScriptableObject
    {
        [TabGroup("Extra Feature")] 
        public ExtraFeatureConfig extraFeatureConfig;

        [TabGroup("Push Notifications")]
        public PushNotificationConfig pushNotificationConfig;
    }
}