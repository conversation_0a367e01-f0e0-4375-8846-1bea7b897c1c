using b100SDK.Scripts.Audio;
using b100SDK.Scripts.Utilities;
using UnityEngine;

#if UNITY_EDITOR
using b100SDK.Scripts.Utilities.Tool;
#endif

namespace b100SDK.Scripts.Configs.Core
{
    [CreateAssetMenu(fileName = "Audio Config", menuName = "b100 SDK/Configs/Core/Audio Config")]
    public class AudioConfig : ScriptableObject
    {
        [Header("Music")]
        [SerializeField]
        private SerializedDictionary<MusicType, string> musicPathMap;
        
        [Header("Sound")]
        [SerializeField]
        private SerializedDictionary<SoundType, string> soundPathMap;
        

        public string GetMusicPath(MusicType musicType)
        {
            if (musicPathMap.TryGetValue(musicType, out string path))
            {
                return path;
            }

            return null;
        }

        public string GetSoundPath(SoundType soundType)
        {
            if (soundPathMap.TryGetValue(soundType, out string path))
            {
                return path;
            }

            return null;
        }

#if UNITY_EDITOR
        
        public void SetMusicPath(SerializedDictionary<MusicType, AudioClip> musicAudioClipMap)
        {
            foreach (var item in musicAudioClipMap)
            {
                if (musicPathMap.ContainsKey(item.Key))
                {
                    musicPathMap[item.Key] = UtilitiesTool.GetResourcePath(item.Value);
                }
                else
                {
                    musicPathMap.TryAdd(item.Key, UtilitiesTool.GetResourcePath(item.Value));
                }
            }
            
            ConfigTool.SaveConfig(this);
        }

        public void SetSoundPath(SerializedDictionary<SoundType, AudioClip> soundAudioClipMap)
        {
            foreach (var item in soundAudioClipMap)
            {
                if (soundPathMap.ContainsKey(item.Key))
                {
                    soundPathMap[item.Key] = UtilitiesTool.GetResourcePath(item.Value);
                }
                else
                {
                    soundPathMap.TryAdd(item.Key, UtilitiesTool.GetResourcePath(item.Value));
                }
            }
            
            ConfigTool.SaveConfig(this);
        }

#endif

    }
}