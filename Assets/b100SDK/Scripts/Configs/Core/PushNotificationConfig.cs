using System;
using System.Collections.Generic;
//using b100SDK.Scripts.PushNotification;
using b100SDK.Scripts.Utilities.Extensions;
using Sirenix.OdinInspector;
using UnityEngine;

namespace b100SDK.Scripts.Configs.Core
{
    [CreateAssetMenu(menuName = "b100 SDK/Configs/Core/Push Notification/Push Notification Config", fileName = "Push Notification Config")]
    [InlineEditor(InlineEditorObjectFieldModes.Hidden)]
    public class PushNotificationConfig : ScriptableObject
    {
        [TableList(ShowPaging = true, DrawScrollView = false, NumberOfItemsPerPage = 5, AlwaysExpanded = true, ShowIndexLabels = false)]
        public List<NotificationContent> notificationContents;

        [PropertySpace(20f)]
        [TableList(ShowPaging = true, DrawScrollView = false, NumberOfItemsPerPage = 5, AlwaysExpanded = true, ShowIndexLabels = false)]
        public List<NotificationTime> notificationTimes;

        /*public List<NotificationData> GetNotificationData()
        {
            var result = new List<NotificationData>();
            
            var notificationTimeCount = notificationContents.Count / notificationTimes.Count;

            var contentLeft = notificationContents.Clone();

            for (int i = 0; i < notificationTimes.Count; i++)
            {
                for (int j = 0; j < notificationTimeCount; j++)
                {
                    var randomContent = contentLeft.GetRandomAndRemove();

                    var data = new NotificationData()
                    {
                        id = i * notificationTimeCount + j,
                        notificationName = randomContent.title + "_" + i,
                        title = randomContent.title,
                        content = randomContent.content,
                        isInputTimer = false,
                        loopInterval = notificationTimes[i].loop ? notificationTimes[i].loopInterval : -1,
                        triggerTime = notificationTimes[i].GetTriggerTimeAfterDay(j)
                    };
                    
                    result.Add(data);
                }    
            }

            return result;
        }*/
    }

    [Serializable]
    public class NotificationTime
    {
        public Vector3Int time;
        [Space]
        public bool loop = true;
        [Tooltip("Minutes"), ShowIf(nameof(loop))]
        public int loopInterval = 6;
        
        public DateTime GetTriggerTime()
        {
            var now = DateTime.Now;
            var dateTime = new DateTime(now.Year, now.Month, now.Day, time.x, time.z, time.z);

            if (dateTime < now)
            {
                dateTime = dateTime.AddDays(1);
            }
            
            return dateTime;
        }
        
        public DateTime GetTriggerTimeAfterDay(int day)
        {
            var now = DateTime.Now;
            var dateTime = new DateTime(now.Year, now.Month, now.Day, time.x, time.z, time.z);

            if (dateTime < now)
            {
                dateTime = dateTime.AddDays(1);
            }
            
            dateTime = dateTime.AddDays(day);
            
            return dateTime;
        }
    }
    
    [Serializable]
    public class NotificationContent
    {
        [TextArea]
        public string title;
        [TextArea]
        public string content;
    }
}