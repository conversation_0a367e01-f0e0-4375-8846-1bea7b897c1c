using System.Collections.Generic;
using b100SDK.Scripts.Asset;
using b100SDK.Scripts.UI;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Tool;
using UnityEngine;

namespace b100SDK.Scripts.Configs.Core
{
    [CreateAssetMenu(fileName = "UI Config", menuName = "b100 SDK/Configs/Core/UI Config")]
    public class UIConfig : ScriptableObject
    {
        [SerializeField]
        private SerializedDictionary<UIPanelType, string> uiPanelPathMap;



        public UIPanel GetPanel(UIPanelType panelType)
        {
            if (uiPanelPathMap.TryGetValue(panelType, out var path))
            {
                return AssetManager.LoadAsset<UIPanel>(path);
            }

            BhDebug.LogError($"Panel not found for type {panelType}");
            return null;
        }


        #region Editor

#if UNITY_EDITOR

        public void SetPath(List<UIPanel> panels)
        {
            foreach (var panel in panels)
            {
                var panelPath = UtilitiesTool.GetResourcePath(panel);
                
                if (string.IsNullOrEmpty(panelPath))
                {
                    BhDebug.LogError($"Panel path is empty for {panel.name}");
                    continue;
                }

                if (uiPanelPathMap.ContainsKey(panel.GetId()))
                {
                    uiPanelPathMap[panel.GetId()] = panelPath;
                }
                else
                {
                    uiPanelPathMap.TryAdd(panel.GetId(), panelPath);
                }
            }
            
            ConfigTool.SaveConfig(this);
        }
        
#endif

        

        #endregion
    }
}