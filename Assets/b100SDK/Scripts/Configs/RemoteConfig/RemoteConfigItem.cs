using b100SDK.Scripts.Analytic;
using Sirenix.OdinInspector;
using UnityEditor;
using UnityEngine;

namespace b100SDK.Scripts.Configs.RemoteConfig
{
    [CreateAssetMenu(fileName = "Remote Config Item", menuName = "b100 SDK/Configs/Firebase/Remote Config/Remote Config")]
    public class RemoteConfigItem : ScriptableObject
    {
        public RemoteConfigType type;
        public string key;

#if UNITY_EDITOR
        [OnValueChanged(nameof(ConfirmValue))]
#endif
        public RemoteConfigDataType dataType;

        [ShowIf(nameof(dataType), RemoteConfigDataType.String), HideInTables]
        public string stringDefaultValue;
        
        [ShowIf(nameof(dataType), RemoteConfigDataType.Boolean), HideInTables]
        public bool boolDefaultValue;
        
        [ShowIf(nameof(dataType), RemoteConfigDataType.Double), HideInTables]
        public double doubleDefaultValue;
        
        [ShowIf(nameof(dataType), RemoteConfigDataType.Long), HideInTables]
        public long longDefaultValue;

#if UNITY_EDITOR
        [OnValueChanged(nameof(ConfirmValue))]
        public string defaultValue;

        [ReadOnly]
        [GUIColor(nameof(GetGuiColor))]
        public string previewDefaultValue;
        
        [ReadOnly]
        [GUIColor(nameof(GetGuiColor))]
        public bool confirmDefaultValue;



        Color GetGuiColor()
        {
            return confirmDefaultValue ? new Color(.1f, 1f, 0f) : Color.red;
        } 
#endif
        

#if UNITY_EDITOR
        public void ConfirmValue()
        {
            switch (dataType)
            {
                case RemoteConfigDataType.String:
                    stringDefaultValue = defaultValue;
                    confirmDefaultValue = true;
                    break;
                
                case RemoteConfigDataType.Boolean:
                    confirmDefaultValue = bool.TryParse(defaultValue, out var boolResult);
                    if (confirmDefaultValue)
                    {
                        boolDefaultValue = boolResult;
                    }
                    else
                    {
                        Debug.LogError("Error with the default value... Please enter data that matches the data type!!!");
                    }
                    
                    break;
                
                case RemoteConfigDataType.Double:
                    confirmDefaultValue = double.TryParse(defaultValue, out var doubleResult);
                    if (confirmDefaultValue)
                    {
                        doubleDefaultValue = doubleResult;
                    }
                    else
                    {
                        Debug.LogError("Error with the default value... Please enter data that matches the data type!!!");
                    }
                    
                    break;
                
                case RemoteConfigDataType.Long:
                    confirmDefaultValue = long.TryParse(defaultValue, out var longResult);
                    if (confirmDefaultValue)
                    {
                        longDefaultValue = longResult;
                    }
                    else
                    {
                        Debug.LogError("Error with the default value... Please enter data that matches the data type!!!");
                    }
                    break;
            }

            if (confirmDefaultValue)
            {
                previewDefaultValue = defaultValue;
            }
            else
            {
                previewDefaultValue = "?????";
            }
            
            EditorUtility.SetDirty(this);
            AssetDatabase.SaveAssets();
        }

        
#endif
        
        public RemoteConfigType GetRemoteConfigType() => type;
        public string GetKey() => key;
        
        public RemoteConfigDataType GetDataType() => dataType;
        public object GetDefaultValue()
        {
            switch (dataType)
            {
                case RemoteConfigDataType.String:
                    return stringDefaultValue;
                
                case RemoteConfigDataType.Boolean:
                    return boolDefaultValue;
                
                case RemoteConfigDataType.Double:
                    return doubleDefaultValue;
                
                case RemoteConfigDataType.Long:
                    return longDefaultValue;
                
                default:
                    return null;
            }
        } 
    }
}