using System.Collections.Generic;
using System.Linq;
using b100SDK.Scripts.Analytic;
using b100SDK.Scripts.Utilities.Tool;
using Sirenix.OdinInspector;
using UnityEngine;

namespace b100SDK.Scripts.Configs.RemoteConfig
{
    [CreateAssetMenu(fileName = "Remote Config Group", menuName = "b100 SDK/Configs/Firebase/Remote Config/Remote Config Group")]
    public class RemoteConfigGroup : ScriptableObject
    {
        [TableList(ShowPaging = true, NumberOfItemsPerPage = 5, ShowIndexLabels = false, DrawScrollView = false, AlwaysExpanded = true)]
        public List<RemoteConfigItem> configs;

        public RemoteConfigItem GetConfig(RemoteConfigType type)
        {
            return configs.FirstOrDefault(x => x.GetRemoteConfigType() == type);
        }

        public RemoteConfigDataType GetDataType(RemoteConfigType type)
        {
            var config = GetConfig(type);

            if (!config)
            {
                return RemoteConfigDataType.String;
            }
            else
            {
                return config.GetDataType();
            }
        }

        public string GetKey(RemoteConfigType type)
        {
            var config = GetConfig(type);

            return config.GetKey();
        }

        public object GetDefaultValue(RemoteConfigType type)
        {
            var config = GetConfig(type);

            if (config)
            {
                return config.GetDefaultValue();
            }
            
            return null;
        }


        /*
        private void OnValidate()
        {
            foreach (var config in configs)
            {
                config.ConfirmValue();
            }
        }
        */


        #region Editor

        #if UNITY_EDITOR

        [PropertySpace(20)]
        [FolderPath]
        public string path;

        [PropertySpace(10)]
        [Button(ButtonSizes.Large)]
        public void GetConfig()
        {
            configs = ConfigTool.GetConfigs<RemoteConfigItem>(path);
        }
        
        [Button(ButtonSizes.Large, ButtonStyle.Box), PropertySpace(20, 20), GUIColor(0.40f, 0.8f, .1f)]
        void CreateNewPackage()
        {
            RemoteConfigCreator.OpenWindow();
        }

        
        #endif
        
        #endregion

    }
    
}