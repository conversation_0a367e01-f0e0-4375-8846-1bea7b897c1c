#region

using System;
using System.Collections.Generic;
using System.IO;
using b100SDK.Scripts.Audio;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Extensions;
using b100SDK.Scripts.Utilities.Tool;
using Sirenix.OdinInspector;
using UnityEditor;
using UnityEngine;

namespace b100SDK.Scripts.Configs.SDKEditor
{

    #endregion

    [CreateAssetMenu(fileName = "Editor Sound Config", menuName = "b100 SDK/Editor/Sound Config")]
    public class SoundConfig : ScriptableObject
    {
        [Title("Music")]
        [SerializeField]
        private SerializedDictionary<MusicType, AudioClip> musicMap;
        
        
        [PropertySpace(20f)]
        [PropertyOrder(20)]
        [Title("Sound")]
        [SerializeField]
        private SerializedDictionary<SoundType, AudioClip> soundMap;
        
        
#if UNITY_EDITOR

        [PropertyOrder(1)]
        [PropertySpace(10f, 10f)]
        [But<PERSON>(ButtonSizes.Large, ButtonStyle.Box)]
        void CreateMusic()
        {
            AudioCreator.OpenWindow(true);
        }

        [PropertyOrder(21)]
        [PropertySpace(10f, 10f)]
        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        void CreateSound()
        {
            AudioCreator.OpenWindow(false);
        }      
        
        [PropertyOrder(22)]
        [PropertySpace(20f, 10f)]
        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        [GUIColor(0.5f, 0.1f, 0.1f)]
        void Close()
        {
            AudioCreator.CloseWindow();
        }

        
        





        public void AddSound(SerializedDictionary<SoundType, AudioClip> soundDic)
        {
            foreach (var soundKeyValuePair in soundDic)
            {
                if (soundMap.ContainsKey(soundKeyValuePair.Key))
                {
                    soundMap[soundKeyValuePair.Key] = soundKeyValuePair.Value;
                }
                else
                {
                    soundMap.TryAdd(soundKeyValuePair.Key, soundKeyValuePair.Value);
                }
            }
            
            ConfigTool.SaveConfig(this);
        }


        public void AddMusic(SerializedDictionary<MusicType, AudioClip> musicDic)
        {
            foreach (var musicKeyValuePair in musicDic)
            {
                if (musicMap.ContainsKey(musicKeyValuePair.Key))
                {
                    musicMap[musicKeyValuePair.Key] = musicKeyValuePair.Value;
                }
                else
                {
                    musicMap.TryAdd(musicKeyValuePair.Key, musicKeyValuePair.Value);
                }
            }
            
            ConfigTool.SaveConfig(this);
        }





        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        [PropertyOrder(30)]
        [PropertySpace(20f)]
        void SaveConfig()
        {
            var audioConfig = ConfigTool.GetAudioConfig();
            audioConfig.SetSoundPath(soundMap);
            audioConfig.SetMusicPath(musicMap);
        }
#endif
    }
}