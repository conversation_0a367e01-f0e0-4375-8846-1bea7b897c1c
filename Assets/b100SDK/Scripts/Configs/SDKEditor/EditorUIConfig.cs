#region

using System.Collections.Generic;
using System.IO;
using b100SDK.Scripts.Asset;
using b100SDK.Scripts.UI;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Tool;
using Sirenix.OdinInspector;
using UnityEditor;
using UnityEngine;

#endregion

namespace b100SDK.Scripts.Configs.SDKEditor
{
    [CreateAssetMenu(fileName = "Editor UI Config", menuName = "b100 SDK/Editor/UI Config")]
    public class EditorUIConfig : ScriptableObject
    {
        [Header("All panels")]
        [SerializeField]
        private List<UIPanel> panelInstances;

        [Title("Prefabs"), Space]
        public GameObject prefabScreen;
        public GameObject prefabPopup;
        [Space]
        public GameObject prefabButtonIcon;
        public GameObject prefabButtonText;
        public GameObject prefabButtonMix;
        public GameObject prefabButtonFree;
        public GameObject prefabButtonSwitch;
        [Space]
        public GameObject dropDownPrefab;
        public GameObject defaultTextPrefab;

#if UNITY_EDITOR
        [FolderPath, Header("Path to panels")]
        public List<string> paths;


        public void AddPanelPath(string path)
        {
            if (!paths.Contains(path))
            {
                paths.Add(path);
            }
        }

        [Button(ButtonSizes.Large, ButtonStyle.Box), PropertySpace(2, 10)]
        public void GetPanelInstances()
        {
            panelInstances.Clear();
            
            foreach (var path in paths)
            {
                if (!Directory.Exists(path))
                    continue;
                var fileEntries = Directory.GetFiles(path);
                foreach (var t in fileEntries)
                {
                    if (t.EndsWith(".prefab"))
                    {
                        var ui = AssetDatabase.LoadAssetAtPath<UIPanel>(t.Replace("\\", "/"));
                        if (ui != null)
                            panelInstances.Add(ui);
                    }
                }
            }

            var uiConfig = ConfigTool.GetUIConfig();

            if (uiConfig)
            {
                uiConfig.SetPath(panelInstances);
            }
        }

        [InfoBox("Create new panel")]
        [Button(ButtonSizes.Large, ButtonStyle.Box), PropertySpace(0, 20), PropertyOrder(-1), GUIColor(0.4f, 0.8f, 1)]
        public void CreatePanel()
        {
            BhPanelCreator.CreateNewScreen();
        }

#endif
    }
}