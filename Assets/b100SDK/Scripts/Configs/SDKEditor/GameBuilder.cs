using b100SDK.Scripts.Utilities.Tool;
using Sirenix.OdinInspector;
using UnityEditor;
using UnityEngine;

namespace b100SDK.Scripts.Configs.SDKEditor
{
    [CreateAssetMenu(fileName = "Game Builder", menuName = "b100 SDK/Editor/Game Builder")]

    public class GameBuilder : ScriptableObject
    {
        [FolderPath]
        public string windowBuildPath = "";

        [FolderPath]
        public string macOsBuildPath = "/Volumes/SSD/AndroidBuild";
        
        

#if UNITY_EDITOR
        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        [PropertySpace(10, 10)]
        public void GetBuildPath()
        {
            windowBuildPath = Application.dataPath.Replace("Assets", "") + "Build";
            UtilitiesTool.GetFolder(windowBuildPath);
            
            macOsBuildPath = Application.dataPath.Replace("Assets", "") + "Build/iOS";
            UtilitiesTool.GetFolder(macOsBuildPath);
        }
        
        
        [But<PERSON>(ButtonSizes.Large, ButtonStyle.Box)]
        [LabelText("Build APK")]
        [GUIColor(0.4f, 0.8f, 1)]
        [PropertySpace(20, 10)]
        public void BuildApk()
        {
            GetBuildPath();
            BuilderTool.BuildApk();
        }
        
        
        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        [LabelText("Build APK (clean)")]
        [GUIColor(0.7f, 0.6f, .15f)]
        [PropertySpace(20, 10)]
        public void BuildApkClean()
        {
            GetBuildPath();
            BuilderTool.BuildApk(BuildOptions.CleanBuildCache);
        }

        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        [LabelText("Build AAB")]
        [GUIColor(0f, 0.9f, .1f)]
        [PropertySpace(10, 10)]
        public void BuildAab()
        {
            GetBuildPath();
            BuilderTool.BuildAab();
        }
#endif
    }
}