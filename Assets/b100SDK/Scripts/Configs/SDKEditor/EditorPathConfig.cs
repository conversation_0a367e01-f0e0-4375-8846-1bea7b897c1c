using Sirenix.OdinInspector;
using UnityEngine;

namespace b100SDK.Scripts.Configs.SDKEditor
{
    [CreateAssetMenu(fileName = "Editor Path Config", menuName = "b100 SDK/Editor/Path Config")]
    public class EditorPathConfig : ScriptableObject
    {
        [Title("Editor")]
        [SerializeField, FilePath]
        private string editorUiConfigFilePath;

        [SerializeField, FilePath]
        private string gameSettingPath;
        
        [SerializeField, FilePath]
        private string gameBuilderPath;


        [PropertySpace(20f)]
        [Title("Runtime")]
        [SerializeField, FilePath]
        private string iapConfigPath;
        
        [SerializeField, FilePath]
        private string remoteConfigPath;
        
        [SerializeField, FilePath]
        private string iaaConfigPath;
        
        [SerializeField, FilePath]
        private string gameConfigPath;
        
        [SerializeField, FilePath]
        private string soundConfigPath;
        
        [SerializeField, FilePath]
        private string uiConfigPath;
        
        [SerializeField, FilePath]
        private string audioConfigPath;


        #region Editor

        
        public string GetEditorUIConfigFilePath()
        {
            return editorUiConfigFilePath;
        }
        
        public string GetGameSettingPath()
        {
            return gameSettingPath;
        }
        
        public string GetGameBuilderPath()
        {
            return gameBuilderPath;
        }
        
        
        #endregion



        #region Runtime

        

        
        
        public string GetIapConfigPath()
        {
            return iapConfigPath;
        }
        
        public string GetRemoteConfigPath()
        {
            return remoteConfigPath;
        }
        
        public string GetIaaConfigPath()
        {
            return iaaConfigPath;
        }
        
        public string GetGameConfigPath()
        {
            return gameConfigPath;
        }
        
        public string GetSoundConfigPath()
        {
            return soundConfigPath;
        }
        
        public string GetUIConfigFilePath()
        {
            return uiConfigPath;
        }
        
        public string GetAudioConfigPath()
        {
            return audioConfigPath;
        }
        
        #endregion

    }
}


