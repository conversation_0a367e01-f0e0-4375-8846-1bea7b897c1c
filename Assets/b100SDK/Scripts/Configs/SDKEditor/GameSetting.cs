using System.IO;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Tool;
using Sirenix.OdinInspector;
using UnityEditor;
using UnityEngine;

namespace b100SDK.Scripts.Configs.SDKEditor
{
    [CreateAssetMenu(fileName = "Game Setting", menuName = "b100 SDK/Editor/Game Setting")]
    public class GameSetting : ScriptableObject
    {
        [BoxGroup("Game ID")]
        [HorizontalGroup("Game ID/Split")]

        [VerticalGroup("Game ID/Split/Left")]
        [BoxGroup("Game ID/Split/Left/Identifier")]
        public string companyName = "b100";        
        
        [VerticalGroup("Game ID/Split/Left")]
        [BoxGroup("Game ID/Split/Left/Identifier")]
        public string gameName = "Prototype";

        [VerticalGroup("Game ID/Split/Left")]
        [BoxGroup("Game ID/Split/Left/Identifier")]
        public string packageName = "com.b100.prototype";        
        
        [VerticalGroup("Game ID/Split/Left")]
        [BoxGroup("Game ID/Split/Left/Identifier/Keystore")]
        [HorizontalGroup("Game ID/Split/Left/Identifier/Keystore/Split")]
        public string keystorePath;
        
        [VerticalGroup("Game ID/Split/Left")]
        [BoxGroup("Game ID/Split/Left/Identifier/Keystore")]
        public string keystorePassword = "12345678";

        [VerticalGroup("Game ID/Split/Left")]
        [BoxGroup("Game ID/Split/Left/Identifier/Keystore")]
        public string alias;
        
        [VerticalGroup("Game ID/Split/Left")]
        [BoxGroup("Game ID/Split/Left/Identifier/Keystore")]
        public string aliasPassword = "12345678";


#if UNITY_EDITOR
        [VerticalGroup("Game ID/Split/Left")]
        [BoxGroup("Game ID/Split/Left/Identifier/Keystore")]
        [HorizontalGroup("Game ID/Split/Left/Identifier/Keystore/Split", 25)]
        [Button(SdfIconType.Folder, Name = "")]
        void SelectPath()
        {
#if UNITY_EDITOR
            string path = EditorUtility.OpenFilePanel("Select keystore file", Directory.GetParent(Application.dataPath)?.FullName, "keystore");
            if (!string.IsNullOrEmpty(path))
            {
                keystorePath = path;
                BhDebug.Log($"Selected File: {path}");
                
                EditorUtility.SetDirty(this);
                AssetDatabase.SaveAssets();
            }
            else
            {
                BhDebug.LogWarning("No file selected.");
            }
#endif
        }
#endif
        
        
        
        [PreviewField(192, ObjectFieldAlignment.Right)]
        [HorizontalGroup("Game ID/Split", 192)]
        [VerticalGroup("Game ID/Split/Right")]
        [HideLabel]
        public Texture2D gameIcon;


        
        [VerticalGroup("A"), PropertySpace(20)]
        public string gameVersion = "1.0";

        [VerticalGroup("A")]
        public int bundleVersion = 1;

        [VerticalGroup("A")]
        public int buildVersion = 1;


#if UNITY_EDITOR 
        [PropertySpace(20)]
        [OnValueChanged(nameof(OnChangeStatusAd))]
        [EnumToggleButtons]
#endif
        public Status adStatus;

#if UNITY_EDITOR
        [PropertySpace(20)]
        [OnValueChanged(nameof(OnChangeStatusFirebase))]
        [EnumToggleButtons]
#endif
        public Status firebaseStatus;

#if UNITY_EDITOR
        [PropertySpace(20)]
        [OnValueChanged(nameof(OnChangeStatusGoogleServices))]
        [EnumToggleButtons]
#endif
        public Status googleServicesStatus = Status.Enable;
        
        #if UNITY_EDITOR
        

        void OnChangeStatusAd()
        {
            Debug.Log("adStatus: " + adStatus);
            if (adStatus == Status.Enable)
            {
                AdsEnable();
            }
            else
            {
                AdsDisable();
            }
        }


        void OnChangeStatusFirebase()
        {
            if (firebaseStatus == Status.Enable)
            {
                FirebaseEnable();
            }
            else
            {
                FirebaseDisable();
            }
        }
        
        void OnChangeStatusGoogleServices()
        {
            if (googleServicesStatus == Status.Enable)
            {
                EnableGoogleServices();
            }
            else
            {
                DisableGoogleServices();
            }
        }

        #region Firebase
        
        private const string _ENABLE_FIREBASE_SYMBOL = "ENABLE_FIREBASE";
        public void FirebaseEnable()
        {
            AddScriptSymbol(_ENABLE_FIREBASE_SYMBOL);
        }


        public void FirebaseDisable()
        {
            RemoveScriptSymbol(_ENABLE_FIREBASE_SYMBOL);
        }
        
        #endregion
        
        
        
        #region Ads
        
        private const string _ENABLE_ADS_SYMBOL = "ENABLE_ADS";

        public void AdsEnable()
        {
            AddScriptSymbol(_ENABLE_ADS_SYMBOL);
        }


        public void AdsDisable()
        {
            RemoveScriptSymbol(_ENABLE_ADS_SYMBOL);
        }
        
        #endregion



        #region Google

        private const string _ENABLE_GOOLE_SERVICES = "ENABLE_GOOGLE_SERVICES";

        public void EnableGoogleServices()
        {
            AddScriptSymbol(_ENABLE_GOOLE_SERVICES);
        }


        public void DisableGoogleServices()
        {
            RemoveScriptSymbol(_ENABLE_GOOLE_SERVICES);
        }

        

        #endregion


        #region Utils

        static void AddScriptSymbol(string scriptSymbol)
        {
            string symbols = PlayerSettings.GetScriptingDefineSymbolsForGroup(EditorUserBuildSettings.selectedBuildTargetGroup);
            if (!symbols.Contains(scriptSymbol))
            {
                symbols += ";" + scriptSymbol;
                PlayerSettings.SetScriptingDefineSymbolsForGroup(EditorUserBuildSettings.selectedBuildTargetGroup, symbols);
                Debug.Log("Added script symbol: " + scriptSymbol);
            }
            else
            {
                Debug.LogWarning("Script symbol already exists: " + scriptSymbol);
            }
        }
        
        static void RemoveScriptSymbol(string scriptSymbol)
        {
            string symbols = PlayerSettings.GetScriptingDefineSymbolsForGroup(EditorUserBuildSettings.selectedBuildTargetGroup);
            if (symbols.Contains(scriptSymbol))
            {
                symbols = symbols.Replace(scriptSymbol, "");
                symbols = symbols.Replace(";;", ";");
                PlayerSettings.SetScriptingDefineSymbolsForGroup(EditorUserBuildSettings.selectedBuildTargetGroup, symbols);
                Debug.Log("Removed script symbol: " + scriptSymbol);
            }
            else
            {
                Debug.LogWarning("Script symbol doesn't exist: " + scriptSymbol);
            }
        }

        static bool HasScriptingSymbol(string scriptSymbol)
        {
            string symbols = PlayerSettings.GetScriptingDefineSymbolsForGroup(EditorUserBuildSettings.selectedBuildTargetGroup);
            return scriptSymbol.Contains(symbols);
        }

        #endregion
        
        #endif
        
#if UNITY_IOS
        [PropertySpace(10, 20), Header("Appstore ID (iOS Only)")]
        public string appstoreId;
#endif
        
        
#if UNITY_EDITOR
        [PropertySpace(20)]
        [Button(ButtonSizes.Large)]
        public static void SaveGameSetting()
        {
            SettingTool.SaveGameSetting();
        }
#endif
    }
}