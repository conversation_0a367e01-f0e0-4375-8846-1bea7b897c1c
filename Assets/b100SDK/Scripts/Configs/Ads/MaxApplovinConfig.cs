using b100SDK.Scripts.Ads;
using b100SDK.Scripts.Utilities.Tool;
using Sirenix.OdinInspector;
using UnityEngine;

namespace b100SDK.Scripts.Configs.Ads
{
    [CreateAssetMenu(menuName = "b100 SDK/Configs/IAA/MaxApplovin Config", fileName = "MaxApplovin Config")]
    public class MaxApplovinConfig : ScriptableObject
    { 
        [LabelWidth(180)]
#if UNITY_EDITOR
        [ShowIf(nameof(maxAdStatus), Status.Enable)]
#endif
        public AdType adType;
        
        [PropertySpace(10, 10)]
        [LabelWidth(180)]
#if UNITY_EDITOR
        [ShowIf(nameof(maxAdStatus), Status.Enable)]
#endif
        public string maxSdkKey = "ENTER_MAX_SDK_KEY_HERE";
        

        #region Properties

        private bool HasAppOpen => adType.HasFlag(AdType.AppOpen) && maxAdStatus == Status.Enable;
        private bool HasInter => adType.HasFlag(AdType.Inter) && maxAdStatus == Status.Enable;
        private bool HasReward => adType.HasFlag(AdType.Reward) && maxAdStatus == Status.Enable;
        private bool HasRewardInter => adType.HasFlag(AdType.RewardInter) && maxAdStatus == Status.Enable;
        private bool HasBanner => adType.HasFlag(AdType.Banner) && maxAdStatus == Status.Enable;
        
        private bool HasMREC => adType.HasFlag(AdType.MRec) && maxAdStatus == Status.Enable;

        #endregion



        #region Ad Data

        [PropertySpace(10, 10)]
        [InlineProperty]
        [ShowIf(nameof(HasAppOpen)), LabelWidth(180)]
        public AdData appOpenData;
        
        
        
        [PropertySpace(10, 10)]
        [InlineProperty]
        [ShowIf(nameof(HasInter)), LabelWidth(180)]
        public AdData interData;
        
        
        
        [PropertySpace(10, 10)]
        [InlineProperty]
        [ShowIf(nameof(HasReward)), LabelWidth(180)]
        public AdData rewardData;
        
        
        
        [PropertySpace(10, 10)]
        [InlineProperty]
        [ShowIf(nameof(HasRewardInter)), LabelWidth(180)]
        public AdData rewardInterData;
        
        
        
        [PropertySpace(10, 10)]
        [InlineProperty]
        [ShowIf(nameof(HasBanner)), LabelWidth(180)]
        public BannerData bannerData;        
        
        
        
        [PropertySpace(10, 10)]
        [InlineProperty]
        [ShowIf(nameof(HasMREC)), LabelWidth(180)]
        public AdData mrecData;


        #endregion


#if UNITY_EDITOR
        [PropertySpace(20)]
        [OnValueChanged(nameof(OnChangeStatusAd))]
        [EnumToggleButtons]
#endif
        public Status maxAdStatus;


#if UNITY_EDITOR
        #region Symbol
        
        private const string _ENABLE_ADS_SYMBOL = "ENABLE_ADS_MAX";
        
        
        void OnChangeStatusAd()
        {
            Debug.Log("adStatus: " + maxAdStatus);
            if (maxAdStatus == Status.Enable)
            {
                AdsEnable();
            }
            else
            {
                AdsDisable();
            }
        }

        void AdsEnable()
        {
            UtilitiesTool.AddScriptSymbol(_ENABLE_ADS_SYMBOL);
        }


        void AdsDisable()
        {
            UtilitiesTool.RemoveScriptSymbol(_ENABLE_ADS_SYMBOL);
        }
        
        #endregion
        
        
        
        #region Package

        private const string _GITHUB_REPO_URL =
            "https://api.github.com/repos/AppLovin/AppLovin-MAX-Unity-Plugin/releases/latest";


        [PropertySpace(20f)]
        [Button]
        async void DownloadLatestMaxApplovinPackage()
        {
           await PackageTool.GetLatestReleasePackage(_GITHUB_REPO_URL);
        }

        
        

        #endregion
        
        #endif

    }
}