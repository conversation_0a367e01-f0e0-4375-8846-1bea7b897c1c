using b100SDK.Scripts.Ads;
using b100SDK.Scripts.Utilities;
using Sirenix.OdinInspector;
using UnityEditor;
using UnityEngine;
#if UNITY_EDITOR && ENABLE_ADS_ADMOB
using GoogleMobileAds.Editor;
#endif

namespace b100SDK.Scripts.Configs.Ads
{
    [CreateAssetMenu(menuName = "b100 SDK/Configs/IAA/IAA Config", fileName = "IAA Config")]
    public class IAAConfig : ScriptableObject
    {
        [PropertySpace(0, 10)]
        public SerializedDictionary<AdType, MediationType> adDefaultMediationMaps;

        [PropertySpace(0, 30)]
        [EnumToggleButtons]
        [HideLabel]
        public MediationType editMediationType;

        [ShowIf(nameof(editMediationType), MediationType.MAX)]
        [TitleGroup("MAX Applovin")]
        [InlineEditor(InlineEditorObjectFieldModes.Hidden, Expanded = true)]
        public MaxApplovinConfig maxApplovinConfig;
        

        [ShowIf(nameof(editMediationType), MediationType.AdMob)]
        [TitleGroup("Admob")]
        [InlineEditor(InlineEditorObjectFieldModes.Hidden, Expanded = true)]
        public AdmobConfig admobConfig;



#if UNITY_EDITOR
        
        [PropertySpace(50)]
        [Button(ButtonSizes.Large)]
        [InfoBox("Click to save data to MAX Applovin Mediation Integration")]
        void SaveDataApplovinMediation()
        {
#if ENABLE_ADS && ENABLE_ADS_MAX
            var dataConfig = AppLovinSettings.Instance;

            dataConfig.SdkKey = maxApplovinConfig.maxSdkKey;
            dataConfig.AdMobAndroidAppId = admobConfig.androidAppId;
            dataConfig.AdMobIosAppId = admobConfig.iOSAppId;

            EditorUtility.SetDirty(dataConfig);
#endif
            
            AssetDatabase.SaveAssets();

            var component = GameObject.FindObjectOfType<MaxAppLovinAdsInstance>();

            if (component)
            {
                component.SetUpAdType(maxApplovinConfig.adType);
            }
            
            BhDebug.Log("Save data MAX Applovin complete!!!");
        }
        
        
        
        [PropertySpace(20)]
        [Button(ButtonSizes.Large)]
        [InfoBox("Click to open Google Mobile Ads Settings")]
        void SaveAndOpenGoogleMobileAdsSetting()
        {
#if ENABLE_ADS && ENABLE_ADS_ADMOB
            GoogleMobileAdsSettingsEditor.OpenInspector();
#endif
            
            var component = GameObject.FindObjectOfType<AdMobAdsInstance>();

            if (component)
            {
                component.SetUpAdType(admobConfig.adType);
            }
            
            BhDebug.Log("Save data Admob complete!!!");
        }

#endif
    }


    public enum MediationSelectEditType
    {
        MaxApplovin,
        Admob,
    }
    
}