using b100SDK.Scripts.Ads;
using b100SDK.Scripts.Utilities.Tool;
using Sirenix.OdinInspector;
using UnityEngine;

namespace b100SDK.Scripts.Configs.Ads
{
    [CreateAssetMenu(menuName = "b100 SDK/Configs/IAA/Admob Config", fileName = "Admob Config")]
    public class AdmobConfig : ScriptableObject
    {
        [LabelWidth(180)]
#if UNITY_EDITOR
        [ShowIf(nameof(admobAdStatus), Status.Enable)]
#endif
        public AdType adType;
        
        [Header("App ID")]
        [PropertySpace(10, 10)]
        [LabelWidth(180)]
#if UNITY_EDITOR
        [ShowIf(nameof(admobAdStatus), Status.Enable)]
#endif
        public string androidAppId = "ENTER_ANDROID_APP_ID_HERE";
        
        [PropertySpace(10, 10)]
        [LabelWidth(180)]
#if UNITY_EDITOR
        [ShowIf(nameof(admobAdStatus), Status.Enable)]
#endif
        public string iOSAppId = "ENTER_IOS_APP_ID_HERE";
        

        #region Properties

        private bool HasAppOpen => adType.HasFlag(AdType.AppOpen) && admobAdStatus == Status.Enable;
        private bool HasInter => adType.HasFlag(AdType.Inter) && admobAdStatus == Status.Enable;
        private bool HasReward => adType.HasFlag(AdType.Reward) && admobAdStatus == Status.Enable;
        private bool HasRewardInter => adType.HasFlag(AdType.RewardInter) && admobAdStatus == Status.Enable;
        private bool HasBanner => adType.HasFlag(AdType.Banner) && admobAdStatus == Status.Enable;
        private bool HasMREC => adType.HasFlag(AdType.MRec) && admobAdStatus == Status.Enable;
        
        private bool HasNative => adType.HasFlag(AdType.Native) && admobAdStatus == Status.Enable;

        private bool HasNativeOverlay => adType.HasFlag(AdType.NativeOverlay) && admobAdStatus == Status.Enable;

        #endregion



        #region Ad Data

        [PropertySpace(10, 10)]
        [InlineProperty]
        [ShowIf(nameof(HasAppOpen)), LabelWidth(180)]
        public AdData appOpenData;
        
        
        
        [PropertySpace(10, 10)]
        [InlineProperty]
        [ShowIf(nameof(HasInter)), LabelWidth(180)]
        public AdData interData;
        
        
        
        [PropertySpace(10, 10)]
        [InlineProperty]
        [ShowIf(nameof(HasReward)), LabelWidth(180)]
        public AdData rewardData;
        
        
        
        [PropertySpace(10, 10)]
        [InlineProperty]
        [ShowIf(nameof(HasRewardInter)), LabelWidth(180)]
        public AdData rewardInterData;
        
        
        
        [PropertySpace(10, 10)]
        [InlineProperty]
        [ShowIf(nameof(HasBanner)), LabelWidth(180)]
        public BannerData bannerData;
        
        
        
        [PropertySpace(10, 10)]
        [InlineProperty]
        [ShowIf(nameof(HasMREC)), LabelWidth(180)]
        public AdData mrecData;   
        
        
        
        [PropertySpace(10, 10)]
        [InlineProperty]
        [ShowIf(nameof(HasNative)), LabelWidth(180)]
        public AdData nativeData;
        
        
        [PropertySpace(10, 10)]
        [InlineProperty]
        [ShowIf(nameof(HasNativeOverlay)), LabelWidth(180)]
        public AdData nativeOverlayData;

        #endregion
        
        
        
        
#if UNITY_EDITOR
        [PropertySpace(20)]
        [OnValueChanged(nameof(OnChangeStatusAd))]
        [EnumToggleButtons]
#endif
        public Status admobAdStatus;

#if UNITY_EDITOR

        #region Symbol
        
        private const string _ENABLE_ADS_SYMBOL = "ENABLE_ADS_ADMOB";
        
        
        void OnChangeStatusAd()
        {
            Debug.Log("adStatus: " + admobAdStatus);
            if (admobAdStatus == Status.Enable)
            {
                AdsEnable();
            }
            else
            {
                AdsDisable();
            }
        }

        void AdsEnable()
        {
            UtilitiesTool.AddScriptSymbol(_ENABLE_ADS_SYMBOL);
        }


        void AdsDisable()
        {
            UtilitiesTool.RemoveScriptSymbol(_ENABLE_ADS_SYMBOL);
        }
        
        #endregion



        #region Package

        private const string _GITHUB_REPO_URL =
            "https://api.github.com/repos/googleads/googleads-mobile-unity/releases/latest";


        [PropertySpace(20f)]
        [Button]
        async void DownloadLatestGoogleMobileAdsPackage()
        {
           await PackageTool.GetLatestReleasePackage(_GITHUB_REPO_URL);
        }

        
        

        #endregion

#endif
    }
} 