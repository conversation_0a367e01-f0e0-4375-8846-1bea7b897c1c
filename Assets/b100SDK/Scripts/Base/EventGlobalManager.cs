#region

using System.Collections;
using System.Diagnostics.CodeAnalysis;
using b100SDK.Scripts.DesignPatterns;
using b100SDK.Scripts.Utilities;
using Sigtrap.Relays;
using UnityEngine;

#endregion

namespace b100SDK.Scripts.Base
{
    [SuppressMessage("ReSharper", "InconsistentNaming")]
    public class EventGlobalManager : Singleton<EventGlobalManager>
    {
        public readonly Relay OnGameInited = new Relay();
        
        public readonly Relay OnStartLoadScene = new Relay();
        public readonly Relay<float, float> OnChangeProgressLoading = new();
        public readonly Relay OnFinishLoadScene = new Relay();
        
        public readonly Relay OnUpdateSetting = new Relay();
        
        public readonly Relay OnEverySecondTick = new Relay();
        
        public readonly Relay<bool> OnGamePaused = new Relay<bool>();
        
        public readonly Relay OnFetchDataRemoteConfigFromFirebaseComplete = new();
        
        public readonly Relay OnPurchaseNoAds = new Relay();
        
        
        
        public readonly Relay OnChangeName = new Relay();
        
        public readonly Relay<bool> OnMoneyChange = new Relay<bool>();
        
        public readonly Relay<Vector3> OnCollectKey = new Relay<Vector3>();
        
        public readonly Relay<int> OnClaimMoneyDefault = new();

        
        
        
            
    
        //AutoTest
        public Relay OnWinLevel = new();
        
        
        
        
        
        
        
        
        
        
        //Gameplay
        public Relay OnGameInit = new();
        
        public Relay<int> OnScoreChange = new();
        public Relay<int> OnComboChange = new();
        public Relay<int> OnMatchChange = new();

        
        
        
        
        //Booster
        public Relay OnUndo = new();
        public Relay OnUndoFinish = new();
        public Relay<int> OnUndoChange = new();
        
        public Relay OnShuffle = new ();
        public Relay<int> OnShuffleChange = new();
        
        public Relay OnHint = new ();
        public Relay<int> OnHintChange = new();
        
        
        
        
        
        
        
        //Editor
        public Relay<int> OnChangeLevelCount = new();

        public Relay Shuffle = new();
        public Relay AutoTest = new();

        public Relay<string> OnUpdateSize = new();
        public Relay<string> OnUpdateShuffleAvg = new();

        public Relay<int> OnUpdateLevelCount = new();
        
        public Relay<int> OnUpdateLevelCreatedCount = new();
        
        private void Start()
        {
            StartCoroutine(EverySecondTick());
        }

        IEnumerator EverySecondTick()
        {
            while (true)
            {
                OnEverySecondTick.Dispatch();
                yield return Yielder.Get(1);
            }
        }
    }
}