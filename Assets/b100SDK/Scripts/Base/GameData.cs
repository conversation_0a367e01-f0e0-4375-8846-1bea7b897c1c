#region

using System;
using b100SDK.Scripts.Utilities;
using Newtonsoft.Json;
using UnityEngine;

namespace b100SDK.Scripts.Base
{

    #endregion

    [Serializable]
    public class GameData
    {
        public SettingData setting = new SettingData();
        public UserData user = new UserData();

        [Serializable]
        public class UserData
        {
            public DateTime lastTimeLogOut = DateTime.Now;
        
            public int level = 1;

            //Progress Data
            public int money;
            public int score;
            public string name = "Player";
            public int luckyWheelProgress;
            public bool isConsentUpdated;
            public DateTime lastFreeSpinTime = DateTime.MinValue;
            public int dailyRewardClaimedCount;
            public DateTime lastDailyRewardClaimTime = DateTime.MinValue;

            //Purchase Data
            public bool purchasedNoAds;
            public bool rated;

            //Leaderboard
            public int totalScoreOffset;

            //Other Data
            public int sessionPlayed;
            private int _keyCount;
        

            public int KeyCount
            {
                get => _keyCount;
                set => _keyCount = Math.Clamp(value, 0, 3);
            }
        
        
            //IAP
            public SerializedDictionary<IAPPackageType, int> totalBuyCountMap = new();

            public void VerifyData()
            {
                foreach (IAPPackageType packageType in Enum.GetValues(typeof(IAPPackageType)))
                {
                    totalBuyCountMap.TryAdd(packageType, 0);
                }
            }
        }

        [Serializable]
        public class SettingData
        {
            public SerializedDictionary<string, int> androidNotificationIndexMap = new();

            public RequestPushNotification canRequestPushNotification = RequestPushNotification.Enable;
            public bool enablePushNotification = false;
        
            public bool haptic = true;
            public float soundVolume = 1;
            public float musicVolume = 1;
        
            public int highPerformance = 1;
        }
    
        public enum RequestPushNotification
        {
            Complete,
            Enable,
            Disable,
        }
    
    }

    public static class Database
    {
        private static string _dataKey = "GameData";

        public static void SaveData()
        {
#if UNITY_EDITOR
            var dataString = JsonConvert.SerializeObject(GameManager.Instance.data);
            PlayerPrefs.SetString(_dataKey, dataString);
            PlayerPrefs.Save();
            return;
#endif
        
            ES3.Save<GameData>(_dataKey, GameManager.Instance.data);
        }

        public static GameData LoadData()
        {
#if UNITY_EDITOR
            if (!PlayerPrefs.HasKey(_dataKey))
                return null;
        
            var dataString = PlayerPrefs.GetString(_dataKey);
            return JsonConvert.DeserializeObject<GameData>(dataString);
        
#endif
        
            if (!ES3.KeyExists(_dataKey))
                return null;

            return ES3.Load<GameData>(_dataKey);
        }
    }
}