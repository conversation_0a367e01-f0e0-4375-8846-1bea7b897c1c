using System;
using b100SDK.Scripts.Analytic;
using b100SDK.Scripts.Utilities;
using Newtonsoft.Json;
using UnityEngine;
using GameManager = b100SDK.Scripts.Base.GameManager;
using RemoteConfigData = b100SDK.Scripts.Base.RemoteConfigData;

namespace b100SDK.Scripts.Base
{
    [Serializable]
    public class RemoteConfigData
    {
        public SerializedDictionary<RemoteConfigType, object> remoteConfigMap = new();
        public void VerifyData()
        {
            foreach (RemoteConfigType type in Enum.GetValues(typeof(RemoteConfigType)))
            {
                if (type != RemoteConfigType.SdkPlaceHolder)
                {
                    remoteConfigMap.TryAdd(type, RemoteConfigManager.Instance.GetDefaultValue(type));
                }
            }
        }
    }
}

public static class RemoteConfigDatabase
{
    private static string _dataKey = "RemoteConfigData";

    public static void SaveData()
    {
        var dataString = JsonConvert.SerializeObject(GameManager.Instance.remoteConfigData);
        PlayerPrefs.SetString(_dataKey, dataString);
        PlayerPrefs.Save();
    }

    public static RemoteConfigData LoadData()
    {
        if (!PlayerPrefs.HasKey(_dataKey))
            return null;

        return JsonConvert.DeserializeObject<RemoteConfigData>(PlayerPrefs.GetString(_dataKey));
    }
}