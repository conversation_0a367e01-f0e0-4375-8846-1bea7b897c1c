#region

using System;
using b100SDK.Scripts.Ads;
using b100SDK.Scripts.DesignPatterns;
//using b100SDK.Scripts.PushNotification;
using b100SDK.Scripts.UI;
using b100SDK.Scripts.UI.Panel;
using GenerateData.Scripts;
using Sirenix.OdinInspector;
using UnityEditor;
using UnityEngine;

#endregion

namespace b100SDK.Scripts.Base
{
    public partial class GameManager : Singleton<GameManager>
    {
        public bool gameInited;
        
#if UNITY_EDITOR
        private bool IsPlaying => EditorApplication.isPlaying;
        [ShowIf(nameof(IsPlaying))] 
#endif
        [Space(20f)]
        public GameData data;

#if UNITY_EDITOR
        [ShowIf(nameof(IsPlaying))]
#endif
        [Space(20f)]
        public RemoteConfigData remoteConfigData;
        
        
        
#if UNITY_EDITOR
        [ShowIf(nameof(IsPlaying))]
#endif
        [Space(20f)]
        public GenerateData.Scripts.GenerateData generateData;
    
        
        
        
        
        [HideInInspector] 
        public float lastClaimOnlineGiftTime;
    
        public bool EnableAds
        {
            get
            {
                return !data.user.purchasedNoAds;
            }
        }
    

        protected override void Awake()
        {
            base.Awake();

            LoadGameData();

            GUIManager.Instance.Init();

#if ENABLE_FIREBASE
        
            FirebaseController.Instance.Init();
#endif
            
            Instance.gameInited = true;

            Evm.OnGameInited.Dispatch();
        
            lastClaimOnlineGiftTime = Time.time;
        }

        
        private void Start()
        {
            //Evm.OnUpdateSetting.Dispatch();
        
            LoadGameplay();
        
            void LoadGameplay()
            {
                LoadingManager.Instance.LoadScene(SceneIndex.Gameplay, MainScreen.Show);
            }
        
            //PushNotificationManager.Instance.SetupPushNotification();
        }

        
        private void LoadGameData()
        {
            data = Database.LoadData();
            if (data == null)
            {
                data = new GameData();
                Database.SaveData();
            }
            data.user.VerifyData();


            remoteConfigData = RemoteConfigDatabase.LoadData();
            if (remoteConfigData == null)
            {
                remoteConfigData = new RemoteConfigData();
                RemoteConfigDatabase.SaveData();
            }
            remoteConfigData.VerifyData();           
            
            
            generateData = GenerateDataBase.LoadData();
            if (generateData == null)
            {
                generateData = new GenerateData.Scripts.GenerateData();
                GenerateDataBase.SaveData();
            }
            generateData.VerifyData();
        
        }

        private void UpdateGraphicSetting()
        {
            if (data.setting.highPerformance == 1)
            {
                Application.targetFrameRate = 60;
                Screen.SetResolution(Screen.width, Screen.height, true);
            }
            else
            {
                Application.targetFrameRate = 30;
                Screen.SetResolution(Screen.width / 2, Screen.height / 2, true);
            }
        }

        public override void OnApplicationQuit()
        {
            Logout();
            base.OnApplicationQuit();
        }

        public void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus)
                Logout();

            if (hasFocus)
            {
                /*if (AdsInstance.canShowAoaWhenAppFocus)
                {
                    AdManager.ShowAppOpenAd();
                }
                else
                {
                    AdsInstance.canShowAoaWhenAppFocus = true;
                }*/
            }
        }

        public void OnApplicationPause(bool pauseStatus)        
        {
            if (pauseStatus)
                Logout();
        }

        private void Logout()
        {
            data.user.lastTimeLogOut = DateTime.Now;
            Database.SaveData();
            RemoteConfigDatabase.SaveData();
            GenerateDataBase.SaveData();
        }

        private void OnEnable()
        {
            if (Evm)
            {
                Evm.OnUpdateSetting.AddListener(UpdateGraphicSetting);
                Evm.OnEverySecondTick.AddListener(CheckInternet);
            }
        }

        private void OnDestroy()
        {
            if (Evm)
            {
                Evm.OnUpdateSetting.RemoveListener(UpdateGraphicSetting);
                Evm.OnEverySecondTick.RemoveListener(CheckInternet);
            }
        }
    
        void CheckInternet()
        {
            if (Application.internetReachability == NetworkReachability.NotReachable)
            {
                PopupNoInternet.Show();
            }
        }
    }
}
