using b100SDK.Scripts.UI;
using b100SDK.Scripts.UI.Panel;
using Sirenix.OdinInspector;

namespace b100SDK.Scripts.Base
{
    public class GameManagerExtension
    {
        
    }
    
    
    public partial class GameManager
    {
        
        
        
        
        
        [PropertySpace(10f)]
        [But<PERSON>(ButtonSizes.Large, ButtonStyle.Box)]
        public void AddMoney(int value)
        {
            data.user.money += value;
            EventGlobalManager.Instance.OnMoneyChange.Dispatch(true);
        }

        [PropertySpace(10f)]
        [<PERSON><PERSON>(ButtonSizes.Large, ButtonStyle.Box)]
        public bool SpendMoney(int value)
        {
            if (data.user.money >= value)
            {
                data.user.money -= value;
                EventGlobalManager.Instance.OnMoneyChange.Dispatch(true);
                return true;
            }

            EventGlobalManager.Instance.OnMoneyChange.Dispatch(false);
            return false;
        }
        
        
        

        

        #region TEST
#if UNITY_EDITOR

        [PropertySpace(20f)]
        [But<PERSON>(ButtonSizes.Large, ButtonStyle.Box)]

        void ShowSetting()
        {
            PopupSetting.Show();
        }


        [Button(ButtonSizes.Large, ButtonStyle.Box)]

        void SaveRemoteConfigData()
        {
            RemoteConfigDatabase.SaveData();
        }

        [Button(ButtonSizes.Large, ButtonStyle.Box)]

        void ShowNoti(string message = "This is a message", float duration = 3)
        {
            PopupNotification.Show(message, duration);
        }



        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        void LoadSceneLevelEditor()
        {
            LoadingManager.Instance.LoadScene(SceneIndex.LevelEditor, null, 1f, false);
        }

        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        void LoadSceneGameplay()
        {
            LoadingManager.Instance.LoadScene(SceneIndex.Gameplay, null, 1f, false);
        }

#endif
        #endregion
    }
}