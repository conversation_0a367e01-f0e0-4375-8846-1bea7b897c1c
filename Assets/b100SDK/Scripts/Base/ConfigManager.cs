#region

using b100SDK.Scripts.Configs.Ads;
using b100SDK.Scripts.Configs.Core;
using b100SDK.Scripts.Configs.SDKEditor;
using b100SDK.Scripts.DesignPatterns;
using b100SDK.Scripts.Utilities.Tool;
using Sirenix.OdinInspector;

#endregion

namespace b100SDK.Scripts.Base
{
    public class ConfigManager : Singleton<ConfigManager>
    {
        public GameSetting gameSetting;
        public AudioConfig audioCfg;
        public GameConfig gameCfg;
        public UIConfig uiConfig;
        public IAAConfig iaaConfig;
        
#if UNITY_EDITOR
        [Button]
        private void LoadConfigs()
        {
            gameSetting = SettingTool.GetGameSetting();
            audioCfg = ConfigTool.GetAudioConfig();
            gameCfg = ConfigTool.GetGameConfig();
            uiConfig = ConfigTool.GetUIConfig();
            iaaConfig = ConfigTool.GetIAAConfig();
        }
#endif

    }
}