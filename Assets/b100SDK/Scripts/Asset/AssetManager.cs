using System.Threading.Tasks;
using UnityEngine;

namespace b100SDK.Scripts.Asset
{
    public class AssetManager
    {

        #region Common
        
        public static T LoadAsset<T>(string path) where T : Object
        {
            return Resources.Load<T>(path);
        }

        public static T[] LoadAssets<T>(string path) where T : Object
        {
            return Resources.LoadAll<T>(path);
        }

        public static async Task<T> LoadAssetAsync<T>(string path) where T : Object
        {
            var request = Resources.LoadAsync<T>(path);
            
            while (!request.isDone)
            {
                await Task.Yield();
            }
            
            return request.asset as T;
        }
        
        public static void UnloadAsset(Object asset)
        {
            Resources.UnloadAsset(asset);
        }
        
        public static void UnloadUnusedAssets()
        {
            Resources.UnloadUnusedAssets();
        }
        
        
        #endregion

        
    }
}