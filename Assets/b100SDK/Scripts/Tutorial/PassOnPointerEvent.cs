using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

namespace b100SDK.Scripts.Tutorial
{
    public class PassOnPointerEvent : MonoBehaviour, IPointerClickHandler
    {
        [SerializeField]
        private RectTransform[] allowedPassThroughRegions;

        private readonly List<RaycastResult> _raycastResults = new List<RaycastResult>();

        public void OnPointerClick(PointerEventData eventData)
        {
            if (IsPointerInSideAllowedRegions(eventData.position))
            {
                EventSystem.current.RaycastAll(eventData, _raycastResults);
                for (int i = 0; i < _raycastResults.Count; i++)
                {
                    if (_raycastResults[i].gameObject == this.gameObject)
                    {
                        continue;
                    }

                    if (ExecuteEvents.ExecuteHierarchy(_raycastResults[i].gameObject, eventData, ExecuteEvents.pointerClickHandler))
                    {
                        break;
                    }
                }
            }
        }

        private bool IsPointerInSideAllowedRegions(Vector2 mousePosition)
        {
            foreach (RectTransform region in allowedPassThroughRegions)
            {
                if (RectTransformUtility.RectangleContainsScreenPoint(region, mousePosition))
                {
                    return true;
                }
            }

            return false;
        }
    }
}
