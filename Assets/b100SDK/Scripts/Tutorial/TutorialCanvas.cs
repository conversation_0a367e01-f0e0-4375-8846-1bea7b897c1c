using DG.Tweening;
using UnityEngine;

namespace b100SDK.Scripts.Tutorial
{
    public class TutorialCanvas : MonoBehaviour
    {
        private bool _isDisappearing;

        void OnDestroy()
        {
            this.DOKill();
        }

        void Update()
        {
            if (_isDisappearing)
            {
                return;
            }

            if (Input.GetMouseButtonDown(0))
            {
                _isDisappearing = true;

                GetComponent<CanvasGroup>()
                    .DOFade(0.0f, 0.5f)
                    .OnComplete(() =>
                    {
                        Destroy(this.gameObject);
                    })
                    .SetTarget(this);
            }
        }
    }
}
