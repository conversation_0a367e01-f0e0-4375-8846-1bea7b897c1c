using b100SDK.Scripts.DesignPatterns;
using UnityEngine;

namespace b100SDK.Scripts.Tutorial
{
    public class TutorialManager : Singleton<TutorialManager>
    {
        [SerializeField]
        private TutorialHand tutorialHand;

        protected override void Awake()
        {
            base.Awake();
            Hide();
        }

        public void Hide()
        {
            tutorialHand.Hide();
        }

        public void ShowHandDrag(Transform startPoint, Transform endPoint)
        {
            Hide();
        
            tutorialHand.DoHandDragAndDrop(startPoint, endPoint, true);
        }

        public void ShowHandTap(Vector3 screenPosition, bool showDimBackground = false, bool animateBackgroundShrink = false)
        {
            Hide();

            tutorialHand.DoHandTap(screenPosition, showDimBackground, animateBackgroundShrink);
        }
    }
}
