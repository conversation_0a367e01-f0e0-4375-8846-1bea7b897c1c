using System;
using DG.Tweening;
using UnityEngine;

namespace b100SDK.Scripts.Tutorial
{
    public class JoystickTutorialPanel : MonoBehaviour
    {
        [SerializeField]
        private CanvasGroup canvasGroup;

        private bool isDismissing;

        private Action panelDismissedAction;


        void Update()
        {
            if (Input.GetMouseButtonDown(0))
            {
                OnClicked();
            }
        }

        void OnDestroy()
        {
            this.DOKill();
        }

        public void SetPanelDismissedAction(Action action)
        {
            panelDismissedAction = action;
        }

        private void OnClicked()
        {
            if (isDismissing)
            {
                return;
            }
            isDismissing = true;

            panelDismissedAction?.Invoke();

            canvasGroup
                .DOFade(0.0f, 0.5f)
                .SetEase(Ease.Linear)
                .SetTarget(this)
                .OnComplete(() =>
                {
                    Destroy(this.gameObject);
                });
        }
    }
}
