using UnityEngine;
using UnityEngine.UI;

namespace b100SDK.Scripts.Tutorial
{
    public class TutorialArrow : MonoBehaviour
    {
        [SerializeField]
        private RectTransform arrowContainerTransform;

        [SerializeField]
        private Image arrowMaskImage;

        private Camera _mainCamera;

        private Canvas _rootCanvas;

        public void ToggleVisibility(bool visible)
        {
            this.gameObject.SetActive(visible);
            SetMaskValue(0.0f);
        }

        public void SetPosition(Vector3 screenStartPosition, Vector3 screenEndPosition)
        {
            arrowContainerTransform.position = screenStartPosition;

            Vector2 direction = new Vector2(screenEndPosition.x - screenStartPosition.x, screenEndPosition.y - screenStartPosition.y);

            float canvasScale = 1.0f;
            if (ResolveRootCanvas() != null)
            {
                canvasScale = _rootCanvas.scaleFactor;
            }
            arrowContainerTransform.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal, direction.magnitude / canvasScale);

            arrowContainerTransform.rotation = Quaternion.Euler(0.0f, 0.0f, Mathf.Atan2(direction.y, direction.x) * Mathf.Rad2Deg);
        }

        public void SetMaskValue(float value)
        {
            arrowMaskImage.fillAmount = value;
        }

        private Camera ResolveCamera()
        {
            if (_mainCamera == null)
            {
                _mainCamera = Camera.main;
            }

            return _mainCamera;
        }

        private Canvas ResolveRootCanvas()
        {
            if (_rootCanvas == null)
            {
                _rootCanvas = GetComponentInParent<Canvas>().rootCanvas;
            }

            return _rootCanvas;
        }
    }
}
