using System;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.Events;

namespace b100SDK.Scripts.EventHandler
{
    [CreateAssetMenu(menuName = "Events/Events/Void Event")]
    public class VoidEventChannelSO : ScriptableObject
    {
        public event UnityAction OnEventRaised;
        
        
        [<PERSON><PERSON>(ButtonSizes.Medium)]
        public void RaiseEvent()
        {
            OnEventRaised?.Invoke();
        }
    }
}