using Sirenix.OdinInspector;
using UnityEngine;

namespace b100SDK.Scripts.EventHandler
{
    public abstract class ResultEventChannelSOBase<T> : ScriptableObject
    {
        public delegate T b100EventHandler();
        
        public event b100EventHandler OnEventRaised;
        
        [<PERSON><PERSON>(ButtonSizes.Medium)]
        public T RaiseEvent()
        {
            if (OnEventRaised != null)
            {
                return OnEventRaised.Invoke();
            }
            
            return default(T);
        }
    }
    
    public abstract class ResultEventChannelSOBase<T, T1> : ScriptableObject
    {
        public delegate T b100EventHandler(T1 parameter);
        
        public event b100EventHandler OnEventRaised;
        
        
        [But<PERSON>(ButtonSizes.Medium)]
        public T RaiseEvent(T1 parameter)
        {
            if (OnEventRaised != null)
            {
                return OnEventRaised.Invoke(parameter);
            }
            
            return default(T);
        }
    }
    
    public abstract class ResultEventChannelSOBase<T, T1, T2> : ScriptableObject
    {
        public delegate T b100EventHandler(T1 parameter1, T2 parameter2);
        
        public event b100EventHandler OnEventRaised;
        
        [<PERSON><PERSON>(ButtonSizes.Medium)]
        public T RaiseEvent(T1 parameter, T2 parameter2)
        {
            if (OnEventRaised != null)
            {
                return OnEventRaised.Invoke(parameter, parameter2);
            }
            
            return default(T);
        }
    }
    
    public abstract class ResultEventChannelSOBase<T, T1, T2, T3> : ScriptableObject
    {
        public delegate T b100EventHandler(T1 parameter1, T2 parameter2, T3 parameter3);
        
        public event b100EventHandler OnEventRaised;
        
        
        [Button(ButtonSizes.Medium)]
        public T RaiseEvent(T1 parameter, T2 parameter2, T3 parameter3)
        {
            if (OnEventRaised != null)
            {
                return OnEventRaised.Invoke(parameter, parameter2, parameter3);
            }
            
            return default(T);
        }
    }

    public abstract class ResultEventChannelSOBase<T, T1, T2, T3, T4> : ScriptableObject
    {
        public delegate T b100EventHandler(T1 parameter1, T2 parameter2, T3 parameter3, T4 parameter4);
        
        public event b100EventHandler OnEventRaised;

        
        [Button(ButtonSizes.Medium)]
        public T RaiseEvent(T1 parameter, T2 parameter2, T3 parameter3, T4 parameter4)
        {
            if (OnEventRaised != null)
            {
                return OnEventRaised.Invoke(parameter, parameter2, parameter3, parameter4);
            }
            
            return default(T);
        }
    }

    public abstract class ResultEventChannelSOBase<T, T1, T2, T3, T4, T5> : ScriptableObject
    {
        public delegate T b100EventHandler(T1 parameter1, T2 parameter2, T3 parameter3, T4 parameter4, T5 parameter5); 
        
        public event b100EventHandler OnEventRaised;
        
        [Button(ButtonSizes.Medium)]
        public T RaiseEvent(T1 parameter, T2 parameter2, T3 parameter3, T4 parameter4, T5 parameter5)
        {
            if (OnEventRaised != null)
            {
                return OnEventRaised.Invoke(parameter, parameter2, parameter3, parameter4, parameter5);
            }
            
            return default(T);
        }
    }

    public abstract class ResultEventChannelSOBase<T, T1, T2, T3, T4, T5, T6> : ScriptableObject
    {
        public delegate T b100EventHandler(T1 parameter1, T2 parameter2, T3 parameter3, T4 parameter4, T5 parameter5, T6 parameter6);
        
        public event b100EventHandler OnEventRaised;

        
        [Button(ButtonSizes.Medium)]
        public T RaiseEvent(T1 parameter, T2 parameter2, T3 parameter3, T4 parameter4, T5 parameter5, T6 parameter6)
        {
            if (OnEventRaised != null)
            {
                return OnEventRaised.Invoke(parameter, parameter2, parameter3, parameter4, parameter5, parameter6);
            }
            
            return default(T);
        }
    }

    public abstract class ResultEventChannelSOBase<T, T1, T2, T3, T4, T5, T6, T7> : ScriptableObject
    {
        public delegate T b100EventHandler(T1 parameter1, T2 parameter2, T3 parameter3, T4 parameter4, T5 parameter5, T6 parameter6, T7 parameter7);
        
        public event b100EventHandler OnEventRaised;

        
        [Button(ButtonSizes.Medium)]
        public T RaiseEvent(T1 parameter, T2 parameter2, T3 parameter3, T4 parameter4, T5 parameter5, T6 parameter6,
            T7 parameter7)
        {
            if (OnEventRaised != null)
            {
                return OnEventRaised.Invoke(parameter, parameter2, parameter3, parameter4, parameter5, parameter6, parameter7);
            }
            
            return default(T);
        }
    }

    public abstract class ResultEventChannelSOBase<T, T1, T2, T3, T4, T5, T6, T7, T8> : ScriptableObject
    {
        public delegate T b100EventHandler(T1 parameter1, T2 parameter2, T3 parameter3, T4 parameter4, T5 parameter5, T6 parameter6,
            T7 parameter7, T8 parameter8);
        
        public event b100EventHandler OnEventRaised;

        
        [Button(ButtonSizes.Medium)]
        public T RaiseEvent(T1 parameter, T2 parameter2, T3 parameter3, T4 parameter4, T5 parameter5, T6 parameter6,
            T7 parameter7, T8 parameter8)
        {
            if (OnEventRaised != null)
            {
                return OnEventRaised.Invoke(parameter, parameter2, parameter3, parameter4, parameter5, parameter6, parameter7, parameter8);
            }
            
            return default(T);
        }
    }
    
    
}