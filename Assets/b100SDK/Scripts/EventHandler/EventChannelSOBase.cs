using UnityEngine;
using UnityEngine.Events;
using System;
using Sirenix.OdinInspector;

namespace b100SDK.Scripts.EventHandler
{
    public abstract class EventChannelSOBase<T> : ScriptableObject 
    {
        public event Action<T> OnEventRaised;
        
        [<PERSON><PERSON>(ButtonSizes.Medium)]
        public void RaiseEvent(T parameter)
        {
            OnEventRaised?.Invoke(parameter);
        }
    }

    public abstract class EventChannelSOBase<T1, T2> : ScriptableObject
    {
        public event Action<T1, T2> OnEventRaised;
        
        [<PERSON><PERSON>(ButtonSizes.Medium)]
        public void RaiseEvent(T1 parameter1, T2 parameter2)
        {
            OnEventRaised?.Invoke(parameter1, parameter2);
        }
    }

    public abstract class EventChannelSOBase<T1, T2, T3> : ScriptableObject
    {
        public event Action<T1, T2, T3> OnEventRaised;
        
        [But<PERSON>(ButtonSizes.Medium)]
        public void RaiseEvent(T1 parameter1, T2 parameter2, T3 parameter3)
        {
            OnEventRaised?.Invoke(parameter1, parameter2, parameter3);
        }
    }

    public abstract class EventChannelSOBase<T1, T2, T3, T4> : ScriptableObject
    {
        public event Action<T1, T2, T3, T4> OnEventRaised;
        
        [<PERSON><PERSON>(ButtonSizes.Medium)]
        public void RaiseEvent(T1 parameter1, T2 parameter2, T3 parameter3, T4 parameter4)
        {
            OnEventRaised?.Invoke(parameter1, parameter2, parameter3, parameter4);
        }
    }

    public abstract class EventChannelSOBase<T1, T2, T3, T4, T5> : ScriptableObject
    {
        public event Action<T1, T2, T3, T4, T5> OnEventRaised;
        
        [Button(ButtonSizes.Medium)]
        public void RaiseEvent(T1 parameter1, T2 parameter2, T3 parameter3, T4 parameter4, T5 parameter5)
        {
            OnEventRaised?.Invoke(parameter1, parameter2, parameter3, parameter4, parameter5);
        }
    }

    public abstract class EventChannelSOBase<T1, T2, T3, T4, T5, T6> : ScriptableObject
    {
        public event Action<T1, T2, T3, T4, T5, T6> OnEventRaised;
        
        [Button(ButtonSizes.Medium)]
        public void RaiseEvent(T1 parameter1, T2 parameter2, T3 parameter3, T4 parameter4, T5 parameter5, T6 parameter6)
        {
            OnEventRaised?.Invoke(parameter1, parameter2, parameter3, parameter4, parameter5, parameter6);
        }
    }

    public abstract class EventChannelSOBase<T1, T2, T3, T4, T5, T6, T7> : ScriptableObject
    {
        public event Action<T1, T2, T3, T4, T5, T6, T7> OnEventRaised;
        
        [Button(ButtonSizes.Medium)]
        public void RaiseEvent(T1 parameter1, T2 parameter2, T3 parameter3, T4 parameter4, T5 parameter5, T6 parameter6, T7 parameter7)
        {
            OnEventRaised?.Invoke(parameter1, parameter2, parameter3, parameter4, parameter5, parameter6, parameter7);
        }
    }

    public abstract class EventChannelSOBase<T1, T2, T3, T4, T5, T6, T7, T8> : ScriptableObject
    {
        public event Action<T1, T2, T3, T4, T5, T6, T7, T8> OnEventRaised;
        
        [Button(ButtonSizes.Medium)]
        public void RaiseEvent(T1 parameter1, T2 parameter2, T3 parameter3, T4 parameter4, T5 parameter5, T6 parameter6, T7 parameter7, T8 parameter8)
        {
            OnEventRaised?.Invoke(parameter1, parameter2, parameter3, parameter4, parameter5, parameter6, parameter7, parameter8);
        }
    }
}