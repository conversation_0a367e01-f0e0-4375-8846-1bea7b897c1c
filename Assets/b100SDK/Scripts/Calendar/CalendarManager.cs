using System;
using b100SDK.Scripts.Base;
using Sirenix.OdinInspector;
using TMPro;
using UnityEngine;

namespace b100SDK.Scripts.Calendar
{
    public class CalendarManager : BhMonoBehavior
    {
        public GameObject dayPrefab; // Prefab cho ô ngày
        public Transform calendarGrid; // Transform của Grid Layout Group
        public TMP_Text monthYearText; // Text để hiển thị tháng và năm
        private int month, year;

        void Start()
        {
            DateTime now = DateTime.Now;
            month = now.Month;
            year = now.Year;
            CreateCalendar(month, year);
        }

        void CreateCalendar(int month, int year)
        {
            // Hiển thị tháng và năm
            monthYearText.text = $"{month}/{year}";

            // Xóa các ngày cũ nếu có
            foreach (Transform child in calendarGrid)
            {
                Destroy(child.gameObject);
            }

            // Lấy số ngày trong tháng và ngày đầu tiên là ngày nào
            DateTime firstDay = new DateTime(year, month, 1);
            int daysInMonth = DateTime.DaysInMonth(year, month);
            int startDay = (int)firstDay.DayOfWeek;

            // Lấy thông tin tháng trước và tháng sau
            int previousMonth = month == 1 ? 12 : month - 1;
            int nextMonth = month == 12 ? 1 : month + 1;
            int previousYear = month == 1 ? year - 1 : year;
            int nextYear = month == 12 ? year + 1 : year;
            int daysInPreviousMonth = DateTime.DaysInMonth(previousYear, previousMonth);

            // Tạo các ô ngày của tháng trước nếu cần
            for (int i = 0; i < startDay; i++)
            {
                GameObject dayObj = Instantiate(dayPrefab, calendarGrid);
                dayObj.GetComponentInChildren<TMP_Text>().text = (daysInPreviousMonth - (startDay - 1 - i)).ToString();
                dayObj.GetComponentInChildren<TMP_Text>().color = Color.gray; // Đổi màu cho ngày của tháng trước
            }

            // Tạo các ô ngày của tháng hiện tại
            for (int day = 1; day <= daysInMonth; day++)
            {
                GameObject dayObj = Instantiate(dayPrefab, calendarGrid);
                dayObj.GetComponentInChildren<TMP_Text>().text = day.ToString();
            }

            // Tạo các ô ngày của tháng sau nếu cần
            int remainingCells = 35 - (daysInMonth + startDay); // Tổng cộng có 42 ô (7x6 lưới)
            for (int i = 1; i <= remainingCells; i++)
            {
                GameObject dayObj = Instantiate(dayPrefab, calendarGrid);
                dayObj.GetComponentInChildren<TMP_Text>().text = i.ToString();
                dayObj.GetComponentInChildren<TMP_Text>().color = Color.gray; // Đổi màu cho ngày của tháng sau
            }
        }

        [Button]
        public void NextMonth()
        {
            month++;
            if (month > 12)
            {
                month = 1;
                year++;
            }
            CreateCalendar(month, year);
        }

        [Button]
        public void PreviousMonth()
        {
            month--;
            if (month < 1)
            {
                month = 12;
                year--;
            }
            CreateCalendar(month, year);
        }
    }
}