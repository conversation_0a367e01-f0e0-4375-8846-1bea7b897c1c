using System;
using System.Collections.Generic;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Extensions;
using b100SDK.Scripts.Utilities.Tool;
using Sirenix.OdinInspector;

#if UNITY_EDITOR
using Sirenix.OdinInspector.Editor;
using UnityEditor;
#endif
using UnityEngine;

namespace b100SDK.Scripts.Audio
{

#if UNITY_EDITOR
    
    public class AudioCreator : OdinEditorWindow
    {
        private const string _AUDIO_ENUM_FILE_PATH = "Assets/b100SDK/Scripts/Audio/AudioEnums.cs";

        private const string _IS_CREATE_MUSIC = "AudioCreateIsCreateMusic";
        
        private const string _AUDIO_CREATOR_IS_STARTED_KEY = "AudioCreatorIsStarted";
        private const string _AUDIO_CREATOR_IS_WAITING_FOR_CREATE_SCRIPT_KEY = "AudioCreatorIsWaitingForCreateScript";
        private const string _AUDIO_CREATOR_IS_DONE_KEY = "AudioCreatorIsDone";


        private static bool IsCreateMusic
        {
            get => PlayerPrefs.GetInt(_IS_CREATE_MUSIC) == 1;

            set
            {
                PlayerPrefs.SetInt(_IS_CREATE_MUSIC, value ? 1 : 0);
            }
        }

        private static bool IsStartedCreateScript
        {
            get => PlayerPrefs.GetInt(_AUDIO_CREATOR_IS_STARTED_KEY) == 1;

            set => PlayerPrefs.SetInt(_AUDIO_CREATOR_IS_STARTED_KEY, value ? 1 : 0);
        }

        private static bool IsWaitingForCreateScript
        {
            get => PlayerPrefs.GetInt(_AUDIO_CREATOR_IS_WAITING_FOR_CREATE_SCRIPT_KEY) == 1;

            set => PlayerPrefs.SetInt(_AUDIO_CREATOR_IS_WAITING_FOR_CREATE_SCRIPT_KEY, value ? 1 : 0);
        }

        private static bool IsDone
        {
            get => PlayerPrefs.GetInt(_AUDIO_CREATOR_IS_DONE_KEY) == 1;

            set => PlayerPrefs.SetInt(_AUDIO_CREATOR_IS_DONE_KEY, value ? 1 : 0);
        }
        
        
        
        
        private List<string> _allKey = new();
        private string _lastKey;

        
        #region Music
        
        [ShowIf(nameof(IsCreateMusic))]
        [Title("Music")]
        [SerializeField]
        private SerializedDictionary<MusicType, AudioClip> musicMap = new();

        private List<AudioClip> _allMusic = new();

        private const string _MUSIC_PREFIX = "Music_";
        
        [PropertySpace(10f)]
        [ShowIf(nameof(IsCreateMusic))]
        [SerializeField, FolderPath()]
        private List<string> musicFolderPaths = new();
        
        void GetAllMusic()
        {
            _allMusic.Clear();
            
            foreach (var musicFolderPath in musicFolderPaths)
            {
                if (string.IsNullOrEmpty(musicFolderPath))
                {
                    BhDebug.LogError("Path is null or empty!!!");
                    continue;
                }
                
                var audioClips = UtilitiesTool.GetResources<AudioClip>(musicFolderPath,
                    new List<string>() { ".ogg", ".wav", ".mp3", ".aac", ".flac", ".wma" });

                if (audioClips == null)
                {
                    continue;
                }

                foreach (var audioClip in audioClips)
                {
                    if (audioClip.name.Contains(_MUSIC_PREFIX) && !_allMusic.Contains(audioClip))
                    {
                        _allMusic.Add(audioClip);
                    }
                }
            }

            foreach (var music in _allMusic)
            {
                var correctKey = music.name.Replace("Music_", "");
                _allKey.Add(correctKey);
            }

            if (_allKey.Count > 0)
            {
                _lastKey = _allKey.GetLast();
            }
        }
        
        
        
        void AddMusicEnumKey()
        {
            var keyNeedAdd = new List<string>();
            
            foreach (var key in _allKey)
            {
                var newType = Enum.TryParse<MusicType>(key, out var parseType);
                if (newType)
                {
                    LogError("This music key is existed. Ignore.");
                }
                else
                {
                    keyNeedAdd.Add(key);
                }
            }


            foreach (var key in keyNeedAdd)
            {
                UtilitiesTool.AddNewEnum(_AUDIO_ENUM_FILE_PATH, key, "SdkMusicPlaceHolder");
            }
            
            AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);
        }
        
        

        void OnAddMusicToConfig(Action onComplete = null)
        {
            if (!Enum.TryParse<MusicType>(_lastKey, out var data))
            {
                Log("Wait for create music enum........");
                return;
            }

            AddMusicToConfig();

            SaveMusicToConfig();

            LogSuccess("Complete add all music!!!");
            
            onComplete?.Invoke();
        }


        void AddMusicToConfig()
        {
            BhDebug.LogError("Clear music");
            musicMap.Clear();
            
            GetAllMusic();

            foreach (var music in _allMusic)
            {
                var correctKey = music.name.Replace("Music_", "");
                if (Enum.TryParse(correctKey, out MusicType parseType))
                {
                    musicMap.TryAdd(parseType, music);
                }
            }
        }

        void SaveMusicToConfig()
        {
            var soundConfig = ConfigTool.GetSoundConfig();
            soundConfig.AddMusic(musicMap);
        }
        
        
        void CreateMusic()
        {
            GetAllMusic();

            AddMusicEnumKey();
            
            IsWaitingForCreateScript = true;
            IsStartedCreateScript = true;
            IsDone = false;
        }

        

        #endregion




        #region Sound


        [HideIf(nameof(IsCreateMusic))]
        [Title("Sound")]
        [SerializeField]
        private SerializedDictionary<SoundType, AudioClip> soundMap = new();
        
        

        private List<AudioClip> _allSound = new();

        private const string _SOUND_PREFIX = "Sound_";
        

        [PropertySpace(10f)]
        [HideIf(nameof(IsCreateMusic))]
        [SerializeField, FolderPath()]
        private List<string> soundFolderPaths = new();
        
        void GetAllSound()
        {
            _allSound.Clear();
            
            foreach (var soundFolderPath in soundFolderPaths)
            {
                if (string.IsNullOrEmpty(soundFolderPath))
                {
                    BhDebug.LogError("Path is null or empty!!!");
                    continue;
                }         
                
                var audioClips = UtilitiesTool.GetResources<AudioClip>(soundFolderPath,
                    new List<string>() { ".ogg", ".wav", ".mp3", ".aac", ".flac", ".wma" });
                
                if (audioClips == null)
                    continue;

                foreach (var audioClip in audioClips)
                {
                    if (audioClip.name.Contains(_SOUND_PREFIX) && !_allSound.Contains(audioClip))
                    {
                        _allSound.Add(audioClip);
                    }
                }
            }

            foreach (var music in _allSound)
            {
                var correctKey = music.name.Replace("Sound_", "");
                _allKey.Add(correctKey);
            }

            if (_allKey.Count > 0)
            {
                _lastKey = _allKey.GetLast();
            }
        }
        
        
        
        void AddSoundEnumKey()
        {
            var keyNeedAdd = new List<string>();
            
            foreach (var key in _allKey)
            {
                var newType = Enum.TryParse<SoundType>(key, out var parseType);
                if (newType)
                {
                    LogError("This sound key is existed. Ignore.");
                }
                else
                {
                    keyNeedAdd.Add(key);
                }
            }


            foreach (var key in keyNeedAdd)
            {
                UtilitiesTool.AddNewEnum(_AUDIO_ENUM_FILE_PATH, key, "SdkSoundPlaceHolder");
            }
            
            AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);
        }
        
        

        void OnAddSoundToConfig(Action onComplete = null)
        {
            if (!Enum.TryParse<SoundType>(_lastKey, out var data))
            {
                Log("Wait for create sound enum........");
                return;
            }

            AddSoundToConfig();

            SaveSoundToConfig();

            LogSuccess("Complete add all sound!!!");
            
            onComplete?.Invoke();
        }


        void AddSoundToConfig()
        {
            soundMap.Clear();
            
            GetAllSound();

            foreach (var sound in _allSound)
            {
                var correctKey = sound.name.Replace("Sound_", "");
                if (Enum.TryParse(correctKey, out SoundType parseType))
                {
                    soundMap.TryAdd(parseType, sound);
                }
            }
        }

        void SaveSoundToConfig()
        {
            var soundConfig = ConfigTool.GetSoundConfig();
            soundConfig.AddSound(soundMap);
        }
        
        
        void CreateSound()
        {
            GetAllSound();

            AddSoundEnumKey();
            
            IsWaitingForCreateScript = true;
            IsStartedCreateScript = true;
            IsDone = false;
        }
        

        #endregion
        
        
        

        
        
        
        
        public static void OpenWindow(bool isMusic)
        {
            GetWindow<AudioCreator>("b100 Audio Creator").Show();
            IsCreateMusic = isMusic;
        }
        
        
        public static void CloseWindow()
        {
            GetWindow<AudioCreator>("b100 Audio Creator").Close();
        }

        private void Awake()
        {
            if (IsCreateMusic)
            {
                AddMusicToConfig();
            }
            else
            {
                AddSoundToConfig();
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            
            IsDone = false;
            IsWaitingForCreateScript = false;
            IsStartedCreateScript = false;
        }

        private void Update()
        {
            if (!IsWaitingForCreateScript) 
                return;
            
            if (!IsStartedCreateScript)
                return;
            
            if (IsDone)
                return;

            AddToConfig(() =>
            {
                IsDone = true;
                IsStartedCreateScript = false;
                IsWaitingForCreateScript = false;
            });
        }

        void AddToConfig(Action onComplete = null)
        {
            if (IsCreateMusic)
            {
                OnAddMusicToConfig(onComplete);
            }
            else
            {
                OnAddSoundToConfig(onComplete);
            }
        }
     

        [PropertySpace(20)]
        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        void Create()
        {
            logEntries.Clear();

            if (IsCreateMusic)
            {
                CreateMusic();
            }
            else
            {
                CreateSound();
            }
        }


        #region Log
        
        private List<LogEntry> logEntries = new();
        private Vector2 scrollPosition;
        
        private void Log(string message = "Test Log")
        {
            AddLog(message, Color.white);
        }
        private void LogWarning(string message)
        {
            AddLog(message, Color.yellow);
        }
        
        private void LogError(string message)
        {
            AddLog(message, Color.red);
        }        
        
        private void LogSuccess(string message)
        {
            AddLog(message, Color.green);
        }

        protected override void OnGUI()
        {
            base.OnGUI(); 

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Width(position.width), GUILayout.Height(position.height-300));

            EditorGUILayout.BeginVertical(new GUILayoutOption[]
            {
                GUILayout.MinHeight(20),
            
            });
            
            foreach (var logEntry in logEntries)
            {
                GUI.contentColor = logEntry.color;
                GUILayout.Label(logEntry.message);
            }

            GUI.contentColor = Color.white;

            EditorGUILayout.EndScrollView();
            
            EditorGUILayout.EndVertical();

        }

        private void AddLog(string message, Color color)
        {
            logEntries.Insert(0, new LogEntry() { message = message, color = color });
            // Set scroll position to the bottom
            scrollPosition.y = 0f;
        }

        private class LogEntry
        {
            public string message;
            public Color color;
        }

        #endregion
    }
#endif

}