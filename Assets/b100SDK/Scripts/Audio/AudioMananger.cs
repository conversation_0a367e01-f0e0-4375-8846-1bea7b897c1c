#region

using System;
using System.Collections.Generic;
using b100SDK.Scripts.Asset;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Configs.Core;
using b100SDK.Scripts.DesignPatterns;
using b100SDK.Scripts.Utilities;
using DG.Tweening;
using Sirenix.OdinInspector;
using UnityEngine;
using GameManager = b100SDK.Scripts.Base.GameManager;
using Random = UnityEngine.Random;

#endregion

namespace b100SDK.Scripts.Audio
{
    [RequireComponent(typeof(AudioListener))]
    public class AudioManager : Singleton<AudioManager>
    {
        [Header("Music")]
        [SerializeField]
        private AudioSource musicAudioSource;
        [SerializeField, Range(0f, 1f)]
        private float volumeBgm = 0.5f;

        [Header("Sound")]
        [SerializeField]
        private AudioSource soundAudioSource;
        [SerializeField, Range(0f, 1f)]
        private float volumeSfx = 1f;

        
        private AudioConfig _audioConfig;


        private void OnEnable()
        {
            Init();
            Evm.OnUpdateSetting.AddListener(UpdateSoundSetting);
        }

        private void OnDestroy()
        {
            if (Evm)
            {
                Evm.OnUpdateSetting.RemoveListener(UpdateSoundSetting);
            }
        }

        private void UpdateSoundSetting()
        {
            if (GameManager.Instance && GameManager.Instance.data != null)
            {
                soundAudioSource.volume = volumeSfx * GameManager.Instance.data.setting.soundVolume;
                musicAudioSource.volume = volumeBgm * GameManager.Instance.data.setting.musicVolume;
            }
        }

        void Init()
        {
            _audioConfig = ConfigManager.Instance.audioCfg;
        }


        #region Sound

        private static Dictionary<SoundType, AudioClip> _soundClipMaps = new();
        private static readonly Dictionary<SoundType, float> LastPlayTime = new();
        private const float _MIN_INTERVAL_BETWEEN_SAME_SOUND = 0.05f;


        public void PreloadSound(List<SoundType> sounds)
        {
            foreach (var sound in sounds)
            {
                LoadSoundToMap(sound);
            }
        }

        public void RemoveSound(List<SoundType> sounds)
        {
            foreach (var sound in sounds)
            {
                _soundClipMaps.Remove(sound);
            }
            
            AssetManager.UnloadUnusedAssets();
        }

        public static void Shot(SoundType typeSound, float minIntervalBetweenSameSound = _MIN_INTERVAL_BETWEEN_SAME_SOUND)
        {
            if (typeSound == SoundType.None)
                return;

            float currentTime = Time.unscaledTime;
            if (LastPlayTime.TryGetValue(typeSound, out float lastTime))
            {
                if (currentTime - lastTime < minIntervalBetweenSameSound)
                    return;
            }

            LastPlayTime[typeSound] = currentTime;

            PlayOneShotSound(GetSoundAudioClip(typeSound));
        }

        static void LoadSoundToMap(SoundType type)
        {
            var soundClipPath = Instance._audioConfig.GetSoundPath(type);
            if (string.IsNullOrEmpty(soundClipPath))
            {
                BhDebug.LogWarning($"Can't find sound clip for {type}");
                return;
            }
            
            var soundClip = AssetManager.LoadAsset<AudioClip>(soundClipPath);

            _soundClipMaps.TryAdd(type, soundClip);
        }

        static AudioClip GetSoundAudioClip(SoundType type)
        {
            if (!_soundClipMaps.ContainsKey(type))
            {
                LoadSoundToMap(type);
            }

            if (_soundClipMaps.TryGetValue(type, out var clip))
            {
                return clip;
            }

            return null;
        }

        static void PlayOneShotSound(AudioClip soundClip)
        {
            if (Instance != null && Instance.soundAudioSource != null && soundClip != null)
            {
                Instance.soundAudioSource.PlayOneShot(soundClip);
            }
        }
        

        #endregion



        
        
        #region Music
        
        
        private Dictionary<MusicType, AudioClip> _musicClipMaps = new();
        private MusicType _currentMusicTrack = MusicType.SdkMusicPlaceHolder;
        private AudioClip _currentMusicTrackClip;

        float MusicVolume
        {
            get
            {
                if (Gm)
                {
                    return Gm.data.setting.musicVolume * volumeBgm;
                }
            
                return volumeBgm;
            }
        }

        public void PreloadMusic(List<MusicType> musicTypes)
        {
            foreach (var musicType in musicTypes)
            {
                LoadMusicToMap(musicType);
            }
        }

        public void RemoveMusic(List<MusicType> musicTypes)
        {
            foreach (var musicType in musicTypes)
            {
                _musicClipMaps.Remove(musicType);
            }
            
            AssetManager.UnloadUnusedAssets();
        }


        void LoadMusicToMap(MusicType type)
        {
            var musicClipPath = Instance._audioConfig.GetMusicPath(type);
            
            if (string.IsNullOrEmpty(musicClipPath))
            {
                BhDebug.LogError($"Can't find music clip for {type}");
                return;
            }
            
            var musicClip = AssetManager.LoadAsset<AudioClip>(musicClipPath);

            _musicClipMaps.TryAdd(type, musicClip);
        }
        
        AudioClip GetMusicAudioClip(MusicType type)
        {
            if (!_musicClipMaps.ContainsKey(type))
            {
                LoadMusicToMap(type);
            }

            if (_musicClipMaps.TryGetValue(type, out var clip))
            {
                return clip;
            }

            return null;
        }
        
        

        public void PlayMusic(MusicType musicType, bool isFade = true, float fadeInDuration = .3f, float fadeOutDuration = .3f)
        {
            if (musicType == MusicType.SdkMusicPlaceHolder)
                return;
            
            if (_currentMusicTrack == musicType)
            {
                return;
            }
            
            _currentMusicTrack = musicType;

            var newTrack = GetMusicAudioClip(musicType);
            
            PlayMusic(newTrack, () =>
            {
                if (_currentMusicTrackClip)
                {
                    AssetManager.UnloadAsset(_currentMusicTrackClip);
                }
                
                _currentMusicTrackClip = newTrack;
            }, isFade, fadeInDuration, fadeOutDuration);
        }

        public void PlayRandomMusic(bool isFade = true, float fadeInDuration = .3f, float fadeOutDuration = .3f)
        {
            var totalMusicTrack = Enum.GetValues(typeof(MusicType)).Length;
            if (totalMusicTrack <= 1)
                return;
            
            var randomIndex = Random.Range(0, totalMusicTrack - 1);
            PlayMusic((MusicType)randomIndex, isFade, fadeInDuration, fadeOutDuration);
        }

        public void StopMusic()
        {
            musicAudioSource.Stop();
        }

        public void PausedMusic()
        {
            musicAudioSource.Pause();
        }
        
        public void ResumeMusic()
        {
            musicAudioSource.UnPause();
        }
        

        void PlayMusic(AudioClip newClip, Action onComplete = null, bool isFade = true, float fadeInDuration = .3f, float fadeOutDuration = .3f)
        {
            musicAudioSource.DOKill(true);
            
            if (!isFade)
            {
                musicAudioSource.clip = newClip;
                musicAudioSource.Play();
                onComplete?.Invoke();
            }
            else
            {
                if (musicAudioSource.isPlaying)
                {
                    musicAudioSource.DOFade(0f, fadeOutDuration).OnComplete(() =>
                    {
                        musicAudioSource.clip = newClip;
                        musicAudioSource.Play();
                        musicAudioSource.DOFade(MusicVolume, fadeInDuration).OnComplete(() =>
                        {
                            onComplete?.Invoke();
                        });
                    });
                }
                else
                {
                    musicAudioSource.clip = newClip;
                    musicAudioSource.Play();
                    musicAudioSource.volume = 0f;
                    musicAudioSource.DOFade(MusicVolume, fadeInDuration).OnComplete(() =>
                    {
                        onComplete?.Invoke();
                    });
                }
            }
        }

        #endregion








        #region Test

#if UNITY_EDITOR

        [Button]
        void PlayMusic(MusicType type)
        {
            PlayMusic(type, true);
        }

        [Button]

        void PlaySound(SoundType soundType)
        {
            Shot(soundType);
        }
        
#endif

        #endregion
    }
}