using b100SDK.Scripts.UI;

namespace TempNamespace
{
    public class TempClassName : UIPanel
    {
        public static TempClassName Instance { get; private set; }
    
        public override UIPanelType GetId()
        {
            return UIPanelType.TempClassName;
        }
    
        public static void Show()
        {
            var newInstance = (TempClassName) GUIManager.Instance.NewPanel(UIPanelType.TempClassName);
            Instance = newInstance;
            newInstance.OnAppear();
        }
    
        public override void OnAppear()
        {
            if (isInited)
                return;
    
            base.OnAppear();
    
            Init();
        }
        
        public override void OnDisappear()
        {
            base.OnDisappear();
            Instance = null;
        }

        protected override void RegisterEvent()
        {
            base.RegisterEvent();
        }
    
        protected override void UnregisterEvent()
        {
            base.UnregisterEvent();
        }
    
        private void Init()
        {
        }
    }
}