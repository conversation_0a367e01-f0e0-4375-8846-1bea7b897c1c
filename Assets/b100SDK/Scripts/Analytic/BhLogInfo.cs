#if ENABLE_FIREBASE

using Firebase.Analytics;
#endif

namespace b100SDK.Scripts.Analytic
{
    public class BhLogInfo
    {
#if ENABLE_FIREBASE
        
        private readonly List<Parameter> _listParameters;

        public Parameter[] Parameters => _listParameters.ToArray();

#endif
        
        public BhLogInfo(BhParameter parameter)
        {
#if ENABLE_FIREBASE
            _listParameters = new List<Parameter> { new Parameter(parameter.param, parameter.value) };
#endif
        }
        
        public BhLogInfo(params BhParameter[] parameters)
        {
#if ENABLE_FIREBASE
            _listParameters = new List<Parameter>();
#endif
            
            foreach (BhParameter parameter in parameters)
            {
#if ENABLE_FIREBASE
                _listParameters.Add(parameter.GetParameter());
#endif
            }
        }

        
    }

    public class BhParameter
    {
        public string param;
        public string value;
        public long longValue;
        public double doubleValue;
        public TrackingValueType trackingValueType;
        
        public BhParameter(string param)
        {
            this.param = param;
            trackingValueType = TrackingValueType.String;
        }
        
        public BhParameter(string param, string value)
        {
            this.param = param;
            this.value = value;
            trackingValueType = TrackingValueType.String;
        }

        public BhParameter(string param, long longValue)
        {
            this.param = param;
            this.longValue = longValue;
            trackingValueType = TrackingValueType.Long;
        }

        public BhParameter(string param, double doubleValue)
        {
            this.param = param;
            this.doubleValue = doubleValue;
            trackingValueType = TrackingValueType.Double;
        }
        
        
#if ENABLE_FIREBASE
        
        public Parameter GetParameter()
        {
            var result = trackingValueType switch
            {
                TrackingValueType.String => new Parameter(param, value),
                TrackingValueType.Long => new Parameter(param, longValue),
                TrackingValueType.Double => new Parameter(param, doubleValue),
                _ => new Parameter(param, value)
            };
            
            return result;
        }
#endif
    }

    public enum TrackingValueType
    {
        String, 
        Long, 
        Double,
    }
}