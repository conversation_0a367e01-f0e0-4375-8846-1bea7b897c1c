using b100SDK.Scripts.Configs;
using b100SDK.Scripts.Configs.RemoteConfig;
using b100SDK.Scripts.DesignPatterns;
using UnityEngine;
#if ENABLE_FIREBASE
using Firebase;
using Firebase.Extensions;
using Firebase.RemoteConfig;
#endif

namespace b100SDK.Scripts.Analytic
{
    public class RemoteConfigManager: Singleton<RemoteConfigManager>
    {
        
        [SerializeField]
        private bool isFetchDataRealtime = false;
        
        [Header("Config")]
        [SerializeField] private RemoteConfigGroup remoteConfigGroup;
        
#if ENABLE_FIREBASE
        
        void SetValues()
        {
            //TODO: set value to remote config data base

            foreach (RemoteConfigType remoteConfigType in Enum.GetValues(typeof(RemoteConfigType)))
            {
                if (remoteConfigType != RemoteConfigType.SdkPlaceHolder)
                {
                    if (GameManager.Instance.remoteConfigData.remoteConfigMap.ContainsKey(remoteConfigType))
                    {
                        GameManager.Instance.remoteConfigData.remoteConfigMap[remoteConfigType] = GetValue(remoteConfigType);
                    }
                    else
                    {
                        GameManager.Instance.remoteConfigData.remoteConfigMap.TryAdd(remoteConfigType, GetValue(remoteConfigType));
                    }
                }
            }
            
            RemoteConfigDatabase.SaveData();
            
            Evm.OnFetchDataRemoteConfigFromFirebaseComplete.Dispatch();
        }
        
        
        
        private void InitGame()
        {
            if (isFetchDataRealtime)
            {
                FirebaseRemoteConfig.DefaultInstance.OnConfigUpdateListener += ActivateValuesOnConfigUpdate;
            }
            
            SetValues();
        }

        private void OnDestroy()
        {
            if (isFetchDataRealtime)
            {
                FirebaseRemoteConfig.DefaultInstance.OnConfigUpdateListener -= ActivateValuesOnConfigUpdate;
            }
        }
        
        
        
        
        public object GetValue(RemoteConfigType type)
        {
            var remoteConfig = FirebaseRemoteConfig.DefaultInstance;

            switch (remoteConfigGroup.GetDataType(type))
            {
                case RemoteConfigDataType.String:
                    return remoteConfig.GetValue(remoteConfigGroup.GetKey(type)).StringValue;
                case RemoteConfigDataType.Long:
                    return remoteConfig.GetValue(remoteConfigGroup.GetKey(type)).LongValue;
                case RemoteConfigDataType.Double:
                    return remoteConfig.GetValue(remoteConfigGroup.GetKey(type)).DoubleValue;
                case RemoteConfigDataType.Boolean:
                    return remoteConfig.GetValue(remoteConfigGroup.GetKey(type)).BooleanValue;
                default:
                    return null;
            }
        }

        public RemoteConfigDataType GetDataType(RemoteConfigType type)
        {
            return remoteConfigGroup.GetDataType(type);
        }
#endif
        
        
        public object GetDefaultValue(RemoteConfigType type)
        {

      
            var remoteConfig = remoteConfigGroup.GetConfig(type);

            switch (remoteConfigGroup.GetDataType(type))
            {
#if ENABLE_FIREBASE
                case RemoteConfigDataType.String:
                    return (string) remoteConfig.GetDefaultValue();
                case RemoteConfigDataType.Long:
                    return (long)remoteConfig.GetDefaultValue();
                case RemoteConfigDataType.Double:
                    return (double)remoteConfig.GetDefaultValue();
                case RemoteConfigDataType.Boolean:
                    return (bool)remoteConfig.GetDefaultValue();
#endif
                default:
                    return null;
            }
        }
        
        
        
        
        
                
#if ENABLE_FIREBASE

        #region Remote Config

        public void InitRemoteConfig()
        {
            SetRemoteConfigDefaults();
        }

         // Sets Remote Config default values and fetchs new ones
        // before starting the game.
        private void SetRemoteConfigDefaults()
        {
            var defaults = new Dictionary<string, object>();

            //TODO: add key and value of remote config data
            
            /*defaults.TryAdd(MapObjects.AccelerationTile.AccelerationTileForceKey,
                MapObjects.AccelerationTile.AccelerationTileForceDefault);

            defaults.TryAdd(States.MainMenu.SubtitleOverrideKey, States.MainMenu.SubtitleOverrideDefault);*/
            
            foreach (RemoteConfigType remoteConfigType in Enum.GetValues(typeof(RemoteConfigType)))
            {
                if (remoteConfigType != RemoteConfigType.SdkPlaceHolder)
                {
                    defaults.TryAdd(remoteConfigGroup.GetKey(remoteConfigType),
                        GameManager.Instance.remoteConfigData.remoteConfigMap[remoteConfigType]);
                }
            }


            var remoteConfig = FirebaseRemoteConfig.DefaultInstance;

            remoteConfig.SetDefaultsAsync(defaults).ContinueWithOnMainThread(previousTask =>
            {
                FetchRemoteConfig(InitGame);
            });
        }

        // (Re)fetches Remote Config values and pass down the onFetchAndActivateSuccessful callback.
        // Called during the initialization flow but can also be called indepedently.
        public void FetchRemoteConfig(Action onFetchAndActivateSuccessful)
        {
            var app = FirebaseApp.DefaultInstance;
            
            if (app == null)
            {
                Debug.LogError("Do not use Firebase until it is properly initialized by calling InitializeFirebaseAndStartGame");
                return;
            }
            
            Debug.Log("Fetching data...");
            
            var remoteConfig = FirebaseRemoteConfig.DefaultInstance;

            remoteConfig.FetchAsync(TimeSpan.Zero).ContinueWithOnMainThread(previousTask =>
            {
                if (!previousTask.IsCompleted)
                {
                    Debug.LogError($"{nameof(remoteConfig.FetchAsync)} incomplete: Status '{previousTask.Status}'");
                    return;
                }

                ActivateRetrievedRemoteConfigValues(onFetchAndActivateSuccessful);
            });
        }

        // The final method in the initialization flow that will activate fetched values
        // and on Success will call onFetchAndActivateSuccessful.
        private void ActivateRetrievedRemoteConfigValues(Action onFetchAndActivateSuccessful)
        {
            var remoteConfig = FirebaseRemoteConfig.DefaultInstance;
            var info = remoteConfig.Info;

            if (info.LastFetchStatus == LastFetchStatus.Success)
            {
                remoteConfig.ActivateAsync().ContinueWithOnMainThread(previousTask =>
                {
                    Debug.Log($"Remote data loaded and ready (last fetch time {info.FetchTime}).");
                    onFetchAndActivateSuccessful?.Invoke();
                });
            }
        }


        void ActivateValuesOnConfigUpdate(object sender, ConfigUpdateEventArgs args)
        {
            if (args.Error != RemoteConfigError.None)
            {
                Debug.Log($"Error occurred while listening: {args.Error}");
                return;
            }
            
            Debug.Log("Update keys: " + string.Join(", ", args.UpdatedKeys));

            var remoteConfig = FirebaseRemoteConfig.DefaultInstance;

            remoteConfig.ActivateAsync().ContinueWithOnMainThread(task =>
            {
                Debug.Log($"Keys from {nameof(ActivateValuesOnConfigUpdate)} activated.");
                SetValues();
            });
        }
        
        #endregion
#endif
        
        
        

    }
}