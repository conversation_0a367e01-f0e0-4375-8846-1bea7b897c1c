using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using b100SDK.Scripts.DesignPatterns;
using UnityEngine;
#if ENABLE_FIREBASE
using Firebase.Analytics;
#endif

namespace b100SDK.Scripts.Analytic
{
    public class AnalyticManager : Singleton<AnalyticManager>
    {
        [SerializeField]
        private string prefix = "";

        private static string _prefix;

        private void Start()
        {
            _prefix = prefix;
        }

        private static string GetCorrectName(string name)
        {
            if (string.IsNullOrEmpty(_prefix))
            {
                return name;
            }
            
            return string.Format("{0}_{1}", _prefix, name);
        }
        
        public static void LogEvent(string eventName, BhLogInfo logInfo)
        {
            Instance.Enqueue(() =>
            {
#if ENABLE_FIREBASE
                if (FirebaseController.Instance.IsInited)
                {
                    BhDebug.Log("Firebase LogEvent: " + eventName, BhColor.Orange);
                    
                    FirebaseAnalytics.LogEvent(GetCorrectName(eventName), logInfo.Parameters);
                }
#endif
            });
        }
        
        
        
        public static void LogEvent(string eventName, string paramName, string paramValue)
        {
            Instance.Enqueue(() =>
            {
#if ENABLE_FIREBASE
                if (FirebaseController.Instance.IsInited)
                {
                    BhDebug.Log("Firebase: " + eventName, BhColor.Orange);
                    FirebaseAnalytics.LogEvent(GetCorrectName(eventName), paramName, paramValue);
                }
#endif
            });
        }

        public static void LogEvent(string eventName)
        {
            Instance.Enqueue(() =>
            {
#if ENABLE_FIREBASE
                if (FirebaseController.Instance.IsInited)
                {
                    BhDebug.Log("Firebase: " + eventName, BhColor.Orange);
                    FirebaseAnalytics.LogEvent(GetCorrectName(eventName));
                }
#endif            
            });

        }



        #region Main Thread

        private static readonly Queue<Action> _executionQueue = new Queue<Action>();

        public void Update() {
            lock(_executionQueue) {
                while (_executionQueue.Count > 0) {
                    _executionQueue.Dequeue().Invoke();
                }
            }
        }

        /// <summary>
        /// Locks the queue and adds the IEnumerator to the queue
        /// </summary>
        /// <param name="action">IEnumerator function that will be executed from the main thread.</param>
        public void Enqueue(IEnumerator action) {
            lock (_executionQueue) {
                _executionQueue.Enqueue (() => {
                    StartCoroutine (action);
                });
            }
        }

        /// <summary>
        /// Locks the queue and adds the Action to the queue
        /// </summary>
        /// <param name="action">function that will be executed from the main thread.</param>
        public void Enqueue(Action action)
        {
            Enqueue(ActionWrapper(action));
        }

        /// <summary>
        /// Locks the queue and adds the Action to the queue, returning a Task which is completed when the action completes
        /// </summary>
        /// <param name="action">function that will be executed from the main thread.</param>
        /// <returns>A Task that can be awaited until the action completes</returns>
        public Task EnqueueAsync(Action action)
        {
            var tcs = new TaskCompletionSource<bool>();

            void WrappedAction() {
                try 
                {
                    action();
                    tcs.TrySetResult(true);
                } catch (Exception ex) 
                {
                    tcs.TrySetException(ex);
                }
            }

            Enqueue(ActionWrapper(WrappedAction));
            return tcs.Task;
        }


        IEnumerator ActionWrapper(Action a)
        {
            a();
            yield return null;
        }


        #endregion
    }
}

