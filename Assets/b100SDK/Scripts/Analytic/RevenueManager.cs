using b100SDK.Scripts.Ads;
using b100SDK.Scripts.DesignPatterns;
#if ENABLE_FIREBASE
using Firebase.Analytics;
#endif

namespace b100SDK.Scripts.Analytic
{
    public class RevenueManager : Singleton<RevenueManager>
    {
        public static void LogRevenue(AdsRevenue adsRevenue)
        {
#if ENABLE_FIREBASE
            var impressionParameters = new[] {
                new Parameter("ad_platform", adsRevenue.networkPlacement),
                new Parameter("ad_source", adsRevenue.networkName),
                new Parameter("ad_unit_name", adsRevenue.adUnitId),
                new Parameter("ad_format", adsRevenue.adFormat),
                new Parameter("value", adsRevenue.revenue),
                new Parameter("currency", adsRevenue.currencyCode),
            };
            
            FirebaseAnalytics.LogEvent("ad_impression", impressionParameters);
#endif
        }

    }
    
    public class AdsRevenue
    {
        public MediationType mediationType;
        
        public string adFormat;
        public string adUnitId;
        public string networkName;
        public string networkPlacement;
        public string placement;
        public double revenue;
        public string currencyCode;
    }

}