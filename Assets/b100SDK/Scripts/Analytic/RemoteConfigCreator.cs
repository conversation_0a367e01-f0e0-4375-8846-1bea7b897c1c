#if UNITY_EDITOR

using System;
using System.Collections.Generic;
using b100SDK.Scripts.Configs;
using b100SDK.Scripts.Configs.RemoteConfig;
using b100SDK.Scripts.Utilities.Tool;
using Sirenix.OdinInspector;
using Sirenix.OdinInspector.Editor;
using UnityEditor;
using UnityEngine;

namespace b100SDK.Scripts.Analytic
{
    public class RemoteConfigCreator : OdinEditorWindow
    {
        private const string _IAP_ENUM_PATH = "Assets/b100SDK/Scripts/Analytic/RemoteConfigEnums.cs";
        private const string _SAVE_PATH = "Assets/b100SDK/Configs/RemoteConfig";
        private RemoteConfigGroup _remoteConfigGroup;

        private static bool IsStartedCreateScript
        {
            get => PlayerPrefs.GetInt("BHRemoteConfigCreatorIsStarted") == 1;

            set => PlayerPrefs.SetInt("BHRemoteConfigCreatorIsStarted", value ? 1 : 0);
        }

        private static bool IsWaitingForCreateScript
        {
            get => PlayerPrefs.GetInt("BHRemoteConfigCreatorIsWaitingForCreateScript") == 1;

            set => PlayerPrefs.SetInt("BHRemoteConfigCreatorIsWaitingForCreateScript", value ? 1 : 0);
        }

        private static bool IsDone
        {
            get => PlayerPrefs.GetInt("BHRemoteConfigCreatorIsDone") == 1;

            set => PlayerPrefs.SetInt("BHRemoteConfigCreatorIsDone", value ? 1 : 0);
        }
        
        
        public string remoteConfigTypeKey = string.Empty;
        public string remoteConfigKey;
        public RemoteConfigDataType dataType;
        
        [ShowIf(nameof(dataType), RemoteConfigDataType.String)]
        public string stringDefaultValue;
        
        [ShowIf(nameof(dataType), RemoteConfigDataType.Boolean)]
        public bool boolDefaultValue;
        
        [ShowIf(nameof(dataType), RemoteConfigDataType.Double)]
        public double doubleDefaultValue;
        
        [ShowIf(nameof(dataType), RemoteConfigDataType.Long)]
        public long longDefaultValue;
        
        
        public static void OpenWindow()
        {
            GetWindow<RemoteConfigCreator>("b100 RemoteConfig Creator").Show();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            
            IsDone = false;
            IsWaitingForCreateScript = false;
            IsStartedCreateScript = false;
        }

        private void Update()
        {
            if (!IsWaitingForCreateScript) 
                return;
            
            if (!IsStartedCreateScript)
                return;
            
            if (IsDone)
                return;

            if (!Enum.TryParse<RemoteConfigType>(remoteConfigTypeKey, out var data))
            {
                Log("Wait for create new package........");
                return;
            }
            
            OnCreateConfig();

            LogSuccess("Complete create remote config " + remoteConfigKey + " --- type: " + dataType +
                       " --- default value: " + (dataType == RemoteConfigDataType.String ? stringDefaultValue :
                           dataType == RemoteConfigDataType.Double ? doubleDefaultValue :
                           dataType == RemoteConfigDataType.Long ? longDefaultValue : boolDefaultValue));
            
            IsDone = true;
            IsStartedCreateScript = false;
            IsWaitingForCreateScript = false;
        }


        [PropertySpace(20)]
        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        void Create()
        {
            logEntries.Clear();
            
            var newType = Enum.TryParse<RemoteConfigType>(remoteConfigTypeKey, out var parseType);
            if (newType)
            {
                LogError("This remote config key is existed. Please choose other name.");
            }
            else
            {
                OnCreateScript();
                IsWaitingForCreateScript = true;
                IsStartedCreateScript = true;
                IsDone = false;
            }
        }
        
        private void OnCreateScript()
        {
            Log("Starting create remote config key: " + remoteConfigTypeKey + " --- key: " + remoteConfigKey);
            
            AddNewEnum();
            AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);
        }

        private void OnCreateConfig()
        {
            CreateConfig();
            AddConfigToList();
        }

        private void CreateConfig()
        {
            RemoteConfigItem newConfig = ScriptableObject.CreateInstance<RemoteConfigItem>();

            newConfig.type = Enum.Parse<RemoteConfigType>(this.remoteConfigTypeKey);
            newConfig.key = remoteConfigKey;
            newConfig.dataType = dataType;

            if (remoteConfigKey.Equals(""))
            {
                newConfig.key = remoteConfigTypeKey;
            }
            else
            {
                newConfig.key = remoteConfigKey;
            }

            switch (dataType)
            {
                case RemoteConfigDataType.String:
                    newConfig.stringDefaultValue = stringDefaultValue;
                    break;
                case RemoteConfigDataType.Boolean:
                    newConfig.boolDefaultValue = boolDefaultValue;
                    break;
                case RemoteConfigDataType.Double:
                    newConfig.doubleDefaultValue = doubleDefaultValue;
                    break;
                case RemoteConfigDataType.Long:
                    newConfig.longDefaultValue = longDefaultValue;
                    break;
            }
            
            var path = _SAVE_PATH + "/Remote Config Item " + newConfig.type + ".asset";
            
            AssetDatabase.CreateAsset(newConfig, path);
            AssetDatabase.SaveAssets();
        }

        private void AddNewEnum()
        {
            var enumScript = System.IO.File.ReadAllText(_IAP_ENUM_PATH);
            if (enumScript.Contains(remoteConfigTypeKey))
                return;

            var index = enumScript.IndexOf("SdkPlaceHolder", StringComparison.Ordinal);

            var newEnumScript = enumScript.Insert(index, remoteConfigTypeKey + ",\n    ");

            System.IO.File.WriteAllText(_IAP_ENUM_PATH, newEnumScript);
        }
        
        
        private void AddConfigToList()
        {
            _remoteConfigGroup = ConfigTool.GetRemoteConfigGroup();
            _remoteConfigGroup.GetConfig();
            EditorUtility.SetDirty(_remoteConfigGroup);
            AssetDatabase.SaveAssets();
        }




        #region Log
        
        private List<LogEntry> logEntries = new List<LogEntry>();
        private Vector2 scrollPosition;
        
        private void Log(string message = "Test Log")
        {
            AddLog(message, Color.white);
        }
        private void LogWarning(string message)
        {
            AddLog(message, Color.yellow);
        }
        
        private void LogError(string message)
        {
            AddLog(message, Color.red);
        }        
        
        private void LogSuccess(string message)
        {
            AddLog(message, Color.green);
        }

        protected override void OnGUI()
        {
            
            
            base.OnGUI(); 

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Width(position.width), GUILayout.Height(position.height-300));

            EditorGUILayout.BeginVertical(new GUILayoutOption[]
            {
                GUILayout.MinHeight(20),
            
            });
            
            foreach (var logEntry in logEntries)
            {
                GUI.contentColor = logEntry.color;
                GUILayout.Label(logEntry.message);
            }

            GUI.contentColor = Color.white;

            EditorGUILayout.EndScrollView();
            
            EditorGUILayout.EndVertical();

        }

        private void AddLog(string message, Color color)
        {
            logEntries.Insert(0, new LogEntry { message = message, color = color });
            // Set scroll position to the bottom
            scrollPosition.y = 0f;
        }

        private class LogEntry
        {
            public string message;
            public Color color;
        }

        #endregion
    }
}

#endif