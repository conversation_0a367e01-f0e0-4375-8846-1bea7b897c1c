using b100SDK.Scripts.Base;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.UI;

namespace b100SDK.Scripts.Utilities
{
    public class ImageSizer : BhMonoBehavior
    {
        [SerializeField] private Image icon;
        [SerializeField] private RectTransform rectTransform;

        [SerializeField] private float defaultScale = 1f;


        public void SetIcon(Sprite sprite)
        {
            icon.sprite = sprite;

            AutoSize();
        }

        void AutoSize()
        {
            var size = icon.sprite.texture;
            rectTransform.sizeDelta = new Vector2(size.width, size.height);
        }


        private void Reset()
        {
            icon = gameObject.GetComponent<Image>();
            rectTransform = gameObject.GetComponent<RectTransform>();

            MultiplySize(defaultScale);
        }



        [Button(Expanded = true)]
        void MultiplySize(float value = 1)
        {
            var img = GetComponent<Image>();

            var imgSize = new Vector2(img.sprite.rect.width, img.sprite.rect.height);

            var rect = GetComponent<RectTransform>();

            rect.sizeDelta = imgSize * value;
        }
    }
}