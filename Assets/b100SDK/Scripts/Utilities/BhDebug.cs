using UnityEngine;

namespace b100SDK.Scripts.Utilities
{
    public static class BhDebug
    {
        private const string _PREFIX_NORMAL = "<b>[b100] </b>";
        private const string _PREFIX_WARNING = "<b><color=yellow>[b100] </color></b>";
        private const string _PREFIX_ERROR = "<b><color=red>[b100] </color></b>";
        private const string _PREFIX = "<b>[b100] </b>";

        public static void Log(object o)
        {
#if UNITY_EDITOR || PROTOTYPE
            Debug.Log(_PREFIX_NORMAL + o);
#endif
        }

        public static void LogWarning(object o)
        {
#if UNITY_EDITOR || PROTOTYPE
            Debug.LogWarning(_PREFIX_WARNING + o);
#endif
        }

        public static void LogError(object o)
        {
#if UNITY_EDITOR || PROTOTYPE
            Debug.LogError(_PREFIX_ERROR + o);
#endif
        }

        public static void Log(object o, BhColor color)
        {
#if UNITY_EDITOR || PROTOTYPE
            Debug.Log($"{_PREFIX}<color={color.ToString().ToLower()}>{o}</color>");
#endif
        }
    }
    
    
    public enum BhColor
    {
        White,
        Black,
        Red,
        Green,
        Blue,
        Orange,
        Violet,
        Aqua,
        Gray,
        Magenta,
        Purple,
        Yellow
    }
}