#region

using System;
using System.Text;

#endregion

namespace b100SDK.Scripts.Utilities.Extensions
{
    public static class StringExtensions
    {


        #region Enum Handler

        
        public static T ToEnum<T>(this string value)
        {
            try
            {
                return (T) Enum.Parse(typeof(T), value, true);
            }
            catch (Exception)
            {
                return (T) Enum.Parse(typeof(T), "None", true);
            }
        }
        
        

        public static bool IsEnum<T>(this string value)
        {
            return !Equals(value.ToEnum<T>(), "None");
        }
        
        #endregion

        

        #region Prefix Handler

        
        public static string GetPrefix(this string value)
        {
            if (string.IsNullOrEmpty(value))
                return "";

            var index = value.IndexOf('_');
            if (index < 0)
                return "";

            return value.Substring(0, index);
        }
        
        

        public static string GetContent(this string value)
        {
            if (string.IsNullOrEmpty(value))
                return "";

            var index = value.IndexOf('_');
            if (index < 0)
                return "";

            return value.Substring(index + 1, value.Length - index - 1);
        }

        #endregion



        #region Time
        

        public static string ToTimeFormat(this int seconds)
        {
            return ToTimeFormat((long)seconds);
        }

        public static string ToTimeFormatCompact(this int seconds)
        {
            return ToTimeFormatCompact((long)seconds);
        }

        public static string ToTimeFormat(this long seconds)
        {
            if (seconds <= 0)
                return "";

            TimeSpan t = TimeSpan.FromSeconds(seconds);

            string formatTime;

            if (seconds >= 3600)
            {
                formatTime = string.Format("{0:D1}h {1:D2}m",
                    t.Hours,
                    t.Minutes);
            }
            else if (seconds >= 60)
            {
                formatTime = string.Format("{0:D2}m {1:D2}s",
                    t.Minutes,
                    t.Seconds);
            }
            else
            {
                formatTime = string.Format("{0:D1}s",
                    t.Seconds);
            }

            return formatTime;
        }

        public static string ToTimeFormatCompact(this long seconds)
        {
            if (seconds < 0)
                return "";

            TimeSpan t = TimeSpan.FromSeconds(seconds);

            string formatTime;

            if (seconds >= 3600)
            {
                formatTime = string.Format("{0:D1}:{1:D2}:{2:D2}",
                    t.Hours + t.Days * 24,
                    t.Minutes,
                    t.Seconds);
            }
            else
            {
                formatTime = string.Format("{0:D2}:{1:D2}",
                    t.Minutes,
                    t.Seconds);
            }

            return formatTime;
        }

        #endregion
        
        
        
        
        

        public static string ToCountFormat(int value, int digits)
        {
            var s = value.ToString();
            var originLength = s.Length;
            if (originLength >= digits)
                return s;

            for (var i = 0; i < digits - originLength; i++)
                s = "0" + s;

            return s;
        }

        public static string ToFormatString(this long s)
        {
            return $"{s:n0}";
        }

        public static string ToFormatString(this int s)
        {
            return $"{s:n0}";
        }

        public static string ToFormatString(this double s)
        {
            return $"{s:n0}";
        }

        public static string ToQuantityString(this long s)
        {
            return $"x{s:n0}";
        }

        public static string ToQuantityString(this int s)
        {
            return $"x{s:n0}";
        }

        public static string ToQuantityString(this double s)
        {
            return $"x{s:n0}";
        }




        #region Other

        public static string StringReplaceAt(string value, int index, char newchar)
        {
            if (value.Length <= index)
                return value;
            var sb = new StringBuilder(value);
            sb[index] = newchar;
            return sb.ToString();
        }

        #endregion
    }
}