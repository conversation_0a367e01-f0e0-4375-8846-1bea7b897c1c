using System.Collections.Generic;
using UnityEngine;

namespace b100SDK.Scripts.Utilities.Extensions
{
    public static class VectorExtensions
    {
        /// <summary>
        /// Converts a Vector3 to a Vector2
        /// </summary>
        /// <param name="vector3"></param>
        /// <returns></returns>
        public static Vector2 Vector3To2(Vector3 vector3)
        {
            return new Vector2(vector3.x, vector3.y);
        }


        
        /// <summary>
        /// Converts a Vector2 to a Vector3
        /// </summary>
        /// <param name="vector2"></param>
        /// <returns></returns>
        public static Vector3 Vector2To3(Vector2 vector2)
        {
            return new Vector3(vector2.x, vector2.y, 0);
        }

        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="vector"></param>
        /// <param name="decimalPlaces"></param>
        /// <returns></returns>
        public static Vector3 Round(this Vector3 vector, int decimalPlaces = 2)
        {
            float multiplier = 1;
            for (var i = 0; i < decimalPlaces; i++)
                multiplier *= 10f;

            return new Vector3(
                Mathf.Round(vector.x * multiplier) / multiplier,
                Mathf.Round(vector.y * multiplier) / multiplier,
                Mathf.Round(vector.z * multiplier) / multiplier);
        }
        
        

        public static void FlipVector3(this List<Vector3> lsV3, int flipX = 1, int flipY = 1)
        {
            for (var i = 0; i < lsV3.Count; i++)
            {
                var temp = lsV3[i];
                temp.x *= flipX;
                temp.y *= flipY;
                lsV3[i] = temp;
            }
        }
        
        

        public static void FlipVector3(this Vector3[] lsV3, int flipX = 1, int flipY = 1)
        {
            for (var i = 0; i < lsV3.Length; i++)
            {
                var temp = lsV3[i];
                temp.x *= flipX;
                temp.y *= flipY;
                lsV3[i] = temp;
            }
        }

        
        
        public static Vector2 Round(this Vector2 vector, int decimalPlaces = 2)
        {
            float multiplier = 1;
            for (var i = 0; i < decimalPlaces; i++)
                multiplier *= 10f;

            return new Vector2(
                Mathf.Round(vector.x * multiplier) / multiplier,
                Mathf.Round(vector.y * multiplier) / multiplier);
        }
        
        

        public static Vector3 ScaleVector(Vector3 original, float x, float y, float z)
        {
            return new Vector3(original.x * x, original.y * y, original.z * z);
        }
    }
}