using System;
using System.Collections.Generic;
using System.Linq;

namespace b100SDK.Scripts.Utilities.Extensions
{
    public static class CollectionExtensions
    {
        public static bool CheckArrayHasString(string[] c, string b)
        {
            var d = false;
            foreach (var child in c)
            {
                if (child != b)
                    continue;
                d = true;
            }

            return d;
        }

        public static T GetRandom<T>(this ICollection<T> collection)
        {
            if (collection == null)
                return default;
            var t = UnityEngine.Random.Range(0, collection.Count);
            foreach (var element in collection)
            {
                if (t == 0)
                    return element;
                t--;
            }

            return default;
        }

        public static T GetLast<T>(this T[] collection)
        {
            if (collection == null)
                return default;
            return collection[collection.Length - 1];
        }

        public static T GetLast<T>(this List<T> collection)
        {
            if (collection == null)
                return default;
            return collection[collection.Count - 1];
        }

        public static bool CheckHaveItemNull<T>(this List<T> collection)
        {
            foreach (var element in collection)
                if (element == null)
                    return true;

            return false;
        }

        public static bool CheckHaveItemNull<T>(this T[] collection)
        {
            foreach (var element in collection)
                if (element == null)
                    return true;

            return false;
        }

        public static bool CheckHaveItemNull<T, TZ>(this Dictionary<T, TZ> collection)
        {
            foreach (var element in collection)
                if (element.Value == null)
                    return true;

            return false;
        }

        public static bool CheckIsNullOrEmpty<T>(this T[] collection)
        {
            if (collection == null || collection.Length == 0)
                return true;
            return false;
        }

        public static bool CheckIsNullOrEmpty<T>(this List<T> collection)
        {
            if (collection == null || collection.Count == 0)
                return true;
            return false;
        }

        public static bool CheckIsNullOrEmpty<T, TZ>(this Dictionary<T, TZ> collection)
        {
            if (collection == null || collection.Count == 0)
                return true;
            return false;
        }

        public static T GetRandom<T>(this List<T> collection)
        {
            if (collection == null)
                return default;
            var t = UnityEngine.Random.Range(0, collection.Count);
            return collection[t];
        }

        public static T GetRandomAndRemove<T>(this List<T> collection)
        {
            if (collection == null)
                return default;
            var t = UnityEngine.Random.Range(0, collection.Count);
            var result = collection[t];
            collection.RemoveAt(t);
            return result;
        }

        public static T PickRandom<T>(this IEnumerable<T> source)
        {
            return source.PickRandom(1).SingleOrDefault();
        }

        public static IEnumerable<T> PickRandom<T>(this IEnumerable<T> source, int count)
        {
            return source.Shuffle().Take(count);
        }

        public static IEnumerable<T> Shuffle<T>(this IEnumerable<T> source)
        {
            return source.OrderBy(x => Guid.NewGuid());
        }

        public static IList<T> Shuffle<T>(this IList<T> list, Random random)
        {
            if (random == null)
                random = new Random(DateTime.Now.Millisecond);
            var n = list.Count;
            while (n > 1)
            {
                n--;
                var k = random.Next(n + 1);
                var value = list[k];
                list[k] = list[n];
                list[n] = value;
            }

            return list;
        }

        public static IList<T> PickRandom<T>(this IList<T> source, int count, Random random = null)
        {
            return source.Shuffle(random).Take(count).ToList();
        }

        public static T PickRandom<T>(this IList<T> source, Random random)
        {
            return source.PickRandom(1, random).SingleOrDefault();
        }

        public static int GetMaxElementCount<T>(
            this IEnumerable<T> source,
            int numberElementDesireToGet
        )
        {
            if (source.Count() < numberElementDesireToGet)
                return source.Count();

            return numberElementDesireToGet;
        }

        public static bool Compare<T>(this List<T> list1, List<T> list2)
        {
            if (list1.Count != list2.Count)
                return false;

            for (var i = 0; i < list1.Count; i++)
                if (!list1[i].Equals(list2[i]))
                    return false;

            var firstNotSecond = list1.Except(list2).ToList();
            var secondNotFirst = list2.Except(list1).ToList();
            return !firstNotSecond.Any() && !secondNotFirst.Any();
        }

        public static bool Compare<T>(this T[] list1, T[] list2)
        {
            if (list1.Length != list2.Length)
                return false;

            for (var i = 0; i < list1.Length; i++)
                if (!list1[i].Equals(list2[i]))
                    return false;

            var firstNotSecond = list1.Except(list2).ToList();
            var secondNotFirst = list2.Except(list1).ToList();
            return !firstNotSecond.Any() && !secondNotFirst.Any();
        }
        
        
        public static List<T> Clone<T>(this List<T> collection)
        {
            var newList = new List<T>();
            foreach (var element in collection)
                newList.Add(element);

            return newList;
        }
        
        /// <summary>
        /// Extension method for List<T>.
        /// Tries to get the element at the specified index in the list.
        /// </summary>
        public static bool TryGetValue<T>(this List<T> list, int index, out T value)
        {
            if (list != null && index >= 0 && index < list.Count)
            {
                value = list[index];
                return true;
            }
            value = default(T);
            return false;
        }

        /// <summary>
        /// Extension method for ICollection<T>.
        /// Because IColllection <T> does not support access to direct index, we browse through the collection.
        /// </summary>
        public static bool TryGetValue<T>(this ICollection<T> collection, int index, out T value)
        {
            if (collection != null && index >= 0 && index < collection.Count)
            {
                int currentIndex = 0;
                foreach (var item in collection)
                {
                    if (currentIndex == index)
                    {
                        value = item;
                        return true;
                    }
                    currentIndex++;
                }
            }
            value = default(T);
            return false;
        }
        
        
        public static T[,,] Clone<T>(this T[,,] original) where T : struct
        {
            int dim1 = original.GetLength(0);
            int dim2 = original.GetLength(1);
            int dim3 = original.GetLength(2);

            T[,,] clone = new T[dim1, dim2, dim3];

            for (int i = 0; i < dim1; i++)
            {
                for (int j = 0; j < dim2; j++)
                {
                    for (int k = 0; k < dim3; k++)
                    {
                        clone[i, j, k] = original[i, j, k];
                    }
                }
            }

            return clone;
        }
    }
}