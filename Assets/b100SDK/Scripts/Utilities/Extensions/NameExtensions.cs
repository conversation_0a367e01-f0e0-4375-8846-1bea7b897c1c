using System;
using System.Collections.Generic;
using b100SDK.Scripts.Base;
using UnityEngine;

namespace b100SDK.Scripts.Utilities.Extensions
{
    public static class NameExtensions
    {
        [Serializable]
        private class NamesList
        {
            public List<string> names;
        }
        
        static NamesList _namesList;
        static NamesList CurrentNamesList
        {
            get
            {
                if (_namesList == null)
                {
                    TextAsset textAsset = ConfigManager.Instance.gameCfg.extraFeatureConfig.nameList;
                    if (textAsset != null) 
                        _namesList = JsonUtility.FromJson<NamesList>(textAsset.text);
                }
                return _namesList;
            }
        }

        public static string GetRandomName()
        {
            return CurrentNamesList.names[UnityEngine.Random.Range(0, CurrentNamesList.names.Count)];
        }

        public static string GetNameByRank(int rank)
        {
            return CurrentNamesList.names[rank % CurrentNamesList.names.Count];
        }
    
        public static List<string> GetRandomNames(int nbNames)
        {
            if (nbNames > CurrentNamesList.names.Count)
                throw new Exception("Asking for more random names than there actually are!");
        
            NamesList copy = new NamesList();
            copy.names = new List<string>(CurrentNamesList.names);

            List<string> result = new List<string>();

            for (int i = 0; i < nbNames; i++)
            {
                int rnd = UnityEngine.Random.Range(0, copy.names.Count);
                result.Add(copy.names[rnd]);
                copy.names.RemoveAt(rnd);
            }

            return result;
        }

    }
}