namespace b100SDK.Scripts.Utilities.Extensions
{
    public static class EnumExtensions
    {
        public static int RaiseFlag(int value, int flagIdx)
        {
            return value | (1 << flagIdx);
        }

        public static bool CheckFlag(int value, int flagIdx)
        {
            return (value & (1 << flagIdx)) != 0;
        }

        public static int DownFlag(int value, int flagIdx)
        {
            return value & ~(1 << flagIdx);
        }

        public static long RaiseFlagLong(long value, int flagIdx)
        {
            var flagVal = (long) 1 << flagIdx;
            return value | flagVal;
        }

        public static long DownFlagLong(long value, int flagIdx)
        {
            var flagVal = (long) 1 << flagIdx;

            return value & ~flagVal;
        }

        public static bool CheckFlagLong(long value, int flagIdx)
        {
            var flagVal = (long) 1 << flagIdx;
            var andVal = value & flagVal;
            return andVal != 0;
        }
    }
}