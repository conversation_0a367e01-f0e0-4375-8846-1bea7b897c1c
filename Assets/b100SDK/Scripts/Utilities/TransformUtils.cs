using UnityEngine;

namespace b100SDK.Scripts.Utilities
{
    public static class TransformUtils
    {
        public static Vector3 GetPositionInScreenSpace(this RectTransform rectTransform, Canvas canvas)
        {
            Vector3 screenPoint = Vector3.zero;

            // Ensure the Canvas is set to Screen Space
            if (canvas.renderMode == RenderMode.ScreenSpaceOverlay)
            {
                // Directly convert anchor position to screen space
                screenPoint = rectTransform.position;
            }
            else if (canvas.renderMode == RenderMode.ScreenSpaceCamera)
            {
                // Convert world position to screen position
                screenPoint = RectTransformUtility.WorldToScreenPoint(canvas.worldCamera, rectTransform.position);
            }
            else
            {
                BhDebug.LogError("<PERSON><PERSON> is not in a screen space render mode.");
            }

            return screenPoint;
        }
        
        public static Vector3 ConvertScreenPositionToViewportPosition(Vector3 screenPosition, Canvas canvas, Camera camera)
        {
            Vector3 viewportPosition = Vector3.zero;

            if (canvas.renderMode == RenderMode.ScreenSpaceOverlay)
            {
                // Directly convert anchor position to screen space
                Vector3 screenPoint = screenPosition;
                viewportPosition = camera.ScreenToViewportPoint(screenPoint);
            }
            else if (canvas.renderMode == RenderMode.ScreenSpaceCamera)
            {
                // Convert world position to screen position
                Vector3 screenPoint = RectTransformUtility.WorldToScreenPoint(canvas.worldCamera, screenPosition);
                viewportPosition = camera.ScreenToViewportPoint(screenPoint);
            }
            else
            {
                BhDebug.LogError("Canvas is not in a screen space render mode.");
            }

            return viewportPosition;
        }
        
        
        public static Vector3 ConvertViewPortToWorldPoint(Vector3 viewportPosition, Camera camera, float offsetZ = 10f)
        {
            var position = viewportPosition;
            position.z = camera.transform.position.z + offsetZ;
            Vector3 worldPosition = camera.ViewportToWorldPoint(position);
            return worldPosition;
        }
    }
}