#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;
using Object = UnityEngine.Object;

namespace b100SDK.Scripts.Utilities.Tool
{
    public class UtilitiesTool : Editor
    {
        public static void ShowInspector()
        {
            EditorApplication.ExecuteMenuItem("Window/General/Inspector");
        }
        
        
        public static void ShowNotification(BhNotifyType notiType, string content, string confirmLabel = "OK")
        {
            var title = string.Empty;
            switch (notiType)
            {
                case BhNotifyType.Normal:
                    title = "b100 SDK Notification";
                    break;

                case BhNotifyType.Warning:
                    title = "b100 SDK Warning";
                    break;
            }

            EditorUtility.DisplayDialog(title, content, confirmLabel);
        }

        public static void ForceResolver()
        {
            EditorApplication.ExecuteMenuItem("Assets/External Dependency Manager/Android Resolver/Force Resolver");
        }
        
        
        public static void AddNewEnum(string filePath, string newItemName, string placeHolder = "SdkPlaceHolder")
        {
            var enumScript = File.ReadAllText(filePath);
            if (enumScript.Contains(newItemName))
                return;

            var index = enumScript.IndexOf(placeHolder, StringComparison.Ordinal);

            var newEnumScript = enumScript.Insert(index, newItemName + ",\n\t\t");

            File.WriteAllText(filePath, newEnumScript);
        }
        
        
        
        
        
        #region Folder

        public static void GetFolder(string folderPath, bool inAsset = false)
        {
            if (string.IsNullOrEmpty(folderPath))
            {
                Debug.LogError("Folder path is null or empty.");
                return;
            }
            
            if (inAsset)
            {
                CreateFolderInAssets(folderPath);
            }
            else
            {
                CreateFolderRecursively(folderPath);
            }
        }

        public static void CreateFolderInAssets(string folderPath)
        {
            string[] folders = folderPath.Split('/');
            string currentPath = "";

            for (int i = 0; i < folders.Length; i++)
            {
                string folderName = folders[i];
                if (i == 0)
                {
                    currentPath = folderName;
                }
                else
                {
                    if (!AssetDatabase.IsValidFolder(currentPath + "/" + folderName))
                    {
                        AssetDatabase.CreateFolder(currentPath, folderName);
                    }
                    currentPath += "/" + folderName;
                }
            }
        }
        
        public static void CreateFolderRecursively(string folderPath)
        {
            // Normalize the path to ensure it uses the correct directory separator for the current platform
            string normalizedPath = Path.GetFullPath(folderPath);

            // Create the directory if it does not exist
            if (!Directory.Exists(normalizedPath))
            {
                Directory.CreateDirectory(normalizedPath);
                Debug.Log($"Directory created at: {normalizedPath}");
            }
            else
            {
                Debug.Log($"Directory already exists at: {normalizedPath}");
            }
        }

        #endregion




        #region Path


        public static string GetPath<T>(T asset) where T : Object
        {
            return AssetDatabase.GetAssetPath(asset);
        }

        public static string GetResourcePath<T>(T asset) where T : Object
        {
            string assetPath = AssetDatabase.GetAssetPath(asset);
            if (string.IsNullOrEmpty(assetPath))
                return null;
            
            string resourceFolderName = "/Resources/";
            int index = assetPath.IndexOf(resourceFolderName, StringComparison.Ordinal);
            if (index < 0)
                return null;

            string relativePath = assetPath.Substring(index + resourceFolderName.Length);
            
            int dotIndex = relativePath.LastIndexOf('.');
            if (dotIndex >= 0)
                relativePath = relativePath.Substring(0, dotIndex);

            return relativePath;
        }
        

        #endregion
        
                
        

        #region Others

        
        public static List<T> GetPrefabs<T>(string path) where T : MonoBehaviour
        {
            var fileEntries = Directory.GetFiles(path, ".", SearchOption.AllDirectories);
            var result = new List<T>();
            foreach (var fileEntry in fileEntries)
            {
                if (fileEntry.EndsWith(".prefab"))
                {
                    var item = AssetDatabase.LoadAssetAtPath<T>(fileEntry.Replace("\\", "/"));

                    if (item)
                        result.Add(item);
                }
            }

            if (result.Count > 0)
                return result;

            return null;
        }
        
        public static List<Material> GetMaterials(string path)
        {
            var fileEntries = Directory.GetFiles(path, ".", SearchOption.AllDirectories);
            var result = new List<Material>();
            foreach (var fileEntry in fileEntries)
            {
                if (fileEntry.EndsWith(".mat"))
                {
                    var item = AssetDatabase.LoadAssetAtPath<Material>(fileEntry.Replace("\\", "/"));

                    if (item)
                        result.Add(item);
                }
            }

            if (result.Count > 0)
                return result;

            return null;
        }

        public static List<Sprite> GetSprite(string path)
        {
            var fileEntries = Directory.GetFiles(path, ".", SearchOption.AllDirectories);
            var result = new List<Sprite>();
            foreach (var fileEntry in fileEntries)
            {
                if (fileEntry.EndsWith(".png"))
                {
                    var item = AssetDatabase.LoadAssetAtPath<Sprite>(fileEntry.Replace("\\", "/"));

                    if (item)
                        result.Add(item);
                }
            }

            if (result.Count > 0)
                return result;

            return null;
        }


        public static List<T> GetResources<T>(string path, List<string> extension) where T : Object
        {
            if (!Directory.Exists(path))
                return null;
            
            var result = new List<T>();
            
            var fileEntries = Directory.GetFiles(path, ".", SearchOption.AllDirectories);
            foreach (var fileEntry in fileEntries)
            {
                if (extension.Any(x=> fileEntry.EndsWith(x)))
                {
                    var item = AssetDatabase.LoadAssetAtPath<T>(fileEntry.Replace("\\", "/"));

                    if (item)
                        result.Add(item);
                }
            }

            if (result.Count > 0)
                return result;

            return null;
        }

        

        #endregion




        
        
        
        #region Utils

        public static void AddScriptSymbol(string scriptSymbol)
        {
            string symbols = PlayerSettings.GetScriptingDefineSymbolsForGroup(EditorUserBuildSettings.selectedBuildTargetGroup);
            
            var allSymbol = symbols.Split(";").ToList();
            
            if (!allSymbol.Contains(scriptSymbol))
            {
                symbols += ";" + scriptSymbol;
                PlayerSettings.SetScriptingDefineSymbolsForGroup(EditorUserBuildSettings.selectedBuildTargetGroup, symbols);
                Debug.Log("Added script symbol: " + scriptSymbol);
            }
            else
            {
                Debug.LogWarning("Script symbol already exists: " + scriptSymbol);
            }
        }
        
        public static void RemoveScriptSymbol(string scriptSymbol)
        {
            string symbols = PlayerSettings.GetScriptingDefineSymbolsForGroup(EditorUserBuildSettings.selectedBuildTargetGroup);

            var allSymbol = symbols.Split(";").ToList();
            
            if (allSymbol.Contains(scriptSymbol))
            {
                symbols = symbols.Replace(scriptSymbol, "");
                symbols = symbols.Replace(";;", ";");
                PlayerSettings.SetScriptingDefineSymbolsForGroup(EditorUserBuildSettings.selectedBuildTargetGroup, symbols);
                Debug.Log("Removed script symbol: " + scriptSymbol);
            }
            else
            {
                Debug.LogWarning("Script symbol doesn't exist: " + scriptSymbol);
            }
        }

        public static bool HasScriptingSymbol(string scriptSymbol)
        {
            string symbols = PlayerSettings.GetScriptingDefineSymbolsForGroup(EditorUserBuildSettings.selectedBuildTargetGroup);
            return scriptSymbol.Contains(symbols);
        }

        #endregion



    }
}

#endif
