#if UNITY_EDITOR

using System.IO;
using b100SDK.Scripts.Configs.SDKEditor;
using UnityEditor;
using UnityEngine;

namespace b100SDK.Scripts.Utilities.Tool
{
    public class BuilderTool : Editor
    {
        private static GameBuilder _gameBuilder;
        static GameBuilder GameBuilder
        {
            get
            {
                if (_gameBuilder == null)
                {
                    _gameBuilder = GetGameBuilder();
                }
                
                return _gameBuilder;
            }
        }
        
        static GameSetting GameSetting
        {
            get
            {
                return SettingTool.GetGameSetting();
            }
        }
        
        public static GameBuilder GetGameBuilder()
        {
            /*var path = "Assets/b100SDK/Configs";
            var fileEntries = Directory.GetFiles(path);
            for (var i = 0; i < fileEntries.Length; i++)
                if (fileEntries[i].EndsWith(".asset"))
                {
                    var item =
                        AssetDatabase.LoadAssetAtPath<GameBuilder>(fileEntries[i].Replace("\\", "/"));
                    if (item != null)
                        return item;
                }

            return null;*/

            return ConfigTool.GetGameBuilderConfig();
        }
        
        
        public static void BuildApk(BuildOptions option = BuildOptions.ShowBuiltPlayer)
        {
            EditorUtility.SetDirty(GameSetting);
            AssetDatabase.SaveAssets();
            
            SettingTool.ValidatePlayerSetting();

            PlayerSettings.Android.useCustomKeystore = false;

            EditorUserBuildSettings.development = false;
            EditorUserBuildSettings.allowDebugging = false;
            
            EditorUserBuildSettings.androidCreateSymbols = AndroidCreateSymbols.Disabled;
            
            EditorUserBuildSettings.buildAppBundle = false;

            EditorUserBuildSettings.SwitchActiveBuildTarget(BuildTargetGroup.Android, BuildTarget.Android);

            var buildPath = string.Empty;
            if (Application.platform == RuntimePlatform.WindowsEditor)
                buildPath = GameBuilder.windowBuildPath;
            else if (Application.platform == RuntimePlatform.OSXEditor)
                buildPath = GameBuilder.macOsBuildPath;

            var playerPath = $"{buildPath}/{GameSetting.gameName} {PlayerSettings.bundleVersion}.apk";
            
            GameSetting.buildVersion += 1;
            EditorUtility.SetDirty(GameSetting);
            AssetDatabase.SaveAssets();

            BuildPipeline.BuildPlayer(GetScenePaths(), playerPath, BuildTarget.Android, option);
        }

        public static void BuildAab()
        {
            PlayerSettings.Android.useCustomKeystore = true;

#if UNITY_ANDROID
            var keystoreName = !string.IsNullOrEmpty(GameSetting.keystorePath)
                ? GameSetting.keystorePath
                : string.Format("Keystore/{0}.keystore", GameSetting.gameName);
            
            PlayerSettings.Android.keystoreName = keystoreName;
            PlayerSettings.Android.keyaliasName = GameSetting.alias;
            PlayerSettings.Android.keystorePass = GameSetting.keystorePassword;
            PlayerSettings.Android.keyaliasPass = GameSetting.aliasPassword;
#endif

            if (string.IsNullOrEmpty(PlayerSettings.Android.keystoreName) || string.IsNullOrEmpty(PlayerSettings.Android.keyaliasPass))
            {
                UtilitiesTool.ShowNotification(BhNotifyType.Warning,
                    "Publishing Setting - Verify your keystore setting before performing a submit build!", "Oke");
                SettingsService.OpenProjectSettings("Project/Player");
                return;
            }

            var hasGoogleServiceFile = false;
            var path = "Assets/StreamingAssets/";
            var fileEntries = Directory.GetFiles(path);
            for (var i = 0; i < fileEntries.Length; i++)
                if (fileEntries[i].Contains("google-services"))
                {
                    hasGoogleServiceFile = true;
                    break;
                }

            if (!hasGoogleServiceFile)
            {
                UtilitiesTool.ShowNotification(BhNotifyType.Warning,
                    "google-services.json file not found!",
                    "Oke");
                BhDebug.LogError("google-services.json file not found at Assets/StreamingAssets/");
                return;
            }

            GameSetting.buildVersion = 1;
            EditorUtility.SetDirty(GameSetting);
            AssetDatabase.SaveAssets();
            
            SettingTool.ValidatePlayerSetting();
            
            PlayerSettings.bundleVersion = string.Format("{0}.{1}.{2}", GameSetting.gameVersion,
                GameSetting.bundleVersion, GameSetting.buildVersion);

            EditorUserBuildSettings.development = false;
            EditorUserBuildSettings.allowDebugging = false;
            
            EditorUserBuildSettings.androidCreateSymbols = AndroidCreateSymbols.Disabled;

            EditorUserBuildSettings.buildAppBundle = true;

            EditorUserBuildSettings.SwitchActiveBuildTarget(BuildTargetGroup.Android, BuildTarget.Android);

            var buildPath = string.Empty;
            if (Application.platform == RuntimePlatform.WindowsEditor)
                buildPath = GameBuilder.windowBuildPath;
            else if (Application.platform == RuntimePlatform.OSXEditor)
                buildPath = GameBuilder.macOsBuildPath;

            var playerPath = $"{buildPath}/{GameSetting.gameName} {PlayerSettings.bundleVersion}.aab";
            
            GameSetting.bundleVersion += 1;
            EditorUtility.SetDirty(GameSetting);
            AssetDatabase.SaveAssets();
            
            BuildPipeline.BuildPlayer(GetScenePaths(), playerPath, BuildTarget.Android, BuildOptions.ShowBuiltPlayer);
        }

        private static string[] GetScenePaths()
        {
            var scenes = new string[EditorBuildSettings.scenes.Length];
            for (var i = 0; i < scenes.Length; i++)
                scenes[i] = EditorBuildSettings.scenes[i].path;

            return scenes;
        }

    }
}

#endif
