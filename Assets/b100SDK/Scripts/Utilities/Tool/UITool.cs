#if UNITY_EDITOR


using UnityEditor;
using UnityEngine;

namespace b100SDK.Scripts.Utilities.Tool
{
    public class UITool : Editor
    {
        
        #region UI Button

        [MenuItem("GameObject/UI/b100/Button/Button Icon", false, 1)]
        private static void CreateBhButtonIcon(MenuCommand menuCommand)
        {
            var parent = menuCommand.context as GameObject;
            if (parent == null || parent.GetComponentsInParent<Canvas>(true).Length == 0)
            {
                Debug.LogError("Invalid parent!");
                return;
            }

            var bhButton = PrefabUtility.InstantiatePrefab(ConfigTool.GetEditorUiConfig().prefabButtonIcon) as GameObject;
            Undo.RegisterCreatedObjectUndo(bhButton, "Button Icon");
            bhButton.name = "Button Icon";
            SetParentAndAlign(bhButton, parent);
            Selection.activeObject = bhButton;
            
            EditorApplication.delayCall += () =>
            {
                EditorWindow.focusedWindow.SendEvent(new Event
                {
                    type = EventType.ExecuteCommand,
                    commandName = "Rename"
                });
            };
        }
        
        [MenuItem("GameObject/UI/b100/Button/Button Text", false, 2)]
        private static void CreateBhButtonText(MenuCommand menuCommand)
        {
            var parent = menuCommand.context as GameObject;
            if (parent == null || parent.GetComponentsInParent<Canvas>(true).Length == 0)
            {
                Debug.LogError("Invalid parent!");
                return;
            }

            var bhButton = PrefabUtility.InstantiatePrefab(ConfigTool.GetEditorUiConfig().prefabButtonText) as GameObject;
            Undo.RegisterCreatedObjectUndo(bhButton, "Button Text");
            bhButton.name = "Button Text";
            SetParentAndAlign(bhButton, parent);
            Selection.activeObject = bhButton;
            
            EditorApplication.delayCall += () =>
            {
                EditorWindow.focusedWindow.SendEvent(new Event
                {
                    type = EventType.ExecuteCommand,
                    commandName = "Rename"
                });
            };
        }
        
        
        [MenuItem("GameObject/UI/b100/Button/Button Mix", false, 3)]
        private static void CreateBhButtonMix(MenuCommand menuCommand)
        {
            var parent = menuCommand.context as GameObject;
            if (parent == null || parent.GetComponentsInParent<Canvas>(true).Length == 0)
            {
                Debug.LogError("Invalid parent!");
                return;
            }

            var bhButton = PrefabUtility.InstantiatePrefab(ConfigTool.GetEditorUiConfig().prefabButtonMix) as GameObject;
            Undo.RegisterCreatedObjectUndo(bhButton, "Button Mix");
            bhButton.name = "Button Mix";
            SetParentAndAlign(bhButton, parent);
            Selection.activeObject = bhButton;
            
            EditorApplication.delayCall += () =>
            {
                EditorWindow.focusedWindow.SendEvent(new Event
                {
                    type = EventType.ExecuteCommand,
                    commandName = "Rename"
                });
            };
        }
        
        [MenuItem("GameObject/UI/b100/Button/Button Free", false, 4)]
        private static void CreateBhButtonFree(MenuCommand menuCommand)
        {
            var parent = menuCommand.context as GameObject;
            if (parent == null || parent.GetComponentsInParent<Canvas>(true).Length == 0)
            {
                Debug.LogError("Invalid parent!");
                return;
            }

            var bhButton = PrefabUtility.InstantiatePrefab(ConfigTool.GetEditorUiConfig().prefabButtonFree) as GameObject;
            Undo.RegisterCreatedObjectUndo(bhButton, "Button Free");
            bhButton.name = "Button Free";
            SetParentAndAlign(bhButton, parent);
            Selection.activeObject = bhButton;
            
            EditorApplication.delayCall += () =>
            {
                EditorWindow.focusedWindow.SendEvent(new Event
                {
                    type = EventType.ExecuteCommand,
                    commandName = "Rename"
                });
            };
        }     
        
        
        [MenuItem("GameObject/UI/b100/Button/Button Switch", false, 5)]
        private static void CreateBhButtonSwitch(MenuCommand menuCommand)
        {
            var parent = menuCommand.context as GameObject;
            if (parent == null || parent.GetComponentsInParent<Canvas>(true).Length == 0)
            {
                Debug.LogError("Invalid parent!");
                return;
            }

            var bhButton = PrefabUtility.InstantiatePrefab(ConfigTool.GetEditorUiConfig().prefabButtonSwitch) as GameObject;
            Undo.RegisterCreatedObjectUndo(bhButton, "Button Switch");
            bhButton.name = "Button Switch";
            SetParentAndAlign(bhButton, parent);
            Selection.activeObject = bhButton;
            
            EditorApplication.delayCall += () =>
            {
                EditorWindow.focusedWindow.SendEvent(new Event
                {
                    type = EventType.ExecuteCommand,
                    commandName = "Rename"
                });
            };
        }
        private static void SetParentAndAlign(GameObject child, GameObject parent)
        {
            if (parent == null)
                return;

            Undo.SetTransformParent(child.transform, parent.transform, "");

            var rectTransform = child.transform as RectTransform;
            if (rectTransform)
            {
                rectTransform.anchoredPosition = Vector2.zero;
                var localPosition = rectTransform.localPosition;
                localPosition.z = 0;
                rectTransform.localPosition = localPosition;
            }
            else
            {
                child.transform.localPosition = Vector3.zero;
            }

            child.transform.localRotation = Quaternion.identity;
            child.transform.localScale = Vector3.one;

            SetLayerRecursively(child, parent.layer);
        }

        private static void SetLayerRecursively(GameObject go, int layer)
        {
            go.layer = layer;
            var t = go.transform;
            for (var i = 0; i < t.childCount; i++)
                SetLayerRecursively(t.GetChild(i).gameObject, layer);
        }

        #endregion



        #region UI Text
                
        
        [MenuItem("GameObject/UI/b100/Text/Default Text", false)]
        private static void CreateBhDefaultText(MenuCommand menuCommand)
        {
            var parent = menuCommand.context as GameObject;
            if (parent == null || parent.GetComponentsInParent<Canvas>(true).Length == 0)
            {
                Debug.LogError("Invalid parent!");
                return;
            }

            var bhText = PrefabUtility.InstantiatePrefab(ConfigTool.GetEditorUiConfig().defaultTextPrefab) as GameObject;
            Undo.RegisterCreatedObjectUndo(bhText, "Bh Default Text");
            bhText.name = "Bh Default Text";
            SetParentAndAlign(bhText, parent);
            Selection.activeObject = bhText;
            
            EditorApplication.delayCall += () =>
            {
                EditorWindow.focusedWindow.SendEvent(new Event
                {
                    type = EventType.ExecuteCommand,
                    commandName = "Rename"
                });
            };
        }


        #endregion
        
        

        #region Drop down
 
        [MenuItem("GameObject/UI/b100/Drop Down/Default Drop Down", false)]
        private static void CreateBhDropDown(MenuCommand menuCommand)
        {
            var parent = menuCommand.context as GameObject;
            if (parent == null || parent.GetComponentsInParent<Canvas>(true).Length == 0)
            {
                Debug.LogError("Invalid parent!");
                return;
            }

            var bhDropDown = PrefabUtility.InstantiatePrefab(ConfigTool.GetEditorUiConfig().dropDownPrefab) as GameObject;
            Undo.RegisterCreatedObjectUndo(bhDropDown, "Bh Drop Down");
            bhDropDown.name = "Bh Drop Down";
            SetParentAndAlign(bhDropDown, parent);
            Selection.activeObject = bhDropDown;
            
            EditorApplication.delayCall += () =>
            {
                EditorWindow.focusedWindow.SendEvent(new Event
                {
                    type = EventType.ExecuteCommand,
                    commandName = "Rename"
                });
            };
        }

        #endregion


    }
}


#endif
