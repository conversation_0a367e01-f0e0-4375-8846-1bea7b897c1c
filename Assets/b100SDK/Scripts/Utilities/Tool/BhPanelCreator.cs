#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.IO;
using b100SDK.Scripts.Configs.SDKEditor;
using b100SDK.Scripts.UI;
using b100SDK.Scripts.UI.Components.Panel;
using Sirenix.OdinInspector;
using Sirenix.OdinInspector.Editor;
using UnityEditor;
using UnityEngine;
#endif

namespace b100SDK.Scripts.Utilities.Tool
{
#if UNITY_EDITOR
    
    public class BhPanelCreator : OdinEditorWindow
    {
        private const string _TEMPLATE_PATH = "Assets/b100SDK/Scripts/Templates/Template1.txt";
        private const string _ENUM_PATH = "Assets/b100SDK/Scripts/UI/PanelEnums.cs";

        private const string _SCRIPT_PATH = "Assets/b100SDK/Scripts/UI/Panel/";
        private const string _PREFAB_PATH = "Assets/b100SDK/Prefabs/UI/Screen And Popup/";
        private const string _POPUP_PREFIX = "Popup";
        private const string _SCREEN_PREFIX = "Screen";
        
        private EditorUIConfig _editorUIConfig;

        private static bool IsStartedCreateScript
        {
            get => PlayerPrefs.GetInt("BHPanelCreatorIsStartedCreateScript") == 1;

            set => PlayerPrefs.SetInt("BHPanelCreatorIsStartedCreateScript", value ? 1 : 0);
        }

        private static bool IsWaitingForCreateScript
        {
            get => PlayerPrefs.GetInt("BHPanelCreatorIsWaitingForCreateScript") == 1;

            set => PlayerPrefs.SetInt("BHPanelCreatorIsWaitingForCreateScript", value ? 1 : 0);
        }

        private static bool IsDone
        {
            get => PlayerPrefs.GetInt("BHPanelCreatorIsDone") == 1;

            set => PlayerPrefs.SetInt("BHPanelCreatorIsDone", value ? 1 : 0);
        }
        
        
        
        public string panelName = string.Empty;
        public bool isPopup;
        [FolderPath]
        public string scriptPath = _SCRIPT_PATH;
        [FolderPath]
        public string prefabPath = _PREFAB_PATH;

        private UIPanelType _parseTye;
        
        

        #region UI Tools

        public static void CreateNewScreen()
        {
            GetWindow<BhPanelCreator>("b100 Panel Creator");
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            
            IsDone = false;
            IsWaitingForCreateScript = false;
            IsStartedCreateScript = false;
        }

        #endregion
        
        public string GetCorrectNamespace()
        {
            var path = scriptPath.ToString();
            
            if (string.IsNullOrEmpty(path))
                return path;
            
            string assetsPrefix = "assets/";
            if (path.StartsWith(assetsPrefix, StringComparison.OrdinalIgnoreCase))
            {
                path = path.Substring(assetsPrefix.Length);
            }
            
            string scriptPrefix = "script";
            if (path.StartsWith(scriptPrefix, StringComparison.OrdinalIgnoreCase))
            {
                int slashIndex = path.IndexOf('/');
                if (slashIndex >= 0)
                {
                    path = path.Substring(slashIndex + 1);
                }
                else
                {
                    path = "b100DefaultNamespace";
                }
            }
            
            path = path.Replace('/', '.');
            
            path = path.TrimEnd('.');

            return path;
        }

        public string GetCorrectPrefixScript()
        {
            return GetCorrectNamespace() + ".";
        }


        private void Update()
        {
            if (!IsWaitingForCreateScript) 
                return;
            
            if (!IsStartedCreateScript)
                return;
            
            if (IsDone)
                return;

            if (!Enum.TryParse<UIPanelType>(panelName, out var data))
            {
                Log("Wait for create new panel........");
                return;
            }
            
            OnCreatePrefab();

            LogSuccess("Complete create panel " + panelName);
            IsDone = true;
            IsStartedCreateScript = false;
            IsWaitingForCreateScript = false;
        }
        
        
        [PropertySpace(20)]
        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        void Create()
        {
            logEntries.Clear();
            
            if (!CheckPathInResources())
            {
                LogError("Please choose path in \"\\Resources\\\" folder.");
                return;
            }

            CorrectClassName();
            
            var newType = Enum.TryParse<UIPanelType>(panelName, out _parseTye);
            
            if (newType)
            {
                LogError("This panel is existed. Please choose other name.");
            }
            else
            {
                OnCreateScript();
                IsWaitingForCreateScript = true;
                IsStartedCreateScript = true;
                IsDone = false;
            }
        }

        bool CheckPathInResources()
        {
            return prefabPath.Contains("/Resources/");
        }

        private void CorrectClassName()
        {
            if (isPopup)
            {
                if (panelName.Contains(_SCREEN_PREFIX))
                    panelName = panelName.Replace(_SCREEN_PREFIX, _POPUP_PREFIX);
                else if (!panelName.Contains(_POPUP_PREFIX))
                    panelName = panelName.Insert(0, _POPUP_PREFIX);
            }
            else
            {
                if (panelName.Contains(_POPUP_PREFIX))
                    panelName = panelName.Replace(_POPUP_PREFIX, _SCREEN_PREFIX);
                else if (!panelName.Contains(_SCREEN_PREFIX))
                    panelName = panelName.Insert(0, _SCREEN_PREFIX);
            }
        }

        private void OnCreateScript()
        {
            AddNewEnum();
            ExportEntity();
            AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);
        }

        private void OnCreatePrefab()
        {
            BhDebug.Log("Start create prefab");
            CreatePrefab();
            AddInstanceToList();
        }

        private void AddNewEnum()
        {
            var enumScript = File.ReadAllText(_ENUM_PATH);
            if (enumScript.Contains(panelName))
                return;

            var index = enumScript.IndexOf("SdkPlaceHolder", StringComparison.Ordinal);

            var newEnumScript = enumScript.Insert(index, panelName + ",\n    \t");

            File.WriteAllText(_ENUM_PATH, newEnumScript);
        }

        private void ExportEntity()
        {
            var templateFilePath = _TEMPLATE_PATH;
            var scriptTemplate = File.ReadAllText(templateFilePath);

            var script = scriptTemplate.Replace("TempClassName", panelName);
            script = script.Replace("TempNamespace", GetCorrectNamespace());

            Directory.CreateDirectory(scriptPath + "/");
            File.WriteAllText(scriptPath + "/" + panelName + ".cs", script);
        }

        private void CreatePrefab()
        {
            _editorUIConfig = ConfigTool.GetEditorUiConfig();
            var obj = PrefabUtility.InstantiatePrefab(isPopup ? _editorUIConfig.prefabPopup : _editorUIConfig.prefabScreen) as GameObject;
            PrefabUtility.UnpackPrefabInstance(obj, PrefabUnpackMode.OutermostRoot, InteractionMode.UserAction);
            if (obj != null)
            {
                obj.name = panelName;
                var type = Type.GetType(GetCorrectPrefixScript() + panelName);
                var panel = (UIPanel)obj.AddComponent(type);
                panel.panelAnimator = obj.GetComponent<PanelAnim>();
                var newObj = PrefabUtility.SaveAsPrefabAsset(obj, prefabPath + "/" + panelName + ".prefab");
                Selection.activeObject = newObj;
                UtilitiesTool.ShowInspector();
                DestroyImmediate(obj);
                AssetDatabase.SaveAssets();
                EditorUtility.FocusProjectWindow();
                Selection.activeObject = newObj;
                
                BhDebug.Log("Panel prefab created successfully!");
            }
        }

        private void AddInstanceToList()
        {
            _editorUIConfig = ConfigTool.GetEditorUiConfig();
            _editorUIConfig.AddPanelPath(prefabPath);
            _editorUIConfig.GetPanelInstances();
            EditorUtility.SetDirty(_editorUIConfig);
            AssetDatabase.SaveAssets();
        }
        
        
        
        #region Log
        
        private List<LogEntry> logEntries = new List<LogEntry>();
        private Vector2 scrollPosition;
        
        private void Log(string message = "Test Log")
        {
            AddLog(message, Color.white);
        }
        private void LogWarning(string message)
        {
            AddLog(message, Color.yellow);
        }
        
        private void LogError(string message)
        {
            AddLog(message, Color.red);
        }        
        
        private void LogSuccess(string message)
        {
            AddLog(message, Color.green);
        }

        protected override void OnGUI()
        {
            
            base.OnGUI(); 

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Width(position.width), GUILayout.Height(position.height-320));

            EditorGUILayout.BeginVertical(new GUILayoutOption[]
            {
                GUILayout.MinHeight(20),
            
            });
            
            foreach (var logEntry in logEntries)
            {
                GUI.contentColor = logEntry.color;
                GUILayout.Label(logEntry.message);
            }

            GUI.contentColor = Color.white;

            EditorGUILayout.EndScrollView();
            
            EditorGUILayout.EndVertical();

        }

        private void AddLog(string message, Color color)
        {
            logEntries.Insert(0, new LogEntry { message = message, color = color });
            // Set scroll position to the bottom
            scrollPosition.y = 0f;
        }

        private class LogEntry
        {
            public string message;
            public Color color;
        }

        #endregion
    }
    
#endif
}