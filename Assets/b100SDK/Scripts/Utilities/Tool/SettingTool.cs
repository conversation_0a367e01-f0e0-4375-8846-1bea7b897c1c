#if UNITY_EDITOR
using System.IO;
using b100SDK.Scripts.Configs.SDKEditor;
using UnityEditor;
using UnityEngine;

namespace b100SDK.Scripts.Utilities.Tool
{
    public class SettingTool : Editor
    {
        private static GameSetting _gameSetting;

        static GameSetting GameSetting
        {
            get
            {
                if (_gameSetting == null)
                {
                    _gameSetting = GetGameSetting();
                }

                return _gameSetting;
            }
        }


        public static void SaveGameSetting()
        {
            EditorUtility.SetDirty(GameSetting);
            AssetDatabase.SaveAssets();
            
            ValidatePlayerSetting();
        }
        
        
        
        public static GameSetting GetGameSetting()
        {
            /*var path = "Assets/b100SDK/Configs";
            var fileEntries = Directory.GetFiles(path);
            for (var i = 0; i < fileEntries.Length; i++)
                if (fileEntries[i].EndsWith(".asset"))
                {
                    var item =
                        AssetDatabase.LoadAssetAtPath<GameSetting>(fileEntries[i].Replace("\\", "/"));
                    if (item != null)
                        return item;
                }

            return null;*/

            return ConfigTool.GetGameSettingConfig();
        }


        
        public static void ValidatePlayerSetting()
        {
            PlayerSettings.companyName = GameSetting.companyName;
            PlayerSettings.productName = GameSetting.gameName;
            
            PlayerSettings.SetIconsForTargetGroup(BuildTargetGroup.Unknown, new Texture2D[] { GameSetting.gameIcon });
            
            if (Application.HasProLicense())
                PlayerSettings.SplashScreen.showUnityLogo = false;
            
            PlayerSettings.bundleVersion = string.Format("{0}.{1}.{2}", GameSetting.gameVersion,
                GameSetting.bundleVersion, GameSetting.buildVersion);

#if UNITY_ANDROID
            PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Android, GameSetting.packageName);
            PlayerSettings.SetScriptingBackend(BuildTargetGroup.Android, ScriptingImplementation.IL2CPP);
            PlayerSettings.Android.bundleVersionCode = GameSetting.bundleVersion;
            
            PlayerSettings.Android.minSdkVersion = AndroidSdkVersions.AndroidApiLevel24;
            PlayerSettings.Android.targetSdkVersion = AndroidSdkVersions.AndroidApiLevel34;
            
            PlayerSettings.Android.targetArchitectures = AndroidArchitecture.ARMv7 | AndroidArchitecture.ARM64;
            PlayerSettings.Android.androidTargetDevices = AndroidTargetDevices.PhonesTabletsAndTVDevicesOnly;
#elif UNITY_IOS


#endif
        }
    }
}

#endif
