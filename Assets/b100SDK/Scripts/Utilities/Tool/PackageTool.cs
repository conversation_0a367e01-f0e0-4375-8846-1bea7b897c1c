#if UNITY_EDITOR


using System;
using System.IO;
using Cysharp.Threading.Tasks;
using Newtonsoft.Json;
using UnityEditor;
using UnityEngine;
using UnityEngine.Networking;

namespace b100SDK.Scripts.Utilities.Tool
{
    public class PackageTool : Editor
    {
        #region Package

        public const string DEFAULT_SAVE_PATH = "Assets/DownloadPackage";

        
        public static async UniTask GetLatestReleasePackage(string githubUrl, string savePath = "")
        {
            UnityWebRequest request = UnityWebRequest.Get(githubUrl);
            request.SetRequestHeader("User-Agent", "request");
            var operation = request.SendWebRequest();

            while (!operation.isDone)
            {
                await UniTask.Yield();
                BhDebug.Log("Waiting for get latest package...");
            }

            if (request.result == UnityWebRequest.Result.ConnectionError || request.result == UnityWebRequest.Result.ProtocolError)
            {
                Debug.LogError(request.error);
            }
            else
            {
                var data = JsonConvert.DeserializeObject<ResponseData>(request.downloadHandler.text);
                string downloadUrl = String.Empty;
                
                foreach (var asset in data.assets)
                {
                    string fileName = asset.name;
                    if (fileName.EndsWith(".unitypackage"))
                    {
                        downloadUrl = asset.browser_download_url;
                        break;
                    }
                }

                if (!string.IsNullOrEmpty(downloadUrl))
                {
                    BhDebug.Log("Latest package download URL: " + downloadUrl);
                    await DownloadAndImportPackage(downloadUrl, savePath);
                }
                else
                {
                    BhDebug.LogError("No .unitypackage file found in the latest release.");
                }
            }
        }

        public static async UniTask DownloadAndImportPackage(string url, string savePath = "")
        {
            UnityWebRequest request = UnityWebRequest.Get(url);
            var operation = request.SendWebRequest();
            
            while (!operation.isDone)
            {
                await UniTask.Yield();
                BhDebug.Log("Waiting for package download... - " + (request.downloadProgress * 100) + "%");
            }
            
            if (request.result == UnityWebRequest.Result.ConnectionError || request.result == UnityWebRequest.Result.ProtocolError)
            {
                BhDebug.LogError(request.error);
            }
            else
            {
                Uri uri = new Uri(url);
                string fileName = Path.GetFileName(uri.LocalPath);

                if (string.IsNullOrEmpty(savePath))
                {
                    savePath = DEFAULT_SAVE_PATH;
                }
                
                string packagePath = savePath + "/" + fileName;
                File.WriteAllBytes(packagePath, request.downloadHandler.data);

                AssetDatabase.ImportPackage(packagePath, false);
                BhDebug.Log("Package downloaded and imported successfully.");
            }
        }

        [Serializable]
        class ResponseData
        {
            public Asset[] assets;
        }

        [Serializable]
        class Asset
        {
            public string name;
            public string browser_download_url;
        }
        

        #endregion
    }
}

#endif
