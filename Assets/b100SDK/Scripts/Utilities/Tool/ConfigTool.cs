#if UNITY_EDITOR

using System.Collections.Generic;
using System.IO;
using b100SDK.Scripts.Configs;
using b100SDK.Scripts.Configs.Ads;
using b100SDK.Scripts.Configs.Core;
using b100SDK.Scripts.Configs.IAP;
using b100SDK.Scripts.Configs.RemoteConfig;
using b100SDK.Scripts.Configs.SDKEditor;
using UnityEditor;
using UnityEngine;

namespace b100SDK.Scripts.Utilities.Tool
{
    public class ConfigTool : Editor
    {
        
        static EditorPathConfig GetEditorPathConfig()
        {
            var path = "Assets/b100SDK/Configs/Editor/";
            var fileEntries = Directory.GetFiles(path);
            for (var i = 0; i < fileEntries.Length; i++)
                if (fileEntries[i].EndsWith(".asset"))
                {
                    var item =
                        AssetDatabase.LoadAssetAtPath<EditorPathConfig>(fileEntries[i].Replace("\\", "/"));
                    if (item != null)
                        return item;
                }

            return null;
        }


        #region Game Config

        public static void ShowGameConfig()
        {
            var cfg = GetGameConfig();
            if (cfg == null)
            {
                cfg = CreateInstance<GameConfig>();
                AssetDatabase.CreateAsset(cfg, GetEditorPathConfig().GetGameConfigPath());
                AssetDatabase.SaveAssets();
            }

            cfg = GetGameConfig();
            Selection.activeObject = cfg;
            UtilitiesTool.ShowInspector();
        }

        public static GameConfig GetGameConfig()
        {
            var path = GetEditorPathConfig().GetGameConfigPath();

            if (string.IsNullOrEmpty(path))
            {
                return null;
            }
            
            var config = AssetDatabase.LoadAssetAtPath<GameConfig>(path);

            if (config != null)
            {
                return config;
            }

            return null;
        }
        
        #endregion


        
        

        #region Sound Configuration


        public static void ShowSoundConfig()
        {
            var cfg = GetSoundConfig();
            if (cfg == null)
            {
                cfg = CreateInstance<SoundConfig>();
                AssetDatabase.CreateAsset(cfg, GetEditorPathConfig().GetSoundConfigPath());
                AssetDatabase.SaveAssets();
            }

            cfg = GetSoundConfig();
            Selection.activeObject = cfg;
            UtilitiesTool.ShowInspector();
        }

        public static SoundConfig GetSoundConfig()
        {
            var path = GetEditorPathConfig().GetSoundConfigPath();
            if (string.IsNullOrEmpty(path))
            {
                return null;
            }
            
            var config = AssetDatabase.LoadAssetAtPath<SoundConfig>(path);
            
            if (config!= null)
            {
                return config;
            }
            
            return null;
        }        
        
        public static AudioConfig GetAudioConfig()
        {
            var path = GetEditorPathConfig().GetAudioConfigPath();
            if (string.IsNullOrEmpty(path))
            {
                return null;
            }
            
            var config = AssetDatabase.LoadAssetAtPath<AudioConfig>(path);
            
            if (config!= null)
            {
                return config;
            }
            
            return null;
        }
        

        #endregion
        
        
        #region UI Config


        public static void ShowUiConfig()
        {
            var cfg = GetEditorUiConfig();
            if (cfg == null)
            {
                cfg = CreateInstance<EditorUIConfig>();
                AssetDatabase.CreateAsset(cfg, GetEditorPathConfig().GetEditorUIConfigFilePath());
                AssetDatabase.SaveAssets();
            }

            cfg = GetEditorUiConfig();
            Selection.activeObject = cfg;
            UtilitiesTool.ShowInspector();
        }
        
        
        public static EditorUIConfig GetEditorUiConfig()
        {
            var path = GetEditorPathConfig().GetEditorUIConfigFilePath();

            if (string.IsNullOrEmpty(path))
            {
                return null;
            }
            
            var item = AssetDatabase.LoadAssetAtPath<EditorUIConfig>(path);
            
            if (item != null)
            {
                return item;
            }
            
            return null;
        }     
        
        
        
        public static UIConfig GetUIConfig()
        {
            var path = GetEditorPathConfig().GetUIConfigFilePath();
            if (string.IsNullOrEmpty(path))
            {
                return null;
            }
            
            var config = AssetDatabase.LoadAssetAtPath<UIConfig>(path);
            
            if (config!= null)
            {
                return config;
            }
            
            return null;
        }
        
        
        #endregion



        #region Game Setting

        public static GameSetting GetGameSettingConfig()
        {
            var path = GetEditorPathConfig().GetGameSettingPath();

            if (string.IsNullOrEmpty(path))
            {
                return null;
            }
            
            var item = AssetDatabase.LoadAssetAtPath<GameSetting>(path);
            
            if (item != null)
            {
                return item;
            }
            
            return null;
        }     

        #endregion




        #region GameBuilder

        public static GameBuilder GetGameBuilderConfig()
        {
            var path = GetEditorPathConfig().GetGameBuilderPath();

            if (string.IsNullOrEmpty(path))
            {
                return null;
            }
            
            var item = AssetDatabase.LoadAssetAtPath<GameBuilder>(path);
            
            if (item != null)
            {
                return item;
            }
            
            return null;
        }     

        #endregion
        
        

        #region IAP

        
        public static void ShowIAPConfig()
        {
            var cfg = GetIAPConfig();
            if (cfg == null)
            {
                cfg = CreateInstance<IAPConfig>();
                AssetDatabase.CreateAsset(cfg, GetEditorPathConfig().GetIapConfigPath());
                AssetDatabase.SaveAssets();
            }

            cfg = GetIAPConfig();
            Selection.activeObject = cfg;
            UtilitiesTool.ShowInspector();
        }

        public static IAPConfig GetIAPConfig()
        {
            var path = GetEditorPathConfig().GetIapConfigPath();
            if (string.IsNullOrEmpty(path))
            {
                return null;
            }
            
            var item = AssetDatabase.LoadAssetAtPath<IAPConfig>(path);
            
            if (item!= null)
            {
                return item;
            }
            
            return null;
        }           

        
        #endregion





        #region Remote Config

        
        public static void ShowRemoteConfigGroup()
        {
            var cfg = GetRemoteConfigGroup();
            if (cfg == null)
            {
                cfg = CreateInstance<RemoteConfigGroup>();
                AssetDatabase.CreateAsset(cfg, GetEditorPathConfig().GetRemoteConfigPath());
                AssetDatabase.SaveAssets();
            }

            cfg = GetRemoteConfigGroup();
            Selection.activeObject = cfg;
            UtilitiesTool.ShowInspector();
        }

        public static RemoteConfigGroup GetRemoteConfigGroup()
        {
            var path = GetEditorPathConfig().GetRemoteConfigPath();
            if (string.IsNullOrEmpty(path))
            {
                return null;
            }
            
            var item = AssetDatabase.LoadAssetAtPath<RemoteConfigGroup>(path);
            
            if (item!= null)
            {
                return item;
            }
            
            return null;
        }       
        
        

        #endregion





        #region IAA

        
        public static void ShowIAAConfig()
        {
            var cfg = GetIAAConfig();
            if (cfg == null)
            {
                cfg = CreateInstance<IAAConfig>();
                AssetDatabase.CreateAsset(cfg, GetEditorPathConfig().GetIaaConfigPath());
                AssetDatabase.SaveAssets();
            }

            cfg = GetIAAConfig();
            Selection.activeObject = cfg;
            UtilitiesTool.ShowInspector();
        }

        public static IAAConfig GetIAAConfig()
        {
            var path = GetEditorPathConfig().GetIaaConfigPath();
            if (string.IsNullOrEmpty(path))
            {
                return null;
            }
            
            var item = AssetDatabase.LoadAssetAtPath<IAAConfig>(path);
            
            if (item!= null)
            {
                return item;
            }
            
            return null;
        }
        

        #endregion



        #region Config

        public static T GetConfig<T>(string path) where T : ScriptableObject
        {
            var fileEntries = Directory.GetFiles(path, ".", SearchOption.AllDirectories);

            foreach (var fileEntry in fileEntries)
                if (fileEntry.EndsWith(".asset"))
                {
                    var item =
                        AssetDatabase.LoadAssetAtPath<T>(fileEntry.Replace("\\", "/"));
                    if (item)
                        return item;
                }

            return null;
        }

        public static List<T> GetConfigs<T>(string path) where T : ScriptableObject
        {
            var fileEntries = Directory.GetFiles(path, ".", SearchOption.AllDirectories);
            var result = new List<T>();
            foreach (var fileEntry in fileEntries)
                if (fileEntry.EndsWith(".asset"))
                {
                    var item = AssetDatabase.LoadAssetAtPath<T>(fileEntry.Replace("\\", "/"));

                    if (item)
                        result.Add(item);
                }

            if (result.Count > 0)
                return result;

            return null;
        }

        public static void SaveConfig(ScriptableObject config)
        {
            EditorUtility.SetDirty(config);
            AssetDatabase.SaveAssets();
        }
        
        #endregion
    }
}


#endif
