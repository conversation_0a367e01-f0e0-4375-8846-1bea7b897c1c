#region

using b100SDK.Scripts.Base;
using PathologicalGames;
using UnityEngine;

#endregion

namespace b100SDK.Scripts.Utilities
{
    public static class PoolHelper
    {
        
        public static Transform Spawn(this GameObject obj, Pool pool)
        {
            var poolName = pool.ToString();
            if (pool == Pool.None)
                poolName = obj.name;
            return PoolManager.Pools[poolName].Spawn(obj, Vector3.zero, Quaternion.identity);
        }

        public static Transform Spawn(this GameObject obj, Transform parent, Pool pool)
        {
            var poolName = pool.ToString();
            if (pool == Pool.None)
                poolName = obj.name;
            return PoolManager.Pools[poolName].Spawn(obj, Vector3.zero, Quaternion.identity, parent);
        }

        public static Transform Spawn(this GameObject prefab, Vector3 pos, Quaternion rot, Pool pool)
        {
            var poolName = pool.ToString();
            if (pool == Pool.None)
                poolName = prefab.name;
            return PoolManager.Pools[poolName].Spawn(prefab, pos, rot);
        }

        public static Transform Spawn(this GameObject prefab, Vector3 pos, Quaternion rot, Transform parent, Pool pool)
        {
            var poolName = pool.ToString();
            if (pool == Pool.None)
                poolName = prefab.name;
            return PoolManager.Pools[poolName].Spawn(prefab, pos, rot, parent);
        }

        public static void Despawn(this GameObject prefab, Pool pool)
        {
            var poolName = pool.ToString();
            if (pool == Pool.None)
            {
                poolName = prefab.name;
                if (poolName.IndexOf('(') > 0)
                    poolName = prefab.name.Substring(0, poolName.IndexOf('('));
            }

            PoolManager.Pools[poolName].Despawn(prefab.transform);
        }
        
        public static T Spawn<T>(this T prefab, Pool pool) where T : BhMonoBehavior
        {
            var poolName = pool.ToString();
            if (pool == Pool.None)
            {
                poolName = prefab.name;
            }

            return PoolManager.Pools[poolName].Spawn(prefab.transform, Vector3.zero, Quaternion.identity)
                .GetComponent<T>();
        }

        public static T Spawn<T>(this T prefab, Transform parent, Pool pool) where T : BhMonoBehavior
        {
            var poolName = pool.ToString();
            if (pool == Pool.None)
            {
                poolName = prefab.name;
            }

            return PoolManager.Pools[poolName].Spawn(prefab.transform, parent).GetComponent<T>();
        }

        public static T Spawn<T>(this T prefab, Vector3 pos, Quaternion rot, Pool pool) where T : BhMonoBehavior
        {
            var poolName = pool.ToString();
            if (pool == Pool.None)
                poolName = prefab.name;
            return PoolManager.Pools[poolName].Spawn(prefab.transform, pos, rot).GetComponent<T>();
        }

        public static T Spawn<T>(this T prefab, Vector3 pos, Quaternion rot, Transform parent, Pool pool) where T : BhMonoBehavior
        {
            var poolName = pool.ToString();
            if (pool == Pool.None)
            {
                poolName = prefab.name;
            }
            return PoolManager.Pools[poolName].Spawn(prefab.transform, pos, rot, parent).GetComponent<T>();
        }

        public static void Despawn<T>(this T prefab, Pool pool) where T : BhMonoBehavior
        {
            var poolName = pool.ToString();
            if (pool == Pool.None)
            {
                poolName = prefab.name;
                if (poolName.IndexOf('(') > 0)
                    poolName = prefab.name.Substring(0, poolName.IndexOf('('));
            }

            PoolManager.Pools[poolName].Despawn(prefab.transform);
        }

        public static void DespawnAll(Pool pool)
        {
            var poolName = pool.ToString();
            PoolManager.Pools[poolName].DespawnAll();
        }
    }
}