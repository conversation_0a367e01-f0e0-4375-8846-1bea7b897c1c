using System.Collections.Generic;
using b100SDK.Scripts.Base;
using Sirenix.OdinInspector;
using UnityEngine;

namespace b100SDK.Scripts.Utilities
{
    public class ConvertBoxColliderUIOverlayToWorld : BhMonoBehavior
    {
        public RectTransform uiElement; // UI element muốn kiểm tra
        public Camera mainCamera;
        public BoxCollider colliderObject; // Collider dùng để kiểm tra va chạm
        
        public Transform middlePoint;
        public Canvas canvas;
        
        public List<Transform> cornerTransforms;

        private void Start()
        {
            Execute();
        }
        
        
        [Button]

        public void Execute()
        {
            Vector3 worldPosition;
            RectTransformUtility.ScreenPointToWorldPointInRectangle(
                uiElement,
                RectTransformUtility.WorldToScreenPoint(mainCamera, uiElement.position),
                mainCamera,
                out worldPosition
            );
            
            colliderObject.center = uiElement.InverseTransformPoint(worldPosition);
            
            middlePoint.position = TransformUtils.ConvertScreenPositionToViewportPosition(uiElement.GetPositionInScreenSpace(canvas), canvas, mainCamera);

            var cachePosition = middlePoint.position;
            cachePosition.z = mainCamera.transform.position.z + 10;

            middlePoint.position = mainCamera.ViewportToWorldPoint(cachePosition);

            middlePoint.position = TransformUtils.ConvertViewPortToWorldPoint(cachePosition, mainCamera);

            var boxCollider = uiElement.GetComponent<BoxCollider>();
            boxCollider.size = new Vector3(uiElement.rect.width, uiElement.rect.height, 0.1f);
            boxCollider.center = uiElement.InverseTransformPoint(middlePoint.position);


            var corners = new Vector3[4];
            uiElement.GetWorldCorners(corners);
            
            List<Vector3> worldCorners = new List<Vector3>();

            /*for (var index = 0; index < cornerTransforms.Count; index++)
            {
                var cornerTransform = cornerTransforms[index];
                
                cornerTransform.transform.position = corners[index];
            }*/

            for (int i = 0; i < 4; i++)
            {
                var cornersWorldPosition = TransformUtils.ConvertViewPortToWorldPoint(
                    TransformUtils.ConvertScreenPositionToViewportPosition(corners[i], canvas, mainCamera), mainCamera);
                
                worldCorners.Add(cornersWorldPosition);
                
                var cornerTransform = cornerTransforms[i];
                cornerTransform.transform.position = cornersWorldPosition;
            }

            var size = new Vector3(Vector3.Distance(worldCorners[2], worldCorners[1]),
                Vector3.Distance(worldCorners[3], worldCorners[2]), .1f);

            colliderObject.size = uiElement.InverseTransformVector(size);
        }
        
        
        [Button]
        void LogViewportPosition()
        {
            if (uiElement != null)
            {
                // Lấy vị trí màn hình thực tế từ RectTransform
                Debug.Log($"Position: {uiElement.position}");
                
                Vector3 screenPosition = uiElement.anchoredPosition;

                // Chuyển từ screen position sang viewport position
                Vector3 viewportPosition = mainCamera.ScreenToViewportPoint(screenPosition);

                Debug.Log($"Viewport Position: {viewportPosition}");
                Debug.Log($"Mouse position: {Input.mousePosition}");
                Debug.Log($"Mouse position on viewPort: {mainCamera.ScreenToViewportPoint(Input.mousePosition)}");
            }
        }

        private void Update()
        {
            if (Input.GetMouseButtonDown(0))
            {
                LogViewportPosition();
            }
        }
    }
}