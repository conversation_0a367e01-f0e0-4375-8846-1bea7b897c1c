#region

using System;
using System.Collections.Generic;
using DG.Tweening;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Object = UnityEngine.Object;

#endregion

namespace b100SDK.Scripts.Utilities
{
    public static class Utils
    {
        public static void ChangeText(this Text text, int start, int end, float duration)
        {
            DOTween.defaultTimeScaleIndependent = true;
            DOTween.To(x => text.text = ((int) x).ToString(), start, end, duration);
        }

        public static void ChangeText(this Text text, long start, long end, float duration, string format)
        {
            DOTween.defaultTimeScaleIndependent = true;
            DOTween.To(x => text.text = string.Format(format, (int) x), start, end, duration);
        }

        public static void ChangeText(this TMP_Text text, int start, int end, float duration)
        {
            DOTween.defaultTimeScaleIndependent = true;
            DOTween.To(x => text.text = ((int) x).ToString(), start, end, duration);
        }

        public static void ChangeText(this TMP_Text text, float start, float end, float duration, string format)
        {
            DOTween.defaultTimeScaleIndependent = true;
            DOTween.To(x => text.text = string.Format(format, x.ToString()), start, end, duration);
        }

        public static void ChangeText(this TMP_Text text, long start, long end, float duration, string format)
        {
            DOTween.defaultTimeScaleIndependent = true;
            DOTween.To(x => text.text = string.Format(format, (int) x), start, end, duration);
        }

        public static void ChangeImageFill(this Image image, float start, float end, float duration)
        {
            DOTween.To(x => image.fillAmount = x, start, end, duration);
        }

        public static void ChangeImageWidth(this RectTransform image, float end, float duration)
        {
            image.DOSizeDelta(new Vector2(end, image.sizeDelta.y), duration);
        }

        public static bool HasElementAtMousePos()
        {
            var eventData = new PointerEventData(EventSystem.current)
            {
                position = Input.mousePosition
            };
            var raycastResults = new List<RaycastResult>();
            EventSystem.current.RaycastAll(eventData, raycastResults);
            raycastResults.RemoveAll(x => x.gameObject.layer == LayerMask.NameToLayer("Ignore Raycast"));
            return raycastResults.Count > 0;
        }

        public static void SnapTo(this ScrollRect scroller, RectTransform child, float duration, float delay = 0, Action onComplete = null)
        {
            Vector2 contentPos = (Vector2)scroller.transform.InverseTransformPoint(scroller.content.position);
            Vector2 childPos = (Vector2)scroller.transform.InverseTransformPoint(child.position);
            Vector2 endPos = contentPos - childPos;

            // If no horizontal scroll, then don't change contentPos.x
            if (!scroller.horizontal) endPos.x = contentPos.x;
            // If no vertical scroll, then don't change contentPos.y
            if (!scroller.vertical) endPos.y = contentPos.y;
            if (duration <= 0)
            {
                scroller.content.anchoredPosition = endPos;
                onComplete?.Invoke();
            }
            else
            {
                scroller.content.DOAnchorPos(endPos, duration).SetDelay(delay).OnComplete(() => onComplete?.Invoke());
            }
        }


        public static void SetAlpha(this Image image, float alpha)
        {
            var color = image.color;
            color.a = alpha;
            image.color = color;
        }  
    
        public static void SetAlphaTween(this Image image, float alpha, float duration)
        {
            image.DOFade(alpha, duration).SetTarget(image);
        }   
    
        public static void SetAlphaTween(this Image image, float alpha, float startAlpha, float duration)
        {
            image.SetAlpha(startAlpha);
            image.DOFade(alpha, duration).SetTarget(image);
        }
        
        public static void SetAlpha(this SpriteRenderer image, float alpha)
        {
            var color = image.color;
            color.a = alpha;
            image.color = color;
        }  
    
        public static void SetAlphaTween(this SpriteRenderer image, float alpha, float duration)
        {
            image.DOFade(alpha, duration).SetTarget(image);
        }   
    
        public static void SetAlphaTween(this SpriteRenderer image, float alpha, float startAlpha, float duration)
        {
            image.SetAlpha(startAlpha);
            image.DOFade(alpha, duration).SetTarget(image);
        }

    

        public static void SetAlpha(this Text text, float alpha)
        {
            var color = text.color;
            color.a = alpha;
            text.color = color;
        }
    
    
        public static void SetAlphaTween(this Text text, float alpha, float duration)
        {
            text.DOFade(alpha, duration).SetTarget(text);
        }
    
        public static void SetAlphaTween(this Text text, float alpha, float startAlpha, float duration)
        {
            text.SetAlpha(startAlpha);
            text.DOFade(alpha, duration).SetTarget(text);
        }
    
        public static bool IsCollider(
            Vector2 posObject1,
            BoxCollider2D boxObject1,
            Vector2 posObject2,
            BoxCollider2D boxObject2
        )
        {
            float l1X, l1Y, r1X, r1Y;
            float l2X, l2Y, r2X, r2Y;

            var bounds1 = boxObject1.bounds;
            l1X = posObject1.x - bounds1.size.x / 2;
            l1Y = posObject1.y + bounds1.size.y / 2;
            r1X = posObject1.x + bounds1.size.x / 2;
            r1Y = posObject1.y - bounds1.size.y / 2;

            var bounds = boxObject2.bounds;
            l2X = posObject2.x - bounds.size.x / 2;
            l2Y = posObject2.y + bounds.size.y / 2;
            r2X = posObject2.x + bounds.size.x / 2;
            r2Y = posObject2.y - bounds.size.y / 2;

            if (l1X > r2X || l2X > r1X)
                return false;

            // If one rectangle is above other  
            if (l1Y < r2Y || l2Y < r1Y)
                return false;

            return true;
        }
    
        public static void DestroyChildren(Transform parent)
        {
            int childCount = parent.childCount;
            for (int i = childCount - 1; i >= 0; i--)
            {
                Object.Destroy(parent.GetChild(i).gameObject);
            }
        }

        public static void DestroyChildrenImmediate(Transform parent)
        {
            int childCount = parent.childCount;
            for (int i = childCount - 1; i >= 0; i--)
            {
                Object.DestroyImmediate(parent.GetChild(i).gameObject);
            }
        }

    
        public static void SetCurveLinear(this AnimationCurve curve) {
            for (int i = 0; i < curve.keys.Length; ++i) {
                float inTangent = 0;
                float outTangent = 0;
                bool inTangentSet = false;
                bool outTangentSet = false;
                Vector2 point1;
                Vector2 point2;
                Vector2 deltaPoint;
                Keyframe key = curve[i];
 
                if (i == 0) {
                    inTangent = 0; inTangentSet = true;
                }
 
                if (i == curve.keys.Length - 1) {
                    outTangent = 0; outTangentSet = true;
                }
 
                if (!inTangentSet) {
                    point1.x = curve.keys[i - 1].time;
                    point1.y = curve.keys[i - 1].value;
                    point2.x = curve.keys[i].time;
                    point2.y = curve.keys[i].value;
 
                    deltaPoint = point2 - point1;
 
                    inTangent = deltaPoint.y / deltaPoint.x;
                }
                if (!outTangentSet) {
                    point1.x = curve.keys[i].time;
                    point1.y = curve.keys[i].value;
                    point2.x = curve.keys[i + 1].time;
                    point2.y = curve.keys[i + 1].value;
 
                    deltaPoint = point2 - point1;
 
                    outTangent = deltaPoint.y / deltaPoint.x;
                }
 
                key.inTangent = inTangent;
                key.outTangent = outTangent;
                curve.MoveKey(i, key);
            }
        }


        #region GameObject


        public static void SetActiveWithChecker(this GameObject gameObject, bool active)
        {
            if (gameObject.activeSelf != active)
            {
                gameObject.SetActive(active);
            }
        }

        #endregion
    }
}