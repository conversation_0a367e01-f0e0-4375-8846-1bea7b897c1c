using b100SDK.Scripts.UI.Components.Button;
using TMPro;
using UnityEngine;

namespace b100SDK.Scripts.IAP
{
    public class TestSubscription : MonoBehaviour
    {
        public BhButton vip3, vip7, vip30;

        public TMP_Text currentText;


        private void OnEnable()
        {
            vip3.onClick.AddListener(BuyVip3);
            vip7.onClick.AddListener(BuyVip7);
            vip30.onClick.AddListener(BuyVip30);
            
        }

        private void OnDisable()
        {
            vip3.onClick.RemoveListener(BuyVip3);
            vip7.onClick.RemoveListener(BuyVip7);
            vip30.onClick.RemoveListener(BuyVip30);
        }

        private void Start()
        {
            UpdateText("NUll");
        }

        void UpdateText(string subscriptionId)
        {
            currentText.text = $"Current Subscription: {subscriptionId ?? "None"}";
        }


        void BuyVip3()
        {
            //IAPManager.Instance.PurchaseSubscription(IAPPackageType.Vip3, () => UpdateText("Vip 3"));
        }
        
        void BuyVip7()
        {
            //IAPManager.Instance.PurchaseSubscription(IAPPackageType.Vip7, () => UpdateText("Vip 7"));
        }
        
        void BuyVip30()
        {
            //IAPManager.Instance.PurchaseSubscription(IAPPackageType.Vip30, () => UpdateText("Vip 30"));
        }
    }
}