using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using b100SDK.Scripts.Configs;
using b100SDK.Scripts.Configs.IAP;
using b100SDK.Scripts.DesignPatterns;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.Purchasing;
using UnityEngine.Purchasing.Extension;

namespace b100SDK.Scripts.IAP
{
    public class IAPManager : Singleton<IAPManager>, IDetailedStoreListener
    {
        [SerializeField]
        private IAPConfig config;
        private IStoreController _storeController;
        
        
#if UNITY_IOS
        private IAppleExtensions _appleExtensions;
#elif UNITY_ANDROID
        private IGooglePlayStoreExtensions _googlePlayStoreExtensions;
#endif

        private Action _onBuyComplete;

        private bool _isInited;


        public UnityEvent OnIAPInited = new();

        private Dictionary<SubscriptionGroupType, SubscriptionGroup> _subscriptionGroupMap = new();

        public bool IsDebugIAP
        {
            get;
            set;
        }
         
        
        private void Start()
        {
            IsDebugIAP = config.isDebugIAP;
            
            _isInited = false;
            InitializePurchase();
        }


        void InitializePurchase()
        {
            _subscriptionGroupMap.Clear();
            
            var builder = ConfigurationBuilder.Instance(StandardPurchasingModule.Instance());

            foreach (IAPPackageType type in Enum.GetValues(typeof(IAPPackageType)))
            {
                if (type == IAPPackageType.SdkPlaceHolder)
                    continue;
                
                var data = config.GetData(type);

                if (data != null)
                {
                    builder.AddProduct(data.packageId, data.productType);
                }
                else
                {
                    Debug.LogError("[b100 - IAP] Init product type " + type + " failure!!!");
                }
            }

            UnityPurchasing.Initialize(this, builder);
        }



        /// <summary>
        /// Purchase an iap package with type of package 
        /// </summary>
        /// <param name="type"></param>
        /// <param name="onPurchaseComplete"></param>
        public void Purchase(IAPPackageType type, Action onPurchaseComplete)
        {
            if (IsDebugIAP)
            {
                onPurchaseComplete?.Invoke();
                return;
            }
            
            if (!_isInited)
            {
                Debug.LogError("IAP is not inited. Return default");
                return;
            }
            
            _onBuyComplete = onPurchaseComplete;

            var data = config.GetData(type);

            if (data!= null)
            {
                _storeController.InitiatePurchase(data.packageId);
            }
            else
            {
                Debug.LogError("[b100 - IAP] Product type " + type + " not found to purchase!!!");
            }
        }


        #region SUBSCRIPTION

        
        public bool IsSubscribedTo(IAPPackageType type)
        {
            var data = config.GetData(type);

            if (data == null)
            {
                Debug.LogError("[b100 - IAP] Product type " + type + " not found to purchase!!!");
                return false;
            }
            
            if (_storeController == null)
                return false;

            var product = _storeController.products.WithID(data.packageId);

            if (product == null)
            {
                Debug.LogError("[b100 - IAP] Cant fetching data product type " + data.productType + " !!!");
                return false;
            }
            
            // If the product doesn't have a receipt, then it wasn't purchased and the user is therefore not subscribed.
            if (product.receipt == null)
            {
                return false;
            }

            var subscriptionProduct = new SubscriptionManager(product, null);

            var info = subscriptionProduct.getSubscriptionInfo();

            return info.isSubscribed() == Result.True;
        }

        
        /// <summary>
        /// Get info of subscription package with package type
        /// </summary>
        /// <param name="type"></param>
        /// <returns> If null is no info => no subscription, else return the info of subscription pack, need handle expired</returns>
        public SubscriptionInfo GetSubscriptionInfo(IAPPackageType type)
        {
            var data = config.GetData(type);

            if (data == null)
            {
                Debug.LogError("[b100 - IAP] Product type " + type + " not found to purchase!!!");
                return null;
            }

            if (_storeController == null)
                return null;

            var product = _storeController.products.WithID(data.packageId);

            if (product == null)
            {
                Debug.LogError("[b100 - IAP] Cant fetching data product type " + data.productType + " !!!");
                return null;
            }
            
            // If the product doesn't have a receipt, then it wasn't purchased and the user is therefore not subscribed.
            if (product.receipt == null)
            {
                return null;
            }

            var subscriptionProduct = new SubscriptionManager(product, null);

            var info = subscriptionProduct.getSubscriptionInfo();
            
            return info;
        }


        public bool IsExpiredSubscription(IAPPackageType type)
        {
            var info = GetSubscriptionInfo(type);

            if (info == null)
            {
                return true;
            }

            return info.isExpired() == Result.True;
        }

        public int GetTimeRemainSubscription(IAPPackageType type)
        {
            var info = GetSubscriptionInfo(type);

            if (info == null)
            {
                return -1;
            }

            return (int) info.getRemainingTime().TotalSeconds;
        }


        public void PurchaseSubscription(IAPPackageType type, Action onPurchaseComplete = null)
        {
            if (!_isInited)
            {
                Debug.LogError("IAP is not inited. Return default");
                return;
            }
            
            _onBuyComplete = onPurchaseComplete;

            var data = config.GetData(type);

            if (data!= null)
            {
#if UNITY_ANDROID
                _subscriptionGroupMap[data.groupType].BuySubscription(data.packageId);
#endif
            }
            else
            {
                Debug.LogError("[b100 - IAP] Product type " + type + " not found to purchase!!!");
            }
        }
        

        #endregion


        
        
        public string GetPrice(IAPPackageType type)
        {
            var data = config.GetData(type);
            

            if (data!= null)
            {
                if (!_isInited)
                {
                    Debug.LogError("IAP is not inited. Return default");
                    return data.defaultPrice.ToString(CultureInfo.InvariantCulture);
                }
                
                var product = _storeController.products.WithID(data.packageId);

                if (product != null && product.availableToPurchase)
                {
                    return product.metadata.localizedPriceString;
                }
                else
                {
                    Debug.LogError("[b100 - IAP] Can get price");

                    return data.defaultPrice.ToString(CultureInfo.InvariantCulture);
                }
            }
            else
            {
                Debug.LogError("[b100 - IAP] Product type " + type + " not found to purchase!!!");
            }

            return "";
        }

        public bool CanPurchase(IAPPackageType package)
        {
            return config.GetData(package).maxTimesBuyCount < 0 || Gm.data.user.totalBuyCountMap[package] < config.GetData(package).maxTimesBuyCount;
        }

        public void RestorePurchase(Action<bool, string> onRestore)
        {
            if (!_isInited)
            {
                Debug.LogError("IAP is not inited. Return default");
                return;
            }
            
#if UNITY_IOS
            _appleExtensions.RestoreTransactions(onRestore);
#elif UNITY_ANDROID
            _googlePlayStoreExtensions.RestoreTransactions(onRestore);
#endif
            
        }

        bool HasProduct(IAPPackageType type)
        {
            var data = config.GetData(type);

            if (data == null)
                return false;

            var product = _storeController.products.WithID(data.packageId);
            
            return product!= null && product.hasReceipt;
        }
        

        #region Store Callback
        
        public void OnInitialized(IStoreController controller, IExtensionProvider extensions)
        {
            Debug.Log("In-App Purchasing successfully initialized");
            _storeController = controller;

            _isInited = true;
            
            OnIAPInited?.Invoke();
            
            
            var subscriptionMap = new Dictionary<SubscriptionGroupType, List<IAPPackageType>>(); 
            
            foreach (IAPPackageType type in Enum.GetValues(typeof(IAPPackageType)))
            {
                if (type == IAPPackageType.SdkPlaceHolder)
                    continue;
                
                var data = config.GetData(type);

                if (data != null)
                {
                    if (data.productType == ProductType.Subscription)
                    {
                        if (subscriptionMap.ContainsKey(data.groupType))
                        {
                            subscriptionMap[data.groupType].Add(data.packageType);
                        }
                        else
                        {
                            subscriptionMap.TryAdd(data.groupType, new List<IAPPackageType>() { data.packageType });
                        }
                    }
                }
                else
                {
                    Debug.LogError("[b100 - IAP] Init product type " + type + " failure!!!");
                }
            }
            
#if UNITY_IOS
            _appleExtensions = extensions.GetExtension<IAppleExtensions>();

            
            if (subscriptionMap.Count > 0)
            {
                foreach (var item in subscriptionMap)
                {
                    _subscriptionGroupMap.TryAdd(item.Key,
                        new SubscriptionGroup(controller, extensions,
                            item.Value.Select(x => config.GetData(x).packageId).ToArray()));
                }
            }
            
#elif UNITY_ANDROID
            _googlePlayStoreExtensions = extensions.GetExtension<IGooglePlayStoreExtensions>();
            
            if (subscriptionMap.Count > 0)
            {
                foreach (var item in subscriptionMap)
                {
                    _subscriptionGroupMap.TryAdd(item.Key,
                        new SubscriptionGroup(controller, extensions, config.upgradeSubscriptionProrationMode,
                            config.downgradeSubscriptionProrationMode,
                            item.Value.Select(x => config.GetData(x).packageId).ToArray()));
                }
            }
#endif
            
        }

        
        public void OnInitializeFailed(InitializationFailureReason error)
        {
            OnInitializeFailed(error, null);
        }

        public void OnInitializeFailed(InitializationFailureReason error, string message)
        {
            var errorMessage = $"Purchasing failed to initialize. Reason: {error}.";

            if (message != null)
            {
                errorMessage += $" More details: {message}";
            }

            Debug.Log(errorMessage);
        }

        public PurchaseProcessingResult ProcessPurchase(PurchaseEventArgs purchaseEvent)
        {
            //Retrieve the purchased product
            var product = purchaseEvent.purchasedProduct;

            var data = config.GetData(product.definition.id);

            if (data != null)
            {
                _onBuyComplete?.Invoke();
                
                Debug.Log("Purchase Complete - Product: " + data.productType);
            }

            
            return PurchaseProcessingResult.Complete;
        }

        public void OnPurchaseFailed(Product product, PurchaseFailureReason failureReason)
        {
            Debug.Log($"Purchase failed - Product: '{product.definition.id}', PurchaseFailureReason: {failureReason}");
        }
        

        public void OnPurchaseFailed(Product product, PurchaseFailureDescription failureDescription)
        {
            Debug.Log($"Purchase failed - Product: '{product.definition.id}'," +
                      $" Purchase failure reason: {failureDescription.reason}," +
                      $" Purchase failure details: {failureDescription.message}");
        }
        
        
        #endregion
    }
}