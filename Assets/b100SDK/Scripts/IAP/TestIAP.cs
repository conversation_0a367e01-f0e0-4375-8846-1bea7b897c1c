using System;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.UI.Components.Button;
using TMPro;
using UnityEngine;

namespace b100SDK.Scripts.IAP
{
    public class TestIAP : BhMonoBehavior
    {
        public BhButton button;
        
        public BhButton buttonRestore;

        public TMP_Text priceText;

        public TMP_Text valueText;

        public int value = 0;


        private void Start()
        {
            IAPManager.Instance.OnIAPInited.AddListener(UpdateValue);
            button.onClick.AddListener(Buy);
            buttonRestore.onClick.AddListener(Restore);

            valueText.text = value.ToString();
        }
        
        private void OnDisable()
        {
            IAPManager.Instance.OnIAPInited.RemoveListener(UpdateValue);
            button.onClick.RemoveListener(Buy);
            buttonRestore.onClick.RemoveListener(Restore);
        }

        private void Restore()
        {
            IAPManager.Instance.RestorePurchase((x, y) =>
            {
                Debug.Log("Restore status: " + x + " ----------- decs: " + y);
            });
        }

        private void Buy()
        {
            /*IAPManager.Instance.Purchase(IAPPackageType.Gold1, () =>
            {
                value += 100;
                valueText.text = value.ToString();
            });*/
        }

        private void UpdateValue()
        {
            //priceText.text = IAPManager.Instance.GetPrice(IAPPackageType.Gold1);
        }
        
        
    }
}