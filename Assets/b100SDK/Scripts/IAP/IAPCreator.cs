# if UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.Globalization;
using b100SDK.Scripts.Configs;
using b100SDK.Scripts.Configs.IAP;
using b100SDK.Scripts.Google.GoogleSpreadSheet;
using b100SDK.Scripts.Utilities.Tool;
using Cysharp.Threading.Tasks;
using Sirenix.OdinInspector;
using Sirenix.OdinInspector.Editor;
using Sirenix.Utilities;
using UnityEditor;
using UnityEngine;
using UnityEngine.Purchasing;

namespace b100SDK.Scripts.IAP
{
    public class IAPCreator : OdinEditorWindow
    {
        public enum CreateMode
        {
            Create,
            Sync,
        }
        
        private const string _IAP_ENUM_PATH = "Assets/b100SDK/Scripts/IAP/IAPEnums.cs";
        private const string _SAVE_PATH = "Assets/b100SDK/Configs/IAP";
        private IAPConfig _iapConfig;

        private static bool IsStartedCreateScript
        {
            get => PlayerPrefs.GetInt("BHIAPCreatorIsStarted") == 1;

            set => PlayerPrefs.SetInt("BHIAPCreatorIsStarted", value ? 1 : 0);
        }

        private static bool IsWaitingForCreateScript
        {
            get => PlayerPrefs.GetInt("BHIAPCreatorIsWaitingForCreateScript") == 1;

            set => PlayerPrefs.SetInt("BHIAPCreatorIsWaitingForCreateScript", value ? 1 : 0);
        }

        private static bool IsDone
        {
            get => PlayerPrefs.GetInt("BHIAPCreatorIsDone") == 1;

            set => PlayerPrefs.SetInt("BHIAPCreatorIsDone", value ? 1 : 0);
        }



        [ReadOnly]
        public CreateMode createMode;
        
        [Title("Create package")]
        public string packageType = string.Empty;
        public string packageId;
        public ProductType productType;

        [PropertySpace(10)]
        public int maxTimesBuyCount = -1;
        public string packageName;
        public float defaultPrice = 4.99f;

        [Title("Sync Data")]
        public TextAsset credentialTextAsset;
        public string spreadSheetId;
        public string sheetName;
        public string valueRangeInSheet;

        private IAPPackageType _parseType;
        
        
        
        public static void OpenWindow()
        {
            GetWindow<IAPCreator>("b100 IAP Creator").Show();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            
            IsDone = false;
            IsWaitingForCreateScript = false;
            IsStartedCreateScript = false;
        }

        private void Update()
        {
            if (!IsWaitingForCreateScript) 
                return;
            
            if (!IsStartedCreateScript)
                return;
            
            if (IsDone)
                return;

            if (!Enum.TryParse<IAPPackageType>(packageType, out var data))
            {
                Log("Wait for create new package........");
                return;
            }

            switch (createMode)
            {
                case CreateMode.Create:
                    OnCreateConfig();
                    LogSuccess("Complete create IAP package " + packageName + " --- type: " + packageType + " --- id: " + packageId);
                    break;
                case CreateMode.Sync:
                    OnCreateConfigSync();
                    LogSuccess("Complete sync IAP from spreadsheet!!!");
                    break;
                    
            }
            
            
            IsDone = true;
            IsStartedCreateScript = false;
            IsWaitingForCreateScript = false;
        }


        [PropertySpace(20)]
        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        void Create()
        {
            createMode = CreateMode.Create;
            
            logEntries.Clear();
            
            var newType = Enum.TryParse<IAPPackageType>(packageType, out _parseType);
            if (newType)
            {
                LogError("This package is existed. Please choose other name.");
            }
            else
            {
                OnCreateScript();
                IsWaitingForCreateScript = true;
                IsStartedCreateScript = true;
                IsDone = false;
            }
        }


        [PropertySpace(20)]
        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        void SyncData()
        {
            createMode = CreateMode.Sync;
            CreateScriptSync();
        }


        #region Sync IAP package from sheet

        
        SyncIap GetDataFromRow(IList<object> row)
        {
            var result = new SyncIap();

            result.id = int.Parse(row[0].ToString());

            if (string.IsNullOrEmpty(row[2].ToString()))
                return null;
            
            result.packageId = (string) row[2];
            result.packageName = (string) row[3];
            
            var productTypeInt = int.Parse(row[4].ToString());
            result.productType = productTypeInt == 1 ? ProductType.Consumable :
                productTypeInt == 2 ? ProductType.NonConsumable : ProductType.Subscription;
            
            if (float.TryParse(row[5].ToString().Replace(",", "."), NumberStyles.Any, CultureInfo.InvariantCulture, out float floatValue1))
            {
                result.price = floatValue1;
            }
            
            result.maxBuyCount = int.Parse(row[6].ToString());

            result.packageType = (string)row[1];
            
            return result;
        }

        async UniTask<List<SyncIap>> GetDataToSync()
        {
            var googleSheet = new GoogleSheetManager(credentialTextAsset);
            
            if (!googleSheet.IsInitedUserCredential)
            {
                await googleSheet.InitCredential();
            } 
            
            var correctRange = $"{sheetName}!{valueRangeInSheet}";

            var valueRange = await googleSheet.GetSingleRangeValue(spreadSheetId, correctRange);

            var values = valueRange.Values;

            var dataIap = new List<SyncIap>();
            
            if (values != null && values.Count > 1)
            {
                for (int i = 1; i < values.Count; i++)
                {
                    var row = values[i];
                    var convertData = GetDataFromRow(row);

                    if (convertData != null)
                    {
                        dataIap.Add(convertData);
                    }
                }
            }
            else
            {
                Debug.Log("No data found.");
            }
            
            dataIap.Sort((a, b) => a.id.CompareTo(b.id));
            
            return dataIap;
        }


        async void CreateScriptSync()
        {
            var dataIap = await GetDataToSync();
            
            if (dataIap.Count > 0)
            {
                foreach (var item in dataIap)
                {
                    Debug.Log("Id=" + item.id + " ---- packetId=" + item.packageId + " ---- packagename=" + item.packageName + " ---- price=" + item.price + " ---- packageType=" + item.packageType);
                    packageType = item.packageType;
                    AddNewEnum();
                }
                
                AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);
                IsWaitingForCreateScript = true;
                IsStartedCreateScript = true;
                IsDone = false;
            }
            else
            {
                Debug.LogError("No data found.");
            }
        }

        async void OnCreateConfigSync()
        {
            var dataIap = await GetDataToSync();
            
            if (dataIap.Count > 0)
            {
                foreach (var item in dataIap)
                {
                    IAPPackageConfig newConfig = ScriptableObject.CreateInstance<IAPPackageConfig>();

                    newConfig.packageType = Enum.Parse<IAPPackageType>(item.packageType);
                    newConfig.packageId = item.packageId;
                    newConfig.productType = item.productType;
                    newConfig.maxTimesBuyCount = item.maxBuyCount;

                    if (string.IsNullOrEmpty(item.packageName))
                    {
                        newConfig.packageName = item.packageId;
                    }
                    else
                    {
                        newConfig.packageName = item.packageName;
                    }

                    newConfig.defaultPrice = item.price;
            
                    var path = _SAVE_PATH + "/IAP Package " + newConfig.packageType + ".asset";
            
                    AssetDatabase.CreateAsset(newConfig, path);
                    AssetDatabase.SaveAssets();
                }
            }
            else
            {
                Debug.LogError("No data found.");
            }
            
            
            AddConfigToList();

        }

        
        
        [Serializable]
        public class SyncIap
        {
            public int id;
            public string packageType;
            public string packageId;
            public string packageName;
            public ProductType productType;// 1: Consumable, 2: Non-consumable, 3: Subscription
            public float price;
            public int maxBuyCount;
            
        }
        

        #endregion
        
        
        
        
        #region Create new IAP package
        
        
        private void OnCreateScript()
        {
            Log("Starting IAP package " + packageName + " --- id: " + packageId);
            
            AddNewEnum();
            AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);
        }

        private void OnCreateConfig()
        {
            CreateConfig();
            AddConfigToList();
        }

        private void CreateConfig()
        {
            IAPPackageConfig newConfig = ScriptableObject.CreateInstance<IAPPackageConfig>();

            newConfig.packageType = Enum.Parse<IAPPackageType>(this.packageType);
            newConfig.packageId = packageId;
            newConfig.productType = productType;

            if (packageName.Equals(""))
            {
                newConfig.packageName = packageName;
            }
            else
            {
                newConfig.packageName = packageName;
            }

            newConfig.defaultPrice = defaultPrice;
            
            var path = _SAVE_PATH + "/IAP Package " + newConfig.packageType + ".asset";
            
            AssetDatabase.CreateAsset(newConfig, path);
            AssetDatabase.SaveAssets();
        }

        private void AddNewEnum()
        {
            var enumScript = System.IO.File.ReadAllText(_IAP_ENUM_PATH);
            if (enumScript.Contains(packageType))
                return;

            var index = enumScript.IndexOf("SdkPlaceHolder", StringComparison.Ordinal);

            var newEnumScript = enumScript.Insert(index, packageType + ",\n    ");

            System.IO.File.WriteAllText(_IAP_ENUM_PATH, newEnumScript);
        }
        
        
        private void AddConfigToList()
        {
            _iapConfig = ConfigTool.GetIAPConfig();
            _iapConfig.GetConfig();
            EditorUtility.SetDirty(_iapConfig);
            AssetDatabase.SaveAssets();
        }


        #endregion


        #region Log
        
        private List<LogEntry> logEntries = new List<LogEntry>();
        private Vector2 scrollPosition;
        
        private void Log(string message = "Test Log")
        {
            AddLog(message, Color.white);
        }
        private void LogWarning(string message)
        {
            AddLog(message, Color.yellow);
        }
        
        private void LogError(string message)
        {
            AddLog(message, Color.red);
        }        
        
        private void LogSuccess(string message)
        {
            AddLog(message, Color.green);
        }

        protected override void OnGUI()
        {
            
            
            base.OnGUI(); 

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Width(position.width), GUILayout.Height(position.height-420));

            EditorGUILayout.BeginVertical(new GUILayoutOption[]
            {
                GUILayout.MinHeight(20),
            
            });
            
            foreach (var logEntry in logEntries)
            {
                GUI.contentColor = logEntry.color;
                GUILayout.Label(logEntry.message);
            }

            GUI.contentColor = Color.white;

            EditorGUILayout.EndScrollView();
            
            EditorGUILayout.EndVertical();

        }

        private void AddLog(string message, Color color)
        {
            logEntries.Insert(0, new LogEntry { message = message, color = color });
            // Set scroll position to the bottom
            scrollPosition.y = 0f;
        }

        private class LogEntry
        {
            public string message;
            public Color color;
        }

        #endregion
    }
    
}
#endif
