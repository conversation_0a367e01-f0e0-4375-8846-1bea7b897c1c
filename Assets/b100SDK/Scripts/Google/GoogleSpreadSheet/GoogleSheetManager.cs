using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using Google.Apis.Auth.OAuth2;
using Google.Apis.Services;
using Google.Apis.Sheets.v4;
using Google.Apis.Sheets.v4.Data;
using Newtonsoft.Json;
using Sirenix.OdinInspector;
using UnityEngine;

namespace b100SDK.Scripts.Google.GoogleSpreadSheet
{
    public class GoogleSheetManager
    {
        private UserCredential _userCredential;
        public bool IsInitedUserCredential { get; private set; }
        
        private GoogleClientIdInstall _clientId;

        public GoogleSheetManager(TextAsset credentialTextAsset)
        {
            _clientId = JsonConvert.DeserializeObject<GoogleClientId>(credentialTextAsset.text).installed;
        }


        [Button]
        public async UniTask InitCredential()
        {
            IsInitedUserCredential = false;

            var userCredential = await GoogleAuthentication.GetAuthenticationAsync(_clientId.client_id,
                _clientId.client_secret, new[] { SheetsService.Scope.Spreadsheets });

            if (userCredential != null)
            {
                _userCredential = userCredential;
                IsInitedUserCredential = true;
                
                Debug.Log("Create user credential successed");
            }
        }


        #region Sheet Methods

        
        /// <summary>
        /// Create new spreadsheet
        /// </summary>
        /// <param name="spreadSheetName"></param>
        /// <param name="sheetName"></param>
        /// <returns></returns>
        public async UniTask<Spreadsheet> CreateNewSpreadSheet(string spreadSheetName, string sheetName = null)
        {
            if (!IsInitedUserCredential)
            {
                await InitCredential();
            }
            
            if (string.IsNullOrEmpty(spreadSheetName))
            {
                Debug.LogError("Name is null. Please set a valid spread sheet name");
                return null;
            }
            
            using (var sheetServices = new SheetsService( new BaseClientService.Initializer() {HttpClientInitializer = _userCredential}))
            {
                var request = sheetServices.Spreadsheets.Create(new Spreadsheet()
                {
                    Sheets = new List<Sheet>()
                    {
                        new Sheet()
                        {
                            Properties = new SheetProperties()
                            {
                                Title = sheetName == null ? spreadSheetName : sheetName,
                            }
                        }
                    },
                    Properties = new SpreadsheetProperties()
                    {
                        Title = spreadSheetName,
                    }
                });

                return request.Execute();
            }
        }
        
        
        
        /// <summary>
        /// Get spreadsheet existed with spreadsheet Id
        /// </summary>
        /// <param name="spreadSheetId"></param>
        /// <returns></returns>
        public async UniTask<Spreadsheet> GetSpreadsheet(string spreadSheetId)
        {
            if (!IsInitedUserCredential)
            {  
                await InitCredential();
            }
            
            if (string.IsNullOrEmpty(spreadSheetId))
            {
                Debug.LogError("Spread sheet id is null. Please set a valid spread sheet id");
                return null;
            }

            using (var sheetServices = new SheetsService(new BaseClientService.Initializer()
                       { HttpClientInitializer = _userCredential }))
                return sheetServices.Spreadsheets.Get(spreadSheetId).Execute();
        }
        
        
        
        /// <summary>
        /// Get value in single range in spreadsheet with id and value range
        /// </summary>
        /// <param name="spreadSheetId"></param>
        /// <param name="valueRange"></param>
        /// <returns></returns>
        public async UniTask<ValueRange> GetSingleRangeValue(string spreadSheetId, string valueRange)
        {
            if (!IsInitedUserCredential)
            {
                await InitCredential();
            }
            
            if (string.IsNullOrEmpty(spreadSheetId))
            {
                Debug.LogError("Spread sheet id is null. Please set a valid spread sheet id");
                return null;
            }

            if (string.IsNullOrEmpty(valueRange))
            {
                Debug.LogError("Value range is null. Please set a valid value range");
                return null;
            }

            using (var sheetServices = new SheetsService(new BaseClientService.Initializer()
                       { HttpClientInitializer = _userCredential }))
            {
                var getValue = sheetServices.Spreadsheets.Values.Get(spreadSheetId, valueRange);
                return getValue.Execute();
            }
            
        }
        
        
        
        /// <summary>
        /// Remove value in single range in spreadsheet with id and value range
        /// </summary>
        /// <param name="spreadSheetId"></param>
        /// <param name="valueRange"></param>
        public async void RemoveSingleValue(string spreadSheetId, string valueRange)
        {
            if (!IsInitedUserCredential)
            {
                await InitCredential();
            }
            
            if (string.IsNullOrEmpty(spreadSheetId))
            {
                Debug.LogError("Spread sheet id is null. Please set a valid spread sheet id");
                return;
            }

            if (string.IsNullOrEmpty(valueRange))
            {
                Debug.LogError("Value range is null. Please set a valid value range");
                return;
            }

            using (var sheetServices = new SheetsService(new BaseClientService.Initializer()
                       { HttpClientInitializer = _userCredential }))
            {
                var removeValueRequest = sheetServices.Spreadsheets.Values.Clear(new ClearValuesRequest(), spreadSheetId, valueRange);
                removeValueRequest.Execute();
            }
        }
        
        
        
        /// <summary>
        /// Get value in multiple range in spreadsheet with id and value range
        /// </summary>
        /// <param name="spreadSheetId"></param>
        /// <param name="ranges"></param>
        /// <returns></returns>
        public async UniTask<BatchGetValuesResponse> GetMultipleValue(string spreadSheetId, string[] ranges)
        {
            if (!IsInitedUserCredential)
            {
                await InitCredential();
            }
            
            if (string.IsNullOrEmpty(spreadSheetId))
            {
                Debug.LogError("Spread sheet id is null. Please set a valid spread sheet id");
                return null;

            }

            if (ranges == null || ranges.Length == 0)
            {
                Debug.LogError("Value range is null or length is zero. Please set a valid value range");
                return null;
            }

            using (var sheetServices = new SheetsService(new BaseClientService.Initializer()
                       { HttpClientInitializer = _userCredential }))
            {
                var getValue = sheetServices.Spreadsheets.Values.BatchGet(spreadSheetId);
                getValue.Ranges = ranges;
                return getValue.Execute();
            }
        }

        
        
        /// <summary>
        /// Remove value in multiple ranges in spreadsheet with id and value ranges
        /// </summary>
        /// <param name="spreadSheetId"></param>
        /// <param name="ranges"></param>
        public async void RemoveMultipleValue(string spreadSheetId, string[] ranges)
        {
            if (!IsInitedUserCredential)
            {
                await InitCredential();
                
            }
            
            if (string.IsNullOrEmpty(spreadSheetId))
            {
                Debug.LogError("Spread sheet id is null. Please set a valid spread sheet id");
                return;
            }

            if (ranges == null || ranges.Length == 0)
            {
                Debug.LogError("Value range is null or length is zero. Please set a valid value range");
                return;
            }

            using (var sheetServices = new SheetsService(new BaseClientService.Initializer()
                       { HttpClientInitializer = _userCredential }))
            {
                var clearValue =
                    sheetServices.Spreadsheets.Values.BatchClear(new BatchClearValuesRequest() { Ranges = ranges },
                        spreadSheetId);
                clearValue.Execute();
            }
        }


        /// <summary>
        /// Get all column name in a table of sheet
        /// </summary>
        /// <param name="spreadSheetId"></param>
        /// <param name="sheetName"></param>
        /// <param name="startTitle"></param>
        /// <returns></returns>
        [Button]
        public async UniTask<HashSet<string>> GetAllColName(string spreadSheetId, string sheetName, int startRow = 1)
        {
            var result = new HashSet<string>();
            
            if (!IsInitedUserCredential)
            {
                    await InitCredential();
            }
            
            if (string.IsNullOrEmpty(spreadSheetId))
            {
                Debug.LogError("Spread sheet id is null. Please set a valid spread sheet id");
                return null;
            }            
            
            if (string.IsNullOrEmpty(sheetName))
            {
                Debug.LogError("Sheet name is null. Please set a valid sheet name");
                return null;
            }
            
            
            using (var sheetServices = new SheetsService(new BaseClientService.Initializer()
                       { HttpClientInitializer = _userCredential }))
            {
                var correctRange = $"{sheetName}!{startRow}:{startRow}"; 
                
                var getValue = sheetServices.Spreadsheets.Values.Get(spreadSheetId, correctRange);
                
                var values = getValue.Execute().Values;
                
                if (values != null && values.Count > 0)
                {
                    var headerRow = values[0];
                    for (int i = 0; i < headerRow.Count; i++)
                    {
                        if (!string.IsNullOrEmpty(headerRow[i].ToString()))
                        {
                            result.Add(headerRow[i].ToString());
                        }
                    }
                }
                else
                {
                    Debug.LogError("No header row found.");
                }
                
            }

            return result;
        }



        public async UniTask<List<T>> GetDataInColumn<T>(string spreadSheetId, string sheetName, int startTitleIndex = 1, string columnName = null)
        {
            if (!IsInitedUserCredential)
            {
                await InitCredential();
            }
            
            if (string.IsNullOrEmpty(spreadSheetId))
            {
                Debug.LogError("Spread sheet id is null. Please set a valid spread sheet id");
                return null;
            }

            if (string.IsNullOrEmpty(sheetName))
            {
                Debug.LogError("Sheet name is null. Please set a valid sheet name");
            }

            var allColumns = await GetAllColName(spreadSheetId, sheetName, startTitleIndex);
            
            if (!allColumns.Contains(columnName))
            {
                Debug.LogError($"Column name '{columnName}' not found.");
                return null;
            }

            var result = new List<T>();

            /*int columnIndex = allColumns[columnName];
            char columnLetter = (char)('A' + columnIndex); // Chuyển chỉ số cột thành ký tự cột
            var range = $"{Sheet}!{columnLetter}:{columnLetter}"; // Đọc toàn bộ cột theo ký tự cột
            SpreadsheetsResource.ValuesResource.GetRequest request =
                service.Spreadsheets.Values.Get(_SPREAD_SHEET_ID, range);

            ValueRange response = request.Execute();
            IList<IList<object>> values = response.Values;

            if (values != null && values.Count > 1)
            {
                for (int i = 1; i < values.Count; i++) // Bỏ qua dòng tiêu đề
                {
                    var row = values[i];
                    if (row.Count > 0)
                    {
                        Debug.Log(row[0].ToString());
                    }
                }
            }
            else
            {
                Debug.Log("No data found.");
            }*/

            return result;
        } 

        #endregion
        
        
    }

    [Serializable]
    public class GoogleClientId
    {
        public GoogleClientIdInstall installed { get; set; }
    }    
    
    [Serializable]
    public class GoogleClientIdInstall
    {
        public string client_id { get; set; }
        public string client_secret { get; set; }
    }
}