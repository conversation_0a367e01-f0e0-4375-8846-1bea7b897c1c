using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using b100SDK.Scripts.Base;
using Sirenix.OdinInspector;
using UnityEngine;

namespace b100SDK.Scripts.Google.GoogleSpreadSheet
{
    public class TestGoogleSheet : BhMonoBehavior
    {
        [SerializeField]
        private TextAsset credentialTextAsset;
        
        [SerializeField]
        private string spreadSheetId;
        
        private GoogleSheetManager _googleSheetManager;

        void InitGoogleSheet()
        {
            if (_googleSheetManager == null)
            {
                _googleSheetManager = new GoogleSheetManager(credentialTextAsset);
            }
        }

        [Button]
        async void TestGetColumn(string sheetName, int startTable = 1)
        {
            if (!_googleSheetManager.IsInitedUserCredential)
            {
                await _googleSheetManager.InitCredential();
            }
            
            var result = await _googleSheetManager.GetAllColName(spreadSheetId, sheetName, startTable);
            
            Debug.Log("Total col: "+ result.Count);

            if (result.Count > 0)
            {
                foreach (var item in result)
                {
                    Debug.Log(item);
                }
            }
        }

        [Button]
        async void GetData(string sheetName = "Bản sao của Trang tính1", string range = "J18:Q33")
        {
            InitGoogleSheet();
            
            if (!_googleSheetManager.IsInitedUserCredential)
            {
                await _googleSheetManager.InitCredential();
            }
            
            var correctRange = $"{sheetName}!{range}";

            var valueRange = await _googleSheetManager.GetSingleRangeValue(spreadSheetId, correctRange);

            var values = valueRange.Values;

            var dataIap = new List<TestSyncIap>();
            
            if (values != null && values.Count > 1)
            {
                for (int i = 1; i < values.Count; i++) // Bỏ qua dòng tiêu đề
                {
                    var row = values[i];
                    dataIap.Add(GetDataFromRow(row));
                }
            }
            else
            {
                Debug.Log("No data found.");
            }

            if (dataIap.Count > 0)
            {
                foreach (var item in dataIap)
                {
                    Debug.Log("Id=" + item.id + " ---- packetId=" + item.packageId + " ---- packagename=" + item.packageName + " ---- price=" + item.price + " ---- packageType=" + item.packageType);
                }
            }
            else
            {
                Debug.LogError("No data found.");
            }
        }
        
        void ProcessRow(IList<object> row)
        {
            List<string> rowData = new List<string>();

            foreach (var cell in row)
            {
                // Bỏ qua các ô trống ở đầu
                if (cell != null && !string.IsNullOrEmpty(cell.ToString()))
                {
                    rowData.Add(cell.ToString());
                }
            }

            if (rowData.Count > 0)
            {
                // In ra dữ liệu của từng hàng
                Debug.Log("Row Data: " + string.Join(", ", rowData));
            }
        }

        TestSyncIap GetDataFromRow(IList<object> row)
        {
            var result = new TestSyncIap();

            result.id = int.Parse(row[0].ToString());
            result.packageId = (string) row[1];
            result.packageName = (string) row[2];
            
            /*if (float.TryParse(row[4].ToString(), NumberStyles.Any, CultureInfo.InvariantCulture, out float floatValue))
            {
                result.price = floatValue;
            }
            else */if (float.TryParse(row[4].ToString().Replace(",", "."), NumberStyles.Any, CultureInfo.InvariantCulture, out float floatValue1))
            {
                result.price = floatValue1;
            }
            //result.price = float.Parse(row[4].ToString());

            result.packageType = "PackageIap" + result.id;
            
            return result;
        }

        [Button]
        async void TestMultipleData()
        {
            if (!_googleSheetManager.IsInitedUserCredential)
            {
                await _googleSheetManager.InitCredential();
            }
            
            var sheet = await _googleSheetManager.GetSpreadsheet(spreadSheetId);
            
            Debug.Log("Sheet id: " + sheet.SpreadsheetId);

            var quantitySkip = await _googleSheetManager.GetSingleRangeValue(spreadSheetId, "E16");
            Debug.Log("Quantity Skip: " + quantitySkip.Values.First().First());

            string numberIndexRange = "A1:A16";
            string quantityRange = "E1:R16";
            string priceRange = "F1:F16";

            string[] valueRanges = new[] { numberIndexRange, quantityRange, priceRange };

            var multipleResponse = await _googleSheetManager.GetMultipleValue(spreadSheetId, valueRanges);

            var numberDataResponse = multipleResponse.ValueRanges.ElementAt(0);
            var quantityDataResponse = multipleResponse.ValueRanges.ElementAt(1);
            var priceDataResponse = multipleResponse.ValueRanges.ElementAt(2);

            for (int i = 0; i < numberDataResponse.Values.Count; i++)
            {
                var index = numberDataResponse.Values[i].First();
                var quantity = quantityDataResponse.Values[i].First();
                var price = priceDataResponse.Values[i].First();
                
                Debug.Log("Index = " + index + " --- quantity = " + quantity + " --- price = " + price);
            }
        }
        
        [Serializable]
        public class TestSyncIap
        {
            public int id;
            public string packageId;
            public string packageName;
            public float price;

            public string packageType;
        }
    }
}