using System;
using System.Collections;
using b100SDK.Scripts.Analytic;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.UI;
using b100SDK.Scripts.Utilities;
using Sirenix.OdinInspector;
using UnityEngine;
using Random = UnityEngine.Random;

#if ENABLE_ADS && ENABLE_ADS_ADMOB
using GoogleMobileAds.Api;
#endif

namespace b100SDK.Scripts.Ads
{
    public class NativeOverlayAdItem : BhMonoBehavior
    {
        [SerializeField]

#if UNITY_IOS
        private string nativeAdUnitId = "ENTER_ANDROID_NATIVE_OVERLAY_AD_UNIT_ID_HERE";
        
#else
        private string nativeAdUnitId = "ENTER_ANDROID_NATIVE_OVERLAY_AD_UNIT_ID_HERE";
        
#endif

        [Space]
        [SerializeField]
        private RectTransform targetRectTransform;
        
        [Space]
        [SerializeField]
        private GameObject container;

        [SerializeField]
        private GameObject loading;

        [SerializeField]
        private Canvas canvas;
        
        [SerializeField]
        private int x = 100;
        
        [SerializeField]
        private int y = 100;


        [Space(20)]
        [SerializeField]
        private bool enableDebug;

#if ENABLE_ADS && ENABLE_ADS_ADMOB

        /// <summary>
        /// Define our native ad advanced options.
        /// </summary>
        public NativeAdOptions Option = new NativeAdOptions
        {
            AdChoicesPlacement = AdChoicesPlacement.TopRightCorner,
            MediaAspectRatio = MediaAspectRatio.Any,
        };

        /// <summary>
        /// Define our native ad template style.
        /// </summary>
        public NativeTemplateStyle Style = new NativeTemplateStyle
        {
            TemplateId = NativeTemplateId.Medium,
        };

        private NativeOverlayAd _nativeOverlayAd;
        private int _retryAttempts;
        

        private void Start()
        {
            StopAllCoroutines();
            StartCoroutine(LoadAdCoroutine());
        }


        IEnumerator LoadAdCoroutine()
        {
            while (!AdManager.Instance || !AdManager.Instance.IsAdmobInited)
            {
                yield return null;
            }

            var randomTime = Random.Range(1f, 5f);
            
            yield return new WaitForSeconds(randomTime);
            
            LoadAd();
        }


        /// <summary>
        /// Loads the ad.
        /// </summary>
        public void LoadAd()
        {
            // Clean up the old ad before loading a new one.
            if (_nativeOverlayAd != null)
            {
                DestroyAd();
            }

            if (enableDebug)
            {
                Debug.Log("Loading native overlay ad.");
            }

            // Create our request used to load the ad.
            var adRequest = new AdRequest();

            // Send the request to load the ad.
            NativeOverlayAd.Load(nativeAdUnitId, adRequest, Option,
                (NativeOverlayAd ad, LoadAdError error) =>
            {
                // If the operation failed with a reason.
                if (error != null)
                {
                    if (enableDebug)
                    {
                        Debug.LogError("Native Overlay ad failed to load an ad with error : " + error);
                    }
                    
                    HandleNativeOverlayFailToLoad();
                    return;
                }
                // If the operation failed for unknown reasons.
                // This is an unexpected error, please report this bug if it happens.
                if (ad == null)
                {
                    if (enableDebug)
                    {
                        Debug.LogError("Unexpected error: Native Overlay ad load event fired with " +
                        " null ad and null error.");
                    }
                    
                    HandleNativeOverlayFailToLoad();
                    return;
                }

                // The operation completed successfully.

                if (enableDebug)
                {
                    Debug.Log("Native Overlay ad loaded with response : " + ad.GetResponseInfo());
                }
                
                _retryAttempts = 0;
                _nativeOverlayAd = ad;

                // Register to ad events to extend functionality.
                RegisterEventHandlers(ad);
                
                RenderAd();
            });
        }

        void HandleNativeOverlayFailToLoad()
        {
            _retryAttempts++;
            var delay = Mathf.Pow(2, Mathf.Min(3, _retryAttempts));
            Invoke(nameof(LoadAd), delay);
        }

        private void RegisterEventHandlers(NativeOverlayAd ad)
        {
            // Raised when the ad is estimated to have earned money.
            ad.OnAdPaid += (AdValue adValue) =>
            {
                RevenueManager.LogRevenue(new AdsRevenue()
                {
                    adFormat = "nativeOverlayAd",
                    adUnitId =  nativeAdUnitId,
                    networkName = "admob",
                    networkPlacement = "admob",
                    placement = "admob",
                    revenue = adValue.Value/1000000f,
                    currencyCode = adValue.CurrencyCode,
                });
            };                               
            // Raised when an impression is recorded for an ad.
            ad.OnAdImpressionRecorded += () =>
            {
                var randomTime = Random.Range(1, 5);
                
                Invoke(nameof(LoadAd), randomTime);
                //LoadAd();
            };
            // Raised when a click is recorded for an ad.
            ad.OnAdClicked += () =>
            {
            };
            // Raised when the ad opened full screen content.
            ad.OnAdFullScreenContentOpened += () =>
            {
            };
            // Raised when the ad closed full screen content.
            ad.OnAdFullScreenContentClosed += () =>
            {
            };
        }

        /// <summary>
        /// Shows the ad.
        /// </summary>
        public void ShowAd()
        {
            if (_nativeOverlayAd != null)
            {
                if (enableDebug)
                {
                    Debug.Log("Showing Native Overlay ad.");
                }
                
                _nativeOverlayAd.Show();
            }
            
            container?.SetActiveWithChecker(true);
            loading?.SetActiveWithChecker(false);
        }

        /// <summary>
        /// Hides the ad.
        /// </summary>
        public void HideAd()
        {
            if (_nativeOverlayAd != null)
            {
                if (enableDebug)
                {
                    Debug.Log("Hiding Native Overlay ad.");
                }
                
                _nativeOverlayAd.Hide();
            }
            
            container?.SetActiveWithChecker(false);
            loading?.SetActiveWithChecker(true);
        }

        /// <summary>
        /// Renders the ad.
        /// </summary>
        public void RenderAd()
        {
            if (_nativeOverlayAd != null)
            {
                if (enableDebug)
                {
                    Debug.Log("Rendering Native Overlay ad.");
                }

                // Renders a native overlay ad at the default size
                // and anchored to the bottom of the screne.

                var adSize = new AdSize((int)targetRectTransform.sizeDelta.x, (int)targetRectTransform.sizeDelta.y);

                var screenPosition = GetAnchorPositionInScreenSpace(targetRectTransform, GUIManager.Instance.root);
                var position = ConvertToDpi(screenPosition);
                
                _nativeOverlayAd.RenderTemplate(Style, adSize, position.x, position.y);
                
                container?.SetActiveWithChecker(true);
                loading?.SetActiveWithChecker(false);
            }
        }

        /// <summary>
        /// Destroys the ad.
        /// When you are finished with the ad, make sure to call the Destroy()
        /// method before dropping your reference to it.
        /// </summary>
        public void DestroyAd()
        {
            if (_nativeOverlayAd != null)
            {
                if (enableDebug)
                {
                    Debug.Log("Destroying Native Overlay ad.");
                }
                
                _nativeOverlayAd.Destroy();
                _nativeOverlayAd = null;
            }

            // Inform the UI that the ad is not ready.
            container?.SetActiveWithChecker(false);
            loading?.SetActiveWithChecker(true);
        }

        public void SetPosition(int x, int y)
        {
            this.x = x;
            this.y = y;
            LoadAd();
        }

        /// <summary>
        /// Logs the ResponseInfo.
        /// </summary>
        public void LogResponseInfo()
        {
            if (_nativeOverlayAd != null)
            {
                var responseInfo = _nativeOverlayAd.GetResponseInfo();
                if (responseInfo != null)
                {
                    Debug.Log(responseInfo);
                }
            }
        }

        (int x, int y) ConvertToDpi(Vector2 vector2)
        {
            var dpi = Screen.dpi / 160f;
            var x = (int)(vector2.x / dpi);
            var y = (int)((Screen.height - (int)vector2.y) / dpi);
            return (x, y);
        }
        
        Vector2 GetAnchorPositionInScreenSpace(RectTransform rectTransform, Canvas canvas)
        {
            Vector2 screenPoint = Vector2.zero;

            // Ensure the Canvas is set to Screen Space
            if (canvas.renderMode == RenderMode.ScreenSpaceOverlay)
            {
                // Directly convert anchor position to screen space
                screenPoint = rectTransform.position;
            }
            else if (canvas.renderMode == RenderMode.ScreenSpaceCamera)
            {
                // Convert world position to screen position
                screenPoint = RectTransformUtility.WorldToScreenPoint(canvas.worldCamera, rectTransform.position);
            }
            else
            {

                if (enableDebug)
                {
                    Debug.LogError("Canvas is not in a screen space render mode.");
                }
            }

            return screenPoint;
        }

#endif

        
    }
}