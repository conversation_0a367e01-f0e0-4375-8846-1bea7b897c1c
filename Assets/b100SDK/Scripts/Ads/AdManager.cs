using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using b100SDK.Scripts.Configs;
using b100SDK.Scripts.Configs.Ads;
using b100SDK.Scripts.DesignPatterns;
using b100SDK.Scripts.Utilities;
using Cysharp.Threading.Tasks;
using QFSW.QC;
using Sirenix.OdinInspector;
using UnityEngine;

namespace b100SDK.Scripts.Ads
{
    public class AdManager : Singleton<AdManager>
    {
        
        [SerializeField] private SerializedDictionary<MediationType, AdsInstance> instanceMap;
        [Space] 
        [SerializeField] private IAAConfig config;

        [Space]
        [SerializeField]
        private bool isDebugAds;

        [Space]
        [SerializeField]
        private LogInfo logInfo = LogInfo.Test;
        
        private static Dictionary<MediationType, AdsInstance> _adInstanceMaps = new();

        private static SerializedDictionary<AdType, MediationType> _adMediationMaps = new();

        private static bool _canShowAds;
        private static bool _isDebugAds;

        private static bool _isInitedComplete;
        
        
        public bool IsDebugAds
        {
            get
            {
                return isDebugAds;
            }
            set
            {
                isDebugAds = value;
                UpdateStatusDebugAds();
            }
        }
        
        
        public bool IsAdmobInited { get; private set; }
        

        private void Start()
        {
            AdsInstance.logInfo = logInfo;
            
            _canShowAds = Gm.EnableAds;
            _isDebugAds = isDebugAds;

            _isInitedComplete = false;
            
            /*LoadMediation();
            
            InitMediationInstance();

            _isInitedComplete = true;*/
        }

        void UpdateStatusDebugAds()
        {
            _isDebugAds = isDebugAds;
        }
        

        void LoadMediation()
        {
            if (_isDebugAds)
                return;
            
            _adMediationMaps.Clear();

            foreach (var item in config.adDefaultMediationMaps)
            {
                _adMediationMaps.TryAdd(item.Key, item.Value);
            }
        }

        void InitMediationInstance()
        {
            if (_isDebugAds)
                return;
            
            foreach (var item in instanceMap)
            {
                _adInstanceMaps.TryAdd(item.Key, item.Value);
            }

            if (_adInstanceMaps.Count <= 0)
            {
                BhDebug.Log("Failed to init mediation", BhColor.Red);
            }
            else
            {
                var adCount = _adMediationMaps.Values.Count(x => x == MediationType.MAX);

                if (adCount >= 1)
                {
                    InitMaxApplovin();
                }
                
                adCount = _adMediationMaps.Values.Count(x => x == MediationType.AdMob);
                
                if (adCount >= 1)
                {
                    InitAdmob();
                }
            }
        }
        
        
        
        

        #region Handler Init Mediation

        
        private async void DelayAction(float delaySeconds, Action action)
        {
            await UniTask.Delay((int) (delaySeconds * 1000));
            action?.Invoke();
        }
        

        
        

        #region Max
        
        
        void InitMaxApplovin()
        {
            _adInstanceMaps[MediationType.MAX].Init(() =>
            {
                InitAppOpenMax();
            }, () =>
            {
                InitBannerMax();
                InitInterstitialMax();
                InitRewardMax();
                InitRewardInterMax();
                InitMRecMax();
            });
        }

        public void InitAppOpenMax()
        {
            if (!_adMediationMaps.ContainsKey(AdType.AppOpen))
            {
                return;
            }
                
            if (_adMediationMaps[AdType.AppOpen] != MediationType.MAX)
            {
                return;
            }

            switch (config.maxApplovinConfig.appOpenData.adLoadType)
            {
                case AdLoadType.AutoLoad:
                    InitAppOpenMaxStatic();
                    break;

                case AdLoadType.DelayLoad:
                    DelayAction(config.maxApplovinConfig.appOpenData.delayLoadTime, () =>
                    {
                        InitAppOpenMaxStatic();
                    });
                    break;

                case AdLoadType.ManualLoad:
                    break;
            }
        }



        void InitInterstitialMax()
        {
            if (!_adMediationMaps.ContainsKey(AdType.Inter))
            {
                return;
            }
                
            if (_adMediationMaps[AdType.Inter] != MediationType.MAX)
            {
                return;
            }

            switch (config.maxApplovinConfig.interData.adLoadType)
            {
                case AdLoadType.AutoLoad:
                    InitInterstitialMaxStatic();
                    break;

                case AdLoadType.DelayLoad:
                    DelayAction(config.maxApplovinConfig.interData.delayLoadTime, () =>
                    {
                        InitInterstitialMaxStatic();
                    });
                    break;

                case AdLoadType.ManualLoad:
                    break;
            }
        }



        void InitRewardMax()
        {
            if (!_adMediationMaps.ContainsKey(AdType.Reward))
            {
                return;
            }
                
            if (_adMediationMaps[AdType.Reward] != MediationType.MAX)
            {
                return;
            }

            switch (config.maxApplovinConfig.rewardData.adLoadType)
            {
                case AdLoadType.AutoLoad:
                    InitRewardMaxStatic();
                    break;

                case AdLoadType.DelayLoad:
                    DelayAction(config.maxApplovinConfig.rewardData.delayLoadTime, () =>
                    {
                        InitRewardMaxStatic();
                    });
                    break;

                case AdLoadType.ManualLoad:
                    break;
            }
        }
        
        
        
        void InitRewardInterMax()
        {
            if (!_adMediationMaps.ContainsKey(AdType.RewardInter))
            {
                return;
            }
                
            if (_adMediationMaps[AdType.RewardInter] != MediationType.MAX)
            {
                return;
            }

            switch (config.maxApplovinConfig.rewardInterData.adLoadType)
            {
                case AdLoadType.AutoLoad:
                    InitRewardInterMaxStatic();
                    break;

                case AdLoadType.DelayLoad:
                    DelayAction(config.maxApplovinConfig.rewardInterData.delayLoadTime, () =>
                    {
                        InitRewardInterMaxStatic();
                    });
                    break;

                case AdLoadType.ManualLoad:
                    break;
            }
        }
        
        
        
        void InitBannerMax()
        {
            if (!_adMediationMaps.ContainsKey(AdType.Banner))
            {
                return;
            }
                
            if (_adMediationMaps[AdType.Banner] != MediationType.MAX)
            {
                return;
            }

            switch (config.maxApplovinConfig.bannerData.adLoadType)
            {
                case AdLoadType.AutoLoad:
                    InitBannerMaxStatic();
                    break;

                case AdLoadType.DelayLoad:
                    DelayAction(config.maxApplovinConfig.rewardInterData.delayLoadTime, () =>
                    {
                        InitBannerMaxStatic();
                    });
                    break;

                case AdLoadType.ManualLoad:
                    break;
            }
        }
        
        
        
        void InitMRecMax()
        {
            if (!_adMediationMaps.ContainsKey(AdType.MRec))
            {
                return;
            }
                
            if (_adMediationMaps[AdType.MRec] != MediationType.MAX)
            {
                return;
            }

            switch (config.maxApplovinConfig.mrecData.adLoadType)
            {
                case AdLoadType.AutoLoad:
                    InitMRecMaxStatic();
                    break;

                case AdLoadType.DelayLoad:
                    DelayAction(config.maxApplovinConfig.mrecData.delayLoadTime, () =>
                    {
                        InitMRecMaxStatic();
                    });
                    break;

                case AdLoadType.ManualLoad:
                    break;
            }
        }


        public static void InitAppOpenMaxStatic()
        {
            _adInstanceMaps[MediationType.MAX].InitAppOpenAd();
        }
        
        public static void InitInterstitialMaxStatic()
        {
            _adInstanceMaps[MediationType.MAX].InitInterstitialAd();
        }
        
        public static void InitRewardMaxStatic()
        {
            _adInstanceMaps[MediationType.MAX].InitRewardedAd();
        }
        
        public static void InitRewardInterMaxStatic()
        {
            _adInstanceMaps[MediationType.MAX].InitRewardedInterstitialAd();
        }
        
        public static void InitBannerMaxStatic()
        {
            _adInstanceMaps[MediationType.MAX].InitBannerAd();
        }
        
        public static void InitMRecMaxStatic()
        {
            _adInstanceMaps[MediationType.MAX].InitMRECAd();
        }

        #endregion




        #region Admob

        
        void InitAdmob()
        {
            _adInstanceMaps[MediationType.AdMob].Init(() =>
            {
                InitAppOpenAdmob();
            }, () =>
            {
                InitBannerAdmob();
                
                InitInterAdmob();
                InitRewardAdmob();
                InitRewardInterAdmob();
                
                
                InitMRecAdmob();
                
                IsAdmobInited = true;
            });
        }
        
       
        
        void InitAppOpenAdmob()
        {
            if (!_adMediationMaps.ContainsKey(AdType.AppOpen))
            {
                return;
            }
                
            if (_adMediationMaps[AdType.AppOpen] != MediationType.AdMob)
            {
                return;
            }

            switch (config.admobConfig.appOpenData.adLoadType)
            {
                case AdLoadType.AutoLoad:
                    InitAppOpenAdmobStatic();
                    break;

                case AdLoadType.DelayLoad:
                    DelayAction(config.admobConfig.appOpenData.delayLoadTime, () =>
                    {
                       InitAppOpenAdmobStatic();
                    });
                    break;

                case AdLoadType.ManualLoad:
                    break;
            }
        }
        
        
        void InitInterAdmob()
        {
            if (!_adMediationMaps.ContainsKey(AdType.Inter))
            {
                return;
            }
                
            if (_adMediationMaps[AdType.Inter] != MediationType.AdMob)
            {
                return;
            }

            switch (config.admobConfig.interData.adLoadType)
            {
                case AdLoadType.AutoLoad:
                    InitInterAdmobStatic();
                    break;

                case AdLoadType.DelayLoad:
                    DelayAction(config.admobConfig.interData.delayLoadTime, () =>
                    {
                        InitInterAdmobStatic();
                    });
                    break;

                case AdLoadType.ManualLoad:
                    break;
            }
        }
        
        
        
        void InitRewardAdmob()
        {
            if (!_adMediationMaps.ContainsKey(AdType.Reward))
            {
                return;
            }
                
            if (_adMediationMaps[AdType.Reward] != MediationType.AdMob)
            {
                return;
            }

            switch (config.admobConfig.rewardData.adLoadType)
            {
                case AdLoadType.AutoLoad:
                    InitRewardAdmobStatic();
                    break;

                case AdLoadType.DelayLoad:
                    DelayAction(config.admobConfig.rewardData.delayLoadTime, () =>
                    {
                        InitRewardAdmobStatic();
                    });
                    break;

                case AdLoadType.ManualLoad:
                    break;
            }
        }
        
        
        void InitRewardInterAdmob()
        {
            if (!_adMediationMaps.ContainsKey(AdType.RewardInter))
            {
                return;
            }
                
            if (_adMediationMaps[AdType.RewardInter] != MediationType.AdMob)
            {
                return;
            }

            switch (config.admobConfig.rewardInterData.adLoadType)
            {
                case AdLoadType.AutoLoad:
                    InitRewardInterAdmobStatic();
                    break;

                case AdLoadType.DelayLoad:
                    DelayAction(config.admobConfig.rewardInterData.delayLoadTime, () =>
                    {
                        InitRewardInterAdmobStatic();
                    });
                    break;

                case AdLoadType.ManualLoad:
                    break;
            }
        }
        
        
        void InitBannerAdmob()
        {
            if (!_adMediationMaps.ContainsKey(AdType.Banner))
            {
                return;
            }
                
            if (_adMediationMaps[AdType.Banner] != MediationType.AdMob)
            {
                return;
            }

            switch (config.admobConfig.bannerData.adLoadType)
            {
                case AdLoadType.AutoLoad:
                    InitBannerAdmobStatic();
                    break;

                case AdLoadType.DelayLoad:
                    DelayAction(config.admobConfig.bannerData.delayLoadTime, () =>
                    {
                        InitBannerAdmobStatic();
                    });
                    break;

                case AdLoadType.ManualLoad:
                    break;
            }
        }
        
        
        void InitMRecAdmob()
        {
            if (!_adMediationMaps.ContainsKey(AdType.MRec))
            {
                return;
            }
                
            if (_adMediationMaps[AdType.MRec] != MediationType.AdMob)
            {
                return;
            }

            switch (config.admobConfig.mrecData.adLoadType)
            {
                case AdLoadType.AutoLoad:
                    InitMRecAdmobStatic();
                    break;

                case AdLoadType.DelayLoad:
                    DelayAction(config.admobConfig.mrecData.delayLoadTime, () =>
                    {
                        InitMRecAdmobStatic();
                    });
                    break;

                case AdLoadType.ManualLoad:
                    break;
            }
        }



        public static void InitAppOpenAdmobStatic()
        {
            _adInstanceMaps[MediationType.AdMob].InitAppOpenAd();
        }
        
        public static void InitInterAdmobStatic()
        {
            _adInstanceMaps[MediationType.AdMob].InitInterstitialAd();
        }
        
        public static void InitRewardAdmobStatic()
        {
            _adInstanceMaps[MediationType.AdMob].InitRewardedAd();
        }
        
        public static void InitRewardInterAdmobStatic()
        {
            _adInstanceMaps[MediationType.AdMob].InitRewardedInterstitialAd();
        }
        
        public static void InitBannerAdmobStatic()
        {
            _adInstanceMaps[MediationType.AdMob].InitBannerAd();
        }
        
        public static void InitMRecAdmobStatic()
        {
            _adInstanceMaps[MediationType.AdMob].InitMRECAd();
        }
        
        
        #endregion
        
        
        
        #endregion



        #region Ads Method
        
        
        public bool CanShowAppOpen()
        {
            if (!Gm.EnableAds)
                return false;

            if (_isDebugAds)
                return false;

            if (!_isInitedComplete)
                return false;
            
            return _adInstanceMaps[_adMediationMaps[AdType.AppOpen]].CanShowAppOpen();
        }

        [PropertySpace]
        [Command]
        [Button]
        public static void ShowAppOpenAd()
        {
            if (!_canShowAds)
                return;
            
            if (_isDebugAds)
                return;
            
            if (!_isInitedComplete)
                return;
            
            _adInstanceMaps[_adMediationMaps[AdType.AppOpen]].ShowAppOpenAd();
        }
        
        
        [PropertySpace]
        [Command]
        [Button]
        public static void ShowInterstitialAd()
        {
            if (!_canShowAds)
                return;
            
            if (_isDebugAds)
                return;
            
            if (!_isInitedComplete)
                return;
            
            _adInstanceMaps[_adMediationMaps[AdType.Inter]].ShowInterstitial();
        }


        public static bool CanShowRewardAd()
        {
            if (_isDebugAds)
            {
                return true;
            }
            
            if (!_isInitedComplete)
                return false;
            
            return _adInstanceMaps[_adMediationMaps[AdType.Reward]].CanShowRewardedAd();
        }
        
        
        [PropertySpace]
        [Button]
        public static void ShowRewardedVideoAd(Action onGetReward = null)
        {
            if (_isDebugAds)
            {
                Instance.Enqueue(() =>
                {
                    onGetReward?.Invoke();
                });
                return;
            }
            
            if (!_isInitedComplete)
                return;
            
            _adInstanceMaps[_adMediationMaps[AdType.Reward]].ShowRewardedAd(() =>
            {
                Instance.Enqueue(() =>
                {
                    onGetReward?.Invoke();
                });
            });

        }
        
        
        [PropertySpace]
        [Button]
        public static void ShowRewardedInterstitialAd(Action onGetReward = null)
        {
            if (_isDebugAds)
            {
                Instance.Enqueue(() =>
                {
                    onGetReward?.Invoke();
                });
                return;
            }
            
            if (!_isInitedComplete)
                return;
            
            _adInstanceMaps[_adMediationMaps[AdType.RewardInter]].ShowRewardedInterstitialAd(() =>
            {
                Instance.Enqueue(() =>
                {
                    onGetReward?.Invoke();
                });
            });

        }

        
        [PropertySpace]
        [Command]
        [Button]
        public static void ShowBannerAd()
        {         
            if (!_canShowAds)
                return;
            
            if (_isDebugAds)
                return;
            
            if (!_isInitedComplete)
                return;
            
            _adInstanceMaps[_adMediationMaps[AdType.Banner]].ShowBanner();

        }


        [PropertySpace]
        [Command]
        [Button]
        public static void HideBannerAd()
        {
            _adInstanceMaps[_adMediationMaps[AdType.Banner]].HideBanner();

        }
        
        
        [PropertySpace]
        [Command]
        [Button]
        public static void ShowMRecAd()
        {
            if (!_canShowAds)
                return;
            
            if (_isDebugAds)
                return;
            
            _adInstanceMaps[_adMediationMaps[AdType.MRec]].ShowMRecAd();

        }
        
        
        [PropertySpace]
        [Command]
        [Button]
        public static void HideMRecAd()
        {
            _adInstanceMaps[_adMediationMaps[AdType.MRec]].HideMRecAd();
        }
        
        
        

        #endregion


        
        #region Main Thread

        private static readonly Queue<Action> _executionQueue = new Queue<Action>();

        public void Update() {
            lock(_executionQueue) {
                while (_executionQueue.Count > 0) {
                    _executionQueue.Dequeue().Invoke();
                }
            }
        }

        /// <summary>
        /// Locks the queue and adds the IEnumerator to the queue
        /// </summary>
        /// <param name="action">IEnumerator function that will be executed from the main thread.</param>
        public void Enqueue(IEnumerator action) {
            lock (_executionQueue) {
                _executionQueue.Enqueue (() => {
                    StartCoroutine (action);
                });
            }
        }

        /// <summary>
        /// Locks the queue and adds the Action to the queue
        /// </summary>
        /// <param name="action">function that will be executed from the main thread.</param>
        public void Enqueue(Action action)
        {
            Enqueue(ActionWrapper(action));
        }

        /// <summary>
        /// Locks the queue and adds the Action to the queue, returning a Task which is completed when the action completes
        /// </summary>
        /// <param name="action">function that will be executed from the main thread.</param>
        /// <returns>A Task that can be awaited until the action completes</returns>
        public Task EnqueueAsync(Action action)
        {
            var tcs = new TaskCompletionSource<bool>();

            void WrappedAction() {
                try 
                {
                    action();
                    tcs.TrySetResult(true);
                } catch (Exception ex) 
                {
                    tcs.TrySetException(ex);
                }
            }

            Enqueue(ActionWrapper(WrappedAction));
            return tcs.Task;
        }


        IEnumerator ActionWrapper(Action a)
        {
            a();
            yield return null;
        }


        #endregion
        
    }

    public enum MediationType
    {
        MAX,
        AdMob,
    }
    
    [Flags]
    public enum AdType
    {
        AppOpen = 1 << 1,
        Inter = 1 << 2,
        Reward = 1 << 3,
        RewardInter = 1 << 4,
        Banner = 1 << 5,
        MRec = 1 << 6,
        Native = 1 << 7,
        NativeOverlay = 1 << 8,
    }
}