using System;
using b100SDK.Scripts.Base;

#if ENABLE_ADS_ADMOB_NATIVE
using GoogleMobileAds.Api;
#endif

using Sirenix.OdinInspector;
using UnityEngine;

namespace b100SDK.Scripts.Ads
{
    public abstract class AdsInstance : BhMonoBehavior
    {
        [SerializeField]
        protected MediationType mediationType;
        
        [SerializeField] 
        protected AdType adType;

        public static bool canShowAoaWhenAppFocus;

        public static float maxTimeRetryAttempt;

        public static LogInfo logInfo;


        protected bool isInited;
        
        
        

        #region Init
        
        public virtual void Init(Action onInitialized, Action onAoaLoadComplete = null)
        {
            isInited = false;
        }

        public virtual bool IsInitialized() => isInited;

        #endregion
        
        
        
        #region App Open Ad Methods

        public virtual void InitAppOpenAd()
        {
            
        }
        
        public virtual bool CanShowAppOpen()
        {
            if (!isInited)
                return false;
            
            return true;
        }
       
        public virtual void ShowAppOpenAd()
        {
            if (!isInited)
                return;
        }

        #endregion

        
        
        #region Interstitial Ad Methods

        public virtual void InitInterstitialAd()
        {
            
        }
        public virtual bool CanShowInterstitial()
        {
            if (!isInited)
                return false;
            
            return true;
        }

        public virtual void ShowInterstitial()
        {
            if (!isInited)
                return;
        }

        #endregion

        
        
        #region Rewarded Ad Methods
        
        public virtual void InitRewardedAd()
        {
            
        }
        
        public virtual bool CanShowRewardedAd()
        {
            if (!isInited)
                return false;
            
            return true;
        }
        
        public virtual void ShowRewardedAd(Action onGetRewardedAd = null)
        {
            if (!isInited)
                return;
        }



        #endregion
        
        
        
        #region Rewarded Interstitial Ad Methods
        
        public virtual void InitRewardedInterstitialAd()
        {
            
        }
        
        public virtual bool CanShowRewardedInterstitialAd()
        {
            if (!isInited)
                return false;
            
            return true;
        }
        

        public virtual void ShowRewardedInterstitialAd(Action onGetRewardedAd = null)
        {
            if (!isInited)
                return;
        }

        #endregion

        
        
        #region Banner Ad Methods
        
        public virtual void InitBannerAd()
        {
            
        }
        
        public virtual bool CanShowBanner()
        {
            if (!isInited)
                return false;
            
            return true;
        }
        
        public virtual void ShowBanner()
        {
            if (!isInited)
                return;
        }


        public virtual void ShowBannerCollapse()
        {
            if (!isInited)
                return;
        }

        public virtual void HideBanner()
        {
            if (!isInited)
                return;
        }

        #endregion

        
        
        #region MREC Ad Methods
        
        public virtual void InitMRECAd()
        {
            
        }
        
        public virtual bool CanShowMRecAd()
        {
            if (!isInited)
                return false;
            
            return true;
        }

        public virtual void ShowMRecAd()
        {
            if (!isInited)
                return;
        }
        
        public virtual void HideMRecAd()
        {
            if (!isInited)
                return;
        }

        #endregion
        
        
        
        #region Editor


#if UNITY_EDITOR

        public virtual void SetUpAdType(AdType adType)
        {
            this.adType = adType;
        }
        
#endif

        #endregion
        
    }


    [Serializable]
    public class AdData
    {
#if UNITY_IOS
        [HorizontalGroup("A"), LabelWidth(80)]
        [VerticalGroup("A/Left")]
        [InfoBox("iOS")]
        public string adUnitId = "ENTER_AD_UNIT_HERE";
#else
        [HorizontalGroup("A"), LabelWidth(80)]
        [VerticalGroup("A/Left")]
        [InfoBox("Android")]
        public string adUnitId = "ENTER_AD_UNIT_HERE";
#endif
        
        [HorizontalGroup("A", MaxWidth = 200, MarginLeft = 50), LabelWidth(80)]
        [VerticalGroup("A/Right")]
        public AdLoadType adLoadType;
        
        [ShowIf(nameof(adLoadType), AdLoadType.DelayLoad)]
        [HorizontalGroup("A", MaxWidth = 200, MarginLeft = 50), LabelWidth(80)]
        [VerticalGroup("A/Right")]
        public float delayLoadTime;
    }

    [Serializable]
    public class BannerData : AdData
    {
        [HorizontalGroup("A", MaxWidth = 200, MarginLeft = 50), LabelWidth(150)]
        [VerticalGroup("A/Left")]
        public bool showAfterLoad;

        [HorizontalGroup("A", MaxWidth = 200, MarginLeft = 50), LabelWidth(150)]
        [VerticalGroup("A/Left")]
        public bool isBannerCollapse = false;
    }

    public enum AdLoadType
    {
        AutoLoad,
        DelayLoad,
        ManualLoad,
    }

    public enum LogInfo
    {
        Production,
        Test,
    }
}