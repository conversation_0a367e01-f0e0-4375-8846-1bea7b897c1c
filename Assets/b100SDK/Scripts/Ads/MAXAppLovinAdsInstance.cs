using System;
using Sirenix.OdinInspector;
using UnityEngine;

namespace b100SDK.Scripts.Ads
{
    
    public class MaxAppLovinAdsInstance : AdsInstance
    {
#if ENABLE_ADS && ENABLE_ADS_MAX
        private string _maxSdkKey = "ENTER_MAX_SDK_KEY_HERE";
        private string _appOpenAdUnitId  = "ENTER_ANDROID_APP_OPEN_AD_UNIT_ID_HERE";
        private string _interstitialAdUnitId = "ENTER_ANDROID_INTERSTITIAL_AD_UNIT_ID_HERE";
        private string _rewardedAdUnitId = "ENTER_ANDROID_REWARD_AD_UNIT_ID_HERE";
        private string _rewardedInterstitialAdUnitId = "ENTER_ANDROID_REWARD_INTER_AD_UNIT_ID_HERE";
        private string _bannerAdUnitId = "ENTER_ANDROID_BANNER_AD_UNIT_ID_HERE";
        private string _mRecAdUnitId = "ENTER_ANDROID_MREC_AD_UNIT_ID_HERE";


        private bool _isBannerShowing;
        private bool _isMRecShowing;

        private int _aoaReloadCount;
        private int _appOpenRetryAttempt;
        private int _interstitialRetryAttempt;
        private int _rewardedRetryAttempt;
        private int _rewardedInterstitialRetryAttempt;

        private bool _isShowBannerAfterLoad;

        private Action _onAoaLoadComplete;



        public override void Init(Action onInitialized, Action onAoaLoadComplete = null)
        {
            _maxSdkKey = Cfg.iaaConfig.maxApplovinConfig.maxSdkKey;
            _appOpenAdUnitId = Cfg.iaaConfig.maxApplovinConfig.appOpenData.adUnitId;
            _interstitialAdUnitId = Cfg.iaaConfig.maxApplovinConfig.interData.adUnitId;
            _rewardedAdUnitId = Cfg.iaaConfig.maxApplovinConfig.rewardData.adUnitId;
            _rewardedInterstitialAdUnitId = Cfg.iaaConfig.maxApplovinConfig.rewardInterData.adUnitId;
            _bannerAdUnitId = Cfg.iaaConfig.maxApplovinConfig.bannerData.adUnitId;
            _mRecAdUnitId = Cfg.iaaConfig.maxApplovinConfig.mrecData.adUnitId;

            _isShowBannerAfterLoad = Cfg.iaaConfig.maxApplovinConfig.bannerData.showAfterLoad;
            
            _onAoaLoadComplete = onAoaLoadComplete;
            
            MaxSdkCallbacks.OnSdkInitializedEvent += sdkConfiguration =>
            {
                onInitialized?.Invoke();

                if (logInfo != LogInfo.Production)
                {
                    Debug.Log("[MAX] MAX SDK Initialized");
                }
            };

            MaxSdk.SetSdkKey(_maxSdkKey);
            MaxSdk.InitializeSdk();
        }
        
        

        #region App Open Ad Methods

        public override void InitAppOpenAd()
        {
            base.InitAppOpenAd();
            
            InitializeAppOpenAds();
        }

        void InitializeAppOpenAds()
        {
            MaxSdkCallbacks.AppOpen.OnAdLoadedEvent += OnAppOpenLoadedEvent;
            MaxSdkCallbacks.AppOpen.OnAdLoadFailedEvent += OnAppOpenFailedToLoadEvent;
            MaxSdkCallbacks.AppOpen.OnAdClickedEvent += OnAppOpenClickedEvent;
            MaxSdkCallbacks.AppOpen.OnAdDisplayedEvent += OnAppOpenDisplayedEvent;
            MaxSdkCallbacks.AppOpen.OnAdDisplayFailedEvent += OnAppOpenDisplayFailedEvent;
            MaxSdkCallbacks.AppOpen.OnAdRevenuePaidEvent += OnAppOpenRevenuePaidEvent;
            MaxSdkCallbacks.AppOpen.OnAdHiddenEvent += OnAppOpenDismissedEvent;
            
            LoadAppOpenAds();

            canShowAoaWhenAppFocus = true;
        }

        void LoadAppOpenAds()
        {
            MaxSdk.LoadAppOpenAd(_appOpenAdUnitId);
        }

        public override bool CanShowAppOpen()
        {
            return MaxSdk.IsAppOpenAdReady(_appOpenAdUnitId);
        }

        public override void ShowAppOpenAd()
        {
            base.ShowAppOpenAd();
            
            if (!canShowAoaWhenAppFocus)
                return;
            
            if (MaxSdk.IsAppOpenAdReady(_appOpenAdUnitId))
            {
                MaxSdk.ShowAppOpenAd(_appOpenAdUnitId);
            }
            else
            {

            }
        }
        
        
        void OnAppOpenDismissedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            //Debug.Log("[MAX] App Open Dismissed");
            LoadAppOpenAds();
            
            canShowAoaWhenAppFocus = true;
        }
        
        
        private void OnAppOpenLoadedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            if (logInfo != LogInfo.Production)
            {
                Debug.Log("[MAX] App open Loaded");
            }
            _appOpenRetryAttempt = 0;
            _aoaReloadCount = 0;
        }
        
        private void OnAppOpenFailedToLoadEvent(string adUnitID, MaxSdkBase.ErrorInfo errorInfo)
        {
            if (logInfo!= LogInfo.Production)
            {
                Debug.LogError("[MAX] App open failed to load with error code: " + errorInfo.Code);
            }

            if (_aoaReloadCount < 3)
            {
                LoadAppOpenAds();
                _aoaReloadCount++;
                
                return;
            }
            
            
            _appOpenRetryAttempt++;
            
            float retryDelay = Mathf.Min(Mathf.Pow(2, Mathf.Min(6, _interstitialRetryAttempt)), maxTimeRetryAttempt);
            
            Invoke(nameof(LoadAppOpenAds), retryDelay);
        }

        private void OnAppOpenClickedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            //Debug.Log("[MAX] App open clicked");
        }

        private void OnAppOpenDisplayedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            //Debug.Log("[MAX] App open displayed");
            canShowAoaWhenAppFocus = false;
        }

        private void OnAppOpenDisplayFailedEvent(string adUnitId, MaxSdkBase.ErrorInfo errorInfo, MaxSdkBase.AdInfo adInfo)
        {
            //Debug.LogError("[MAX] App open display failed with error code: " + errorInfo.Code);
            LoadAppOpenAds();
        }

        private void OnAppOpenRevenuePaidEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            //Debug.Log("[MAX] App open revenue paid");
            TrackAdRevenue(adInfo);
        }

        #endregion
        

        
        
        
        #region Interstitial Ad Methods

        public override void InitInterstitialAd()
        {
            base.InitInterstitialAd();
            
            InitializeInterstitialAds();
        }

        private void InitializeInterstitialAds()
        {
            // Attach callbacks
            MaxSdkCallbacks.Interstitial.OnAdLoadedEvent += OnInterstitialLoadedEvent;
            MaxSdkCallbacks.Interstitial.OnAdLoadFailedEvent += OnInterstitialFailedEvent;
            MaxSdkCallbacks.Interstitial.OnAdDisplayedEvent += OnInterstitialDisplayedEvent;
            MaxSdkCallbacks.Interstitial.OnAdDisplayFailedEvent += InterstitialFailedToDisplayEvent;
            MaxSdkCallbacks.Interstitial.OnAdHiddenEvent += OnInterstitialDismissedEvent;
            MaxSdkCallbacks.Interstitial.OnAdRevenuePaidEvent += OnInterstitialRevenuePaidEvent;
            
            // Load the first interstitial
            LoadInterstitial();
        }

        private void OnInterstitialDisplayedEvent(string arg1, MaxSdkBase.AdInfo arg2)
        {
            canShowAoaWhenAppFocus = false;
        }

        public override bool CanShowInterstitial()
        {
            return MaxSdk.IsInterstitialReady(_interstitialAdUnitId);
        }

        void LoadInterstitial()
        {
            MaxSdk.LoadInterstitial(_interstitialAdUnitId);
        }

        public override void ShowInterstitial()
        {
            base.ShowInterstitial();
            
            if (MaxSdk.IsInterstitialReady(_interstitialAdUnitId))
            {
                //Debug.Log("[MAX] Showing interstitial");
                MaxSdk.ShowInterstitial(_interstitialAdUnitId);
            }
            else
            {
                
            }
        }

        private void OnInterstitialLoadedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            // Interstitial ad is ready to be shown. MaxSdk.IsInterstitialReady(interstitialAdUnitId) will now return 'true'

            if (logInfo != LogInfo.Production)
            {
                Debug.Log("[MAX] Interstitial loaded");
            }
            
            // Reset retry attempt
            _interstitialRetryAttempt = 0;
        }

        private void OnInterstitialFailedEvent(string adUnitId, MaxSdkBase.ErrorInfo errorInfo)
        {
            // Interstitial ad failed to load. We recommend retrying with exponentially higher delays up to a maximum delay (in this case 64 seconds).
            _interstitialRetryAttempt++;
            float retryDelay = Mathf.Min(Mathf.Pow(2, Mathf.Min(6, _interstitialRetryAttempt)), maxTimeRetryAttempt);

            if (logInfo != LogInfo.Production)
            {
                Debug.LogError("[MAX] Interstitial failed to load with error code: " + errorInfo.Code);
            }
            
            Invoke(nameof(LoadInterstitial), retryDelay);
        }

        private void InterstitialFailedToDisplayEvent(string adUnitId, MaxSdkBase.ErrorInfo errorInfo, MaxSdkBase.AdInfo adInfo)
        {
            // Interstitial ad failed to display. We recommend loading the next ad
            //Debug.LogError("[MAX] Interstitial failed to display with error code: " + errorInfo.Code);
            LoadInterstitial();
        }

        private void OnInterstitialDismissedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            // Interstitial ad is hidden. Pre-load the next ad
            //Debug.Log("[MAX] Interstitial dismissed");
            LoadInterstitial();
            
            canShowAoaWhenAppFocus = true;
        }

        private void OnInterstitialRevenuePaidEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            // Interstitial ad revenue paid. Use this callback to track user revenue.
            //Debug.Log("[MAX] Interstitial revenue paid");

            /*// Ad revenue
            double revenue = adInfo.Revenue;
            
            // Miscellaneous data
            string countryCode = MaxSdk.GetSdkConfiguration().CountryCode; // "US" for the United States, etc - Note: Do not confuse this with currency code which is "USD"!
            string networkName = adInfo.NetworkName; // Display name of the network that showed the ad (e.g. "AdColony")
            string adUnitIdentifier = adInfo.AdUnitIdentifier; // The MAX Ad Unit ID
            string placement = adInfo.Placement; // The placement this ad's postbacks are tied to*/
            
            TrackAdRevenue(adInfo);
        }

        #endregion

        
        
        
        #region Rewarded Ad Methods

        public override void InitRewardedAd()
        {
            base.InitRewardedAd();
            
            InitializeRewardedAds();
        }

        private void InitializeRewardedAds()
        {
            // Attach callbacks
            MaxSdkCallbacks.Rewarded.OnAdLoadedEvent += OnRewardedAdLoadedEvent;
            MaxSdkCallbacks.Rewarded.OnAdLoadFailedEvent += OnRewardedAdFailedEvent;
            MaxSdkCallbacks.Rewarded.OnAdDisplayFailedEvent += OnRewardedAdFailedToDisplayEvent;
            MaxSdkCallbacks.Rewarded.OnAdDisplayedEvent += OnRewardedAdDisplayedEvent;
            MaxSdkCallbacks.Rewarded.OnAdClickedEvent += OnRewardedAdClickedEvent;
            MaxSdkCallbacks.Rewarded.OnAdHiddenEvent += OnRewardedAdDismissedEvent;
            MaxSdkCallbacks.Rewarded.OnAdReceivedRewardEvent += OnRewardedAdReceivedRewardEvent;
            MaxSdkCallbacks.Rewarded.OnAdRevenuePaidEvent += OnRewardedAdRevenuePaidEvent;

            // Load the first RewardedAd
            LoadRewardedAd();
        }

        private void LoadRewardedAd()
        {
            MaxSdk.LoadRewardedAd(_rewardedAdUnitId);
        }


        public override bool CanShowRewardedAd()
        {
            return MaxSdk.IsRewardedAdReady(_rewardedAdUnitId);
        }


        private Action _onGetRewardedAd;

        public override void ShowRewardedAd(Action onGetRewardedAd = null)
        {
            base.ShowRewardedAd(onGetRewardedAd);

            _onGetRewardedAd = onGetRewardedAd;
            
            if (MaxSdk.IsRewardedAdReady(_rewardedAdUnitId))
            {
                //Debug.Log("[MAX] Showing Reward Ad");
                MaxSdk.ShowRewardedAd(_rewardedAdUnitId);
            }
            else
            {
                //Debug.LogError("[MAX] Ad is not ready");
            }
        }
        

        private void OnRewardedAdLoadedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            // Rewarded ad is ready to be shown. MaxSdk.IsRewardedAdReady(rewardedAdUnitId) will now return 'true'

            if (logInfo != LogInfo.Production)
            {
                Debug.Log("[MAX] Rewarded ad loaded");
            }
            
            // Reset retry attempt
            _rewardedRetryAttempt = 0;
        }

        private void OnRewardedAdFailedEvent(string adUnitId, MaxSdkBase.ErrorInfo errorInfo)
        {
            // Rewarded ad failed to load. We recommend retrying with exponentially higher delays up to a maximum delay (in this case 64 seconds).
            _rewardedRetryAttempt++;
            float retryDelay = Mathf.Pow(2, Mathf.Min(6, _rewardedRetryAttempt));

            if (logInfo != LogInfo.Production)
            {
                Debug.LogError("[MAX] Rewarded ad failed to load with error code: " + errorInfo.Code);
            }
            
            Invoke(nameof(LoadRewardedAd), retryDelay);
        }

        private void OnRewardedAdFailedToDisplayEvent(string adUnitId, MaxSdkBase.ErrorInfo errorInfo, MaxSdkBase.AdInfo adInfo)
        {
            // Rewarded ad failed to display. We recommend loading the next ad

            if (logInfo != LogInfo.Production)
            {
                Debug.LogError("[MAX] Rewarded ad failed to display with error code: " + errorInfo.Code);
            }
            
            LoadRewardedAd();
        }

        private void OnRewardedAdDisplayedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            //Debug.Log("[MAX] Rewarded ad displayed");
            canShowAoaWhenAppFocus = false;
        }

        private void OnRewardedAdClickedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            //Debug.Log("[MAX] Rewarded ad clicked");
        }

        private void OnRewardedAdDismissedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            // Rewarded ad is hidden. Pre-load the next ad
            //Debug.Log("[MAX] Rewarded ad dismissed");
            
            LoadRewardedAd();
            
            canShowAoaWhenAppFocus = true;
        }

        private void OnRewardedAdReceivedRewardEvent(string adUnitId, MaxSdk.Reward reward, MaxSdkBase.AdInfo adInfo)
        {
            // Rewarded ad was displayed and user should receive the reward
            //Debug.Log("[MAX] Rewarded ad received reward");
            _onGetRewardedAd?.Invoke();
        }

        private void OnRewardedAdRevenuePaidEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            // Rewarded ad revenue paid. Use this callback to track user revenue.
            //Debug.Log("[MAX] Rewarded ad revenue paid");

            /*// Ad revenue
            double revenue = adInfo.Revenue;
            
            // Miscellaneous data
            string countryCode = MaxSdk.GetSdkConfiguration().CountryCode; // "US" for the United States, etc - Note: Do not confuse this with currency code which is "USD"!
            string networkName = adInfo.NetworkName; // Display name of the network that showed the ad (e.g. "AdColony")
            string adUnitIdentifier = adInfo.AdUnitIdentifier; // The MAX Ad Unit ID
            string placement = adInfo.Placement; // The placement this ad's postbacks are tied to*/
            
            TrackAdRevenue(adInfo);
        }

        #endregion
        
        
        
        
        
        #region Rewarded Interstitial Ad Methods

        private Action _onGetRewardedInterstitial;

        public override void InitRewardedInterstitialAd()
        {
            base.InitRewardedInterstitialAd();
            
            InitializeRewardedInterstitialAds();   
        }

        private void InitializeRewardedInterstitialAds()
        {
            // Attach callbacks
            MaxSdkCallbacks.RewardedInterstitial.OnAdLoadedEvent += OnRewardedInterstitialAdLoadedEvent;
            MaxSdkCallbacks.RewardedInterstitial.OnAdLoadFailedEvent += OnRewardedInterstitialAdFailedEvent;
            MaxSdkCallbacks.RewardedInterstitial.OnAdDisplayFailedEvent += OnRewardedInterstitialAdFailedToDisplayEvent;
            MaxSdkCallbacks.RewardedInterstitial.OnAdDisplayedEvent += OnRewardedInterstitialAdDisplayedEvent;
            MaxSdkCallbacks.RewardedInterstitial.OnAdClickedEvent += OnRewardedInterstitialAdClickedEvent;
            MaxSdkCallbacks.RewardedInterstitial.OnAdHiddenEvent += OnRewardedInterstitialAdDismissedEvent;
            MaxSdkCallbacks.RewardedInterstitial.OnAdReceivedRewardEvent += OnRewardedInterstitialAdReceivedRewardEvent;
            MaxSdkCallbacks.RewardedInterstitial.OnAdRevenuePaidEvent += OnRewardedInterstitialAdRevenuePaidEvent;

            // Load the first RewardedInterstitialAd
            LoadRewardedInterstitialAd();
        }

        private void LoadRewardedInterstitialAd()
        {
            MaxSdk.LoadRewardedInterstitialAd(_rewardedInterstitialAdUnitId);
        }

        public override bool CanShowRewardedInterstitialAd()
        {
            return MaxSdk.IsRewardedInterstitialAdReady(_rewardedInterstitialAdUnitId);
        }

        public override void ShowRewardedInterstitialAd(Action onGetRewardedAd = null)
        {
            base.ShowRewardedInterstitialAd(onGetRewardedAd);
            
            _onGetRewardedInterstitial = onGetRewardedAd;
            
            if (MaxSdk.IsRewardedInterstitialAdReady(_rewardedInterstitialAdUnitId))
            {
                //Debug.Log("[MAX] Showing");
                MaxSdk.ShowRewardedInterstitialAd(_rewardedInterstitialAdUnitId);
            }
            else
            {
                //Debug.LogError("[MAX] Ad not ready");
            }
        }
        

        private void OnRewardedInterstitialAdLoadedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            // Rewarded interstitial ad is ready to be shown. MaxSdk.IsRewardedInterstitialAdReady(rewardedInterstitialAdUnitId) will now return 'true'

            if (logInfo != LogInfo.Production)
            {
                Debug.Log("[MAX] Rewarded interstitial ad loaded");
            }
            
            // Reset retry attempt
            _rewardedInterstitialRetryAttempt = 0;
        }

        private void OnRewardedInterstitialAdFailedEvent(string adUnitId, MaxSdkBase.ErrorInfo errorInfo)
        {
            // Rewarded interstitial ad failed to load. We recommend retrying with exponentially higher delays up to a maximum delay (in this case 64 seconds).
            _rewardedInterstitialRetryAttempt++;
            float retryDelay = Mathf.Pow(2, Mathf.Min(6, _rewardedInterstitialRetryAttempt));

            if (logInfo != LogInfo.Production)
            {
                Debug.LogError("[MAX] Rewarded interstitial ad failed to load with error code: " + errorInfo.Code);
            }
            
            Invoke(nameof(LoadRewardedInterstitialAd), retryDelay);
        }

        private void OnRewardedInterstitialAdFailedToDisplayEvent(string adUnitId, MaxSdkBase.ErrorInfo errorInfo, MaxSdkBase.AdInfo adInfo)
        {
            // Rewarded interstitial ad failed to display. We recommend loading the next ad
            //Debug.LogError("[MAX] Rewarded interstitial ad failed to display with error code: " + errorInfo.Code);
            
            LoadRewardedInterstitialAd();
        }

        private void OnRewardedInterstitialAdDisplayedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            //Debug.Log("[MAX] Rewarded interstitial ad displayed");
            canShowAoaWhenAppFocus = false;
        }

        private void OnRewardedInterstitialAdClickedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            //Debug.Log("[MAX] Rewarded interstitial ad clicked");
            
        }

        private void OnRewardedInterstitialAdDismissedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            // Rewarded interstitial ad is hidden. Pre-load the next ad
            //Debug.Log("[MAX] Rewarded interstitial ad dismissed");
            
            LoadRewardedInterstitialAd();
            
            canShowAoaWhenAppFocus = true;
        }

        private void OnRewardedInterstitialAdReceivedRewardEvent(string adUnitId, MaxSdk.Reward reward, MaxSdkBase.AdInfo adInfo)
        {
            // Rewarded interstitial ad was displayed and user should receive the reward
            //Debug.Log("[MAX] Rewarded interstitial ad received reward");
            
            _onGetRewardedInterstitial?.Invoke();
        }

        private void OnRewardedInterstitialAdRevenuePaidEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            // Rewarded interstitial ad revenue paid. Use this callback to track user revenue.
            //Debug.Log("[MAX] Rewarded interstitial ad revenue paid");
            /*// Ad revenue
            double revenue = adInfo.Revenue;
            
            // Miscellaneous data
            string countryCode = MaxSdk.GetSdkConfiguration().CountryCode; // "US" for the United States, etc - Note: Do not confuse this with currency code which is "USD"!
            string networkName = adInfo.NetworkName; // Display name of the network that showed the ad (e.g. "AdColony")
            string adUnitIdentifier = adInfo.AdUnitIdentifier; // The MAX Ad Unit ID
            string placement = adInfo.Placement; // The placement this ad's postbacks are tied to*/
            
            TrackAdRevenue(adInfo);
        }

        #endregion

        
        
        
        #region Banner Ad Methods

        private bool _isLoadedBanner;


        public override void InitBannerAd()
        {
            base.InitBannerAd();
            
            InitializeBannerAds();
        }

        private void InitializeBannerAds()
        {
            _isLoadedBanner = false;
            
            // Attach Callbacks
            MaxSdkCallbacks.Banner.OnAdLoadedEvent += OnBannerAdLoadedEvent;
            MaxSdkCallbacks.Banner.OnAdLoadFailedEvent += OnBannerAdFailedEvent;
            MaxSdkCallbacks.Banner.OnAdClickedEvent += OnBannerAdClickedEvent;
            MaxSdkCallbacks.Banner.OnAdRevenuePaidEvent += OnBannerAdRevenuePaidEvent;

            // Banners are automatically sized to 320x50 on phones and 728x90 on tablets.
            // You may use the utility method `MaxSdkUtils.isTablet()` to help with view sizing adjustments.
            MaxSdk.CreateBanner(_bannerAdUnitId, MaxSdkBase.BannerPosition.BottomCenter);
            MaxSdk.SetBannerExtraParameter(_bannerAdUnitId, "adaptive_banner", "true");
            // Set background or background color for banners to be fully functional.
            MaxSdk.SetBannerBackgroundColor(_bannerAdUnitId, new Color(0f, 0f, 0f, 0f));
        }

        public override bool CanShowBanner()
        {
            return _isLoadedBanner;
        }


        public override void ShowBanner()
        {
            base.ShowBanner();
            
            if (_isBannerShowing) 
                return;
            _isBannerShowing = true;
            
            //Debug.Log("[MAX] Showing Banner");
            MaxSdk.ShowBanner(_bannerAdUnitId);
        }

        public override void HideBanner()
        {
            base.HideBanner();
            
            if (!_isBannerShowing) return;
            _isBannerShowing = false;
            
            //Debug.Log("[MAX] Hiding Banner");
            MaxSdk.HideBanner(_bannerAdUnitId);
        }

        private void OnBannerAdLoadedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            // Banner ad is ready to be shown.
            // If you have already called MaxSdk.ShowBanner(BannerAdUnitId) it will automatically be shown on the next ad refresh.

            if (logInfo != LogInfo.Production)
            {
                Debug.Log("[MAX] Banner ad loaded");
            }

            _isLoadedBanner = true;

            if (_isShowBannerAfterLoad)
            {
                ShowBanner();
            }
        }

        private void OnBannerAdFailedEvent(string adUnitId, MaxSdkBase.ErrorInfo errorInfo)
        {
            // Banner ad failed to load. MAX will automatically try loading a new ad internally.

            if (logInfo != LogInfo.Production)
            {
                Debug.LogError("[MAX] Banner ad failed to load with error code: " + errorInfo.Code);
            }

            _isLoadedBanner = false;
        }

        private void OnBannerAdClickedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            //Debug.Log("[MAX] Banner ad clicked");
        }

        private void OnBannerAdRevenuePaidEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            // Banner ad revenue paid. Use this callback to track user revenue.
            //.Log("[MAX] Banner ad revenue paid");

            /*// Ad revenue
            double revenue = adInfo.Revenue;
            
            // Miscellaneous data
            string countryCode = MaxSdk.GetSdkConfiguration().CountryCode; // "US" for the United States, etc - Note: Do not confuse this with currency code which is "USD"!
            string networkName = adInfo.NetworkName; // Display name of the network that showed the ad (e.g. "AdColony")
            string adUnitIdentifier = adInfo.AdUnitIdentifier; // The MAX Ad Unit ID
            string placement = adInfo.Placement; // The placement this ad's postbacks are tied to*/
            
            TrackAdRevenue(adInfo);
        }

        #endregion

        
        
        
        #region MREC Ad Methods

        public override void InitMRECAd()
        {
            base.InitMRECAd();
            
            InitializeMRecAds();
        }

        private void InitializeMRecAds()
        {
            // Attach Callbacks
            MaxSdkCallbacks.MRec.OnAdLoadedEvent += OnMRecAdLoadedEvent;
            MaxSdkCallbacks.MRec.OnAdLoadFailedEvent += OnMRecAdFailedEvent;
            MaxSdkCallbacks.MRec.OnAdClickedEvent += OnMRecAdClickedEvent;
            MaxSdkCallbacks.MRec.OnAdRevenuePaidEvent += OnMRecAdRevenuePaidEvent;

            // MRECs are automatically sized to 300x250.
            MaxSdk.CreateMRec(_mRecAdUnitId, MaxSdkBase.AdViewPosition.BottomCenter);
        }

        public override void ShowMRecAd()
        {
            base.ShowMRecAd();
            
            if (_isMRecShowing) return;
            _isMRecShowing = true;

            if (logInfo != LogInfo.Production)
            {
                Debug.Log("[MAX] Showing MRec Ad");
            }
            MaxSdk.ShowMRec(_mRecAdUnitId);
        }
        
        

        public override void HideMRecAd()
        {
            base.HideMRecAd();
            
            if (!_isMRecShowing) return;
            _isMRecShowing = false;

            if (logInfo != LogInfo.Production)
            {
                Debug.Log("[MAX] Hiding MRec Ad");
            }
            MaxSdk.HideMRec(_mRecAdUnitId);
        }

        private void OnMRecAdLoadedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            // MRec ad is ready to be shown.
            // If you have already called MaxSdk.ShowMRec(MRecAdUnitId) it will automatically be shown on the next MRec refresh.
            if (logInfo!= LogInfo.Production)
            {
                Debug.Log("[MAX] MRec ad loaded");
            }
        }

        private void OnMRecAdFailedEvent(string adUnitId, MaxSdkBase.ErrorInfo errorInfo)
        {
            // MRec ad failed to load. MAX will automatically try loading a new ad internally.
            if (logInfo!= LogInfo.Production)
            {
                Debug.LogError("[MAX] MRec ad failed to load with error code: " + errorInfo.Code);
            }
        }

        private void OnMRecAdClickedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            if (logInfo != LogInfo.Production)
            {
                Debug.Log("[MAX] MRec ad clicked");
            }
        }

        private void OnMRecAdRevenuePaidEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            // MRec ad revenue paid. Use this callback to track user revenue.
            if (logInfo != LogInfo.Production)
            {
                Debug.Log("[MAX] MRec ad revenue paid");
            }

            /*// Ad revenue
            double revenue = adInfo.Revenue;
            
            // Miscellaneous data
            string countryCode = MaxSdk.GetSdkConfiguration().CountryCode; // "US" for the United States, etc - Note: Do not confuse this with currency code which is "USD"!
            string networkName = adInfo.NetworkName; // Display name of the network that showed the ad (e.g. "AdColony")
            string adUnitIdentifier = adInfo.AdUnitIdentifier; // The MAX Ad Unit ID
            string placement = adInfo.Placement; // The placement this ad's postbacks are tied to*/
            
            TrackAdRevenue(adInfo);
        }

        #endregion
        
        private void TrackAdRevenue(MaxSdkBase.AdInfo adInfo)
        {
            RevenueManager.LogRevenue(new AdsRevenue()
            {
                mediationType = MediationType.MAX,
                adFormat = adInfo.AdFormat,
                adUnitId = adInfo.AdUnitIdentifier,
                networkName = adInfo.NetworkName,
                networkPlacement = adInfo.NetworkPlacement,
                placement = adInfo.Placement,
                revenue = adInfo.Revenue,
                currencyCode = "USD",
            });


        }
#endif




        #region Editor


#if UNITY_EDITOR

        public override void SetUpAdType(AdType adType)
        {
            this.mediationType = MediationType.MAX;
            this.adType = adType;
        }
        
#endif

        #endregion

    }
}