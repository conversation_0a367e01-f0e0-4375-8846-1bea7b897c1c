using System;
using System.Collections;
using System.Collections.Generic;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.UI;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.UI;

#if ENABLE_ADS_ADMOB_NATIVE
using GoogleMobileAds.Api;
#endif

namespace b100SDK.Scripts.Ads
{
    public class NativeAdsUI : BhMonoBehavior
    {
        #if ENABLE_ADS && ENABLE_ADS_ADMOB_NATIVE
        [Header("Ad Info")]
        [SerializeField]
        private string nativeAdUnitId = "ENTER_ANDROID_NATIVE_AD_UNIT_ID_HERE";
        
        [Header("Ad Show")]
        [SerializeField] 
        private RawImage nativeAdIcon;
        
        [SerializeField] 
        private RawImage nativeAdChoicesIcon;
        
        [SerializeField] 
        private Text adHeadline;
        
        [SerializeField] 
        private Text adDescription;
        
        [SerializeField] 
        private Text adCallToAction;

        [Space] 
        [SerializeField] 
        private GameObject nativeAdsContainer;
        
        [SerializeField] 
        private GameObject adLoadGameObject;

        /*[Space]
        [Header("Ad Click")]
        [SerializeField]
        private RectTransform rectTransformContainer;

        [SerializeField]
        private RectTransform rectTransformCollider;
        
        [SerializeField]
        private BoxCollider colliderClick;
        
        [SerializeField]
        private Canvas canvas;
        
        [SerializeField]
        private Camera cam;*/
        
        
        [Space]
        [SerializeField]
        private float expiredTime = 1800f;

        [Space]
        [SerializeField]
        private bool enableDebug = false;

        [Space]
        [SerializeField]
        private float maxTimeReload = 10f;
        
        private NativeAd _nativeAd;
        private bool _isNativeAdShowed;
        private bool _isNativeAdLoaded;
        
        private int _retryAttempt;


        private float _showTimer = 0f;
        

        

        private void Start()
        {/*
            if (!cam)
            {
                cam = Camera.main;
            }

            if (!canvas)
            {
                canvas = GUIManager.Instance.root;
            }

            InitColliderClick();*/
            
            LoadNewAd();
        }

        void LoadNewAd()
        {
            nativeAdsContainer?.SetActiveWithChecker(false);
            adLoadGameObject?.SetActiveWithChecker(true);
            
            _isNativeAdShowed = false;
            
            StopAllCoroutines();
            StartCoroutine(InitNativeAdCoroutine());
        }
        

        IEnumerator InitNativeAdCoroutine()
        {
            while (!AdManager.Instance || !AdManager.Instance.IsAdmobInited)
            {
                yield return null;
            }
            
            RequestNativeAd();
        }
        


        void RequestNativeAd()
        {
            AdLoader adLoader = new AdLoader.Builder(nativeAdUnitId).ForNativeAd().Build();
            adLoader.OnNativeAdLoaded += this.HandleNativeAdLoaded;
            adLoader.OnAdFailedToLoad += this.HandleAdFailedToLoad;
            adLoader.LoadAd(new AdRequest());
        }

        private void NativeAdOnOnPaidEvent(object sender, AdValueEventArgs e)
        {
            RevenueManager.LogRevenue(new AdsRevenue()
            {
                adFormat = "nativeAd",
                adUnitId =  nativeAdUnitId,
                networkName = "admob",
                networkPlacement = "admob",
                placement = "admob",
                revenue = e.AdValue.Value/1000000f,
                currencyCode = e.AdValue.CurrencyCode,
            });
        }

        private void HandleNativeAdLoaded(object sender, NativeAdEventArgs args)
        {
            if (enableDebug)
            {
                Debug.Log("[Admob] Native ad loaded with response : " + args.nativeAd.GetResponseInfo());
            }

            this._nativeAd = args.nativeAd;
            _isNativeAdLoaded = true;
            
            _nativeAd.OnPaidEvent += NativeAdOnOnPaidEvent;

            _retryAttempt = 0;

        }

        private void HandleAdFailedToLoad(object sender, AdFailedToLoadEventArgs args)
        {
            if (enableDebug)
            {
                Debug.Log("Native ad failed to load: " + args.LoadAdError.GetMessage());
            }

            _retryAttempt++;
            
            var delay = Mathf.Min(maxTimeReload, Mathf.Pow(2, Mathf.Min(4, _retryAttempt)));
            
            Invoke(nameof(RequestNativeAd), delay);
        }

        

        private void Update()
        {
            _showTimer += Time.deltaTime;


            if (_showTimer >= expiredTime)
            {
                LoadNewAd();
                _showTimer = 0f;
            }
            
            
            if (!_isNativeAdLoaded || _isNativeAdShowed)
                return;

            if (_nativeAd == null) 
                return;
            _isNativeAdShowed = true;

            if (enableDebug)
            {
                Debug.Log("Native ad showed");
            }
                    
            adLoadGameObject?.SetActiveWithChecker(false);
            nativeAdsContainer?.SetActiveWithChecker(true);
                    
            if (nativeAdIcon)
            {
                if (!_nativeAd.GetIconTexture() || IsTextureWhiteOptimized(_nativeAd.GetIconTexture()))
                {
                    if (_nativeAd.GetImageTextures().Count > 0)
                    {
                        nativeAdIcon.texture = _nativeAd.GetImageTextures()[0];
                    }
                    else
                    {
                        
                    }
                }
                else
                {
                    nativeAdIcon.texture = _nativeAd.GetIconTexture();
                }
                
                
                if (!_nativeAd.RegisterIconImageGameObject(nativeAdIcon.gameObject))
                {
                    if (enableDebug)
                    {
                        Debug.LogError("Fail to register icon image for native ad");
                    }
                }
                else
                {
                    if (enableDebug)
                    {
                        Debug.Log("Register icon image for native ad"); 
                    }
                }
            }

            if (nativeAdChoicesIcon)
            {
                nativeAdChoicesIcon.texture = _nativeAd.GetAdChoicesLogoTexture();
                if (!_nativeAd.RegisterAdChoicesLogoGameObject(nativeAdChoicesIcon.gameObject))
                {
                    if (enableDebug)
                    {
                        Debug.LogError("Fail to register ad choices logo for native ad");
                    }
                }
                else
                {
                    if (enableDebug)
                    {
                        Debug.Log("Register ad choices logo for native ad"); 
                    }
                }
                        
            }

            if (adHeadline)
            {
                adHeadline.text = _nativeAd.GetHeadlineText();
                if (!_nativeAd.RegisterHeadlineTextGameObject(adHeadline.gameObject))
                {
                    if (enableDebug)
                    {
                        Debug.LogError("Fail to register headline text for native ad");
                    }
                }
                else
                {
                    if (enableDebug)
                    {
                        Debug.Log("Register headline text for native ad");
                    } 
                }
            }
                
            if (adDescription)
            {
                adDescription.text = _nativeAd.GetBodyText();
                if (!_nativeAd.RegisterBodyTextGameObject(adDescription.gameObject))
                {
                    if (enableDebug)
                    {
                        Debug.LogError("Fail to register body text for native ad");
                    }
                }
                else
                {
                    if (enableDebug)
                    {
                        Debug.Log("Register body text for native ad");
                    } 
                }
            }

            if (adCallToAction)
            {
                adCallToAction.text = _nativeAd.GetCallToActionText();
                if (!_nativeAd.RegisterCallToActionGameObject(adCallToAction.gameObject))
                {
                    if (enableDebug)
                    {
                        Debug.LogError("Fail to register call to action for native ad");
                    }
                }
                else
                {
                    if (enableDebug)
                    {
                        Debug.Log("Register call to action for native ad"); 
                    }
                }
            }
        }
        #endif
        
        
        
        bool IsTextureWhiteOptimized(Texture2D tex)
        {
            byte[] rawData = tex.GetRawTextureData();
            for (int i = 0; i < rawData.Length; i += 4)
            {
                if (rawData[i] != 255 || rawData[i + 1] != 255 || rawData[i + 2] != 255 || rawData[i + 3] != 255)
                {
                    return false;
                }
            }
            return true;
        }


        /*
        [Button]
        void InitColliderClick()
        {
            //Center
            var center =
                TransformUtils.ConvertScreenPositionToViewportPosition(
                    rectTransformContainer.GetPositionInScreenSpace(canvas), canvas, cam);

            center = TransformUtils.ConvertViewPortToWorldPoint(center, cam);
            colliderClick.center = rectTransformCollider.InverseTransformPoint(center);
            
            
            //Size
            var corners = new Vector3[4];
            rectTransformContainer.GetWorldCorners(corners);
            
            List<Vector3> worldCorners = new List<Vector3>();

            for (int i = 0; i < 4; i++)
            {
                var cornersWorldPosition = TransformUtils.ConvertViewPortToWorldPoint(
                    TransformUtils.ConvertScreenPositionToViewportPosition(corners[i], canvas, cam), cam);
                
                worldCorners.Add(cornersWorldPosition);
            }

            var size = new Vector3(Vector3.Distance(worldCorners[2], worldCorners[1]),
                Vector3.Distance(worldCorners[3], worldCorners[2]), .1f);

            colliderClick.size = rectTransformCollider.InverseTransformVector(size);
        }*/
    }
}