using System;
using System.Collections;
using b100SDK.Scripts.Analytic;
using Sirenix.OdinInspector;
using UnityEngine;

#if ENABLE_ADS_ADMOB
using GoogleMobileAds.Api;
#endif

namespace b100SDK.Scripts.Ads
{
    public class AdMobAdsInstance : AdsInstance
    {
#if ENABLE_ADS && ENABLE_ADS_ADMOB

        private string _appOpenAdUnitId = "ENTER_ANDROID_APP_OPEN_AD_UNIT_ID_HERE";
        private string _interstitialAdUnitId = "ENTER_ANDROID_INTERSTITIAL_AD_UNIT_ID_HERE";
        private string _rewardedAdUnitId = "ENTER_ANDROID_REWARD_AD_UNIT_ID_HERE";
        private string _rewardedInterstitialAdUnitId = "ENTER_ANDROID_REWARD_INTER_AD_UNIT_ID_HERE";
        private string _bannerAdUnitId = "ENTER_ANDROID_BANNER_AD_UNIT_ID_HERE";
        private string _mRecAdUnitId = "ENTER_ANDROID_MREC_AD_UNIT_ID_HERE";
        
        private bool _isShowBannerAfterLoad;
        
        private bool _isBannerShowing;
        
        private bool _isBannerCollapsed;


        private Action _onAoaLoadComplete;
        
        
        #region Init

        public override void Init(Action onInitialized, Action onAoaLoadComplete = null)
        {
            base.Init(onInitialized);

            _appOpenAdUnitId = Cfg.iaaConfig.admobConfig.appOpenData.adUnitId;
            _interstitialAdUnitId = Cfg.iaaConfig.admobConfig.interData.adUnitId;
            _rewardedAdUnitId = Cfg.iaaConfig.admobConfig.rewardData.adUnitId;
            _rewardedInterstitialAdUnitId = Cfg.iaaConfig.admobConfig.rewardInterData.adUnitId;
            _bannerAdUnitId = Cfg.iaaConfig.admobConfig.bannerData.adUnitId;
            _mRecAdUnitId = Cfg.iaaConfig.admobConfig.mrecData.adUnitId;
            
            _isShowBannerAfterLoad = Cfg.iaaConfig.admobConfig.bannerData.showAfterLoad;
            _isBannerCollapsed = Cfg.iaaConfig.admobConfig.bannerData.isBannerCollapse;

            _onAoaLoadComplete = onAoaLoadComplete;
            
            // Initialize the Google Mobile Ads SDK.
            MobileAds.Initialize((InitializationStatus initStatus) =>
            {
                onInitialized?.Invoke();
                
                Debug.Log("[Admob] Admob Inited");
            });
        }

        #endregion


        
        #region App Open Ad Methods

        private AppOpenAd _appOpenAd;
        private int _appOpenRetryCount;
        private int _appOpenReloadWithDelay;

        public override void InitAppOpenAd()
        {
            base.InitAppOpenAd();

            InitializeAppOpenAds();
        }

        public override bool CanShowAppOpen()
        {
            if (_appOpenAd == null)
                return false;
            
            return _appOpenAd.CanShowAd();
        }

        void InitializeAppOpenAds()
        {
            canShowAoaWhenAppFocus = true;
            LoadAppOpenAd();
        }

        void LoadAppOpenAd()
        {
            // Clean up the old ad before loading a new one.
            if (_appOpenAd != null)
            {
                _appOpenAd.Destroy();
                _appOpenAd = null;
            }

            if (logInfo != LogInfo.Production)
            {
                Debug.Log("[Admob] Loading the app open ad.");
            }

            // Create our request used to load the ad.
            var adRequest = new AdRequest();

            // send the request to load the ad.
            AppOpenAd.Load(_appOpenAdUnitId, adRequest,
                (AppOpenAd ad, LoadAdError error) =>
                {
                    _onAoaLoadComplete?.Invoke();
                    
                    // if error is not null, the load request failed.
                    if (error != null || ad == null)
                    {

                        if (logInfo != LogInfo.Production)
                        {
                            Debug.LogError("[Admob] App open ad failed to load an ad " +
                                           "with error : " + error);
                        }

                        HandleAppOpenAdFailedToLoad();

                        return;
                    }

                    if (logInfo != LogInfo.Production)
                    {
                        Debug.Log("[Admob] App open ad loaded with response : "
                                  + ad.GetResponseInfo());
                    }

                    _appOpenRetryCount = 0;
                    _appOpenReloadWithDelay = 0;
                    _appOpenAd = ad;

                    RegisterAppOpenAdEventHandlers(ad);
                });
        }


        public override void ShowAppOpenAd()
        {
            if (!canShowAoaWhenAppFocus)
                return;

            if (_appOpenAd != null && _appOpenAd.CanShowAd())
            {
                //Debug.Log("[Admob] Showing app open ad.");
                _appOpenAd.Show();
            }
            else
            {
                //Debug.Log("[Admob] App open ad is not ready yet.");
                
            }
        }

        public void DestroyAppOpenAd()
        {
            if (_appOpenAd != null)
            {
                //Debug.Log("[Admob] Destroying app open ad.");
                _appOpenAd.Destroy();
                _appOpenAd = null;
            }
        }


        private void RegisterAppOpenAdEventHandlers(AppOpenAd ad)
        {
            // Raised when the ad is estimated to have earned money.
            ad.OnAdPaid += (AdValue adValue) =>
            {
                //Debug.Log("[Admob] App open ad paid " + adValue.Value + adValue.CurrencyCode);
                
                RevenueManager.LogRevenue(new AdsRevenue()
                {
                    mediationType = MediationType.AdMob,
                    adFormat = "app_open",
                    adUnitId = _appOpenAdUnitId,
                    networkName = "admob",
                    networkPlacement = "admob",
                    revenue = adValue.Value/1000000f,
                    currencyCode = adValue.CurrencyCode,
                });

            };

            // Raised when an impression is recorded for an ad.
            ad.OnAdImpressionRecorded += () =>
            {
                //Debug.Log("[Admob] App open ad recorded an impression.");
            };

            // Raised when a click is recorded for an ad.
            ad.OnAdClicked += () =>
            {
                //Debug.Log("[Admob] App open ad was clicked.");
            };

            // Raised when an ad opened full screen content.
            ad.OnAdFullScreenContentOpened += () =>
            {
                //Debug.Log("[Admob] App open ad full screen content opened.");

                canShowAoaWhenAppFocus = false;
            };

            // Raised when the ad closed full screen content.
            ad.OnAdFullScreenContentClosed += () =>
            {
                //Debug.Log("[Admob] App open ad full screen content closed.");

                canShowAoaWhenAppFocus = true;
                
                LoadAppOpenAd();
            };

            // Raised when the ad failed to open full screen content.
            ad.OnAdFullScreenContentFailed += (AdError error) =>
            {
                /*Debug.LogError("[Admob] App open ad failed to open full screen content " +
                               "with error : " + error);*/
                LoadAppOpenAd();
            };
        }


        void HandleAppOpenAdFailedToLoad()
        {
            _appOpenRetryCount++;

            if (_appOpenRetryCount < 3)
            {
                LoadAppOpenAd();
            }
            else
            {
                _appOpenReloadWithDelay++;

                var delay = Mathf.Pow(2, Mathf.Min(3, _appOpenReloadWithDelay));
                
                Invoke(nameof(LoadAppOpenAd), delay);
            }
        }

        #endregion


        
        #region Interstitial Ad Methods

        private InterstitialAd _interstitialAd;
        private int _interstitialRetryCount;

        public override void InitInterstitialAd()
        {
            base.InitInterstitialAd();

            InitializeInterstitialAd();
        }

        void InitializeInterstitialAd()
        {
            LoadInterstitialAd();
        }

        public override bool CanShowInterstitial()
        {
            if (_interstitialAd == null)
                return false;
            
            return _interstitialAd.CanShowAd();
        }

        void LoadInterstitialAd()
        {
            // Clean up the old ad before loading a new one.
            if (_interstitialAd != null)
            {
                DestroyInterstitialAd();
            }

            //Debug.Log("[Admob] Loading interstitial ad.");

            // Create our request used to load the ad.
            var adRequest = new AdRequest();

            // Send the request to load the ad.
            InterstitialAd.Load(_interstitialAdUnitId, adRequest, (InterstitialAd ad, LoadAdError error) =>
            {
                // If the operation failed with a reason.
                if (error != null)
                {
                    if (logInfo != LogInfo.Production)
                    {
                        Debug.LogError("[Admob] Interstitial ad failed to load an ad with error : " + error);
                    }

                    HandleInterstitialAdFailedToLoad();
                    return;
                }

                // If the operation failed for unknown reasons.
                // This is an unexpected error, please report this bug if it happens.
                if (ad == null)
                {

                    if (logInfo != LogInfo.Production)
                    {
                        Debug.LogError(
                            "[Admob] Unexpected error: Interstitial load event fired with null ad and null error.");
                    }

                    HandleInterstitialAdFailedToLoad();
                    return;
                }

                // The operation completed successfully.
                Debug.Log("[Admob] Interstitial ad loaded with response : " + ad.GetResponseInfo());
                _interstitialAd = ad;
                _interstitialRetryCount = 0;

                // Register to ad events to extend functionality.
                RegisterInterstitialAdEventHandlers(ad);
            });
        }

        public override void ShowInterstitial()
        {
            if (!canShowAoaWhenAppFocus)
                return;
            
            if (_interstitialAd != null && _interstitialAd.CanShowAd())
            {
                //Debug.Log("[Admob] Showing interstitial ad.");
                _interstitialAd.Show();
            }
            else
            {
                //Debug.LogError("[Admob] Interstitial ad is not ready yet.");

            }
        }

        public void DestroyInterstitialAd()
        {
            if (_interstitialAd != null)
            {
                //Debug.Log("[Admob] Destroying interstitial ad.");
                _interstitialAd.Destroy();
                _interstitialAd = null;
            }
        }

        private void RegisterInterstitialAdEventHandlers(InterstitialAd ad)
        {
            // Raised when the ad is estimated to have earned money.
            ad.OnAdPaid += (AdValue adValue) =>
            {
                //Debug.Log("[Admob] Interstitial ad paid " + adValue.Value + adValue.CurrencyCode);
                
                RevenueManager.LogRevenue(new AdsRevenue()
                {
                    mediationType = MediationType.AdMob,
                    adFormat = "interstitial",
                    adUnitId = _interstitialAdUnitId,
                    networkName = "admob",
                    networkPlacement = "admob",
                    revenue = adValue.Value/1000000f,
                    currencyCode = adValue.CurrencyCode,
                });
            };

            // Raised when an impression is recorded for an ad.
            ad.OnAdImpressionRecorded += () =>
            {
                //Debug.Log("[Admob] Interstitial ad recorded an impression.");
            };

            // Raised when a click is recorded for an ad.
            ad.OnAdClicked += () =>
            {
                //Debug.Log("[Admob] Interstitial ad was clicked.");
            };

            // Raised when an ad opened full screen content.
            ad.OnAdFullScreenContentOpened += () =>
            {
                //Debug.Log("[Admob] Interstitial ad full screen content opened.");

                canShowAoaWhenAppFocus = false;
            };

            // Raised when the ad closed full screen content.
            ad.OnAdFullScreenContentClosed += () =>
            {
                //Debug.Log("[Admob] Interstitial ad full screen content closed.");

                //canShowAoaWhenAppFocus = tru;
                
                LoadInterstitialAd();
            };

            // Raised when the ad failed to open full screen content.
            ad.OnAdFullScreenContentFailed += (AdError error) =>
            {
                /*Debug.LogError("[Admob] Interstitial ad failed to open full screen content with error : "
                               + error);*/
                LoadInterstitialAd();
            };
        }


        void HandleInterstitialAdFailedToLoad()
        {
            _interstitialRetryCount++;

            float delay = Mathf.Min(Mathf.Pow(2, Mathf.Min(6, _interstitialRetryCount)), maxTimeRetryAttempt);

            Invoke(nameof(LoadInterstitialAd), delay);
        }

        #endregion


        
        #region Rewarded Ad Methods

        private RewardedAd _rewardedAd;
        private int _rewardedRetryCount;

        public override void InitRewardedAd()
        {
            base.InitRewardedAd();

            InitializeRewardedAd();
        }


        void InitializeRewardedAd()
        {
            LoadRewardedAd();
        }

        public override bool CanShowRewardedAd()
        {
            if (_rewardedAd == null)
                return false;
            
            return _rewardedAd.CanShowAd();
        }

        public void LoadRewardedAd()
        {
            // Clean up the old ad before loading a new one.
            if (_rewardedAd != null)
            {
                DestroyRewardedAd();
            }

            //Debug.Log("[Admob] Loading rewarded ad.");

            // Create our request used to load the ad.
            var adRequest = new AdRequest();

            // Send the request to load the ad.
            RewardedAd.Load(_rewardedAdUnitId, adRequest, (RewardedAd ad, LoadAdError error) =>
            {
                // If the operation failed with a reason.
                if (error != null)
                {
                    if (logInfo!= LogInfo.Production)
                    {
                        Debug.LogError("[Admob] Rewarded ad failed to load an ad with error : " + error);
                    }

                    HandleRewardedAdFailedToLoad();
                    return;
                }

                // If the operation failed for unknown reasons.
                // This is an unexpected error, please report this bug if it happens.
                if (ad == null)
                {
                    if (logInfo!= LogInfo.Production)
                    {
                        Debug.LogError(
                            "[Admob] Unexpected error: Rewarded load event fired with null ad and null error.");
                    }

                    HandleRewardedAdFailedToLoad();
                    return;
                }

                // The operation completed successfully.

                if (logInfo != LogInfo.Production)
                {
                    Debug.Log("[Admob] Rewarded ad loaded with response : " + ad.GetResponseInfo());
                }
                _rewardedAd = ad;
                _rewardedRetryCount = 0;

                // Register to ad events to extend functionality.
                RegisterRewardedAdEventHandlers(ad);
            });
        }

        public override void ShowRewardedAd(Action onGetRewardedAd = null)
        {
            base.ShowRewardedAd(onGetRewardedAd);

            if (_rewardedAd != null && _rewardedAd.CanShowAd())
            {
                //Debug.Log("[Admob] Showing rewarded ad.");
                _rewardedAd.Show((Reward reward) =>
                {
                    /*Debug.Log(String.Format("[Admob] Rewarded ad granted a reward: {0} {1}",
                        reward.Amount,
                        reward.Type));*/

                    onGetRewardedAd?.Invoke();
                });
            }
            else
            {
                //Debug.LogError("[Admob] Rewarded ad is not ready yet.");
            }
        }

        public void DestroyRewardedAd()
        {
            if (_rewardedAd != null)
            {
                //Debug.Log("[Admob] Destroying rewarded ad.");
                _rewardedAd.Destroy();
                _rewardedAd = null;
            }
        }

        private void RegisterRewardedAdEventHandlers(RewardedAd ad)
        {
            // Raised when the ad is estimated to have earned money.
            ad.OnAdPaid += (AdValue adValue) =>
            {
                /*Debug.Log(String.Format("[Admob] Rewarded ad paid {0} {1}.",
                    adValue.Value,
                    adValue.CurrencyCode));*/
                
                RevenueManager.LogRevenue(new AdsRevenue()
                {
                    mediationType = MediationType.AdMob,
                    adFormat = "reward",
                    adUnitId = _rewardedAdUnitId,
                    networkName = "admob",
                    networkPlacement = "admob",
                    revenue = adValue.Value/1000000f,
                    currencyCode = adValue.CurrencyCode,
                });
                
                DestroyRewardedAd();
            };


            // Raised when an impression is recorded for an ad.
            ad.OnAdImpressionRecorded += () =>
            {
                /*Debug.Log("[Admob] Rewarded ad recorded an impression.");
                DestroyRewardedAd();*/
            };


            // Raised when a click is recorded for an ad.
            ad.OnAdClicked += () =>
            {
                //Debug.Log("[Admob] Rewarded ad was clicked.");
            };


            // Raised when the ad opened full screen content.
            ad.OnAdFullScreenContentOpened += () =>
            {
                //Debug.Log("[Admob] Rewarded ad full screen content opened.");

                canShowAoaWhenAppFocus = false;
            };


            // Raised when the ad closed full screen content.
            ad.OnAdFullScreenContentClosed += () =>
            {
                //Debug.Log("[Admob] Rewarded ad full screen content closed.");
                
                //canShowAoaWhenAppFocus = false;
                
                LoadRewardedAd();
            };


            // Raised when the ad failed to open full screen content.
            ad.OnAdFullScreenContentFailed += (AdError error) =>
            {
                /*Debug.LogError("[Admob] Rewarded ad failed to open full screen content with error : "
                               + error);*/
                LoadRewardedAd();
            };
        }


        void HandleRewardedAdFailedToLoad()
        {
            _rewardedRetryCount++;

            float delay = Mathf.Min(Mathf.Pow(2, Mathf.Min(6, _rewardedRetryCount)), maxTimeRetryAttempt);

            Invoke(nameof(LoadRewardedAd), delay);
        }
        
        #endregion


        #region Rewarded Interstitial Ad Methods

        private RewardedInterstitialAd _rewardedInterstitialAd;
        private int _rewardedInterstitialRetryCount;

        public override void InitRewardedInterstitialAd()
        {
            base.InitRewardedInterstitialAd();

            InitializeRewardedInterstitialAd();   
        }


        void InitializeRewardedInterstitialAd()
        {
            LoadRewardedInterstitialAd();
        }
        
        public override bool CanShowRewardedInterstitialAd()
        {
            if (_rewardedInterstitialAd == null)
                return false;
            
            return _rewardedInterstitialAd.CanShowAd();
        }

        public void LoadRewardedInterstitialAd()
        {
            // Clean up the old ad before loading a new one.
            if (_rewardedInterstitialAd != null)
            {
                DestroyRewardedInterstitialAd();
            }

            //Debug.Log("[Admob] Loading rewarded interstitial ad.");

            // Create our request used to load the ad.
            var adRequest = new AdRequest();

            // Send the request to load the ad.
            RewardedInterstitialAd.Load(_rewardedInterstitialAdUnitId, adRequest,
                (RewardedInterstitialAd ad, LoadAdError error) =>
                {
                    // If the operation failed with a reason.
                    if (error != null)
                    {
                        Debug.LogError("[Admob] Rewarded interstitial ad failed to load an ad with error : "
                                       + error);

                        HandleRewardedInterstitialAdFailedToLoad();
                        return;
                    }

                    // If the operation failed for unknown reasons.
                    // This is an unexpexted error, please report this bug if it happens.
                    if (ad == null)
                    {
                        Debug.LogError(
                            "[Admob] Unexpected error: Rewarded interstitial load event fired with null ad and null error.");

                        HandleRewardedInterstitialAdFailedToLoad();
                        return;
                    }

                    // The operation completed successfully.
                    Debug.Log("[Admob] Rewarded interstitial ad loaded with response : "
                              + ad.GetResponseInfo());
                    _rewardedInterstitialAd = ad;
                    _rewardedInterstitialRetryCount = 0;

                    // Register to ad events to extend functionality.
                    RegisterRewardedInterstitialAdEventHandlers(ad);
                });
        }


        public override void ShowRewardedInterstitialAd(Action onGetRewardedAd = null)
        {
            base.ShowRewardedInterstitialAd(onGetRewardedAd);

            if (_rewardedInterstitialAd != null && _rewardedInterstitialAd.CanShowAd())
            {
                _rewardedInterstitialAd.Show((Reward reward) =>
                {
                    //Debug.Log("[Admob] Rewarded interstitial ad rewarded : " + reward.Amount);

                    onGetRewardedAd?.Invoke();

                });
            }
            else
            {
                //Debug.LogError("[Admob] Rewarded interstitial ad is not ready yet.");
            }

        }


        public void DestroyRewardedInterstitialAd()
        {
            if (_rewardedInterstitialAd != null)
            {
                //Debug.Log("[Admob] Destroying rewarded interstitial ad.");
                _rewardedInterstitialAd.Destroy();
                _rewardedInterstitialAd = null;
            }
        }

        void RegisterRewardedInterstitialAdEventHandlers(RewardedInterstitialAd ad)
        {
            // Raised when the ad is estimated to have earned money.
            ad.OnAdPaid += (AdValue adValue) =>
            {
                /*Debug.Log(String.Format("[Admob] Rewarded interstitial ad paid {0} {1}.",
                    adValue.Value,
                    adValue.CurrencyCode));*/
                
                RevenueManager.LogRevenue(new AdsRevenue()
                {
                    mediationType = MediationType.AdMob,
                    adFormat = "reward_inter",
                    adUnitId = _rewardedInterstitialAdUnitId,
                    networkName = "admob",
                    networkPlacement = "admob",
                    revenue = adValue.Value/1000000f,
                    currencyCode = adValue.CurrencyCode,
                });
            };


            // Raised when an impression is recorded for an ad.
            ad.OnAdImpressionRecorded += () =>
            {
                //Debug.Log("[Admob] Rewarded interstitial ad recorded an impression.");
            };


            // Raised when a click is recorded for an ad.
            ad.OnAdClicked += () =>
            {
                //Debug.Log("[Admob] Rewarded interstitial ad was clicked.");
            };


            // Raised when an ad opened full screen content.
            ad.OnAdFullScreenContentOpened += () =>
            {
                //Debug.Log("[Admob] Rewarded interstitial ad full screen content opened.");


                canShowAoaWhenAppFocus = true;
            };


            // Raised when the ad closed full screen content.
            ad.OnAdFullScreenContentClosed += () =>
            {
                //Debug.Log("[Admob] Rewarded interstitial ad full screen content closed.");

                canShowAoaWhenAppFocus = false;
                
                LoadRewardedInterstitialAd();
            };
            // Raised when the ad failed to open full screen content.
            ad.OnAdFullScreenContentFailed += (AdError error) =>
            {
                /*Debug.LogError("[Admob] Rewarded interstitial ad failed to open full screen content" +
                               " with error : " + error);*/
                LoadRewardedInterstitialAd();
            };
        }

        void HandleRewardedInterstitialAdFailedToLoad()
        {
            _rewardedInterstitialRetryCount++;

            float delay = Mathf.Min(Mathf.Pow(2, Mathf.Min(6, _rewardedInterstitialRetryCount)), maxTimeRetryAttempt);

            Invoke(nameof(LoadRewardedInterstitialAd), delay);
        }

        #endregion


        #region Banner Ad Methods

        private BannerView _bannerView;

        private bool _canShowBanner;

        public override void InitBannerAd()
        {
            base.InitBannerAd();

            InitializeBannerAd();
        }

        void InitializeBannerAd()
        {
            _canShowBanner = false;

            if (_isBannerCollapsed)
            {
                LoadBannerCollapse();
            }
            else
            {
                LoadBannerAd();
            }
        }

        /// <summary>
        /// Creates a 320x50 banner at top of the screen.
        /// </summary>
        void CreateBannerView()
        {
            //Debug.Log("[Admob] Creating banner view.");

            // If we already have a banner, destroy the old one.
            if (_bannerView != null)
            {
                DestroyBannerAd();
            }

            AdSize adaptiveSize =
                AdSize.GetCurrentOrientationAnchoredAdaptiveBannerAdSizeWithWidth(AdSize.FullWidth);

            // Create a 320x50 banner at top of the screen.
            _bannerView = new BannerView(_bannerAdUnitId, adaptiveSize, AdPosition.Bottom);

            // Listen to events the banner may raise.
            ListenToBannerAdEvents();

            //Debug.Log("[Admob] Banner view created.");
        }

        /// <summary>
        /// Creates the banner view and loads a banner ad.
        /// </summary>
        void LoadBannerAd()
        {
            // Create an instance of a banner view first.

            _canShowBanner = false;
            
            if (_bannerView == null)
            {
                CreateBannerView();
            }

            // Create our request used to load the ad.
            var adRequest = new AdRequest();
            
            // Send the request to load the ad.
            //Debug.Log("[Admob] Loading banner ad.");
            _bannerView.LoadAd(adRequest);
        }


        void LoadBannerCollapse()
        {
            _canShowBanner = false;
            
            if (_bannerView == null)
            {
                CreateBannerView();
            }

            // Create our request used to load the ad.
            var adRequest = new AdRequest();

            adRequest.Extras.Add("collapsible", "bottom");

            // Send the request to load the ad.
            //Debug.Log("[Admob] Loading banner ad.");
            _bannerView.LoadAd(adRequest);
        }

        public override bool CanShowBanner()
        {
            return _canShowBanner;
        }


        public override void ShowBanner()
        {
            if (_bannerView != null)
            {
                //Debug.Log("[Admob] Showing banner view.");
                _bannerView.Show();
            }
        }

        public override void HideBanner()
        {
            if (_bannerView != null)
            {
                //Debug.Log("[Admob] Hiding banner view.");
                _bannerView.Hide();
            }
        }


        public override void ShowBannerCollapse()
        {
            base.ShowBannerCollapse();

            _isShowBannerAfterLoad = true;
            LoadBannerCollapse();
        }

        /// <summary>
        /// Destroys the ad.
        /// When you are finished with a BannerView, make sure to call
        /// the Destroy() method before dropping your reference to it.
        /// </summary>
        void DestroyBannerAd()
        {
            if (_bannerView != null)
            {
                //Debug.Log("[Admob] Destroying banner view.");
                _bannerView.Destroy();
                _bannerView = null;
            }
        }

        /// <summary>
        /// Listen to events the banner may raise.
        /// </summary>
        private void ListenToBannerAdEvents()
        {
            // Raised when an ad is loaded into the banner view.
            _bannerView.OnBannerAdLoaded += () =>
            {
                if (logInfo != LogInfo.Production)
                {
                    Debug.Log("[Admob] Banner view loaded an ad with response : "
                              + _bannerView.GetResponseInfo());
                }

                _canShowBanner = true;

                if (_isShowBannerAfterLoad)
                {
                    ShowBanner();
                }
                else
                {
                    HideBanner();
                }
            };

            // Raised when an ad fails to load into the banner view.
            _bannerView.OnBannerAdLoadFailed += (LoadAdError error) =>
            {
                if (logInfo != LogInfo.Production)
                {
                    Debug.LogError("[Admob] Banner view failed to load an ad with error : " + error);
                }

                _canShowBanner = false;

            };


            // Raised when the ad is estimated to have earned money.
            _bannerView.OnAdPaid += (AdValue adValue) =>
            {
                /*Debug.Log(String.Format("[Admob] Banner view paid {0} {1}.",
                    adValue.Value,
                    adValue.CurrencyCode));*/

                RevenueManager.LogRevenue(new AdsRevenue()
                {
                    mediationType = MediationType.AdMob,
                    adFormat = "banner_collapse",
                    adUnitId = _bannerAdUnitId,
                    networkName = "admob",
                    networkPlacement = "admob",
                    revenue = adValue.Value / 1000000f,
                    currencyCode = adValue.CurrencyCode,
                });
                
                TrackAdRevenue(adValue);
            };


            // Raised when an impression is recorded for an ad.
            _bannerView.OnAdImpressionRecorded += () =>
            {
                //Debug.Log("[Admob] Banner view recorded an impression.");
            };


            // Raised when a click is recorded for an ad.
            _bannerView.OnAdClicked += () =>
            {
                //Debug.Log("[Admob] Banner view was clicked.");
            };


            // Raised when an ad opened full screen content.
            _bannerView.OnAdFullScreenContentOpened += () =>
            {
               // Debug.Log("[Admob] Banner view full screen content opened.");
            };


            // Raised when the ad closed full screen content.
            _bannerView.OnAdFullScreenContentClosed += () =>
            {
                //Debug.Log("[Admob] Banner view full screen content closed.");
            };
        }

        #endregion
        

        private void TrackAdRevenue(AdValue adValue)
        {


        }
#endif
        
        
        
        
        
        #region Editor


#if UNITY_EDITOR

        public override void SetUpAdType(AdType adType)
        {
            mediationType = MediationType.AdMob;
            this.adType = adType;
        }
        
#endif

        #endregion
    }
}