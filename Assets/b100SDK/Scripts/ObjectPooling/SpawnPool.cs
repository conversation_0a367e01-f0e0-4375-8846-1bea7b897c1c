using System;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities;
using UnityEngine;

namespace b100SDK.Scripts.ObjectPooling
{
    public class SpawnPool : BhMonoBehavior
    {
        [SerializeField]
        private BhMonoBehavior prefab;

        [SerializeField]
        private int preloadCount;
        private void Start()
        {
            CreatePool(prefab);
        }

        private void CreatePool<T>(T prefab) where T : BhMonoBehavior
        {
            if (Pool<T>.Instance == null)
            {
                Pool<T>.Instance = new BhPool<T>();
            }
            
            for (int i = 0; i < preloadCount; i++)
            {
                Pool<T>.Instance.Spawn((T)prefab);
            }
            
            Pool<T>.Instance.DespawnAll();
            
            BhDebug.Log("Created pool " + typeof(T));
        }

        public BhMonoBehavior SpawnItem()
        {
            BhMonoBehavior newItem = Instantiate(prefab, transform);
            return newItem;
        }

        public T SpawnItem<T>() where T : BhMonoBehavior
        {
            T newItem = Instantiate<T>((T)prefab, transform);
            return newItem;
        }
    }
}