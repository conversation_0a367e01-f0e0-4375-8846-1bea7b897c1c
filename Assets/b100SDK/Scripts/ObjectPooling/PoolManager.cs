using System.Collections.Generic;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities;
using UnityEngine;

namespace b100SDK.Scripts.ObjectPooling
{
    public static class Pool<T> where T : BhMonoBehavior
    {
        public static BhPool<T> Instance { get; set; }
    }
    public static class PoolManager
    {
        private static void CreatePool<T>() where T : BhMonoBehavior
        {
            if (Pool<T>.Instance == null)
            {
                Pool<T>.Instance = new BhPool<T>();
            }
            BhDebug.Log("Created pool " + typeof(T));
        }
        
        public static void DespawnAll<T>() where T : BhMonoBehavior
        {
            Pool<T>.Instance.DespawnAll();
        }

        public static void Despawn<T>(T instance) where T : BhMonoBehavior
        {
            Pool<T>.Instance.Despawn(instance);
        }

        public static T Spawn<T>(T prefab) where T : BhMonoBehavior
        {
            if (Pool<T>.Instance == null)
            {
                Pool<T>.Instance = new BhPool<T>();
            }
            
            return Pool<T>.Instance.Spawn(prefab);
        }

        public static T Spawn<T>(T prefab, Transform parent) where T : BhMonoBehavior
        {
            if (Pool<T>.Instance == null)
            {
                Pool<T>.Instance = new BhPool<T>();
            }
            
            return Pool<T>.Instance.Spawn(prefab, parent);
        }

        public static T Spawn<T>(T prefab, Vector3 position, Quaternion rotation) where T : BhMonoBehavior
        {
            if (Pool<T>.Instance == null)
            {
                Pool<T>.Instance = new BhPool<T>();
            }
            
            return Pool<T>.Instance.Spawn(prefab, position, rotation);
        }

        public static T Spawn<T>(T prefab, Vector3 position, Quaternion rotation, Transform parent)
            where T : BhMonoBehavior
        {
            if (Pool<T>.Instance == null)
            {
                Pool<T>.Instance = new BhPool<T>();
            }
            
            return Pool<T>.Instance.Spawn(prefab, position, rotation, parent);
        }

        public static List<T> GetSpawnedItems<T>() where T : BhMonoBehavior
        {
            return Pool<T>.Instance.SpawnedItems;
        }

        public static List<T> GetActiveItems<T>() where T : BhMonoBehavior
        {
            return Pool<T>.Instance.ActiveItems;
        }

        public static List<T> GetInactiveItems<T>() where T : BhMonoBehavior
        {
            return Pool<T>.Instance.InactiveItems;
        }
    }
}