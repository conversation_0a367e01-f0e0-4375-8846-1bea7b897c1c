using System.Collections.Generic;
using System.Linq;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities;
using UnityEngine;

namespace b100SDK.Scripts.ObjectPooling
{
    public delegate T GetObjectPool<T>();
    
    public class BhPool<T> where T : BhMonoBehavior
    {
        private List<T> _activeItems = new();
        private Queue<T> _inactiveItems = new();

        public List<T> SpawnedItems
        {
            get
            {
                var newData = new List<T>();
                newData.AddRange(_activeItems);
                newData.AddRange(_inactiveItems);
                return newData;
            }
        }

        public List<T> ActiveItems => _activeItems;
        
        public List<T> InactiveItems => _inactiveItems.ToList();

        public void ClearPool()
        {
            _activeItems.Clear();
            _inactiveItems.Clear();
        }

        public T Spawn(T prefab)
        {
            if (_inactiveItems.Count <= 0)
            {
                return SpawnNew(prefab);
            }
            else
            {
                return GetItemFromPool();
            }
        }

        public T Spawn(T prefab, Transform parentTransform)
        {
            if (_inactiveItems.Count <= 0)
            {
                return SpawnNew(prefab, parentTransform);
            }
            else
            {
                return GetItemFromPool(parentTransform);
            }
        }


        public T Spawn(T prefab, Vector3 position, Quaternion rotation)
        {
            if (_inactiveItems.Count <= 0)
            {
                return SpawnNew(prefab, position, rotation);
            }
            else
            {
                return GetItemFromPool(position, rotation);
            }
        }

        public T Spawn(T prefab, Vector3 position, Quaternion rotation, Transform parentTransform)
        {
            if (_inactiveItems.Count <= 0)
            {
                return SpawnNew(prefab, position, rotation, parentTransform);
            }
            else
            {
                return GetItemFromPool(position, rotation, parentTransform);
            }
        }



        #region Spawn

        
        T GetItemFromPool()
        {
            var item = _inactiveItems.Dequeue();
            item.gameObject.SetActiveWithChecker(true);
            item.OnSpawned();
            return item;
        }

        T GetItemFromPool(Transform parentTransform)
        {
            var item = GetItemFromPool();
            
            item.transform.SetParent(parentTransform);
            return item;
        }

        T GetItemFromPool(Vector3 position, Quaternion rotation)
        {
            var item = GetItemFromPool();
            
            item.transform.SetPositionAndRotation(position, rotation);
            return item;
        }
        
        T GetItemFromPool(Vector3 position, Quaternion rotation, Transform parentTransform)
        {
            var item = GetItemFromPool();
            item.transform.SetParent(parentTransform);
            item.transform.SetPositionAndRotation(position, rotation);
            return item;
        }
        
        T SpawnNew(T prefab)
        {
            var newItem = Object.Instantiate(prefab);
            _activeItems.Add(newItem);
            newItem.OnSpawned();
            return newItem;
        }

        T SpawnNew(T prefab, Transform parentTransform)
        {
            var item = SpawnNew(prefab);
            item.transform.SetParent(parentTransform);
            return item;
        }

        T SpawnNew(T prefab, Vector3 position, Quaternion rotation)
        {
            var item = SpawnNew(prefab);
            item.transform.SetPositionAndRotation(position, rotation);
            return item;
        }

        T SpawnNew(T prefab, Vector3 position, Quaternion rotation, Transform parentTransform)
        {
            var item = SpawnNew(prefab);
            item.transform.SetParent(parentTransform);
            item.transform.SetPositionAndRotation(position, rotation);
            return item;
        }


        #endregion



        #region Despawn



        public void Despawn(T item)
        {
            _inactiveItems.Enqueue(item);
            item.OnDespawned();
            item.gameObject.SetActiveWithChecker(false);
        }

        public void DespawnAll()
        {
            for (int j = 0; j < _activeItems.Count; j++)
            {
                var item = _activeItems[j];
                if (!_inactiveItems.Contains(item))
                {
                    Despawn(item);
                }
            }
        }

        #endregion

    }
}