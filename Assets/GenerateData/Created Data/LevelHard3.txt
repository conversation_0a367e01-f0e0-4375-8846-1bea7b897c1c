[{"id": "1", "q": "001,3,5,7,9,B.20,2,5,7,A,C.40,2,4,6,8,A,C.62,5,7,A.82,4,6,8,A.90,C.A2,4,6,8,A.B0,C.C2,4,6,8,A;101,3,5,7,9,B.20,2,A,C.36.42,A.65,7.82,4,8,A.96.A0,C.B2,A.C5,7:KRLSRY265Z1GMT2KPZPTWVHLMTVG6HQVSQY2W7L71VZOHT9YRZ95Y1155RHLC7C662O7", "hasFlower": 0, "hasSeason": 0}, {"id": "2", "q": "000,2,4,8,A,C.20,3,5,7,9,C.40,3,6,9,C.61,3,5,7,9,B.80,2,4,6,8,A,C.A0,2,4,8,A,C.B6.C1,4,8,B.D6.E0,4,8,C;103,9.10,C.24,8.40,6,C.62,A.74,8.82,A.A0,C.B6.C1,B.D4,8.E0,C:2T7YW4VUUOJG2BI1CNIM68301CYECXGMR698FEP0JOMBWQPZVACMKZ59QR54SASF7N3TKX", "hasFlower": 0, "hasSeason": 0}, {"id": "3", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.80,2,4,6,8,A,C,E.A0,2,4,6,8,A,C,E.C0,2,4,6,8,A,C,E.E1,4,6,8,A,D;105,9.50,E.66,8.72,C.96,8.A0,E:37X7BJDPHNP3DE9XM3IB36HNN2N96T10521Z25HM2XXTG0515ZG7C47BH4E1SPI4PB4FFJKCSK", "hasFlower": 0, "hasSeason": 0}, {"id": "4", "q": "000,2,4,6,8,A,C.22,5,7,A.40,2,4,8,A,C.60,2,4,6,8,A,C.80,3,5,7,9,C.A0,2,4,8,A,C.C1,3,5,7,9,B.E0,2,4,6,8,A,C;100,4,8,C.16.22,A.40,2,A,C.62,A.76.A1,B.C1,4,8,B.E3,6,9:FR6183FG97QRL147HLPNUYGTCY20DV089YA24NWTKFO8DS3JHU8QWKSSSP6OG9FCY9ABVGBJ", "hasFlower": 0, "hasSeason": 0}, {"id": "5", "q": "001,4,8,B.20,2,5,7,A,C.40,2,4,6,8,A,C.60,2,4,6,8,A,C.80,3,6,9,C.A2,4,6,8,A.B0,C.C3,9;101,B.20,5,7,C.32,A.44,8.50,2,6,A,C.64,8.80,6,C.A2,4,8,A.B0,C;225,7.32,A.53,6,9.86.A2,A:H77HWP7NR5Y81V5NEEILR0148LNM9RP91W1EUFUCWOOCIHKWE7KOH9YVF4IRMUIU0TMT95N5MO", "hasFlower": 0, "hasSeason": 0}, {"id": "6", "q": "000,3,5,7,9,B,E.20,3,5,7,9,B,E.40,2,6,8,C,E.54,A<PERSON>61,6,8,D.73,B.80,5,9,E.92,7,C.A4,A.B0,2,6,8,C,E.D0,3,5,7,9,B,E;100,6,8,E.14,A.46,8.61,6,8,D.85,9.A3,7,B.D4,A;200,E.47:HH8G34ETQ4YY4PHE3GC42BRRBY0SI20I2B7JPLC2PQ7YRSRT8P7ENLNA80NA8JEQCNQB07CH", "hasFlower": 0, "hasSeason": 0}, {"id": "7", "q": "000,2,4,6,8,A,C.20,3,6,9,C.40,2,4,6,8,A,C.60,3,5,7,9,C.80,2,5,7,A,C.A0,3,5,7,9,C.C0,2,4,8,A,C.D6.E0,2,4,8,A,C;105,7.10,C.23,9.41,B.60,5,7,C.90,5,7,C.D2,A.E0,C:YKNY0VIYQICLPG7CDL9GGE2INEC7Y0QB9KP20ANAADDKCPB7B5AD959BPIVVKEEVNLGL07", "hasFlower": 0, "hasSeason": 0}, {"id": "8", "q": "001,3,5,9,B,D.20,2,5,9,C,E.37.40,2,4,A,C,E.57.60,3,B,E.75,7,9.81,D.93,6,8,B.A0,E.B2,4,6,8,A,C.D0,2,5,9,C,E;111,D.32,C.60,3,7,B,E.75,9.87.93,B.A6,8.B2,C.D0,2,C,E;260,E.77.D0,2,C,E:3YFR2YXXSKUCE7PHLNK65JUIHRLN3NX7EPUEA5HSNJGIBMB7L2II0H2RFJMR6JK7CEUA0FGLKXF2", "hasFlower": 0, "hasSeason": 0}, {"id": "9", "q": "000,2,4,6,8,A,C.21,4,6,8,B.40,2,4,8,A,C.56.60,2,4,8,A,C.76.80,2,4,8,A,C.A0,2,4,6,8,A,C.C0,2,4,6,8,A,C.E0,2,4,6,8,A,C;102,A.15,7.31,B.50,4,8,C.62,A.82,A.94,8.A0,C.C1,6,B.E5,7:8YJQDY8ABHDQDWW2I6M26M6BY0JE3AMQ3B0VI4W0UVAI3VEEHAHEDU2UMQV6I8U42H3YB404WJ8J", "hasFlower": 0, "hasSeason": 0}, {"id": "10", "q": "004,6,8.10,2,A,C.24,6,8.30,C.45,7.50,2,A,C.64,6,8.72,A<PERSON>85,7.92,A.A0,4,8,C.B6.C0,3,9,C<PERSON>D5,7.E0,2,A,C;106.10,3,9,C.25,7.45,7.50,2,A,C.72,A.92,A.A0,C.B6.C3,9.D5,7.E0,C;252,A.92,A.C3,9:ZAGNJGT77TXP2LGFC7AZ2VSVK72XAN8ARKPU6ZCF32SXNTGJYY6LSD1MNQ3M1XQC0TU0RD8ZSC", "hasFlower": 0, "hasSeason": 0}, {"id": "11", "q": "001,3,5,7,9,B.20,2,4,6,8,A,C.40,2,5,7,A,C.60,2,4,8,A,C.80,2,4,6,8,A,C.A1,3,5,7,9,B.C0,3,5,7,9,C.E0,5,7,C.F2,A.G0,4,6,8,C;102,6,A.24,8.50,2,A,C.72,4,8,A.A2,4,8,A.C0,6,C.G6:RMGBMTM80UFIT3OQY4990OU7GD7TQ0BYDFYRRF80G8Z9TZUY8WUB47IE3CCZQ4WMC9Q4B7RGEFZC", "hasFlower": 0, "hasSeason": 0}, {"id": "12", "q": "000,3,5,7,9,C.20,3,6,9,C.40,2,4,8,A,C.56.60,2,4,8,A,C.76.80,4,8,C.92,6,A.A4,8.B0,2,A,C.D0,3,6,9,C;104,6,8.10,C.26.41,3,9,B.64,6,8.70,C.94,6,8.B1,B.D6;204,8.41,3,9,B.65,7.95,7.B1,B;304,8;404,8:DDHIBTL8LWXGFTYFBLJMRHMWP2WR4GNC2MG8NN8FDRI6GTM4DVH1FIT2PHJ6LV1CIY5JE23NEJ583RXW", "hasFlower": 0, "hasSeason": 0}, {"id": "13", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.80,2,4,6,8,A,C,E.A0,2,4,6,8,A,C,E.C0,2,4,6,8,A,C,E.E0,2,4,6,8,A,C,E.G0,2,4,6,8,A,C,E.I0,2,4,6,8,A,C,E:AJ3ZGTMIHEKTCKT3IMSMV6M4GYG65EK5XIC6RJRSST4CCEGAJHXZYZJ35IHR4XZRYA563SVVYXAH4EVK", "hasFlower": 0, "hasSeason": 0}, {"id": "14", "q": "001,3,5,7,9,B.22,6,A.30,4,8,C<PERSON>42,A<PERSON>50,4,6,8,C.71,3,5,7,9,B.90,2,A,C.A4,6,8.B0,C<PERSON>C4,6,8.D1,B.E3,6,9.G0,2,4,6,8,A,C;102,5,7,A.30,C.42,A.56.64,8.72,A.B0,4,8,C.C6.D1,B.G0,4,8,C;202,A.D1,B:9O202V4SKHKNTFV1BCLO44BSE9PO211MP9KBTHFNMFSGP10NVDEHFTYHYT2XPYLNKDSGGCVXBYO9G4", "hasFlower": 0, "hasSeason": 0}, {"id": "15", "q": "000,2,4,6,8,A.20,3,5,7,A.40,2,4,6,8,A.60,2,8,A.74,6.90,2,8,A.A4,6.B0,2,8,A.C5.D0,3,7,A.E5;115.20,A.33,7.40,A.60,2,8,A.74,6.90,2,8,A.B0,A.D0,4,6,A;230,A.62,8.92,8.C0,A;330,A.C0,A;430,A.C0,A:MCSB5ROPRAS4J8BWP5NIKUMJBJQVXXGQWVOHVRAGWUS4MANAQP8HVMYGB8HYCWYI48JUGS4QYPHKRU", "hasFlower": 0, "hasSeason": 0}, {"id": "16", "q": "001,3,5,7,9,B.20,3,6,9,C.40,3,6,9,C.60,2,4,6,8,A,C.80,6,C.A0,2,4,8,A,C.B6.C1,3,9,B.D6.E0,2,4,8,A,C.F6.G0,2,A,C;106.23,9.46.50,3,9,C.65,7.A2,4,8,A.C1,3,9,B.D6.E3,9.F1,B;265,7.B2,A:TP9KKZ6MC6T9THYJ8KNSFHLP88J8RPC7Y774PBLMFYR4H4ZSYSKBHR4FBFLZT6NLNNJ6Z9RS9B7J", "hasFlower": 0, "hasSeason": 0}, {"id": "17", "q": "000,2,4,6,8,A,C,E.20,3,5,9,B,E.37.40,2,4,A,C,E.57.60,3,B,E.75,7,9.80,2,C,E.94,7,A.A0,2,C,E.B4,7,A.C0,2,C,<PERSON><PERSON>D4,A.E1,6,8,D;105,7,9.23,B.60,3,B,E.82,C.90,E.C2,C;263,B.C2,C:J6HE79AKYIC4SHBCX1E0GK4BLPV0H9PU6CE72KZICSYVMHA2Z3JIFW2EILSS1UFX2MKW030G", "hasFlower": 0, "hasSeason": 0}, {"id": "18", "q": "001,3,6,8,B,<PERSON><PERSON>23,5,9,B.31,7,D<PERSON>44,A<PERSON>50,7,E<PERSON>62,5,9,C.70,7,E.82,4,A,C<PERSON>96,8.A0,4,A,E.B2,7,C<PERSON>C5,9.D3,B<PERSON>E0,5,9,E;101,6,8,D.13,B.25,9.47.50,E.65,7,9.70,E<PERSON>83,7,B.95,9.A0,E.B7.C5,9:L4935W33KRZVXDN91LB45QSKJB7RW5QS1LQJDK7Z71ZRWVJXS51NR7LZW3QS99XDJKBVDBVX", "hasFlower": 0, "hasSeason": 0}, {"id": "19", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,2,4,6,8,A,C.60,2,4,6,8,A,C.80,2,4,6,8,A,C.A0,2,4,6,8,A,C.C0,2,4,6,8,A,C.E0,2,4,6,8,A,C;100,C.14,6,8.21,B.35,7.42,A.B2,4,8,A.D1,B.E4,6,8:7YYCYUVHU771FT33U4L2GL3K3GRF1S179RL9VPVTS84TP8HY8RP414S9C92TPCJ0LJU0KVSR8OOC", "hasFlower": 0, "hasSeason": 0}, {"id": "20", "q": "000,3,5,7,9,B,E.20,2,4,6,8,A,C,E.40,2,4,7,A,C,E.60,5,9,E.72,7,C.80,4,A,E.96,8.A0,2,4,A,C,E.B7.C0,2,4,A,C,E.D6,8.E0,2,4,A,C,E;110,5,9,E.32,C.40,E.B0,2,C,E.D4,A.E0,E:8QUA5J8VEC9QOY6G539U79XY8GHRVX7K33V8QGVKYEL53XJAHXA9OU5YHALORK2HGKCO26UQ", "hasFlower": 0, "hasSeason": 0}, {"id": "21", "q": "001,3,5,7,9,B.20,3,9,C.35,7.40,2,A,C<PERSON>54,8.60,6,C<PERSON>73,9.81,5,7,B.93,9.A1,5,7,B.C0,3,5,7,9,C.E3,6,9;102,4,6,8,A.23,9.30,6,C.42,A.50,4,8,C.84,8.91,B.C0,3,6,9,C.E6;203,9.40,C.54,8:C05SHDKZEE039OS8I0IDYIGQGYTZ5E8YZORA9TDPHIW1T0C3EPX3PZKSTQ3ADXY8OWPKRO1KS8", "hasFlower": 0, "hasSeason": 0}, {"id": "22", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,2,4,6,8,A,C.60,2,4,6,8,A,C.80,2,4,6,8,A,C.A0,2,4,6,8,A,C.C0,2,4,6,8,A,C.E0,2,4,6,8,A,C.G0,2,4,6,8,A,C;112,6,A.43,9.55,7.60,C.83,9.B0,<PERSON><PERSON>C5,7.D3,9.F5,7.G2,A:NZV32I4H2IHW3ON6HKWY0PSPLOWZLKZ71029PUY7OUNP9I3L6U6SOYSWH1VV24I7S64LK3Y9970Z4V1NU0K1", "hasFlower": 0, "hasSeason": 0}, {"id": "23", "q": "000,4,A,E.12,6,8,C.20,4,A,E.43,6,8,B.50,E.62,4,6,8,A,C.70,E.83,6,8,B.A0,2,4,7,A,C,E.C0,3,5,9,B,E.D7.E0,2,4,A,C,E;104,A.10,7,E.24,A.50,3,B,E.65,9.77.93,B.A0,E.C0,3,B,E.E0,3,B,E:ZEUMD5LXUVFZMT55VYCIVA8CYEUX08EAHI2QTNDU0V09FAAN92NQQMHFCX22ECNHM850OQFLOXH8", "hasFlower": 0, "hasSeason": 0}, {"id": "24", "q": "000,2,4,7,A,C,E.20,2,4,6,8,A,C,E.40,2,4,7,A,C,E.60,2,4,6,8,A,C,E.80,3,5,7,9,B,E.A0,2,4,6,8,A,C,E.C0,2,4,6,8,A,C,E.E0,2,4,A,C,E.F6,8.G0,2,4,A,C,E;112,C.24,A.37.67.86,8.B7.F4,A.G2,C:OLW1B83EXLKV43SJW0W428BADVOUPV1QCT6K9RJKCHXS5B2F79JDBHAKDTPUCQZ64YVEFJ7WD4CY50RZ", "hasFlower": 0, "hasSeason": 0}, {"id": "25", "q": "000,2,4,6,8,A,C,E.21,4,6,8,A,D.40,2,4,6,8,A,C,E.61,3,5,7,9,B,D.80,3,6,8,B,E.A0,2,4,6,8,A,C,E.C0,2,4,6,8,A,C,E.E0,2,5,7,9,C,E;125,9.42,6,8,C.63,7,B.93,7,B.B2,6,8,C.C4,A:RD1QGSBY1XFB84O311998PVKSG8Q6H7G36SJBJSL3ON7U3NNQXNFDO9URAG8B4HXOXV9ARQKRYPL", "hasFlower": 0, "hasSeason": 0}, {"id": "26", "q": "001,3,5,7,9,B.20,2,5,7,A,C.40,4,6,8,C.52,A.66.72,A.80,5,7,C.A1,3,5,7,9,B.C0,4,6,8,C;103,6,9.20,2,5,7,A,C.56.62,A.76.A1,4,8,B.B6.C0,C;216.20,2,A,C.56.76.A1,4,8,B.C0,C;3A1,4,8,B.C0,C;4C0,C:V8YXAELVPZDAS48QKCNN24PULST3T3X2QKLRRPUET9ZZQZKA8YPX9XNUYC2QK2UL7D4N3Y4V3T7A8V", "hasFlower": 0, "hasSeason": 0}, {"id": "27", "q": "000,2,4,6,8,A.20,2,4,6,8,A.41,3,5,7,9.60,2,4,6,8,A.80,2,4,6,8,A.A0,2,4,6,8,A.C0,2,4,6,8,A.E0,2,4,6,8,A;104,6.10,A.25.41,4,6,9.60,3,7,A.75.81,3,7,9.A0,2,8,A.B4,6.D1,5,9:07M6WQC6PLLRCQI5LZK0PKLJ6ZR5ZK0XJ47JKMP7JC556IX8FTIWW8ICZGFQPGGW8RG0RTQ847", "hasFlower": 0, "hasSeason": 0}, {"id": "28", "q": "000,2,4,6,8,A,C.20,3,5,7,9,C.40,2,4,6,8,A,C.60,2,5,7,A,C.81,3,6,9,B.A0,2,4,6,8,A,C.C1,3,5,7,9,B.E0,2,5,7,A,C;110,5,7,C.23,9.40,2,5,7,A,C.61,6,B.83,9.91,6,B.B1,5,7,B.D2,5,7,A.E0,C:JE5OQ6PGBZ6W42E43ZGVZ5TOZ36T2VW2XBOJBUEXKVXTE5PXUVTDG43J9U99WJUGQKK65D43OWD2BKD9", "hasFlower": 0, "hasSeason": 0}, {"id": "29", "q": "000,2,4,7,A,C,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.80,2,4,6,8,A,C,E.A1,3,5,7,9,B,D.C0,2,4,6,8,A,C,E.E1,3,6,8,B,D;122,7,C.40,4,7,A,E.A4,A.B7.D2,7,C:6YE5UOVRJODSU2JV67O7T9H2Z0LI75QWNLYTPZIWHZ7DHNUY26XP0VFAH6E9LRAVXSYULFZQ2O", "hasFlower": 0, "hasSeason": 0}, {"id": "30", "q": "000,3,5,7,A.21,3,5,7,9.40,2,5,8,A.60,2,5,8,A.80,2,4,6,8,A.A0,4,6,A.B2,8.C0,4,6,A.D2,8.E0,A;103,7.22,4,6,8.40,2,5,8,A.60,2,5,8,A.80,2,8,A.94,6.B0,2,4,6,8,A.D0,2,8,A:N72E047RBW1VOGOPNJV6S9LR5POJMJ90NX4EM5NXCOSGGBPXE61BERPJQSVLVWX2GSQ9BR9C", "hasFlower": 0, "hasSeason": 0}, {"id": "31", "q": "000,2,4,6,8,A,C,E.20,3,5,9,B,E.37.40,2,4,A,<PERSON>,E<PERSON>56,8.61,D.73,6,8,B.80,E.92,4,6,8,A,C.B1,5,7,9,D.D0,2,4,7,A,C,E.F0,3,5,9,B,E.G7;125,9.33,B.61,7,D.95,9.B1,5,7,9,D.D3,B.F5,9:LJXGZRMVOWKRCHYJS7EL7VSMOQD142W1F2W4QFODGPZC6PHNAQYN1CUCY1AO96UXW9UUBYEQKB", "hasFlower": 0, "hasSeason": 0}, {"id": "32", "q": "002,4,8,A.10,6,C<PERSON>22,A.35,7.40,2,A,C<PERSON>54,6,8.60,2,A,C<PERSON>74,6,8.81,B.95,7.A0,2,A,C.B4,6,8.C2,<PERSON><PERSON>D0,5,7,C<PERSON>E3,9;111,6,B.35,7.40,2,A,<PERSON><PERSON>54,6,8.61,B.75,7.91,6,B.B2,6,A.D5,7;216.45,7.75,7.B6:VY6S2Z39H0M2FWDSUFEMVGZ3UZ9SI0OE2VEE6W306F9OY0RVGZ62GW39GUHRIWHHDYUDSDIIYF", "hasFlower": 0, "hasSeason": 0}, {"id": "33", "q": "000,2,4,7,A,C,E.20,2,4,7,A,C,E.40,2,4,6,8,A,C,E.61,3,5,7,9,B,D.80,2,4,7,A,C,E.A0,2,4,6,8,A,C,E.C0,2,4,7,A,C,E.E0,2,4,6,8,A,C,E;114,A.20,E.40,E.56,8.74,A.97.B0,E.D0,E.E4,A:74VBDAXDN765B2467QA8U40RX6M8G5U2V0VG2X0NAM0QDD8RLBR5LNGQQLUN5627V8AXBM4LMURG", "hasFlower": 0, "hasSeason": 0}, {"id": "34", "q": "000,2,4,6,8,A,C.20,2,4,8,A,C.36.40,2,4,8,A,C.56.60,2,4,8,A,C.80,4,6,8,C.A0,2,5,7,A,C.C0,3,5,7,9,C.E0,2,5,7,A,C.G1,3,5,7,9,B;121,B.40,3,9,C.55,7.63,9.80,C.A2,5,7,A.C5,7.D0,C.F1,B:F21GMX97WYXDJFZG9HY7EHKCE4MB41G78JHSKKY8ZEFH8M1W2BSJXCCWM98YZBECB7DZ4K9XG1W4FJ", "hasFlower": 0, "hasSeason": 0}, {"id": "35", "q": "001,3,5,7,9,B.20,2,5,7,A,C.40,3,9,C.60,2,5,7,A,C.80,4,6,8,C.92,A.A0,C<PERSON>B3,9.C5,7.D0,2,A,C.E5,7;101,3,6,9,B.20,5,7,C.40,3,9,C.60,2,6,A,C.84,6,8.90,2,A,C.B3,9.D0,5,7,C;202,A.20,C.62,A.92,A.D0,C:2NKVOZI4O1JHNSZXKW07AYRZH6MBDL5Z961TLJ3JD7VOWSXRJPV02BY9HDARGMXH385PLRTIEOD8EXG4VL", "hasFlower": 0, "hasSeason": 0}, {"id": "36", "q": "001,3,5,7,9,B.21,3,6,9,B.40,4,6,8,C<PERSON>52,A.60,5,7,C<PERSON>73,9.80,5,7,C.92,A.A0,4,6,8,C.B2,<PERSON><PERSON>C5,7.D0,C<PERSON>E2,4,8,A.F6.G0,2,4,8,A,C;101,5,7,B.22,A.40,C.52,6,A.70,3,9,C.A0,3,9,C.C6.D0,C.F2,5,7,A:WBPDGOU3WQZKGMLNYDQ6YO6H9F001YMJKI7L9ZWHQU4S1B6R3XNEKF7BW81DJ4QTT8DKEPY6SRI1BX", "hasFlower": 0, "hasSeason": 0}, {"id": "37", "q": "010,2,4,6,8,A,C.30,2,4,6,8,A,C.50,2,4,6,8,A,C.70,2,4,6,8,A,C.90,2,4,6,8,A,C.B0,2,4,6,8,A,C.D0,2,4,6,8,A,C;111,3,5,7,9,B.30,3,9,C.51,4,8,B.66.85,7.A1,4,8,B.C0,3,9,C.D5,7:0LV7DSRQ0A9EFA7EZCZ0XI16E4IBQRS94ABBCBQ0D7FVV7VJCCSSAX91QX16DZFLXI4E4FDJ1I9Z", "hasFlower": 0, "hasSeason": 0}, {"id": "38", "q": "000,2,4,8,A,C.20,3,5,7,9,C.40,3,5,7,9,C.60,2,6,A,C.80,3,5,7,9,C.A0,5,7,C.B2,A.C0,5,7,C.D3,9.E0,C.F3,5,7,9.G0,C;104,8.10,C.23,9.46.61,B.83,9.A5,7.B1,B.F3,9.G0,C;210,C.61,B.A5,7.B1,B.G0,C:ZEXM8HOSNLHTXV2258AEJX5ZRHQVRRVDA6RLAPLEZ71NT91M7024A87Q0D9PJ4T2X86OVTMELS7ZHM", "hasFlower": 0, "hasSeason": 0}, {"id": "39", "q": "002,5,7,9,C.10,E.22,4,6,8,A,C.40,3,5,7,9,B,E.60,5,9,E<PERSON>72,7,C<PERSON>80,<PERSON><PERSON>95,9.A0,2,7,C,<PERSON><PERSON>B4,<PERSON><PERSON>C1,6,8,<PERSON><PERSON>D3,<PERSON><PERSON>E0,5,7,9,E;102,6,8,C.22,4,A,C.36,8.55,9.60,E<PERSON>72,C.80,E<PERSON>95,9.A2,7,C.C1,6,8,D.D3,B.E5,9:EZKN0WX6PTYGCUZF3KQNOGISUPC3FKF2O92Q1N0N3AXR6LMGH773YERFA4C8G8W41KM7PT7JIH9JPCSL", "hasFlower": 0, "hasSeason": 0}, {"id": "40", "q": "000,2,4,6,8,A.20,2,4,6,8,A.41,4,6,9.61,3,5,7,9.81,4,6,9.A1,3,5,7,9.C0,2,4,6,8,A.E1,3,5,7,9;101,3,7,9.15.21,3,7,9.44,6.51,9.65.71,9.95.A1,9.B3,5,7.D1,3,7,9.E5;215.23,7.44,6.65.95.B4,6.D3,7.E5:QMF5TTMCLRGVZFGJQHIFYI2978Q2CS5243017NWYGVE2UHRGBU0ZXF1UQAESN4O83XSWUBK1LAKS9J1O", "hasFlower": 0, "hasSeason": 0}, {"id": "41", "q": "000,4,7,A,E.12,C.25,9.31,3,7,B,<PERSON><PERSON>52,4,A,C<PERSON>60,7,<PERSON><PERSON>73,<PERSON><PERSON>80,7,E.92,4,A,C.B1,3,6,8,B,D.D2,5,9,C.E0,E;100,4,A,E.22,5,9,C.42,C.67.70,E.87.94,A.A2,C.B7.C2,C.D5,9.E0,E;200,E.22,5,9,C.67.D5,9;325,9.D5,9;425,9.D5,9:FSWYCMRCM3N9VY31K82JCZILF25AKNQ592IR85MGIX8I18SWW5VA3XYZMNGQ3LU2RU4JRYX4W4XQK1K41QCN", "hasFlower": 0, "hasSeason": 0}, {"id": "42", "q": "002,4,6,8,A.10,C.22,4,6,8,A.30,C.42,5,7,A.50,C<PERSON>62,4,6,8,A.70,C.82,4,6,8,A.90,C.A3,5,7,9.B1,B<PERSON>C3,5,7,9.D0,C<PERSON>E3,5,7,9.F0,C.G2,5,7,A;103,9.11,5,7,B.23,9.30,C.45,7.51,B.D5,7.E0,C<PERSON>G5,7;213,9.51,B:DQ6VWSJI62QWCBZXV1JG42BFIQXMZJUMBIWZRD1FRMRRUJ4SFQ6S82W2SIVVGMXU16FXD8GBC8ZG18UD", "hasFlower": 0, "hasSeason": 0}, {"id": "43", "q": "001,3,5,7,9,B,D.22,4,6,8,A,C.30,E.42,5,7,9,C.50,E.62,5,7,9,C.70,E<PERSON>84,6,8,A.91,D.A4,7,A.B0,2,C,E.C4,6,8,A.D2,C.E0,4,6,8,A,E;101,5,7,9,D.22,5,9,C.37.40,E.52,6,8,C.70,E.94,A.A7.B0,E.C7.D2,5,9,C:5U1ZATVHD6627854XUO75IR0RCIO0EZACVKE8VJ7S2XHO6TDGBK94VG571T9JUUOT4884BG6RZGRSJHZHJ", "hasFlower": 0, "hasSeason": 0}, {"id": "44", "q": "000,2,4,6,8,A,C.20,3,6,9,C.40,2,4,8,A,C.56.62,A.70,4,6,8,C.92,A.A0,4,6,8,C.B2,A.C6.D0,3,9,C.E6;102,A.10,6,C.30,C.42,4,8,A.62,6,A.92,A.A0,5,7,C.B2,A.C6.D0,C.E6;220,C.A1,B.B6.D0,6,C;320,C.C6.D0,C:0TQ7A6AG7U58YJ1ZZHRTQQY1D6CUVJMM5VGUYZ8785TATD4DRGGPYPA8EEH44J4D50VPQZMJRRC7PMUV", "hasFlower": 0, "hasSeason": 0}, {"id": "45", "q": "000,2,4,7,A,C,E.21,3,5,9,B,D.41,3,6,8,B,D.60,2,4,6,8,A,C,E.80,2,6,8,C,E.94,A.A2,7,C.B4,A.C0,2,C,E.D4,6,8,A.E0,E;101,3,B,D.23,5,9,B.41,D.60,7,E.80,E.97.C2,4,A,C.D0,6,8,E;213,B.41,D.70,E.D0,E;313,B:VALOJKJO56EEYG5A4X5G608MLDRE9P3R33MJ8PRVOW8OMPYRLGG3XJWB6LX89W5KA2W6M002B4KXEA4DP04K", "hasFlower": 0, "hasSeason": 0}, {"id": "46", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,3,6,9,C.61,3,5,7,9,B.80,2,6,A,C.A0,2,4,6,8,A,C.C0,2,4,6,8,A,C.E1,3,5,7,9,B.G0,2,4,6,8,A,C;104,8.10,C.23,5,7,9.61,B.86.B1,B.C5,7.D2,A.F3,5,7,9.G0,C:6Y6Y7XDML01ERDPZE3A7X8AZD1135PXKE5QK7DEM5Z6HZPH57L8Q3S6K0YMHMQXSR0ASKHQ1PALLS30Y", "hasFlower": 0, "hasSeason": 0}, {"id": "47", "q": "000,2,4,7,A,C,E.20,2,4,7,A,C,E.41,5,7,9,D.60,2,4,6,8,A,C,E.80,2,4,7,A,C,E.A0,4,6,8,A,E.C0,2,5,9,C,E.D7.E1,4,A,D;101,4,7,A,D.22,4,A,C.55,7,9.61,3,B,D.77.82,4,A,C.97.A0,5,9,E.C2,C;214,A.57.62,C.84,A:BHVSKE5H6CQ1GJ2C1Q2OSGPLLBZ61KVBIO5UZHE2VUSJLYQULCN6Z88SRI568ZUVOR5INNBOQG2HKYPGICNK81", "hasFlower": 0, "hasSeason": 0}, {"id": "48", "q": "000,2,4,6,8,A,C,E.20,2,5,7,9,C,E.40,2,4,6,8,A,C,E.60,2,5,9,C,E.77.80,2,4,A,C,E.96,8.A0,2,C,E.B5,9.C1,3,B,<PERSON>.D6,8.E0,2,4,A,C,E.F6,8.G2,4,A,C;112,C.27.30,E.50,E.65,9.70,E<PERSON>82,C.96,8.A0,E<PERSON>B5,9.E0,E.F3,7,B:DTL74H8PRAHREQUTA7UDW8M1EFSOMOEMJ9S5VFWV3NS1U4QOPGFUJLRNANHL9SPRE3CGLF7MHW65NOAQQ6W7CP", "hasFlower": 0, "hasSeason": 0}, {"id": "49", "q": "000,2,4,7,A,C,E.21,3,6,8,B,D.40,3,7,B,E.62,5,7,9,C.70,E.82,4,6,8,A,C.90,E.A2,4,7,A,C.C1,3,6,8,B,D.E1,4,6,8,A,D.G1,3,5,9,B,D;102,7,C.21,7,D.57.62,C.86,8.A4,A.B2,7,C.D1,6,8,D.F1,D.G3,B;227.86,8.D7;386,8.D7;4D7:IR56VZ1GGDQ9R3S7ZF2N7D0CT2ZDZM0PVUD5HPFX00WTNU2AY2T5NTGW5X9FIUKBJLCLYAHNB61GUYF8K83MJYQS", "hasFlower": 0, "hasSeason": 0}, {"id": "50", "q": "000,2,4,6,8,A,C,E.23,6,8,B.30,E.44,6,8,A.50,2,C,E.65,7,9.70,2,C,E.85,7,9.90,2,C,E.A4,A<PERSON>B6,8.C0,2,4,A,C,E.D6,8.E0,2,<PERSON>,E;103,B.16,8.44,7,A.51,D.65,9.71,D.85,9.90,2,C,E.B4,7,A.C1,D.E1,D;203,B.16,8.71,D.92,C;316,8;416,8:XIU27K1CWAFECZPFV7450PEIWH6ZR34GX2RUVGG12EHYVAIPA0RVJCHKAY1J7Z7GSH8JOTJ86O53P2ECRSIZTK1OOK", "hasFlower": 0, "hasSeason": 0}, {"id": "51", "q": "000,2,4,6,8,A,C.21,5,7,B.40,3,5,7,9,C.60,3,5,7,9,C.80,2,5,7,A,C.A0,2,5,7,A,C.C2,5,7,A.D0,C.E2,4,6,8,A.G1,3,5,7,9,B;103,6,9.21,B.36.43,9.55,7.75,7.82,A.95,7.B6.D1,B.E6.F4,8;236.82,A.E6;382,A:1MW88CZJWJSNCWJ0Y1EBE85GO0L8DLZOBYCGSRNSJ0QWMDECS5NQTGGII0QR5LDMTYTLDQYM5N1I1IET", "hasFlower": 0, "hasSeason": 0}, {"id": "52", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,4,6,8,C.52,A.64,6,8.70,2,A,C.85,7.A1,3,5,7,9,B.C0,2,4,6,8,A,C.E0,2,5,7,A,C;100,2,5,7,A,C.24,8.30,C.52,4,8,A.72,6,A.A2,4,8,A.C0,C.D5,7.E1,B;200,2,A,C:UTNS3RHR52SKGOK96HPKM592G5CCP25T3U8M9GLWGKWUCERTERHEBY7L1J9U2JYHOA3E83NT6BA7CLL1", "hasFlower": 0, "hasSeason": 0}, {"id": "53", "q": "000,2,5,7,A,C.20,2,4,6,8,A,C.40,2,4,6,8,A,C.60,3,5,7,9,C.80,2,6,A,C.A0,2,5,7,A,C.C0,2,A,C.D4,6,8.E1,B.F4,6,8.G2,A;102,A.10,C.22,A.34,8.40,C.60,6,C.A2,A.B0,C.E4,8.G2,A;222,A.60,6,C.B0,C;366:Z7AVH4GVBKIA901W1G7QCQ51GQZTHIL2WLBBWBGKWKS1LKPC2TERRQ5E06LOOTVRZ39Z3SU66U6V4PRT", "hasFlower": 0, "hasSeason": 0}, {"id": "54", "q": "001,3,9,B.15,7.20,2,A,C.34,6,8.40,2,A,C.54,6,8.62,A<PERSON>74,6,8.82,A.94,8.A0,2,6,A,C.C0,2,4,6,8,A,C.E0,2,4,6,8,A,C;120,C.34,8.40,C.52,A.72,A.A2,A.B0,C.C4,8.D0,C.E4,8;220,C.72,A.D0,C.E4,8;320,C.D0,C:VZAPQPTYG6C0I05AQO56HWRPXJEAO0VC8RV4XIEYMAL4LPVMLYRW4OTOZ4BBEGQHM86J6BRQEIIY0MBL", "hasFlower": 0, "hasSeason": 0}, {"id": "55", "q": "000,3,5,7,9,C.21,3,6,9,B.40,2,4,8,A,C.60,3,5,7,9,C.80,2,5,7,A,C.A0,2,6,A,C.C0,2,4,8,A,C.E0,2,4,6,8,A,C.G0,2,5,7,A,C;103,9.16.21,3,9,B.64,6,8.70,C.86.A0,C.C1,B.E1,3,5,7,9,B.G0,5,7,C:9HWETLJG808BS4NOX8IIT807HLA3GV4J9PVAON9TPP0Z7S33YP3ENQQOGSZX0NQD9WSOZG4YTLZ4QLBD", "hasFlower": 0, "hasSeason": 0}, {"id": "56", "q": "000,2,4,6,8,A,C.20,2,5,7,A,C.40,3,5,7,9,C.60,3,6,9,C.81,4,6,8,B.A0,2,4,6,8,A,C.C0,3,5,7,9,C.E1,3,5,7,9,B.G0,2,5,7,A,C;102,5,7,A.10,C.26.30,C.43,6,9.A1,B.B5,7.D3,6,9.F6.G0,C;230,C.46.D6:TP2PZMJ8IZNU9FCVKP1V9RODXYJO46WT8MUQE815WENSDIA2YNTXHHBN4GQSR6GFQPBRT484CQRA5LKL", "hasFlower": 0, "hasSeason": 0}, {"id": "57", "q": "000,2,4,8,A,C.20,2,4,8,A,C.36.41,B.53,6,9.60,C.72,<PERSON><PERSON>80,C.92,4,6,8,A.B1,5,7,B.D0,2,4,8,A,C;100,2,4,8,A,C.21,3,9,B.36.53,6,9.60,C.80,C.95,7.B5,7.D1,3,9,B;202,A.21,B.53,9.60,C.B5,7.D1,B;3B5,7;4B6:01IX6QUEA1O7UZH7VFWDPCVSARFGK7DYAKNE3E0B7ADCI3439LRGQ444GM3ENB56R9DLPYGHXWM5ORZS", "hasFlower": 0, "hasSeason": 0}, {"id": "58", "q": "000,2,4,8,A,C.21,3,9,B.35,7.40,2,A,C<PERSON>54,8.60,6,C.72,4,8,A.80,C.94,8.A0,2,6,A,C.C0,2,4,8,A,C.E0,4,6,8,C.F2,A.G0,4,8,C;100,C.12,A.31,B.54,8.71,3,9,B.94,8.A1,6,B.C1,3,9,B.F0,3,9,C;212,A.31,B:BFKHCCVCWQ5XOQDTLPIANJF8LFZDGUNX8FO1UIWBGCZ467KDZ1754MP01ID3ZHJM065PV993RAPI1TR5", "hasFlower": 0, "hasSeason": 0}, {"id": "59", "q": "000,2,4,8,A,C.20,2,4,8,A,C.36.40,2,4,8,A,C.60,3,6,9,C.81,3,6,9,B.A1,3,5,7,9,B.C0,3,5,7,9,C.E0,2,5,7,A,C;104,8.10,2,A,C.24,8.32,A.60,3,9,C.81,B.93,9.A1,B.B3,9.D6.E0,2,A,C;223,9.63,9.93,9:PTGWRT48MXVKIRX3VDCR3VGDDX81WMHJJFFTJH7PKMWTCCIJRKXS17FSD34IHGC1PW147HVFIK4PG37M", "hasFlower": 0, "hasSeason": 0}, {"id": "60", "q": "000,2,4,6,8,A,C.20,2,A,C.40,C.60,C.80,2,A,C.A0,C.C0,C.E0,2,A,C.G0,2,4,6,8,A,C;100,3,5,7,9,C.20,2,A,C.40,C.70,C.90,C.C0,C.E1,B.G1,4,8,B;200,5,7,C.20,2,A,C.70,C.C0,C.F1,B.G4,8;320,C.C0,C.G4,8:IXJFD53OD7292E549LJA6X76PHECCW9KSZ61HCO92PVNVIH6PK3N4PSZ4FH2AW5EVLLJKCEJOK541LOV", "hasFlower": 0, "hasSeason": 0}, {"id": "61", "q": "000,3,5,7,9,C.20,3,6,9,C.40,3,9,C<PERSON>55,7.61,B.73,5,7,9.81,B.95,7.A1,3,9,B.C4,8.D0,6,C.E2,4,8,A;104,8.10,6,C.23,9.40,C.55,7.61,B.73,9.81,B.96.A2,A.C4,8.D0,6,C.E2,4,8,A;204,8.55,7.61,B.81,B.96.A2,A.D0,5,7,C:9DD2TGS2XNKG930C127XBJTJ5UWRXNRTSEQU17Y8ZXC5QDPWBUG1P3NACYW610CTU7GW2Z53KRR53ND87E6A", "hasFlower": 0, "hasSeason": 0}, {"id": "62", "q": "000,2,5,7,9,C,E.20,2,4,6,8,A,C,E.40,2,5,7,9,C,E.60,2,4,6,8,A,C,E.80,2,4,7,A,C,E.A0,2,4,6,8,A,C,E.C0,2,4,6,8,A,C,E.E1,4,6,8,A,D.G0,2,4,6,8,A,C,E;105,9.45,9.60,E.82,7,C.A1,5,9,D.C4,A;245,9:ZYTIVN1N6BNX63K3M7FFFPTKXZBHNJPB7ZHTHJJV75JW63PFROC6XWR1Y3M07IODZHYWPXV15BWCT0DDDYV1", "hasFlower": 0, "hasSeason": 0}, {"id": "63", "q": "000,2,6,A,C.21,3,5,7,9,B.41,5,7,B.60,5,7,C.73,9.80,5,7,C.92,A.A4,6,8.B0,C.C2,4,6,8,A.E0,2,4,8,A,C.F6.G0,2,A,C;101,B.16.35,7.55,7.75,7.80,C.95,7.C3,5,7,9.E2,4,8,A.F0,C.G2,A;216.56.C6.F0,C.G2,A:NLAN299REF6PXN52RTVOWOV2TL5O9APF3727SXLDQ6DS3REEP73P75FOV3WXNE6G6UWXRTG9L5FUWTQV", "hasFlower": 0, "hasSeason": 0}, {"id": "64", "q": "000,2,4,6,8,A,C.20,3,5,7,9,C.40,3,6,9,C.60,2,4,8,A,C.80,2,6,A,C.A0,3,5,7,9,C.C3,5,7,9.D0,<PERSON><PERSON>E5,7.F0,3,9,C.G5,7;110,3,5,7,9,C.30,6,C.43,9.50,C.70,2,A,C.86.A0,5,7,C.C5,7.E0,6,C.F4,8;260,C.86.B6.F4,8:MIO5SL7BI4QVA3SZEXBL8Z9DF5I9ARZG5LRAXMRTMW6TCD94E91T5EYG7YATJCNN0ME016ZL3WDIOF8DQJRV", "hasFlower": 0, "hasSeason": 0}, {"id": "65", "q": "000,2,4,6,8,A,C,E.20,2,5,7,9,C,E.43,5,9,B.50,7,E.62,5,9,C.70,E.82,6,8,C.90,E.A2,4,A,C.B0,6,8,E.C2,4,A,C.E0,2,5,9,C,E.F7.G0,2,C,E;101,5,7,9,D.27.44,A.71,D.86,8.91,D.A4,A.C2,4,A,C.F7.G0,E;244,A.A4,A.C2,C.G0,E:I244XBHPEXXOSWOUSGUHG99RBWHS9DGZWCADDCBPUIA2O2EFAHRF4CWFIPB7O7A4X2079C0ESG07RDFZPUZZIR0E", "hasFlower": 0, "hasSeason": 0}, {"id": "66", "q": "000,3,6,8,B,E.20,2,4,6,8,A,C,E.40,3,7,B,E.55,9.60,2,C,E<PERSON>76,8.80,4,A,E<PERSON>96,8.A0,2,C,E.B5,9.C1,3,B,D.D7.E0,2,5,9,C,E.F7.G1,5,9,D;107.10,E.27.50,E.62,C.70,6,8,E.97.A0,2,C,E.C1,D.E6,8.G1,D;207.10,E.27.50,E.76,8.B1,D.E6,8;376,8:TWIG65V90Z0SUCQGFV6OB1OJJQIPXIGFT6JJKDSIN3Y1R359CPENLAK4ATQZ331XEVUYQBBUMLSHEGUVB6ETDWSH4R1M", "hasFlower": 0, "hasSeason": 0}, {"id": "67", "q": "000,2,5,7,A,C.20,3,5,7,9,C.41,6,B.63,5,7,9.70,C.82,4,6,8,A.90,C.A2,4,6,8,A.C0,2,5,7,A,C.E0,2,4,8,A,C.F6.G0,3,9,C;100,C.20,3,6,9,C.41,6,B.63,9.70,C.86.90,C.C1,6,B.E2,4,8,A.F0,6,C.G3,9;210,C.23,6,9.C6.E4,8.F6;310,C:3OA7YL9D382W2ZH9YG82JG693WKJD48V7ZPVLWYD2PMEWVPZRAL4OEL349ZR7VQ6MEQ7AOHAH8MR4RPYQJOEHQDKJM", "hasFlower": 0, "hasSeason": 0}, {"id": "68", "q": "000,2,4,6,A,C,E,G.18.20,2,4,6,A,C,E,G.38.40,2,4,6,A,C,E,G.61,3,6,8,A,D,F.80,4,6,8,A,C,G.A0,2,4,6,8,A,C,E,G.C1,3,5,7,9,B,D,F.E0,2,4,6,8,A,C,E,G.G0,2,4,6,8,A,C,E,G;100,2,4,6,A,C,E,G.18.38.D8.F7,9.G5,B;2G5,B:ARHYIFTGH9I87FZDAUYAQZRMYMRS14O7HSD4DSOF6755QY4D6M4969TEAQ81GF15G8MU15RZI8ZUEUOTHEE96SIQG7OT", "hasFlower": 0, "hasSeason": 0}, {"id": "69", "q": "000,2,5,7,A,C.21,3,6,9,B.40,2,4,6,8,A,C.60,2,6,A,C.80,3,5,7,9,C.A0,2,A,C.B6.C1,4,8,B.D6.E2,4,8,A.F6.G0,2,4,8,A,C;100,C.21,B.33,9.40,6,C.52,A.60,6,C.80,4,8,C.B6.C4,8.D6.E2,A;221,B.33,9.50,C.66.80,C.B6.E2,A;350,C:SIXO7WTH2LB6FBFE3WS3LEX7C9RCO596NOZ7FW74BB531IR5TXJT3I94MFTJLHCP2EM9X5O80PN8ZC2MW21LME0I", "hasFlower": 0, "hasSeason": 0}, {"id": "70", "q": "001,4,6,8,B.22,4,8,A.36.40,2,4,8,A,C.56.61,4,8,B.80,2,4,6,8,A,C.A5,7.B1,B<PERSON>C3,5,7,9.D0,C<PERSON>E2,5,7,A.G1,3,5,7,9,B;114,8.22,A.35,7.40,2,A,C.55,7.61,B.81,3,5,7,9,B.A5,7.B1,<PERSON><PERSON>C5,7.D0,<PERSON><PERSON>E5,7.F2,A.G4,6,8;236.A6.E6.G5,7:XQ00VFQMUX7TSMTXRQ1A5R1HP4VO6GNNI6UAU67IIIMST0RFSOBR4NO5OHGM044QGGB5SBHAV6T1VHX5313UBNAP", "hasFlower": 0, "hasSeason": 0}, {"id": "71", "q": "001,3,5,9,B,D.20,2,4,6,8,A,C,E.41,3,5,9,B,D.57.60,2,4,A,C,E.77.81,4,A,D.96,8.A2,C.B5,9.C0,2,7,C,E.D4,A.E0,2,C,E;103,5,9,B.11,D.23,7,B.31,D.44,A.52,7,C.60,4,A,E.96,8.A2,C.C1,7,D.D3,B.E1,D;211,D.27.53,B.60,E.97.D2,C;3D2,C:Y5HGK9FWJHJY361N8W96SEE3XFVR9ITBJSBJKVYMM45YXT09H8FH80K0LWBV5I6G38TXKSGXIZ4IL3S0NV16RT5BFGWZ", "hasFlower": 0, "hasSeason": 0}, {"id": "72", "q": "000,2,7,C,E.14,A.20,2,6,8,C,E.40,2,5,7,9,C,E.60,4,7,A,E<PERSON>72,C.80,6,8,E.A0,2,4,6,8,A,C,E.C1,5,9,D.D3,7,<PERSON>.E0,E.F2,4,6,8,A,C.G0,E;101,D.26,8.42,6,8,C.50,E.72,7,C.97.A2,C.B5,9.C1,D.E7.F3,5,9,B.G0,E;236,8.E7.F4,A:7D4J8XV6Z1IPAPDCUQCT9H89KUSKJ0OZZ4DKQ6I7L1AVQCS6YAWVRRXUWYXZJL6SKSRI1TIA40PUCD4JX1HRPQOV", "hasFlower": 0, "hasSeason": 0}, {"id": "73", "q": "001,3,6,8,B,D.21,3,6,8,B,D.41,3,5,9,B,D.57.60,2,C,E.74,7,A.80,2,C,E.95,7,9.A0,2,C,E.B6,8.C0,3,B,<PERSON><PERSON>D5,9.E1,7,D.F3,B.G0,5,9,E;102,6,8,C.21,7,D.41,3,5,9,B,D.57.71,3,B,D.87.91,5,9,D.B6,8.D5,9.E1,D;231,D.57.71,D.E1,D:8LLRRISNYWUS2VP7JEC08ZE09RGAJH1T5VELTV9L4H2AIBZSDTP53YC9A0ZBUP19I7GA3DWQ6RGINNSGT60NVZP4EQ", "hasFlower": 0, "hasSeason": 0}, {"id": "74", "q": "000,2,4,8,A,C.16.20,2,A,C.34,8.40,C.52,4,6,8,A.60,C.72,4,6,8,A.90,3,9,C.A5,7.B0,<PERSON><PERSON>C4,8.D0,2,A,C.E6;100,3,9,C.16.21,B.34,8.40,C.53,5,7,9.60,C.75,7.90,3,9,C.A5,7.B0,C<PERSON>C4,8.D1,B<PERSON>E6;200,C.34,8.53,9.75,7.C4,8:SYMTMFXVJPX3150YJVTLIF0YQ2Q3KKA1QETOFABPYPD11JSQNAEVA823O03D8NULTBNXJ0OIDFNDUOV5GGXP", "hasFlower": 0, "hasSeason": 0}, {"id": "75", "q": "000,2,4,6,8,A,C,E.21,4,6,8,A,D.40,2,4,6,8,A,C,E.60,3,5,7,9,B,E.80,2,4,7,A,C,E.A0,2,4,6,8,A,C,E.C0,2,5,7,9,C,E.E1,4,6,8,A,D;102,7,C.25,9.43,B.60,7,E.80,2,C,E.97.A1,3,B,D.C5,9.E6,8;2A2,C.C5,9:GVAHMV7JLO4M1V0GZS1CQODY9C3ONA2842K3LESPP69DX6R74XBHW0GARECA5B5G6O4JDC1K1IYWQIN68VZD", "hasFlower": 0, "hasSeason": 0}, {"id": "76", "q": "003,5,7,9,B.20,2,4,7,A,C,E.40,2,5,7,9,C,E.61,3,5,9,B,D.77.80,2,4,A,C,E.96,8.A0,2,C,E.B5,7,9.C1,3,B,D.D5,7,9.E2,C;104,6,8,A.22,7,C.30,E.42,7,C.55,9.72,C.A2,6,8,C.C2,4,6,8,A,C.E2,C;205,9.31,D.E2,C:2SGMDMUNQSF1M5Q5P0OP5ODPEJ06G2R3THEYDH4RXTJVR6UNOJUB5MJXBQ1NU64SYXPSYDVQY3FN3G6GR3OX", "hasFlower": 0, "hasSeason": 0}, {"id": "77", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.80,2,4,6,8,A,C,E.A0,3,5,7,9,B,E.C0,2,4,7,A,C,E.E0,2,4,6,8,A,C,E.G0,2,4,6,8,A,C,E.I0,2,4,6,8,A,C,E.K0,2,4,6,8,A,C,E:C63T1EY1SQG5WO4EYUBFS7V7U102TXLSANV6AP3B0O68W57BNC2Q5UFU4033C412Q8PXPG4LQ0STW2CWPB6T75", "hasFlower": 0, "hasSeason": 0}, {"id": "78", "q": "003,5,7,9,B.10,E.22,6,8,C.30,E<PERSON>42,4,7,A,<PERSON><PERSON>50,E<PERSON>62,4,6,8,A,C.70,E.83,6,8,B.A0,3,7,B,E.C0,3,5,7,9,B,E.E0,2,5,9,C,E;103,5,9,B.27.31,D.47.50,2,C,E.64,A.83,B.A0,E.B7.C3,B.D5,9.E1,D;203,5,9,B.27.47.83,B.B7.C3,B.E1,D;347.B7.C3,B:D6EA4Q8LKZ24AZ6LWT85X6SZP8M54U4PUVW85ESBXMPVK0BU30BL79D69DL2AS0WPDZ7U0NATGKBG3N2MNMQNESW25EK", "hasFlower": 0, "hasSeason": 0}, {"id": "79", "q": "001,5,7,9,D.13,B.21,6,8,D<PERSON>33,B<PERSON>45,9.50,2,7,C,E.64,A.70,2,6,8,C,E.84,A.90,2,6,8,C,E.B0,2,4,6,8,A,C,E.D4,6,8,A.E0,2,C,E.F4,6,8,A.G0,2,C,E;101,5,7,9,D.22,6,8,C.45,9.61,3,B,D.77.83,B.A0,2,7,C,E.C4,6,8,A.E0,3,7,B,E.F5,9.G0,E:QIYYQDZZJYP24U9P5XLD2RV4R9WLWRNPZ5LZO4OJH9GA0SST16H9Q5JNQYRNDT6GFLINF02F5T1WUX0DVOF4A0GUOPTUJ2GW", "hasFlower": 0, "hasSeason": 0}, {"id": "80", "q": "000,4,6,8,A,E.22,4,7,A,C.30,E.42,4,6,8,A,C.50,E.62,4,7,A,C.81,3,5,9,B,D.97.A0,4,A,E.B2,6,8,C.C0,E.D2,4,7,A,C.E0,E;105,9.27.31,3,B,D.45,9.50,E.62,7,C.74,A.82,C.97.A0,4,A,E.C1,D.D7.E0,E;231,3,B,D.62,C.C1,D;331,3,B,D.62,C.C1,D;432,C:O1VBYLBTGGBVGE1QSVQ2CCUYBKUZA2VG7WCXPOXATQ2EJ3SDPZHALH17TUOZEY3YODXUE7DPWL3AWSPKSTKCZ2KD71QX3WJL", "hasFlower": 0, "hasSeason": 0}, {"id": "81", "q": "002,4,6,8,A,C<PERSON>10,E.23,5,7,9,B.31,D<PERSON>44,7,A.51,D<PERSON>63,5,7,9,B.70,E<PERSON>84,6,8,A<PERSON>90,E<PERSON>A3,6,8,<PERSON><PERSON>B0,<PERSON><PERSON>C5,7,9.D3,<PERSON><PERSON>E1,5,9,D.F7.G1,3,B,D.H5,7,9.I0,3,B,E;102,4,A,C.16,8.37.41,4,A,D.74,7,A.80,<PERSON><PERSON>A0,E<PERSON>B6,8.E1,5,9,D.F7.G3,<PERSON><PERSON>H5,7,9;216,8.41,D.74,A.B6,8:EE5X8SC9TS8QLN9US4KJ0KRG73J1LD4P26UDHYCHWZG6CSA0TDDXXEL7CK8W7A1EGRVHN2TYTMQ50HVQ3FKF7XG20UQ28ZPMUL", "hasFlower": 0, "hasSeason": 0}, {"id": "82", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,3,7,B,E<PERSON>55,9.60,2,7,C,E.74,A<PERSON>80,2,6,8,C,E.94,A<PERSON>A1,7,D.B3,5,9,B.C0,7,E<PERSON>D3,B.E0,5,7,9,E<PERSON>F2,C<PERSON>G4,7,A;105,9.11,D.23,5,9,B.30,E<PERSON>43,B.55,7,9.61,D.74,7,A.94,7,A.B3,7,B.D3,B.E0,5,9,E.F2,C:7BTSZTCZ708T0O5WMEA2211AQOOECC8W1WE5NQ0A735BZBK6PHR3O8RQPEZKP768WNRQNMSCHM1RK32STM5A3SB2N0KHPH", "hasFlower": 0, "hasSeason": 0}, {"id": "83", "q": "001,6,B.13,9.21,5,7,B.33,9.40,5,7,C<PERSON>52,A.64,8.70,2,6,A,C.84,8.91,B.A3,6,9.B0,C<PERSON>C3,5,7,9.D1,B.E3,5,7,9;121,3,9,B.40,5,7,C.62,4,8,A.81,4,8,B.B0,6,C.C4,8.D1,B;221,B.40,5,7,C.62,4,8,A.B0,C.D1,B;321,B.62,4,8,A.D1,B:5BYU081BVEY7K1YHXFA62EUEEFCCCB2AI0TUA8B9O0XWO72CIA75V9K55YTH1VX8FV6OI49KO1447XI9F08U2WK4", "hasFlower": 0, "hasSeason": 0}, {"id": "84", "q": "000,2,4,6,8,A,C,E.20,3,5,7,9,B,E.40,2,4,7,A,C,E.60,2,4,6,8,A,C,E.80,2,4,6,8,A,C,E.A2,4,6,8,A,C.B0,E.C3,5,7,9,B.D0,E<PERSON>E4,7,A;101,6,8,D.13,B.25,7,9.33,B.47.50,E.64,6,8,A.84,A.96,8.B7.C3,B.D7.E4,A;201,D.24,A.47.64,A.B7;324,A:DON3L1U3NZKGO1VN0OY0SDX4Z7U8Q4SASAAEQDZF7QF2YXVX1CC21LVLGEZGCK2N8F2OKSU3V0Q8YEFKXUEAY34GC8D4L0", "hasFlower": 0, "hasSeason": 0}, {"id": "85", "q": "000,2,4,8,C,E,G.16,A.20,3,D,G.35,7,9,B.42,E.50,8,G.62,4,C,E.70,7,9,G.82,4,C,E.96,8,A.A0,2,4,C,E,G.B8.C0,2,E,G.D4,6,A,C.E0,2,8,E,G.F6,A.G1,4,C,F;108.13,D.36,8,A.60,3,D,G.A0,2,<PERSON>,G.D0,4,C,G.E2,6,8,A,E;237,9.D4,C.E7,9;3D4,C:KP9SL5QPTCGOW9P8K87WTGT26SRTN27WU952FRRJJYL7U23FC58JNSFUNP3YLOWK0QU8QF5931QGONG10O01170SKJ6R3L", "hasFlower": 0, "hasSeason": 0}, {"id": "86", "q": "000,2,6,8,C,E.20,4,6,8,A,E.32,C.40,4,6,8,A,E.52,<PERSON><PERSON>64,6,8,A.72,C<PERSON>84,7,A.91,D.A3,6,8,B.B0,E.C2,7,C.D0,4,A,E.E2,6,8,C;100,2,C,E.17.20,5,9,E.32,C.40,5,7,9,E.53,B.65,9.73,B.A3,B.B0,7,E<PERSON>C2,<PERSON><PERSON>D0,E<PERSON>E7;210,E.35,9.53,B.A3,B:8Q9FI65BTY6PKLZCIXJU2PRM64VWF48EDDBHH3GMRZYV31J6WSW39881LI7C45YN1HK5BWYH3STEZNZG7QX5B21UI4", "hasFlower": 0, "hasSeason": 0}, {"id": "87", "q": "001,3,5,7,9,B.22,4,6,8,A.44,6,8.50,2,A,C.64,6,8.70,C.84,6,8.90,C.A3,5,7,9.B0,C<PERSON>C2,5,7,A<PERSON>E3,9.F6.G0,3,9,C;101,3,5,7,9,B.22,A<PERSON>34,8.46.52,A.65,7.70,C<PERSON>A0,3,9,C<PERSON>B5,7.C2,A<PERSON>E3,9.G3,9;204,8.12,A.52,A.65,7.B5,7.C2,<PERSON><PERSON>F3,9;304,8.65,7.B5,7;465,7.B5,7:2ESJ6CI6HHPVW49TPZ1PXWKIUZFS6N09JG48P8XTJCU1F1K14052EK9HUDJXNSWOFZGE5WVRNHUSAZ4DRVDN6KF9EV8D0X80AO", "hasFlower": 0, "hasSeason": 0}, {"id": "88", "q": "000,2,4,8,A,C.16.21,B.34,6,8.41,B.53,6,9.60,C.73,5,7,9.80,C.94,6,8.A0,2,A,C.B6.C1,4,8,B.E1,4,6,8,B;101,B.21,6,B.34,8.46.60,6,C.73,9.90,6,C.B6.C4,8.D1,B;226.34,8.46.60,C.90,C.A6.C4,8;334,8.60,C.90,C.C4,8;434,8.60,C.90,C.C4,8:56P4T5YHSZLVPWZFJZEF2KL8LMWIIHL1D8HQX6VX2QMI42MU6EMSUYU1P554X6SX4PVDHZNVBURR1NYT2IKBYCC1SJ", "hasFlower": 0, "hasSeason": 0}, {"id": "89", "q": "000,3,5,9,B,E.17.20,2,C,E<PERSON>35,7,9.42,C<PERSON>55,7,9.62,C.74,6,8,A.81,D.94,6,8,A.A1,D.B4,A.C2,7,<PERSON><PERSON>D4,A<PERSON>E0,7,E<PERSON>F2,C.G0,5,7,9,E;103,5,9,B.10,7,E.32,5,9,C.55,9.62,7,C.81,7,D.A1,D.C3,B.F1,7,D.G5,9;203,5,9,B.17.32,5,9,C.67.A1,D.C3,B.F1,7,D.G5,9;304,A.A1,D.F7:YDLNWQ4DYQ38ELTPHRBSVLXMZIOCMIV4U3ZDLTWA20NACD1O84PHUA8O18YXE2YP1O04IN6IVE2SMEX2UZJ66UPRXRA6QVNZB1QMRJ", "hasFlower": 0, "hasSeason": 0}, {"id": "90", "q": "000,2,4,7,9,C,E,G.20,2,4,8,C,E,G.36,A.40,4,8,C,G.52,6,A,E.64,8,C.70,G.82,5,7,9,B,E.90,G.A4,7,9,C.B1,F.C4,6,8,A,C.D0,G.E2,4,6,A,C,E.F0,8,G.G2,4,6,A,C,E;101,3,8,D,F.38.44,C.57,9.78.97,9.A4,C.B8.C4,C.E2,E.F5,B;202,E.38.68.A8.B4,C;368.A8.B4,C:F7AKYFK4DE7EW7C343ZTKL9ZOMAVSPQPMPJ94WB6JQZBOYS6W3Y4CSDFL9BEL6DA98DF38BJUECZU6JM7QSVU5WYQTVALCK5UVMP", "hasFlower": 0, "hasSeason": 0}, {"id": "91", "q": "001,4,6,8,A,C,F.20,2,5,7,9,B,E,G.40,3,5,B,D,G.58.60,2,4,6,A,C,E,G.80,2,5,7,9,B,E,G.A0,3,6,A,D,G.B8.C1,3,5,B,D,F.E0,2,4,8,C,E,G.F6,A.G1,3,D,F;105,B.21,7,9,F.40,3,D,G.58.60,5,B,G.72,E.80,7,9,G.A0,3,D,G.B8.D3,D.E0,G.F3,D;260,G.A0,G:7UM2Y6PAO7IAHXGEVUIZXO7M9Q6XZ9IX9C48O3YO4K5GRDEBL2GI4Z72VZKRNM9AKKG6W2NHHBU5JP43WDAT8DTUQDHPJM6L00PC", "hasFlower": 0, "hasSeason": 0}, {"id": "92", "q": "000,2,7,C,E<PERSON>14,A.21,6,8,D.33,B.45,7,9.51,D.63,5,9,B.70,7,E<PERSON>82,5,9,C.90,7,E<PERSON>A3,B.B5,7,9.C0,2,C,E<PERSON>D4,6,8,A<PERSON>E1,D<PERSON>F5,7,9.G0,3,B,<PERSON><PERSON>H5,7,9.I0,3,B,E;101,D.21,6,8,D.33,B.45,7,9.63,B.C2,C.D4,7,A.F5,7,9.G3,B.H5,9;226,8.33,B.45,9.C2,C<PERSON>F5,9.G3,B.H5,9;345,9.F5,9:6OZTIQYCWHNGOAWSOXYIB87HWCR6K44SYOE6KETXGJI7PUIX932PG7NN96008GAYTPQ73ZA2FUWU00NZFFJHSTU4KP2XK24HZ99RFASB", "hasFlower": 0, "hasSeason": 0}, {"id": "93", "q": "000,2,5,7,9,C,E.20,2,5,7,9,C,E.40,3,5,7,9,B,E.60,2,4,6,8,A,C,E.80,2,C,E.94,7,A.A0,E.B3,5,7,9,B.C0,E<PERSON>D2,C.E0,4,7,A,E.F2,C.G0,5,7,9,E.H3,B.I1,5,7,9,D.J3,B<PERSON>K5,7,9;101,5,7,9,D.21,5,7,9,D.45,9.66,8.72,C.A4,<PERSON><PERSON>D1,D<PERSON>H5,9.J5,7,9;205,9.21,5,9,D.I5,9:6YQ84DUZUYROT1EEQTDR4VH0XU1TRKML8RJV8AQKDLBX33Y6XBMAIAJ0BOJODIKHILE0JKATO0G13VN4BZHE48GNV6H3YILZMM61ZQUX", "hasFlower": 0, "hasSeason": 0}, {"id": "94", "q": "000,2,4,6,8,A,C,E.22,4,6,8,A,C.30,E.43,5,7,9,B.50,E.63,5,7,9,B.70,E.82,4,6,8,A,C.90,E.A2,4,6,8,A,C.C0,2,7,C,E.D4,A.E0,2,6,8,C,E;101,3,6,8,B,D.22,C.30,4,A,E.55,9.74,A.94,6,8,<PERSON><PERSON>A2,C.C0,E.D2,C.E7;201,D.22,C.34,A.94,A.A2,C.D2,C;334,A:DAUAE0IR2CWVH0WOPIHRVOK0L45O4TYCGW2FVC8450BP4ADQNN5QER8I77EKYMTRYCHETHQAUMFDFLKLT82IDY58VQGWBFLKO2", "hasFlower": 0, "hasSeason": 0}, {"id": "95", "q": "000,2,5,7,9,C,E.20,3,5,7,9,B,E.40,2,4,6,8,A,C,E.60,2,5,7,9,C,E.80,2,4,6,8,A,C,E.A1,5,7,9,D.B3,B.C0,5,7,9,E.D2,C.E4,7,<PERSON><PERSON>F0,E.G2,5,7,9,C;101,6,8,D.24,6,8,A.30,E.44,6,8,A<PERSON>51,<PERSON><PERSON>72,5,9,C.87.95,9.B3,B.E4,A.F7.G2,5,9,C;206,8.25,9.30,7,E:3JTGVZQ27GNP32SRXYWDYSQ7YVTGRX7CMXQEVP2MJVHXCWG23B3R9NAACUTMDLQJPNSIC94ASYL4JBUAN6ITZD76HHMDIRPEEIHE", "hasFlower": 0, "hasSeason": 0}, {"id": "96", "q": "001,3,6,9,B.20,2,4,6,8,A,C.40,4,8,C.60,2,4,8,A,C.76.81,3,9,B.96.A2,4,8,A.B0,C.C3,5,7,9.D0,C.E2,4,6,8,A<PERSON>F0,C.G3,5,7,9;101,3,9,B.16.20,2,A,C.40,4,8,C.60,3,9,C.76.81,B.96.A3,9.B0,<PERSON><PERSON>C4,8.D0,C.E3,5,7,9.F0,C.G6;216.20,C.86.A3,9.F0,C.G6:TTQYM3T7YBPNLR07ZVMR72XVGD1ELGD920QM192UESAXBD3A9GXZQN1YALULZGQXADBR9PSBYTZP0MS31UUEPV3E2R07NSVN", "hasFlower": 0, "hasSeason": 0}, {"id": "97", "q": "000,2,5,7,9,C,E.20,2,4,7,A,C,E.40,2,4,7,A,C,E.60,2,5,9,C,E.77.80,2,4,A,C,E.96,8.A0,2,C,E<PERSON>B4,6,8,A.C1,<PERSON><PERSON>D3,5,9,B.E0,E<PERSON>F2,4,7,A,<PERSON><PERSON>G0,E.H2,4,7,A,C.I0,E;101,6,8,D.23,7,B.41,4,7,A,D.60,2,C,E.77.84,A.90,7,E<PERSON>B4,<PERSON><PERSON>E4,A<PERSON>F1,7,D.H3,7,B:9O8Z5WSKYCI5G63II4C7EKWYKM4YJBAG0HBSM0S0EX3VKUOB2AF2Z8XCPFH9O0J23GBOMHG4XPIYPFP4NHUUVS2MEC7E6N3FXU", "hasFlower": 0, "hasSeason": 0}, {"id": "98", "q": "000,2,4,6,8,A,C,E.21,5,7,9,D.33,B.40,6,8,E<PERSON>52,<PERSON><PERSON>60,5,7,9,E.72,C.80,4,7,A,E<PERSON>92,C.A0,4,6,8,A,<PERSON><PERSON>B2,C<PERSON>C4,6,8,A.D0,2,C,E.E4,6,8,A.F0,2,C,E.G5,9.H0,2,7,C,E<PERSON>I4,A<PERSON>J1,6,8,<PERSON><PERSON>K3,<PERSON>;106,8.37.52,C.65,9.77.91,D.A4,<PERSON><PERSON>C2,6,8,C.E4,7,A.G2,C.I7:E3PSMHV8CMBH1OTMGCCQ0OP80K5AARPE1S8JBN1TBVQMTHQBFG5NKNA3IO3RF0KRJGROPT8IFCGJEHI0NA3K5V5FEQVIS1JS", "hasFlower": 0, "hasSeason": 0}, {"id": "99", "q": "000,2,4,6,8,A,C,E.20,2,4,7,A,C,E.40,2,4,6,8,A,C,E.60,2,6,8,C,E.81,3,6,8,B,D.A0,2,C,E.B6,8.C2,C.D0,4,6,8,A,E.E2,C.F0,4,7,A,E.G2,C;101,3,5,9,B,D.17.22,4,A,C.30,7,E.43,B.56,8.72,7,C.92,C.A0,E.B7.D3,B.E0,7,E.F2,4,A,C;214,7,A.30,E.E0,E:KAHGYSDD4X3RGMC0YR8MY08M3IWHQOXTV37KPGVVWXQTCIOA4W80L78MEOEP03GOKLEANE7CWTLAY417LNSVHQNQNK4STHP1SCPX", "hasFlower": 0, "hasSeason": 0}, {"id": "100", "q": "004,6,8,A.10,E.22,4,6,8,A,C<PERSON>30,E.43,5,9,B.50,7,E<PERSON>63,B.70,5,7,9,E<PERSON>83,B.91,5,9,D.A3,7,B.C0,2,4,6,8,A,C,E.E0,2,4,6,8,A,C,E.G0,3,6,8,B,E;104,7,A.22,4,A,C.30,E.43,B.60,3,B,E.77.85,9.92,C.A7.B3,B.C1,5,9,D.D3,7,B.E0,5,9,E.F3,B.G7;292,C.D4,A:ILT3A3AUVOPL09EE1RXN40EHTVJRRBHUN5XZDZA5ATJO10VNLQ05QLP4IZDXRVKBIQ1JUX39HUJDP5KDTB9O4HIBNZOPE493QK1K", "hasFlower": 0, "hasSeason": 0}, {"id": "101", "q": "002,4,7,A,C.10,E.22,4,7,A,C.40,2,4,6,8,A,C,E.60,2,5,7,9,C,E.81,3,5,7,9,B,D.A0,4,6,8,A,E.B2,C.C0,5,7,9,E.D2,C.E4,6,8,A.F2,C.G0,4,6,8,A,E;102,C.10,E.23,B.42,6,8,C.50,E.75,7,9.81,D.94,6,8,A.B1,D.C7.D2,<PERSON><PERSON>E5,9.F3,B.G0,E;210,E.75,7,9.G0,E:MRBFKG43DDGLGCLT7HIWP6FRSHO17B6HAH4CQADUN27BERQIAQNLJ8BIC8PWOCUDNNWJTSWME41LOMST41TJ17FROQGAS32KJFIM", "hasFlower": 0, "hasSeason": 0}, {"id": "102", "q": "000,2,4,6,8,A,C,E,G.20,2,5,7,9,B,E,G.40,2,4,6,8,A,C,E,G.61,4,7,9,C,F.80,2,4,7,9,C,E,G.A0,2,4,6,8,A,C,E,G.C1,4,6,8,A,C,F.E0,2,4,7,9,C,E,G.G0,2,4,6,A,C,E,G;103,6,A,D.25,B.31,F.46,A.51,8,F.A3,D.B1,F.C8.E1,F.G5,B;206,A.31,F.E1,F:YKV9EHFS197N7RWQ1WWBRC8HZ88EYNJCMYHXT30QKB00B8LXFVA2RVNT37HKZQQNDLLAREX9F3EZV2TBFM2T0XYAWD23JZS97LKA", "hasFlower": 0, "hasSeason": 0}, {"id": "103", "q": "000,2,4,6,8,A,C,E.20,2,4,7,A,C,E.42,4,6,8,A,C.50,E.62,4,6,8,A,C.80,2,5,7,9,C,E.A0,2,4,7,A,C,E.C0,3,5,9,B,E.D7.E2,4,A,C.F6,8.G0,3,B,<PERSON>.H5,9.I1,3,7,B,D.J5,9.K0,7,E;111,D.32,7,C.53,6,8,B.86,8.91,D.A3,B.C5,9.D7.G3,B.I2,6,8,C;286,8.A3,B:TYPHPQ5B0UXY6AA8I3ZCJDI3CELHY8NAEZ7D2338TA50PQRQUIL6876XJJBVCINV0WWXQKVUFHUJRL0HR6CK2V2PNX5RY2L5FBNB", "hasFlower": 0, "hasSeason": 0}, {"id": "104", "q": "000,2,6,8,A,E,G.14,C.20,8,G.32,6,A,E.48.50,2,5,B,<PERSON>,G.67,9.70,2,4,C,E,G.87,9.90,3,5,B,D,G.A7,9.B2,E.C0,5,8,B,G.D3,D.E0,6,A,G.F3,8,D.G0,5,B,G;100,8,G.32,8,E.52,5,8,B,E.70,4,C,G.87,9.94,C.B2,E.C5,8,B.E3,D.G5,B;208.32,E.74,C;374,C;474,C;574,C:9SVHIHC9EZJ9T81IANASS42KJCAORZ7KZ8FRDVFKKO5IX1NC8AX12JP2IEQJQQNZDH5DXCOVRQE5D9TTPFNPS4PVF17O8EX25THR", "hasFlower": 0, "hasSeason": 0}, {"id": "105", "q": "001,3,5,9,B,D.17.20,2,5,9,C,E.37.40,2,4,A,C,E.56,8.60,2,4,A,C,E.76,8.82,C.90,4,7,A,E.B0,2,4,7,A,C,E.D0,2,5,7,9,C,E;101,3,B,D.16,8.20,E.41,4,7,A,D.62,4,6,8,A,C.94,7,A.B1,4,7,A,D.D0,6,8,E;201,D.16,8.41,D.62,C.B1,D;341,D.B1,D;441,D.B1,D:HBNON0RFSZABMPPYI2XHJ5I2HADOBY33AX95W22YH3RSN0V0ZID9Y9MMJJFUVIGUSGGM0XUZ5OZJPDSOP9VXWUWN3ARVBWDR5FGF", "hasFlower": 0, "hasSeason": 0}, {"id": "106", "q": "000,2,5,8,B,E,G.20,2,4,6,8,A,C,E,G.41,3,5,7,9,B,D,F.60,3,5,7,9,B,D,G.80,2,4,6,8,A,C,E,G.A0,2,4,6,8,A,C,E,G.C0,2,5,7,9,B,E,G.E1,3,5,B,D,F.F7,9.G0,2,4,C,E,G;123,5,8,B,D.46,A.53,8,D.60,G.73,8,D.93,D.A1,8,F.C8.E5,B.F3,8,D;228.53,D.F8:2YDS0MJZ6FXS5TZ43HK5MZWLG75X8G8EQNSC00SBKJEL8KBB8WV6NFYMVTNRG0HRD3RNZCRA7XX5YKTT22JLJ92VAV6QQB4LGM6Y9Q", "hasFlower": 0, "hasSeason": 0}, {"id": "107", "q": "003,6,9.20,3,6,9,C.40,5,7,C<PERSON>53,9.60,5,7,C<PERSON>73,9.85,7.93,9.A5,7.B0,3,9,C<PERSON>C6.D0,C.E3,5,7,9.F0,C<PERSON>G2,4,8,A;113,6,9.30,6,C.50,3,5,7,9,C.74,6,8.95,7.B6.C0,C<PERSON>E0,6,C.F4,8.G2,A;213,9.30,C.50,5,7,C.74,8.86.C0,C.E0,C.F4,8.G2,A;356.74,8.G2,A;456;556:5M7Y34QIEX1YYE5HAX3HS3381GLW2M6EUCMA4TPJS4ZW74TY7A17PNFP9ZLJ8GENNC9PAH15XMHIQCSZFCOZQLXNO25QUSL6", "hasFlower": 0, "hasSeason": 0}, {"id": "108", "q": "001,3,5,7,9,B,D.20,3,5,7,9,B,E.42,4,6,8,A,C.50,E.62,4,6,8,A,C.70,E.82,4,6,8,A,C.90,E.A3,5,9,B.B0,E.C2,4,6,8,A,C.E2,4,6,8,A,C.F0,E.G2,4,6,8,A,C;102,C.15,9.23,B.35,9.50,3,6,8,B,E.73,5,9,B.81,D.93,B.A5,9.B0,E<PERSON>C3,6,8,B.E3,5,9,B.G5,9:483NS3U1XI0HDB7WWCII4N5TE158FUSN4H66F01XAWQSXOB8UE7YV9B4JIFYETLBD7PO5XUCQ2GL87ODV5G2PO00DF9E1JSAWZNZ", "hasFlower": 0, "hasSeason": 0}, {"id": "109", "q": "000,2,5,8,B,E,G.20,2,4,7,9,C,E,G.43,6,8,A,D.50,G.63,5,7,9,B,D.70,G.82,4,8,C,E.90,G.A5,7,9,B.B0,3,D,<PERSON><PERSON>C6,A<PERSON>D3,8,D.E0,5,B,G.F2,7,9,E.G5,B;100,5,B,G.18.23,D.38.46,A.50,3,8,D,G.65,B.73,D.80,8,G.A5,B.C3,6,A,<PERSON>.E5,8,B;200,G.47,9.E5,B;348:HX3G0AXFUJCHATSVK3KZ7UNVX09SWCAO99SKMYXTOQCGN0A9QUGF8ON8YY8KW3OMUH7II3VIWYS80Q7NZHTTJCI7FMVZWGZFQM", "hasFlower": 0, "hasSeason": 0}, {"id": "110", "q": "000,3,5,7,9,C.20,4,8,C.32,A.44,6,8.50,2,A,C<PERSON>65,7.72,A<PERSON>80,5,7,C.92,A.A4,6,8.B0,2,A,C.C4,6,8.D1,B.E3,5,7,9.F0,C.G4,6,8;103,9.10,C.32,4,8,A.46.50,C.62,A.75,7.80,C.A3,5,7,9.C1,5,7,B.E3,5,7,9.G6;203,9.33,9.50,C.80,C.A4,8.C6.E3,6,9.G6;303,9.E6;403,9;503,9:4VFIEZ82HLGOTJ9H0OWV993CI40K7DTCPN7FDLIB75W3AJ5KKP42B851TZL9EZCV0PYMECIAY7J8DB8J3M0GNKETS354PSVZ1LNNDB", "hasFlower": 0, "hasSeason": 0}, {"id": "111", "q": "001,5,8,B,F.13,D.21,5,7,9,B,F.33,D.40,G.53,7,9,D.61,F.74,7,9,C.80,G.94,C.A0,2,7,9,E,G.B4,C.C0,7,9,G.D2,E.E0,7,9,G.F3,5,B,D.G7,9;105,8,B.12,E.24,7,9,C.40,3,D,G.58.61,F.74,8,C.80,G.94,C.A1,8,F.C8.D0,G.F3,5,7,9,B,D;218.40,G.58.74,C.C8.D0,G.F5,8,B:FQP29XLU0ELDRZ0YQONYISRZBXTZLTZ1JV7302N2B7DYB4PJXVN5351TDYPJWOJGAX475O0IAC5A7SFOEEAGTSI6C2USBPE9D6LIWN", "hasFlower": 0, "hasSeason": 0}, {"id": "112", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,4,7,A,E.81,4,6,8,A,D.A0,2,4,7,A,C,E.C3,5,7,9,B.D0,E.E2,4,6,8,A,C.F0,E.G4,6,8,A.H0,2,C,E.I5,9;101,7,D.25,9.33,7,B.45,9.57.64,A.85,9.A0,E<PERSON>C3,B.E2,4,6,8,A,C.G7.H5,9;201,7,D.37.45,9.A0,E.G7;345,9:MOUVZTS0ZO588GO1J26B6CUVKIBEWXMDHAKXDXJH6CDZIGTRD5TGVCUR0IZ8KE5PAMBPR5PWJPHR2OMA02SBUXHGVEECA02T61J8KISWWS", "hasFlower": 0, "hasSeason": 0}, {"id": "113", "q": "000,2,6,8,A,E,G.14,C.20,6,8,A,G.32,E.40,4,6,8,A,<PERSON>,<PERSON>.52,E.60,7,9,G.72,4,C,E.86,8,A.91,3,D,F.A5,7,9,B.B0,3,D,G.C5,7,9,B.D0,3,D,G.E6,8,A.F0,3,D,G.G5,8,B;100,G.16,8,A.30,6,A,G.44,C.50,G.62,E.77,9.91,6,A,F.B4,C.C0,G.D6,A.E0,3,D,G.G8;200,G.36,A.77,9:8R9KO1C3Y5LX4QUBAOT52LZ20YVOSUI8W9JOHKU88SPXPPCHGDNBWWUV9NCT0XKGTSWJH2QM0N1LKSDMA2D9RJ1X41A0ZHD3ANPLCJIT", "hasFlower": 0, "hasSeason": 0}, {"id": "114", "q": "000,2,4,6,A,C,E,G.18.21,5,B,F.37,9.40,5,B,G.57,9.60,2,4,C,E,G.77,9.80,3,5,B,D,G.97,9.A1,3,D,F.B7,9.C0,4,C,G.D6,A.E1,4,8,C,F.G0,3,5,7,9,B,D,G;102,E.15,B.35,B.40,7,9,G.60,2,4,7,9,C,E,G.80,5,7,9,B,G.A2,E.B7,9.C0,G.D4,C.E1,F.G0,3,5,7,9,B,D,G:Y472XU62Q9VA92OENXOL4ARBFLY6HEHU7MY4TS2BUMTLRV5CPH9J7GETWVO6NVNAU7BGWPSACFX6WTCYWSHJ9JLEOSJGXFGQP5NFC4BP", "hasFlower": 0, "hasSeason": 0}, {"id": "115", "q": "000,3,5,7,9,B,E.25,7,9.31,3,B,D.45,9.50,2,C,E.64,6,8,A.81,3,6,8,B,D.A0,3,5,7,9,B,E.C3,5,9,B.D1,D.E3,5,9,B.F1,D.G3,5,9,B.H0,E.I2,4,A,C.J6,8.K2,C;100,3,6,8,B,E.25,9.32,C.45,9.50,2,C,E.65,7,9.83,6,8,B.A4,6,8,A.C4,<PERSON><PERSON>E4,<PERSON><PERSON>F1,D.G3,5,9,B.H0,E.I2,C.J6,8:YXXDQ5W6IJMID4J0IWPEUDB0XMVKUVRAW5TXCVSK0P53UBVWCJFBRHM0C9639SJ4HD4KRNY36SCB5MAK9ZNTAPQARQYSQ3F9YEI4Z6UPFF", "hasFlower": 0, "hasSeason": 0}, {"id": "116", "q": "000,2,4,6,8,A,C,E.20,2,7,C,E.34,A.40,2,6,8,C,E.60,2,4,6,8,A,C,E.80,2,6,8,C,E.A0,2,5,7,9,C,E.C0,2,4,6,8,A,C,E.E1,5,9,D.F3,7,B.G0,E.H2,7,C.I0,4,A,E;102,C.21,D.33,B.40,E.56,8.70,7,E.92,C.A5,9.C0,3,7,B,E.E5,9.H1,D;221,D.33,B.40,E.H1,D;321,D.H1,D:ZRHJ3M96I0KF582ZTNTNZ62DQE0KM6CQM9YLURCC8IOEQLAF5KITNJHYL5JZH9ARUETI32JOH67RD58FQ8093NUA3M7O7FL0UA7E2COK", "hasFlower": 0, "hasSeason": 0}, {"id": "117", "q": "003,B.11,5,7,9,D.30,3,6,8,B,E<PERSON>53,5,9,B.60,7,E<PERSON>72,5,9,C.80,7,E.95,9.A3,B.B0,E.C3,6,8,B.D0,E.E2,5,7,9,C;103,B.16,8.36,8.53,B.60,5,9,E.72,C.95,9.A3,B.B0,E.C6,8.D0,E.E2,6,8,C;216,8.36,8.53,B.65,9.95,9.A3,B.C0,6,8,E.E2,6,8,C;327.65,9.95,9.C0,E.D7;4C0,E;5C0,E:O15DF1AJ6YBQEVXHUJB21ZKA5V1T9T7GU7GZYEDZZHFWJO9YUV0UN2OQTFNNQAHDKH6D9TMBE0O6K79YEGAX62MNBVXMKQM75GJ2WXF5", "hasFlower": 0, "hasSeason": 0}, {"id": "118", "q": "000,3,5,9,B,E.17.20,3,B,E.35,7,9.40,2,C,E.54,6,8,A.60,2,C,E.74,6,8,A.80,2,C,E.94,6,8,A.A2,C.B4,6,8,A.C0,2,C,E.D4,6,8,A.E0,2,C,E.F4,6,8,A.G0,E.H2,4,6,8,A,C.I0,E.J3,6,8,B.K0,E;100,4,A,E.27.40,E.80,2,C,E.95,9.C0,2,5,9,C,E.H0,4,A,E.J7;240,E.H0,E:RMVN6MX5Y2RV3855ZBAZ3QQTEC3HPZHIHIHM71RS3KLLR2NW1D4D7IC220O0896MBSEEZT5E9ONPPNLWIKTJ746A8X1JY7SOP1L86STO", "hasFlower": 0, "hasSeason": 0}, {"id": "119", "q": "001,5,9,D.13,B.21,5,9,D.37.42,4,A,C.50,6,8,E.62,C.70,6,8,E.82,4,A,C.90,6,8,E.A3,B.C0,2,6,8,C,E.E0,2,5,9,C,E.F7.G0,2,5,9,C,E.I0,2,4,6,8,A,C,E;105,9.11,D.44,A.50,E.66,8.70,E.82,4,6,8,A,C.A3,B.C0,6,8,E.E0,E.G1,D.I0,2,4,7,A,C,E;285,9.H1,D.I7;3I7;4I7:EBIY5CCUJBS5FVX8Q5M9VF4PMQPIY94U2AC28EIFUQV3N2YHB54XAO67XOYM6CEUS7V69LH2677NI3SMAFXENLWNOSWLL83OJB4398QA", "hasFlower": 0, "hasSeason": 0}, {"id": "120", "q": "000,2,5,7,9,B,E,G.20,5,7,9,B,G.33,D.40,6,A,G.52,8,E.60,4,6,A,C,G.80,2,5,7,9,B,E,G.A0,3,6,A,D,G.B8.C0,2,5,B,E,G.D7,9.E0,3,5,B,D,G.F7,9.G0,2,4,C,E,G;106,A.10,8,G.25,B.30,G.46,A.58.64,C.80,8,G.C5,8,B.E0,4,8,C,G.G0,3,D,G;218.20,G.64,C.F0,G:COCCS43YB3U8OP9WWKD190A4DX5BABU1UKV5L7CYPWESYKO8HX4V80BIDA9GL7IH14AGEOHPD1ZI9S8K70ZHYIG5SGLW5U7LX3XP30", "hasFlower": 0, "hasSeason": 0}, {"id": "121", "q": "001,3,5,7,9,B,D.22,5,9,C.30,7,E.42,5,9,C.50,7,E<PERSON>63,5,9,B.71,D.84,6,8,A.90,2,C,E.A4,6,8,A.B0,E<PERSON>C2,C<PERSON>D5,7,9.E0,2,C,E.F5,9.G0,3,7,B,E.H5,9.I0,2,C,E;103,7,B.15,9.31,5,7,9,D.50,5,7,9,E.71,4,A,D.90,2,5,7,9,C,E.B0,E<PERSON>C2,C.D5,9.E0,E.F5,9.G7.H5,9.I1,D:D3UMH5SXG5JKDED8TDCC7ZQQO8JGUKQJBYIB4ZHEHP3KS49X8RMQ0LV770XE04HRY6YS9V6850PLKEO6I79SU9UCO1314XC6P3JTY5PO", "hasFlower": 0, "hasSeason": 0}, {"id": "122", "q": "000,2,4,7,9,C,E,G.20,2,8,E,G.34,C.42,6,A,E.58.61,3,6,A,D,F.80,2,7,9,E,G.A1,3,5,7,9,B,D,F.C1,3,6,8,A,D,F.E2,4,C,E.F0,8,G.G2,E;101,3,7,9,D,F.20,2,E,G.42,E.57,9.61,3,D,F.80,7,9,G.A1,3,D,F.B8.C1,F.D3,D.F0,2,E,G;212,E.57,9.87,9.B8.C1,F.D3,D;357,9:6L5YO2RE7214DK1PINWLQJY5PTXICXXJZQUTR2WY30UTSP3DLTJNU045L0S2KQZCC4QS6ND76RE5SKRZ063PUDO37NXWZOOK4CIYJWI7", "hasFlower": 0, "hasSeason": 0}, {"id": "123", "q": "000,3,6,8,B,E.20,3,6,8,B,E.47.52,4,A,C.60,6,8,E.72,4,A,C.80,7,E.94,A.A2,6,8,C.B0,4,A,E.C2,C.D7.E0,3,5,9,B,E.G0,4,A,E;100,3,6,8,B,E.20,6,8,E.47.53,B.60,6,8,E.72,C.80,E.A2,C.B0,4,A,E.D7.E0,3,5,9,B,E.G4,A;206,8.10,E.26,8.67.71,D.B4,A.E0,3,B,E;3E3,B:6SE3ZVIM4RZVA2KDI423LQXZDM7R1Z9NOASQO1B3TAIETIRXBA1LO591TL6R70DENX6MS2ELOB5005VV3Q5SKBTK6QNX074MN9D249K7", "hasFlower": 0, "hasSeason": 0}, {"id": "124", "q": "001,4,6,8,A,C,F.20,2,4,6,8,A,C,E,G.40,2,4,7,9,C,E,G.60,2,4,6,8,A,C,E,G.80,2,6,8,A,E,G.A1,5,7,9,B,F.B3,D.C0,5,7,9,B,G.D2,E.E0,4,6,8,A,C,G.F2,E.G4,6,8,A,C;105,7,9,B.22,5,7,9,B,E.51,F.63,D.82,6,A,E.B3,D.D5,B.E1,F.F5,7,9,B;216,A.82,6,A,E:HJ6BHVAQ3E2BEGKFBAU7JGEF65FU864SQLCT39K4UCH5AX89S5B8K7JXQM28DX7K2TXLLIQIT9CD7EVMG6AMUF2SM3I5LHIGVSJCVT93", "hasFlower": 0, "hasSeason": 0}, {"id": "125", "q": "000,2,6,8,C,E.14,A.20,6,8,E.32,4,A,C.40,7,E<PERSON>52,C<PERSON>60,4,6,8,A,E.81,3,6,8,B,D.A0,2,6,8,C,E.B4,A<PERSON>C1,6,8,D.D3,<PERSON><PERSON>E0,5,7,9,E;101,6,8,D.27.30,4,A,E.47.50,E<PERSON>65,7,9.81,7,D.A0,6,8,E<PERSON>B4,<PERSON><PERSON>C1,6,8,D.E6,8;201,D.34,7,A<PERSON>65,7,9.81,D.97.B7.D6,8;365,9;465,9:7JBL2UVX60V0DN3VXQJD6FTID44T2L5U7Z5IP4MDTNVZ5BP1MI1MC39N07UBXI9NQ8H81SMFZSX75023PH2UJ3ZHH4P9JB89C18T", "hasFlower": 0, "hasSeason": 0}, {"id": "126", "q": "000,2,5,7,9,C,E.20,2,6,8,C,E.40,2,7,C,E.60,2,4,6,8,A,C,E.80,2,4,A,C,E.96,8.A1,3,B,D.B5,9.C0,2,C,E.D4,6,8,A.E0,2,C,E.F7.G3,B.H0,6,8,E.I2,C;111,D.30,E.47.51,D.63,6,8,B.80,2,C,E.97.B2,5,9,C.D0,2,4,6,8,A,C,E.F7.I2,C;251,7,D.63,B.B5,9.D3,B.E7:EFA23ZAVV60H2SXLMAWFVYNICP44IUVWTO1EQPEW72ZZRLDT43XX7WSEH36BLBP9J190PDOC96CBBN9CAZL40U3RIIJTYMQ0652XT5", "hasFlower": 0, "hasSeason": 0}, {"id": "127", "q": "000,2,A,C.15,7.20,C.33,5,7,9.50,2,4,8,A,C.66.71,B.83,9.90,C.A2,6,A.B4,8.C0,2,A,C.D5,7.E0,3,9,C.F6.G1,3,9,B;100,2,A,C.20,C.33,5,7,9.51,B.83,9.90,C.A6.B2,4,8,A.D0,5,7,C.E3,9.G3,9;200,2,A,C.33,5,7,9.90,C.B2,A.D0,5,7,C.E3,9;300,C.34,8.B2,A.D5,7;434,8;534,8:FUP4WD69CD02IHL7DOBIKX2F1ROLAEE7N4NRVGJOH4BQ99H1CQT65BMGENTVWMWWHC9UB3A5I6J0LKX6LDAANQQ4J1GPIJG371OUCUE7", "hasFlower": 0, "hasSeason": 0}, {"id": "128", "q": "000,3,5,9,B,E.17.21,3,B,D.36,8.41,4,A,D.56,8.60,E<PERSON>72,4,6,8,A,C.80,E.92,6,8,C.A4,A.B0,6,8,E.C2,C<PERSON>D4,6,8,A.E2,C<PERSON>G1,3,5,7,9,B,D;100,3,5,9,B,E.17.37.41,4,A,D.56,8.71,7,D.96,8.C6,8.D2,4,A,C.G2,4,6,8,A,C;203,B.56,8.87.C6,8.G2,5,9,C;356,8.C6,8;456,8.C6,8:RS1GU5D4FUHI20IYRIFDPY3H3U14BKKRXY5GWMSLTLUOBQ44GQ381FSO0W2KTBPLPG8HTD0LOMY0KW2QRF381SMDXB2WIXPHMO85TXQ5", "hasFlower": 0, "hasSeason": 0}, {"id": "129", "q": "000,3,5,7,9,B,D,G.20,2,4,6,A,C,E,G.38.40,2,4,C,E,G.56,8,A.60,2,4,C,E,G.76,8,A.81,3,D,F.95,7,9,B.A1,3,D,F.B6,A.C0,3,8,D,G.E0,3,5,8,B,D,G.G1,3,7,9,D,F;105,B.24,C.30,2,E,G.48.50,4,C,G.66,A.73,D.81,6,A,F.A3,6,A,D.C0,G.D8.E0,4,C,G.F8;273,D.A3,D:41ZXGMK2W7L3GOI69D5JPCKBOIN5HQ4IJ6HA1YCA7ZC2KQ4A2LB7GJB9WSV8SRP7RWMMY9MDGRLQ6QAZ8S289FISZC3K4VBLW86RFJNX", "hasFlower": 0, "hasSeason": 0}, {"id": "130", "q": "000,4,6,8,A,E.12,C.20,4,7,A,E.32,C.40,5,7,9,E<PERSON>53,<PERSON><PERSON>61,7,D.73,5,9,B.80,7,E.92,4,A,C.A0,7,E.B2,4,A,C.C6,8.D1,3,B,D.E6,8.F0,E.G2,4,6,8,A,C.H0,E.I2,4,6,8,A,C;104,6,8,A.11,D.47.53,B.73,6,8,B.80,E.92,4,A,C.B3,B.C6,8.D2,C.E7.G4,A.H1,D.I7;263,B.77.92,C.C7.G4,A:BSYH0OQZ486INC2S97LMS4YQNEKXVJL2CIKBJBJ4R6YB78W9RS60F85XV2HW5DY2OEMIO5Z6UF9UX41FUKQKXF8LJ5WNHM9WUDOHQL1IMN", "hasFlower": 0, "hasSeason": 0}, {"id": "131", "q": "001,3,6,8,B,D.20,2,4,A,C,E.36,8.40,4,A,E.52,6,8,C.64,A.70,2,C,E.91,4,6,8,A,D.B0,4,A,E.C2,6,8,C.D0,4,A,E.E6,8;101,3,6,8,B,D.21,D.34,7,A.52,6,8,C.64,A.72,C.91,4,A,D.C2,4,7,A,C.E6,8;203,6,8,B.34,7,A.52,6,8,C.C2,4,7,A,C.E6,8;306,8.D7;407:RLQ8J8N0VMSKW6N4A96XJKBL549CUCUQWZBWBHPPEBKHPNJXN7M9QP480L7RX6E0LX2R79KHVJQZ3VW3VUHA3Z04MZMUS52867R3", "hasFlower": 0, "hasSeason": 0}, {"id": "132", "q": "002,5,7,9,B,E.21,3,6,8,A,D,F.40,3,6,8,A,D,G.60,5,7,9,B,G.72,E.80,6,A,G.92,8,E.A0,5,B,G.B2,E.C0,4,6,8,A,C,G.E1,3,6,8,A,D,F.G4,6,8,A,C.H1,F.I7,9;106,8,A.12,E.26,A.33,D.40,6,8,A,G.60,5,7,9,B,G.80,6,A,G.92,E.B0,G.C8.D6,A.E3,8,D.F6,A.H1,7,9,F:H34IQVLUY52VUDTICWX3EWKZP0C3GZ9BZ7NVEVDAN54LIP1SIC9126TSUEBH6HJ3OL1CQJLDS67690XQO0OZWG1S22WBKEAH0DOQYBU9", "hasFlower": 0, "hasSeason": 0}, {"id": "133", "q": "000,2,4,6,8,A,C,E.20,2,6,8,C,E.40,2,6,8,C,E.54,A.60,2,7,C,E.74,A.80,2,6,8,C,E.A0,3,6,8,B,E.C0,2,4,6,8,A,C,E.E2,C.F0,6,8,E.G2,4,A,C;102,C.17.22,C.30,6,8,E.42,C.50,4,A,E.62,C.74,7,A.81,D.97.A3,B.C0,2,4,A,C,E.F2,6,8,C;217.42,C.81,D.C1,D.F6,8;3F7:RYQ1ZTNIJTF0ZU8LLBPRAR3XNML1VF3LPEAKHUECVDQKDJAGMDB8UWB3N15EUVHHK1K3C6NT6YIRFWI4VE045IGMZAZX4PM4TBFJPDHJ", "hasFlower": 0, "hasSeason": 0}, {"id": "134", "q": "000,3,5,9,B,E.20,2,4,A,C,E.41,3,5,7,9,B,D.60,2,4,6,8,A,C,E.82,4,6,8,A,C.A0,2,6,8,C,E.B4,A.C1,7,D.D3,5,9,B.E0,E.F2,4,A,C.G0,6,8,E;113,B.21,D.46,8.51,3,B,D.65,9.72,C.85,7,9.A2,6,8,C.C1,7,D.F1,3,B,D.G7;246,8.51,3,B,D.75,9.B7.C1,D.G7;346,8.51,D.75,9.C1,D.G7:DSZ8RDR0VQ5NUD9AQJEYIVU3JA2IYHN31O7SNX7PDCQAMSZRPKTEZH353HPV9UJK012U7TRTX9SCVZABN22XOGPBCMKG9BYGXB8HKCT7JGYQ", "hasFlower": 0, "hasSeason": 0}, {"id": "135", "q": "004,7,A.10,E<PERSON>22,5,7,9,C.30,E<PERSON>43,5,7,9,B.51,D.64,<PERSON><PERSON>71,7,D.84,A.91,6,8,D.B1,5,7,9,D.C3,<PERSON><PERSON>D0,5,7,9,E<PERSON>E2,<PERSON><PERSON>F4,A<PERSON>G1,6,8,D.H3,B.I0,6,8,E.J2,C<PERSON>K0,4,7,A,E;110,E.25,9.30,7,E.43,5,9,B.51,D.64,A.71,7,D.84,A.97.A1,D.C4,7,A.E2,C<PERSON>F4,A.G1,6,8,D.H3,B.I0,7,E.K0,4,A,E:0A8ANY8J84VRWYX4STU112GZ5KKXB5F23DJXMDHNCAGK35QBWWNCO3MOKE43MHVJPA0ZVNQ2RQBZTDFUQF0S2MEUU8VPGXRJZG0D4FWB5R", "hasFlower": 0, "hasSeason": 0}, {"id": "136", "q": "001,3,5,7,9,B,D.20,2,5,7,9,C,E.40,4,6,8,A,E.60,2,5,7,9,C,E.84,7,A.A0,6,8,E.B2,C.C4,6,8,A.D0,E.E2,6,8,C.F0,E.G2,4,A,C;102,5,9,C.20,5,9,E.37.40,4,A,E.56,8.60,2,C,E.77.84,A.A6,8.B2,C.C4,6,8,A.D0,E.E2,6,8,C.F0,E.G2,C;205,9.30,7,E<PERSON>61,D.84,A.C5,9.D7.E0,E.F2,C:GFRWT8LF3LY4645AUK06UFATMGSO0O8R6C7B52IDO2J1JRMYWR6TM3IU1LKY0MNSLG50DUNB883TIFAVSS1V532BKWK7AG2WONVYNI1BV7C7", "hasFlower": 0, "hasSeason": 0}, {"id": "137", "q": "002,4,6,8,A,C,E.22,4,7,9,C,E.41,4,C,F.56,A.60,2,4,8,C,E,G.76,A.80,2,8,E,G.96,A.A0,2,4,8,C,E,G.B6,A.C1,3,D,F.E1,3,5,7,9,B,D,F.G2,4,6,8,A,C,E;103,7,9,D.23,7,9,D.51,F.63,8,D.80,2,6,A,E,G.A4,7,9,C.C1,F.E3,5,8,B,D.G2,5,7,9,B,E;268.80,6,A,G.E8;368.86,A:2H0KISLED4XECM8J473SF1GYH7E29Z2FX5L4FB98M638ZIN4BYWJHO05QXPG796JWTGXF7CCO30QCMND9RI1QMP6IG8WKE1K13WR0JHKTQ62", "hasFlower": 0, "hasSeason": 0}, {"id": "138", "q": "000,3,5,B,D,G.17,9.20,3,5,B,D,G.37,9.40,2,5,B,E,G.58.60,2,4,C,E,G.76,8,A.81,3,D,F.96,8,A.A0,G.B2,5,7,9,B,E.C0,G.D2,4,7,9,C,E.E0,G.F2,5,7,9,B,E.G0,G.H3,5,8,B,D.I0,G;115,B.28.35,B.41,8,F.60,8,G.81,7,9,F.A7,9.B1,5,B,F.D0,8,G.F1,8,F.G5,B.H8;260,G.B5,B.D0,G:09N33F9U085YX1XIHCIVLTRUZQJP82ZSS1GIHJP5T6JUGXI50LP19GC82ZV6N6RQQJTZRLRS2F56HY2PXAALQVAANS3UVF3H1N08GTYCCFY9", "hasFlower": 0, "hasSeason": 0}, {"id": "139", "q": "000,2,6,8,C,E.20,3,6,8,B,E.41,3,B,D.55,9.62,7,C.75,9.80,2,C,E.94,6,8,A.A1,D.B5,9.C1,D.D3,5,7,9,B.F1,3,B,D.G5,7,9.H0,3,B,E.I5,7,9;106,8.10,E.23,6,8,B.41,3,B,D.55,9.62,C.80,E<PERSON>A1,D.C1,D.D6,8.F1,3,B,D.G6,8.H3,B.I5,9;241,D.80,E.A1,D.D6,8.F1,D.G6,8.I5,9;3A1,D.I5,9:AE047KVQMAYLOB8JUN78QKLYY3U860EHDP0ER6RJDEXJZRN38BV6PORLZ63M7CYSWKVJ2FVFX5NPN5X1S7AL413CD0AZ1X2I1CPC5ZWHDK5I", "hasFlower": 0, "hasSeason": 0}, {"id": "140", "q": "002,5,7,A.20,5,7,C.32,A.40,5,7,C<PERSON>53,9.60,5,7,C.72,A<PERSON>84,6,8.91,B.A4,8.B0,6,C.C3,9.D0,5,7,C.E2,A.F0,5,7,C;102,A.15,7.20,C.32,5,7,A.40,C.53,9.66.85,7.91,B.A4,8.B6.C3,9.D0,C.E2,5,7,A.F0,C;215,7.30,2,5,7,A,C.66.85,7.91,B.B6.E0,2,5,7,A,C;332,5,7,A.E2,5,7,A:IDSUPFSVNMZVT4XPJLI11YYLS6QJGXN4YJB6QNKFOBDNMWJCEVK5P313DOZT241KOCMI5GTTQ6E6ZHGYDLQ2HV3ZGBOMSILEB4EW3UPK", "hasFlower": 0, "hasSeason": 0}, {"id": "141", "q": "003,5,7,9,B.10,E.23,5,9,B.30,E.42,5,7,9,C.50,E.63,5,7,9,B.80,2,4,7,A,C,E.A0,2,4,7,A,C,E.C2,4,6,8,A,C.D0,E.E2,4,A,C.F0,6,8,E.G2,C.H0,4,A,E.I6,8;103,5,7,9,B.24,A.30,E.42,6,8,C.50,E.63,5,7,9,B.80,2,4,A,C,E.97.A1,4,A,D.C6,8.D3,B.E0,E.F2,6,8,C.G0,E.H4,A:764Z56NDA21ELH3SGM3G1CAIQP7WVL1GLUCVJMEB405ROL9QR1Q6GAYBMEN2DI6NZ0AOFI88B2VMB3IU0Y25ON07UQPFO53PSVEHH7HWPU9J", "hasFlower": 0, "hasSeason": 0}, {"id": "142", "q": "001,4,6,8,A,C,F.20,2,6,A,E,G.34,8,C.42,6,A,E.50,4,C,G.66,A.70,2,4,8,C,E,G.86,A.92,4,8,C,E.A0,6,A,G.B3,D.C0,7,9,G.D2,4,C,E.E0,6,8,A,G.F2,E.G0,5,B,G;101,4,C,F.22,6,A,E.34,C.46,A.60,5,B,G.72,7,9,E.84,C.98.A0,G.C3,D.D7,9.E2,E.G0,5,B,G;2D7,9.G0,G:GX5UIR3QAREYWMGTPWVVAWOOSTEA5JOEBDJBHWIKI2XYM69QP1UOTNBAL6QKTHI1D4DM9B4P163NJMSZJLF35F5VCZHZ6QVZ3HEPD12C", "hasFlower": 0, "hasSeason": 0}, {"id": "143", "q": "003,6,8,B.10,E.22,4,6,8,A,C<PERSON>30,E<PERSON>42,4,7,A,C.62,4,A,<PERSON><PERSON>70,E.82,4,6,8,A,C.90,E.A4,A.B2,C.C7.D0,2,4,A,C,E.E6,8.F2,4,A,C.G0,6,8,E;103,6,8,B.10,E.23,B.53,B.74,A.A4,A.B2,C.C7.D1,D.E3,6,8,B.G0,E;203,B.10,E.23,B.74,A.A4,A.D1,7,D.G0,E;374,A.A4,A.D1,D;474,A.A4,A:BVEOJFZF5EN76SULNJZR6VU535Y50OVSQNTLY6F2RN1SK90Y1MIM9EJ9QB2KBRIYLFU7JTK7O1I2K33ZV7OZRUQME0B3TT9M160IQ2LS", "hasFlower": 0, "hasSeason": 0}, {"id": "144", "q": "000,2,4,A,C,E.16,8.21,3,B,D.35,9.40,2,7,C,E.55,9.60,2,C,E<PERSON>74,7,A.80,2,C,E.95,7,9.A0,3,B,E.B7.C0,2,5,9,C,E.D7.E1,4,A,D.F6,8.G0,2,C,E.H4,6,8,A.I1,D.J3,5,7,9,B.K0,E;132,C.40,5,9,E.60,E.73,7,B.81,D.97.B7.C2,C.E7.G6,8.H1,<PERSON><PERSON>I4,<PERSON><PERSON>K0,<PERSON>;281,7,D.G6,8;387.G6,8:349HUT3OX7GF4HIXBRTNOEVA4AP991AJ2YKKKROYCWI1BJFCPNVXRLWUUOUR76AVG2LPT9T31FEQ62QH3VIPLIKFLCCH1QQ7XE7N4N2E", "hasFlower": 0, "hasSeason": 0}, {"id": "145", "q": "000,2,4,A,C,E.16,8.20,2,C,E.35,7,9.51,3,5,7,9,B,D.71,4,A,D.86,8.90,2,4,A,C,E.B0,2,4,6,8,A,C,E.D4,A.E0,2,6,8,C,E.G2,5,7,9,C;100,4,A,E.12,6,8,C.20,E.36,8.51,3,6,8,B,D.81,7,D.93,B.B1,5,9,D.D4,A.E6,8.F2,C.G5,7,9;212,6,8,C.20,E.47.F7;316,8;416,8;517:RZ8ORY4H9DQAI9WQP7ZYDPIEYH3DIUES4L4P29E21LL5FBYX2PR37TSTDW1SUWZROXIT8A7B0FX9U8UOBFTSQO0X70W5E80L4233FBQZ", "hasFlower": 0, "hasSeason": 0}, {"id": "146", "q": "000,2,4,7,A,C,E.22,C.35,7,9.40,2,C,E.56,8.60,2,C,E.77.80,3,B,E.97.A0,2,C,E.B6,8.C0,3,B,E.D7.E1,5,9,D.F3,B.G1,5,9,D;101,3,7,B,D.35,7,9.40,E.57.61,D.77.80,E.97.A1,D.C0,3,7,B,E.E5,9.G1,D;201,3,7,B,D.35,7,9.40,E.61,D.77.97.A1,D.C3,B.E5,9;301,7,D.61,D.C3,B:LK8DRYFTQELTCGRJFSQQREYBA5XNA3E4B0B0MT18347LMMYU4LKEK7D43UC1XKF71XBA2JURNNCD1J5C35NMUJ0FQ2G02DAST527Y88X", "hasFlower": 0, "hasSeason": 0}, {"id": "147", "q": "000,2,4,7,9,B,E,G,I.20,2,4,6,9,C,E,G,I.40,2,4,6,9,C,E,G,I.60,2,4,6,9,C,E,G,I.80,3,5,7,9,B,D,F,I.A0,2,4,6,8,A,C,E,G,I.C0,4,7,B,E,I.D2,9,G.E0,4,6,C,E,I.F2,9,G.G0,4,6,C,E,I.H2,9,G.I0,4,6,C,E,I;108,A.13,F.29.41,H.61,4,E,H.C4,7,B,E.D1,H.F1,H.H9:IS29XZ0CYTJD3WRGZTDPA8NRCMFU5QX58GGMH49E24EOF87SDBPOL2FJ03RNQFP09BEM04ISKSXUW4CIJT9Y2Y7WCGMY3PXRW8AHIJKD3ETL", "hasFlower": 0, "hasSeason": 0}, {"id": "148", "q": "000,2,4,6,8,A,C,E.20,2,5,7,9,C,E.41,3,5,9,B,D.57.61,4,A,D.77.82,5,9,C.90,7,E.A3,B.B0,7,E<PERSON>C3,B.D5,9.E7.F1,4,A,D.G6,8.H1,3,B,D.I7.J0,2,5,9,C,E;101,4,7,A,D.20,6,8,E.44,A<PERSON>51,7,D.64,A.85,7,9.A0,3,B,E<PERSON>C3,B.D5,9.F1,4,7,A,D.H7.J0,E;204,A.54,A.A0,E.F1,D;304,A:JMYNFIDW4MK6M3YJYV84WL4DLB5FO133P2ENGOIKKJTFF9O9OW186EI4T86XRL7HLB3JYVX19WH7GRH55GTRVMTHRI12V8957GPE7KB6EB", "hasFlower": 0, "hasSeason": 0}, {"id": "149", "q": "000,2,4,7,A,C,E.21,4,7,A,D.40,2,6,8,C,E.54,A.60,2,6,8,C,E.74,A.80,2,7,C,E.94,A.A1,7,D.B3,5,9,B.C0,E.D2,5,7,9,C.E0,E.F2,6,8,C.G4,A.H1,7,D.I4,A;101,3,7,B,D.31,D.53,5,7,9,B.60,E<PERSON>73,B.80,E.97.B4,A.D0,2,5,7,9,C,E.F2,C.I4,A;201,7,D.31,D.57.B4,A.E2,C:LGWDOMGR58KZS97N6BGFQD17LXE766L9WBALZ2WINEXJ5ZA5AODDCJ11QQ4Z5AVOX4M32X3QCI601F7YE8SP2NR28KEV0VPSSYVWNO8G", "hasFlower": 0, "hasSeason": 0}, {"id": "150", "q": "000,2,5,8,B,E,G.20,4,6,A,C,G.41,4,6,A,C,F.61,4,C,F.76,A.81,3,8,D,F.96,A.A2,4,C,E.C1,4,6,A,C,F.E4,7,9,C.F0,G.G3,5,7,9,B,D;100,2,E,G.15,B.20,G.35,B.41,F.61,4,C,F.86,A.A2,4,C,E.C5,B.E7,9.F0,G.G5,B;202,E.20,G.35,B.61,4,C,F.86,A.A3,D.F0,G;302,E.61,F:LSAY75G1AW1X11YA6VMZ42LOJBIO97Y56HJX8ZWGVMIALHI582LEB47TV9STB929H46VY7MICGCEZB48GSEZM35WTOHJT2E38J3W6O3S", "hasFlower": 0, "hasSeason": 0}, {"id": "151", "q": "000,2,6,8,A,E,G.14,C.21,7,9,F.33,5,B,D.41,7,9,F.53,D.60,5,B,G.72,8,E.84,6,A,C.91,F.A4,6,8,A,C.B0,G.C2,4,7,9,C,E.E0,2,5,8,B,E,G.G1,4,6,8,A,C,F;100,2,7,9,E,G.14,C.27,9.32,5,B,E.48.53,D.72,8,E.86,A.A4,7,9,C.C2,4,8,C,E.E2,5,8,B,E.G4,6,8,A,C:XOA8NPMQEFYAGS3255OBB9F30OE8CVHG028Y7UVXAPO7JSC3G95YJFQRYCMMEVZB0L0LTFRNB7WUILE37IJJC5VQ8WPGINTHPNMALZQI", "hasFlower": 0, "hasSeason": 0}, {"id": "152", "q": "001,3,5,7,9,B,D.20,3,6,8,B,E.41,3,5,9,B,D.57.62,4,A,C.70,6,8,E<PERSON>83,B.95,7,9.B3,5,9,B.C0,E.D2,4,6,8,A,C.F1,3,5,9,B,D.G7.H0,3,B,E.I5,7,9;101,7,D.13,B.42,4,A,C.57.62,4,A,C.70,E.87.C0,3,B,E.D5,7,9.E2,C.F4,A.I5,9;244,A.62,C.87.D5,7,9.F4,A.I5,9;362,C.87:0513W1LUZOA2B37D4WCNBM40OZ81OK22R4NWB750WY8DLMDKRXLXUNB7713P6G5CMKU508X32PACYNGG6RZGLAK6RP6C4YDUXZOAPMY8", "hasFlower": 0, "hasSeason": 0}, {"id": "153", "q": "000,2,4,6,8,A,C,E,G.21,3,6,8,A,D,F.41,4,6,8,A,C,F.63,8,D.70,G.82,5,7,9,B,E.A0,7,9,G.B3,D.C1,7,9,F.D4,C.E0,6,8,A,G.F2,4,C,E.G0,7,9,G;100,2,5,B,E,G.17,9.22,E.36,8,A.70,8,G.98.A0,G.D7,9.F2,4,7,9,C,E;218.37,9.70,8,G.A0,G.D7,9;318.38.70,8,G.A0,G;478:U8ADQ0IM2KIVE4OQP8JUOVNJIN2QPE7RZDZX804JU7SP2D04RFWGI05GCDROFCFVOAMKPSMSCUQWF2ETCXNX5NEWSVZJZ7G4MTGX7WR8", "hasFlower": 0, "hasSeason": 0}, {"id": "154", "q": "000,2,5,7,9,B,E,G.20,2,4,6,A,C,E,G.38.40,2,4,6,A,C,E,G.58.60,2,6,A,E,G.74,8,C.80,2,E,G.94,8,C.A0,6,A,G.B2,E.C4,6,8,A,C.D0,2,E,G.E4,6,8,A,C.F0,2,E,G.G6,A;101,7,9,F.15,B.20,G.32,4,C,E.46,8,A.52,E.66,8,A.70,G.88.A0,6,A,G.B2,E<PERSON>C6,A.D3,8,D.E0,G:R6F2XNQWZY0EF77OHEKIKOCPXB6MCG5OXCOKWFLAB65ELYX6SM14GN433122KS45H7MA5C4IBW9PTWP0ZMI12S719QFZIPZES9YTBR9Y", "hasFlower": 0, "hasSeason": 0}, {"id": "155", "q": "001,3,B,D.15,9.20,E.32,5,7,9,C.50,3,6,8,B,E.72,4,A,C.80,7,E.A6,8.B0,4,A,E.C2,C.D0,4,6,8,A,E.F2,5,9,C.G0,7,E.H4,A.I0,2,C,E;101,D.20,5,9,E.32,C.47.50,3,B,E.73,B.97.B4,A.C0,E.D4,6,8,A.F2,5,9,C.H0,4,A,E;201,D.20,5,9,E.50,E.63,B.97.D5,9.H0,E;325,9.50,E.D5,9:BT30BS8VSCXWP3N20C9JRT89NVOP6Q1PQS0MLA2CCINLBDJTV857YVRRGSJ7KI05Z2LBP7WN965MGZLOA2QX863434G6YTKR57G1QOOJD9", "hasFlower": 0, "hasSeason": 0}, {"id": "156", "q": "000,4,7,A,E.20,3,5,7,9,B,E.41,3,5,9,B,D.57.61,3,B,<PERSON><PERSON>76,8.80,E.92,5,7,9,C.A0,E.B5,7,9.C0,3,B,E.D6,8.E1,4,A,D.F6,8.G0,2,4,A,C,E.H7.I1,5,9,D;107.20,3,6,8,B,E.42,C.62,C.76,8.90,7,E.B5,7,9.C0,E.D6,8.G0,7,E;217.42,C.76,8.90,7,E.B5,9.D7.G0,7,E;317.76,8.97.D7.G7:61C4QAW75BD8L6PU1VP7EPRZQR9K4MZDKEGIBIBKLG5KGM1C969Z3VR3BD63Q7CWHCHH7GEUAFSP5LFVF4DWQMAM4L3IIZFRHASE58V19W", "hasFlower": 0, "hasSeason": 0}, {"id": "157", "q": "000,2,4,A,C,E.16,8.20,3,B,E.40,2,4,6,8,A,C,E.60,4,6,8,A,E.80,3,6,8,B,E.A3,7,B.B0,5,9,E.D0,2,4,6,8,A,C,E.F0,3,5,7,9,B,E;101,3,B,D.16,8.33,B.40,E.60,5,9,E.77.97.A3,B.B0,5,9,E.D0,2,4,A,C,E.E6,8;203,B.40,E.60,5,9,E.A3,B.B0,5,9,E.D0,2,C,E.E6,8:XAZ65BUZHM6EZI89QCRPUTYJUTWPLC8WTLP1NAYEIOMCXE50MQZ6LL0A580X2NBV69NH7WVB94WBR92XCJUQ71QPT5M84O4E4RNAR0II", "hasFlower": 0, "hasSeason": 0}, {"id": "158", "q": "000,2,4,6,8,A,C,E,G.20,2,4,6,8,A,C,E,G.40,3,6,A,D,G.58.60,3,6,A,D,G.78.80,2,4,6,A,C,E,G.98.A0,2,4,C,E,G.B7,9.C0,2,4,C,E,G.D6,8,A.E0,2,4,C,E,G.F6,8,A.G0,3,D,G.H6,A.I0,2,8,E,G.J4,6,A,C.K0,2,8,E,G;113,5,B,D.21,F.33,D.80,G.92,E.A8.B1,F.D0,G.I7,9.J1,F:XWGHVS6IVAANQPQ7U9B27M6XI1O3ER3ZECI7TZNR1WJ3B5E3NF9C5CFR2722DO1PW1OR8N4AQ4MDJHW6DD8T86PAIZ5TQUPEXTXCG9Z85O9S", "hasFlower": 0, "hasSeason": 0}, {"id": "159", "q": "000,2,4,7,9,B,E,G,I.20,2,4,6,8,A,C,E,G,I.40,2,4,6,9,C,E,G,I.61,3,5,8,A,D,F,H.80,2,4,6,8,A,C,E,G,I.A2,4,6,8,A,C,E,G.B0,I.C2,4,7,9,B,E,G.E0,2,4,6,8,A,C,E,G,I.G0,3,5,7,9,B,D,F,I.I0,2,6,C,G,I;120,I.40,9,I.55,D.74,E.97,B.C4,E.E0,5,D,I.F9.H0,I:X7LIQ9SPN1R7YWRGB1LVIYK3ORUXY7O4VG3OQ9FAA60WDJJAWN61BNOD3D5VJQ43BADUQZ759RIWPPFJTXISNFKXU9U1SZYLSHT8BHVP8F0L", "hasFlower": 0, "hasSeason": 0}, {"id": "160", "q": "000,2,4,6,8,A,C,E.20,3,5,9,B,E.41,3,5,9,B,D.57.63,5,9,B.70,7,E.82,4,A,C.90,7,E.A2,C.B0,4,6,8,A,E.C2,C.D4,6,8,A.E0,2,C,E.F4,6,8,A.G0,2,C,E.H5,9.I1,7,D;101,D.14,A.20,E.45,9.53,7,B.73,7,B.80,E.92,C.A0,E.B2,5,7,9,C.D2,C.E0,6,8,E.I1,D;253,B.67.90,2,C,E.D2,C:WCL1JACIW7PLH6F8PWNLCZUO0RBYEBWPYNH7NZIRUZ6ABTOH5T6EDYJXAXD1HJ4FY1CLTKN07FA7DX8ZOPIKI5B8RTD6K0E50J1K5EX8OFR4", "hasFlower": 0, "hasSeason": 0}, {"id": "161", "q": "001,4,6,8,A,D.20,5,7,9,E<PERSON>33,B<PERSON>40,5,7,9,E<PERSON>52,<PERSON><PERSON>64,A<PERSON>70,2,6,8,C,E.96,8.A0,2,C,E.B4,A<PERSON>C1,7,D.D5,9.E0,3,7,B,E<PERSON>F5,9.G0,2,7,C,E;101,4,6,8,A,D.25,7,9.45,9.52,C.64,A.70,2,7,C,E.96,8.A0,2,C,E.B4,A.D5,9.F5,7,9.G2,C;201,5,9,D.17.35,9.62,C.E5,9.F7.G2,C;317.35,9.E5,9;417:FND5S8I43KNCL6WCL4S9ETRUVN1VUCAVFVN0AFK96PUAQD5IR83AGGS131LT0PR74DEDRJZYK3W6G4MYZG891CFEP7QLKY69E07MJP78S0YU", "hasFlower": 0, "hasSeason": 0}, {"id": "162", "q": "000,2,4,8,C,E,G.16,A.20,3,D,G.36,A.40,2,4,8,C,<PERSON>,G.56,A.60,4,8,C,G.72,6,A,E.80,4,8,C,G.92,E.A0,4,6,8,A,C,G.C0,2,4,7,9,C,E,G.E0,2,5,7,9,B,E,G.G0,2,6,8,A,E,G.H4,C.I0,2,6,A,E,G.K0,2,5,7,9,B,E,G;104,C.10,G.30,G.54,C.66,A.73,D.90,G.B0,G.F6,A.I0,G.K0,G;220,G.J0,G:DFWQOHFEACRY29MDE4PVFZH8RU9JOJPOH0RM5Y4SI8NFKOVZNSI4MPBCRX4K2TDGMTA1AQP9JJ5ZTX7E2W9DIKA7WWUQVH10CQSG2ECIYTVKSZYB", "hasFlower": 0, "hasSeason": 0}, {"id": "163", "q": "000,2,4,7,A,C,E.20,2,5,9,C,E.37.40,3,5,9,B,E.57.61,4,A,D.81,3,5,9,B,D.A1,3,5,7,9,B,D.C0,2,C,E.D5,9.E0,2,C,E.F4,6,8,A.G2,C.H0,4,6,8,A,E.J0,2,5,9,C,E;100,2,7,C,E.20,2,C,E.37.40,3,B,E.57.64,A.81,3,5,9,B,D.A1,6,8,D.C2,C.D5,9.E2,C.F4,A.G7.H0,4,A,E.J0,2,C,E:KA2LJQHHRHMCC2A7LV0D5WZ03BNUFFBV5SWRFNYY8OTEKEMQJQ1783NRJO0LMC7YU1ZO7Q211YT530LFOMCTW8DD83Z29UXEU9WSJETRDXZNH5", "hasFlower": 0, "hasSeason": 0}, {"id": "164", "q": "000,3,5,8,B,D,G.20,2,4,6,8,A,C,E,G.40,2,4,6,8,A,C,E,G.60,2,4,6,8,A,C,E,G.81,3,5,7,9,B,D,F.A0,2,4,7,9,C,E,G.C0,3,6,8,A,D,G.E0,2,5,7,9,B,E,G.G1,4,7,9,C,F;103,5,B,D.18.31,3,5,B,D,F.54,6,A,C.68.73,5,B,D.81,7,9,F.A3,7,9,D.C6,A.D0,G.E2,5,B,E.G8:ZMHHE4UTMY0XF1K1ZF32Y19ARYIUAE60WMLR92V6PYWPQQNA564NUF8TA38VQU5MNKI3FJRXPKXVI3LLLQNXJE21Z8WE45458K6Z2WP99VIR", "hasFlower": 0, "hasSeason": 0}, {"id": "165", "q": "000,2,7,C,E.14,A.20,2,6,8,C,E.41,4,6,8,A,D.62,5,7,9,C.70,E.82,4,7,A,C.90,E.A2,4,6,8,A,C.C0,2,4,6,8,A,C,E.E0,2,7,C,E.F4,A.G0,2,6,8,C,E;100,7,E.12,4,A,C.20,6,8,E.44,6,8,A.65,7,9.71,D.84,7,A.90,E.A4,7,A.C0,2,5,9,C,E.E1,7,D.G1,D;212,7,C.36,8.57.71,7,D.A7.C0,E.E7:M2A03121YTJV1JZAE7X4E0093XOCPMYDWDHRL4DZG2HY92JSSTIAGDPMNSYRCCP1EL7ZGT5F0RH8789HWEFR7GSZNJ5PFAXTX9MCLFOVLINVV3N3", "hasFlower": 0, "hasSeason": 0}, {"id": "166", "q": "001,4,7,9,C,F.20,4,7,9,C,G.32,E.40,4,6,8,A,C,G.60,2,5,B,E,G.77,9.80,2,4,C,E,G.98.A0,2,4,C,E,G.B6,8,A.C1,3,D,F.D5,B.E3,D.F0,6,8,A,G.G2,E.H0,4,7,9,C,G.I2,E;101,4,7,9,C,F.24,C.38.44,C.61,5,B,F.77,9.83,D.91,F.A3,D.C1,F.D5,B.E3,D.G8.H4,C;214,C.34,8,C.93,D.G8;324,C.38.G8:SNRFV87LZ73OKIVH64OWLIXNE4NY92NJXP74ZZL6P9IFM65S1CHQ5K1RVQPE1FUCK2SD48Q7MCU53RZ8XESJU6W2CYQX25V13IDREYLU3POO8KYF", "hasFlower": 0, "hasSeason": 0}, {"id": "167", "q": "000,3,5,7,9,B,E.20,2,4,6,8,A,C,E.40,2,4,A,C,E.56,8.60,2,4,A,C,E.76,8.80,2,4,A,C,E.97.A0,E.B2,4,6,8,A,C.C0,E.D2,4,6,8,A,C.E0,E.F2,4,A,C.G0,E.H2,4,6,8,A,C.I0,E;110,3,5,9,B,E.27.32,C.44,A.52,C.72,5,9,C.87.A7.B2,C<PERSON>C5,9.E2,<PERSON><PERSON>F4,<PERSON><PERSON>G2,<PERSON><PERSON>H6,8.I0,E;215,9.42,C.97.F2,C.H6,8:JK2RYDIPHTIW18M2Y6UAGU89E0Q0NG04PDJTLXXD4GRC51IWJF3R84F46R9LKGJK3TEQ8MH5A6Y0AK1HMLEIDCC71EQWUY7F6MWLUHXFXCPNATQP", "hasFlower": 0, "hasSeason": 0}, {"id": "168", "q": "001,3,6,8,B,D.22,4,6,8,A,C<PERSON>30,E<PERSON>45,7,9.51,D.74,6,8,A<PERSON>81,D.93,7,B.A0,E.B5,7,9.D0,E<PERSON>E4,6,8,A.G0,2,6,8,C,<PERSON><PERSON>H4,A<PERSON>I0,7,E;101,D.16,8.30,7,E.76,8.81,D.A0,E.B5,9.D0,E.E4,6,8,A.G0,2,7,C,E.I0,E;201,D.16,8.37.76,8.E4,6,8,A.G7.H0,E;301,D.16,8.37.E5,7,9.G7;401,D.17.37.E6,8.G7;501,D:CXJCFHVTZR8RSEY00N9GE9CXMRM0NV584577UD6MLFE8HB6GVGBUD0Y622B9UXGFZRWCNV2N8MWLZWY2FJEXB6T9DDST7475Z5WUYJJ4STLS4L", "hasFlower": 0, "hasSeason": 0}, {"id": "169", "q": "000,2,4,6,8,A,C,E,G,I.20,2,4,6,8,A,C,E,G,I.40,2,4,6,8,A,C,E,G,I.60,2,4,6,8,A,C,E,G,I.80,2,4,6,8,A,C,E,G,I.A0,2,4,6,8,A,C,E,G,I.C0,2,4,6,8,A,C,E,G,I.E0,2,4,6,8,A,C,E,G,I.G0,2,4,6,8,A,C,E,G,I.I1,3,5,7,B,D,F,H;106,C.48,A.68,A.88,A.B8,A.D8,A.F8,A:8VOQF749OH2OU137C6RC41KYTYX5T3EPKKV5YY1E6W8E6JQURQ09TCPJ5W7WC2LUB1DS058XJ7PH8DVXO39B4SVUQ2L6TXP3WFELRRK90B02L4BJ", "hasFlower": 0, "hasSeason": 0}, {"id": "170", "q": "004,6,8,A.12,C.26,8.30,2,C,E.44,6,8,A.60,5,7,9,E<PERSON>72,C.80,4,A,E.A0,2,6,8,C,E.C3,7,B.D1,5,9,D.E3,B.F6,8.G2,4,A,C;104,6,8,A.12,C.26,8.32,C.44,6,8,A.60,5,9,E.72,C.80,4,A,E.A0,2,7,C,E.C3,B.D1,5,9,D.F3,6,8,B;204,6,8,A.26,8.44,7,A.71,D.A1,D.F3,6,8,B;305,9.47;447:NJZPNRTZM1JX7BYLLY0V71C8U5TMTF03HLKN4PZ78C6O96UU4R7F1JZAN4RF9LYX6BAM3XHB501KROM450A8K3OC5FBCEVO8PUJKP36EXYTA", "hasFlower": 0, "hasSeason": 0}, {"id": "171", "q": "001,3,5,8,B,D,F.23,5,8,B,D.30,G.43,5,8,B,D.50,G.62,4,6,A,C,E.70,8,G.82,4,C,E.97,9.A0,2,4,C,E,G.B6,A.C0,2,4,8,C,E,G.D6,A.E0,2,4,8,C,E,G.F6,A.G2,4,C,E;104,8,C.30,5,B,G.54,C.70,2,4,C,E,G.94,C.A0,2,E,G.B4,C.C8.D4,6,A,C.E0,G;204,C.74,C.94,C.D4,C;374,C;474,C:SJKLITSJFX7TK4ZGFI4AMP15LXN9LFD1PAOOQZMV8FA7Z8J56M2X58XRLNNVPZ4T6CAVQYJ4DVYRDRY1SGQP952CI1ON9M79DQR7ST6I6O8Y", "hasFlower": 0, "hasSeason": 0}, {"id": "172", "q": "000,4,6,8,A,C,G.20,2,4,6,8,A,C,E,G.40,3,5,7,9,B,D,G.63,5,B,D.71,F.83,5,7,9,B,D.90,G.A3,5,B,D.C0,2,4,C,E,G.D6,A.E0,4,C,G.F2,6,8,A,E.G4,C;104,6,8,A,C.10,G.23,5,B,D.37,9.40,3,D,G.55,B.71,4,C,F.93,D.A5,B.C0,2,E,G.D6,A.F3,5,B,D;204,C.23,D.71,4,C,F.F3,D:XSPD8LVDY0FSINWP9HX0Q49NATQT7PHCEDM34NYZVM6XIA5I6S4FKIMEFUHFADWK09CNXCUTR59T7EQKSAZQW5766GKRP857GVCHMW4L03VE", "hasFlower": 0, "hasSeason": 0}, {"id": "173", "q": "000,2,4,6,8,A,C,E.20,3,5,7,9,B,E.41,3,6,8,B,D.61,3,6,8,B,D.80,3,5,7,9,B,E.A2,4,A,C.C0,2,5,7,9,C,E.E0,5,7,9,E.F2,C.G5,7,9.H1,3,B,D.I6,8.J0,3,B,E.K5,7,9;101,7,D.26,8.46,8.51,3,B,D.86,8.A2,4,A,C.C1,5,9,D.F5,9.G7.I6,8;246,8.51,D.86,8.A4,A.C5,9;3C5,9:E4KFNGRW93CYC2F02U0877HKUIW502TAHYM8RPM5H1US4AAIINFRM93WUXXHKXNIDRQ1ETSGBKSN9T1Z15DSZTABDXGG5077QD9W2BMPFB", "hasFlower": 0, "hasSeason": 0}, {"id": "174", "q": "000,2,4,6,8,A,C,E,G.21,4,C,F.36,8,A.41,3,D,F.56,A.60,2,8,E,G.80,2,5,7,9,B,E,G.A2,8,E.B0,6,A,G.C4,C.D1,6,8,A,F.E3,D.F1,F.G4,6,8,A,C;100,4,6,8,A,C,G.24,C.31,7,9,F.43,D.51,6,A,F.70,8,G.82,5,B,E.98.B0,6,A,G.D1,7,9,F.G4,7,9,C;238.43,D.70,G.D7,9;338.D8:X6GSOGPYML41DGVDO01ZLVH68SAM10OIQ4F8Y80QS2EWL36KZB9XE295RH6RMPWIF7WKIWTLOY2TBB9M2YT4KDTSIK0915348BDGQ7AXQX", "hasFlower": 0, "hasSeason": 0}, {"id": "175", "q": "000,3,7,B,E.15,9.22,7,C.34,A.42,C<PERSON>60,3,5,7,9,B,E<PERSON>80,3,B,E<PERSON>95,9.A0,2,C,E<PERSON>B4,A.C6,8.D0,3,B,E.F2,4,A,C.H2,5,7,9,C.I0,E;103,7,B.22,7,C.34,A.42,C.63,5,7,9,B.70,E.83,B.90,E.C7.D3,B.F2,4,A,C.H2,6,8,C;203,B.17.22,C.34,A.66,8.70,E.F3,B.H2,C;317.34,A;434,A;534,A:JHROAHW7UO1VVDXRAJ7SQM0R8K91X10DLVJC3IBYFCQ5SU2NBHXC8Y2BHXNA606CO69E8NWBQK0ZF55M53GGWR1QLVE86GGJIOWAZSSN", "hasFlower": 0, "hasSeason": 0}, {"id": "176", "q": "001,3,6,8,B,D.20,6,8,E.42,4,6,8,A,C.50,E.62,4,7,A,C.81,3,5,7,9,B,D.A0,4,6,8,A,E.B2,C<PERSON>C4,A.D1,7,D.E3,B.F5,7,9.H0,6,8,E.I2,C;101,6,8,D.20,6,8,E.43,6,8,B.63,B.77.81,5,9,D.97.A0,4,A,E.B2,C.D1,D.E7.G6,8.H0,E;207.26,8.47.53,B.77.97.A4,A.B2,C.F7;317.53,B.77.97;453,B.77:CHEVB2PER44M04HQ1E9M4LJWV9XBOY68P8S1BCJHSJVI3CK8GRYKZJ3WAM2NND9UXNLCVUE0OIZ9DW05UX63LO5H8QONGG3GSQSL0MWQBAUX", "hasFlower": 0, "hasSeason": 0}, {"id": "177", "q": "000,2,5,7,9,C,E.20,2,4,6,8,A,C,E.40,2,4,A,C,E<PERSON>56,8.62,4,A,C.70,6,8,E.83,B.96,8.A0,2,4,A,C,E.B6,8.C2,4,A,C.E0,2,6,8,C,E.F4,A.G0,6,8,E.H2,4,A,C.I0,6,8,E.J2,4,A,C.K6,8;101,7,D.15,9.20,2,7,C,E.43,B.55,9.63,B.70,E.96,8.A0,<PERSON><PERSON>C2,<PERSON><PERSON>E0,<PERSON><PERSON>G5,9.H3,B.I7.J2,C.K6,8:BICS4BWAAVUQ6HPKU69H1K9AMNM0X2X74KTB32C4PLO21LIFIXSNN3081O1FHKY3SV9OWCH28INFFYYXM7CTAWP6SVPQQQ09YVO40W38B68M", "hasFlower": 0, "hasSeason": 0}, {"id": "178", "q": "000,2,4,7,A,C,E.20,2,5,9,C,E.37.40,2,5,9,C,E.57.60,3,B,E.75,7,9.80,2,C,E.95,7,9.A0,2,C,E.B5,7,9.C2,C.D0,4,6,8,A,E.F0,3,7,B,E.G5,9.H0,2,C,E.I4,7,A.J0,2,C,E.K4,6,8,A;101,4,A,D.20,E.40,5,9,E.60,7,E.80,E<PERSON>92,C.C2,C.D0,4,A,E.F0,7,<PERSON><PERSON>H0,<PERSON><PERSON>I3,B.J0,E.K4,6,8,A:Z7R37L90OUFJ1E0EMY53A5GLZVE3WTC296G2RZNM7GQ6W085BX8CSUOKKMZ0YEL67R8GTAD5A28VFYQK2YMUONV36CASIILOVKWRCBXDWU1J", "hasFlower": 0, "hasSeason": 0}, {"id": "179", "q": "001,3,5,8,B,D,F.20,4,6,8,A,C,G.32,E.40,5,7,9,B,G<PERSON>52,E.60,4,6,8,A,C,G.72,E.80,4,8,C,G.92,E.A0,4,6,8,A,C,G.C0,2,4,7,9,C,E,G.E0,2,4,8,C,E,G.F6,A.G1,3,8,D,F;108.24,7,9,C.45,B.51,7,9,F.64,C.80,2,8,E,G.A6,A.B4,C.C1,7,9,F.D3,D.F3,7,9,D;264,C.88.B4,C.D3,D:4MQQNNBR6UYZ7MJE0TOHVJ17ABEZ26T9FKY0E249KH91KRIFJV4OAON6O02FX7B729VQUYKXTZXIIRHT4EZM60WAU1BXWIFUVJHWQMRW1NAY", "hasFlower": 0, "hasSeason": 0}, {"id": "180", "q": "003,5,9,B.11,7,D.23,B.30,6,8,E<PERSON>43,<PERSON><PERSON>51,6,8,D.63,B<PERSON>70,5,7,9,E.82,C.A0,5,7,9,E.B3,B.C1,6,8,D.D3,B.E0,6,8,E.F2,4,A,C.G6,8;104,A.11,7,D.30,6,8,E.43,B.56,8.70,E.82,C.A0,E.C2,6,8,C.E0,6,8,E.F4,A.G6,8;217.30,E.46,8.82,C.C2,6,8,C.E0,E.F4,6,8,A;330,E.82,C.C7.E0,E;430,E.82,C.E0,E;582,C:8MW5L74R9S17G0QHTFZEDDV0YSQSJU3BJXQT5BDTYLB34C2FL2ZHYDOI3ET3HBXZOZ085EE6XV1G7RA164Q5CASHUJGVUYGJFMC9LIF1CVWRX0U7R4", "hasFlower": 0, "hasSeason": 0}, {"id": "181", "q": "001,3,5,7,9,B,D.23,5,7,9,B.40,3,6,8,B,E.61,3,5,9,B,D.83,5,7,9,B.90,E.A3,5,7,9,B.C4,6,8,A.D1,D.E3,6,8,B.F0,E.G3,5,7,9,B.I2,C;101,3,5,7,9,B,D.23,6,8,B.40,3,6,8,B,E.61,3,B,D.84,A.90,E.A3,5,9,B.C4,6,8,A.D1,D.E3,B.F0,6,8,E.G3,B.I2,C;202,4,A,C.36,8.61,3,B,D.A3,B.D1,D.F6,8:8QQTVNB9T00PSKLT9LLAPCM4OJR7W6NZ4QC76QNH33ON3ZAVD0G8PPX8IVCMHFEA2OCS20ZRAJG27WLXBHBKKMW66IV94ETZ3BF2X9WIMFI4XHK7FO8D", "hasFlower": 0, "hasSeason": 0}, {"id": "182", "q": "005,7,9.10,E.22,4,6,8,A,C<PERSON>30,E<PERSON>45,9.50,2,C,<PERSON><PERSON>66,8.73,<PERSON><PERSON>80,E.92,4,6,8,A,C.B0,2,4,A,C,E.C6,8.D0,E<PERSON>E2,4,A,C<PERSON>F6,8.G0,3,B,E.H6,8.I0,4,A,E;107.15,9.20,2,7,C,E.35,9.40,E.52,C.73,B<PERSON>B4,<PERSON><PERSON>C6,8.E2,4,A,C.F7.H0,7,E;207.15,9.22,7,C.35,9.40,E<PERSON>52,C.73,B.B4,A.C7.E2,4,A,C.F7.H7;307.27.52,C.E2,C.G7;427:ULC9L2RJ53E81NF27W5WFZNSCHPMON724GECHRGKKLMJRYPHQWB8VXGF7KQP352SMGRCJ1V49809X9L1DSOQOHE7K1NO4QDS8WY40X0UTXFZEB0J5TMP", "hasFlower": 0, "hasSeason": 0}, {"id": "183", "q": "000,2,4,8,C,E,G.16,A.21,3,D,F.35,7,9,B.40,2,E,G.54,6,8,A,C.60,2,E,G.75,7,9,B.80,2,E,G.95,7,9,B.A0,3,D,G.B6,8,A.C1,F.D3,6,8,A,D.E0,G.F2,4,8,C,E.G0,6,A,G.H2,4,C,E.I0,6,8,A,G;100,2,E,G.23,D.31,6,A,F.54,6,A,C.60,8,G.72,6,A,E.95,7,9,B.C6,A.D3,8,D.G1,3,6,A,D,F:V6PCK6TRIZMBJNJOIMS1RVF2KQ37QQ6O9YIK3JZ33MV72VN9LP7I1BJ8ESC6WLEY11O0AE9KAXN05AEBT27O2FXNP5YX0XQ5CF88PM0FC9Y5B8AW", "hasFlower": 0, "hasSeason": 0}, {"id": "184", "q": "001,3,5,7,9,B,D.20,2,4,6,8,A,C,E.41,4,6,8,A,D.60,2,4,6,8,A,C,E.81,3,5,7,9,B,D.A1,4,6,8,A,D.C1,3,5,7,9,B,D.E0,2,4,A,C,E.F6,8.G1,4,A,D.H6,8.I0,2,4,A,C,E.J6,8.K0,2,4,A,C,E;104,A.12,C.41,5,9,D.65,9.71,7,D.94,A.A6,8.C2,4,A,<PERSON>.E1,D.F5,9.G1,D.H5,9.I2,C.K2,4,A,C;265,9.F5,9.G1,D:UJRVY03MAJU45DA4CY1SI68HV6KKIV10CTU0NL95OPD2M4QQV9NYWBS5RHHBOXDIABLRF6XPTHM6WLCNTC7KLAJ09K1M3NYRT185P342F7BFP9XUXF3IDJ", "hasFlower": 0, "hasSeason": 0}, {"id": "185", "q": "000,5,7,9,B,G.13,D.20,7,9,G.34,C.41,6,A,F.53,8,D.61,5,B,F.73,7,9,D.85,B.92,8,E.A4,6,A,C.B1,8,F.C3,D.D1,6,A,F.E4,C.F0,7,9,G.G3,5,B,D;100,5,7,9,B,G.13,D.20,7,9,G.58.74,6,A,C.88.92,5,B,E.A7,9.F0,4,7,9,C,G;200,6,8,A,G.20,7,9,G.76,A.88.A7,9.F0,7,9,G;306,A.10,8,G.A7,9;410,G:EX7IG2ZFO1MFJW50CL88F39BY495ZGIV4JWIVOCYVKWT61SLL7YPNSZT29ZMJKXDOXDPM1S4CRE3YC87DRPM3NX90OF5G37EWLI54VEBS1GJP2682D", "hasFlower": 0, "hasSeason": 0}, {"id": "186", "q": "000,2,4,6,8,A,C,E,G.20,2,4,7,9,C,E,G.40,4,7,9,C,G.52,E.60,4,6,A,C,G.72,E.A2,6,A,E.B0,4,C,G.C2,7,9,E.D0,4,C,G.E6,8,A.F0,2,4,C,E,G.G6,8,A;100,5,8,B,G.12,E.20,G.37,9.40,G.54,C.72,E.A2,6,A,E.B4,C.C0,8,G.E6,8,A.F0,3,D,G;205,B.30,7,9,G.54,C.B4,C.C0,G.E7,9;337,9.54,C.E7,9;438.E8:12JQ9TS1U134QJ4CHKU6PYJ2Q0GH3779I27JE8CA5S9WDRDTPLRQ50BSSNU4N7TINNADAH0CYKRB8LUWG43I6P0EDKEG92WBP1WTRI5GBYH5EK6AYC36", "hasFlower": 0, "hasSeason": 0}, {"id": "187", "q": "000,2,4,7,A,C,E.20,3,5,7,9,B,E.40,2,5,7,9,C,E.61,4,7,A,D.80,3,5,7,9,B,E.A0,2,4,6,8,A,C,E.C0,3,6,8,B,E.E0,2,5,7,9,C,E.G2,4,6,8,A,C.H0,E.I4,6,8,A.J0,E.K2,4,6,8,A,C;103,B.10,E.23,B.36,8.40,E.87.93,B.A0,E.C3,B.D7.E0,2,C,E.F5,9.H0,E.I6,8.J0,4,A,E;210,E.36,8.E2,C.I6,8:56X7A9Z0M9U3RQAISWA6F2CSY3VHX3GBIZWIV3CDPYAFN5C1HNRP79DSVOHUR2YMF61GDM64WUZ0OV50L4GRL2QC94F7PQN74MBWNQ2UPHIZG5S0YD", "hasFlower": 0, "hasSeason": 0}, {"id": "188", "q": "000,3,9,F,I.15,D.21,3,7,B,F,H.35,9,D.40,3,F,I.57,B.61,3,5,9,D,F,H.77,B.80,2,4,9,E,G,I.96,C.A1,3,8,A,F,H.B5,D.C0,2,7,9,B,G,I.D4,E.E1,7,B,H.F3,9,F.G0,5,7,B,D,I.H2,G.I0,4,6,C,E,I;115,D.22,7,B,G.35,9,D.53,F.66,C.72,G.89.91,6,C,H.B9.C2,<PERSON><PERSON>D4,<PERSON><PERSON>F8,A<PERSON>G5,D.H2,G.I5,D;239.66,C.89.B9.F9;3F9:LLW2PZAUPJ5YVOBL3BCUSR8NRVK7KY9P2RK9DOKE6APCJTGT3GZJWXNZO6D256X8S8G6LER7C2C7B57VVOXSYDANNY9IMTB5W3T8ZSIA3MUWGUEDIJXIE9", "hasFlower": 0, "hasSeason": 0}, {"id": "189", "q": "000,3,5,9,B,E.21,3,5,9,B,D.37.41,3,5,9,B,D.57.60,2,5,9,C,E.77.80,3,5,9,B,E.97.A0,2,4,A,C,E.B7.C0,4,A,E.D6,8.E0,2,4,A,C,E.G4,6,8,A.H1,D.I4,6,8,A.J1,D.K3,5,9,B;100,4,A,E.21,3,B,D.35,9.42,C.62,5,9,C.70,E.94,7,A.A1,D.B4,7,A.D0,<PERSON><PERSON>E4,<PERSON><PERSON>G4,A<PERSON>I5,9.J1,D.K3,B;204,A.97.A1,4,A,D.D0,E:K8GPE6YC63U4EY7EF3H0YUFZALWR19G7Z1SS4JOSHBP85LQBWU3OJPJ5TT7U0Y5R8H0KG1L96W7PTTO4ENJQ03HCBAR1CAZN8R9ZOWA6LQBKG4Q9K5SC", "hasFlower": 0, "hasSeason": 0}, {"id": "190", "q": "000,2,4,6,8,A,C,E.20,3,6,8,B,E.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.81,3,5,9,B,D.A0,2,4,6,8,A,C,E.C0,2,4,A,C,E.E0,2,4,6,8,A,C,E.G0,4,7,A,E.H2,C.I0,4,6,8,A,E.K1,3,5,7,9,B,D;103,5,9,B.23,B.42,6,8,C.67.83,5,9,B.A1,6,8,D.C0,3,B,E.E6,8.H2,7,C.I5,9.K1,3,B,D:4R730UMHIIJYKOUGWJZBBTU7OCQ2X3CXAJO7DH6SISMV6DEQSI2KHDEKJ4YAMXAGVA1O06W9QZZ2R7RGHSMRUB33DZETXBKC690LCLT02QLLE1GT", "hasFlower": 0, "hasSeason": 0}, {"id": "191", "q": "000,2,4,6,8,A,C,E,G.20,2,4,6,8,A,C,E,G.40,3,6,8,A,D,G.60,3,5,8,B,D,G.80,3,5,7,9,B,D,G.A1,4,6,8,A,C,F.C2,4,6,A,C,E.D0,8,G.E3,5,B,D.F0,7,9,G.G2,5,B,E.H0,8,G.I2,6,A,E;102,7,9,E.10,5,B,G.37,9.43,D.50,8,G.78.85,B.97,9.B5,B.D0,8,G.F5,7,9,B.H0,G.I2,E;248.F8.H0,G:904KMLF09Y8YS7COC7V2NBITUBET91KSI5KTM2DSUWE3QO2LFESXO4NMMI1KNUOV2V1DD18Q3VI3YDX9Y0CC75Q34X8FWQLE5W7L48FUB5WXBNT0", "hasFlower": 0, "hasSeason": 0}, {"id": "192", "q": "000,2,4,A,C,E.17.24,A.31,6,8,D.43,B.50,5,7,9,E<PERSON>62,C<PERSON>70,7,E.83,5,9,B.91,7,D.B0,2,4,A,C,E.C7.D0,2,5,9,C,E.E7.F4,A.G0,7,E;101,D.24,7,A.31,D.50,6,8,E.62,C.85,7,9.91,D.B1,3,B,D.D0,2,5,9,C,E.F4,7,A.G0,E;250,6,8,E.62,C.85,9.B1,3,B,D.D0,E.G0,E;350,E.85,9.B1,D.D0,E;450,E;550,E:OV1TQ4LHHL5KIWTQ5ODAHVMQ1WAI8AR8T1RBB39FB0X163L42OHZM4VZ7MX85OUS260FZ77K42RWLB09SSD7AXSVIWKQR3KIM80ZF2996X6F3U5T", "hasFlower": 0, "hasSeason": 0}, {"id": "193", "q": "000,2,4,6,8,A,C,E.20,2,5,7,9,C,E.40,3,5,7,9,B,E.61,3,5,7,9,B,D.80,2,5,7,9,C,E.A0,2,4,7,A,C,E.C0,2,4,6,8,A,C,E.E1,3,6,8,B,D.G4,7,A.H0,2,C,E.I4,6,8,A;100,2,5,9,C,E.17.30,E.43,5,9,B.63,B.77.82,C.90,E.A4,A.B2,7,C.C4,A.G4,A.I5,7,9;243,B.G4,A.I5,9;3I5,9;4I5,9;5I5,9:VJ828PVFTEQQTFBWJLDIPSGNN7X80W9FKIXMKK2BQULUREMTJHH8NRV4BU3CG9G5RQ7U74H49DBW3DAFXVXSC33T70OGOZANKHLJP4R95WDZ00LP", "hasFlower": 0, "hasSeason": 0}, {"id": "194", "q": "000,2,4,6,8,A,C,E.20,3,6,8,B,E.40,2,5,9,C,E.57.60,3,5,9,B,E.77.81,3,5,9,B,D.97.A0,2,4,A,C,E.B6,8.C0,2,C,E.D4,6,8,A.E0,2,C,E.F5,9.G0,2,7,C,E.I0,2,4,6,8,A,C,E;101,D.13,6,8,B.20,E.41,D.60,5,9,E.81,6,8,D.94,A.A0,7,E.B2,C.D0,5,9,E.F1,D.H0,2,7,C,E.I4,A:SZ1R01Z8DY4QZPBH2L12MMXERU1HLNLFYTJEZNFC00AK9SAR2U74SYPFBT9OHNMUBEHXL4MJD7RQPJDOECAQO0PBX8YODUK8Q48SAJX2KKNF", "hasFlower": 0, "hasSeason": 0}, {"id": "195", "q": "000,2,4,7,A,C,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.80,2,4,6,8,A,C,E.A0,2,4,6,8,A,C,E.C1,3,5,7,9,B,D.E1,3,5,9,B,D.G0,2,4,6,8,A,C,E;102,C.10,7,E.25,9.30,2,C,E.51,3,5,9,B,D.74,7,A.80,E.93,B.A7.B4,A.C1,6,8,D.E2,C.F5,9.G0,2,7,C,E:XTOQ0LARMS92DENART43B4THZE0MS670PHOLQQ732BMUU7IGB4G6CG1E93CHGJP2ZM16IBE7INLN5JRQ2SLSX5HDACJOINC4D056JTRA53DO", "hasFlower": 0, "hasSeason": 0}, {"id": "196", "q": "000,2,4,7,9,C,E,G.20,3,5,7,9,B,D,G.40,2,4,6,A,C,E,G.58.60,3,5,B,D,G.77,9.80,2,4,C,E,G.97,9.A1,3,5,B,D,F.B7,9.C2,4,C,E.D0,8,G.E2,5,B,E.F0,7,9,G.G2,4,C,E.H0,6,A,G.I2,4,C,E;101,3,D,F.17,9.34,6,A,C.58.63,D.81,4,C,F.A3,5,B,D.E2,8,E.G4,C.I2,E;234,C.G4,C:MZBI33R9JA9VSPHRSG7P8HJGC3YAKNPRJ56BCR9E2E6OZVOQM5EZDSSG9VMOB7JKQ2IG811IHV521MQ7KDBODETU887635QHYU6K1ZNTP2DI", "hasFlower": 0, "hasSeason": 0}, {"id": "197", "q": "001,3,B,D.20,2,4,A,C,E.40,2,4,6,8,A,C,E.64,6,8,A.70,E.82,4,6,8,A,C.90,E.A2,4,6,8,A,C.B0,E<PERSON>C4,6,8,A.E3,5,9,B.F0,E.G2,4,A,C.H0,E.I2,C;102,C.23,B.31,D.43,6,8,B.64,A.76,8.80,3,B,E.95,7,9.A1,D.B4,7,A.D4,A.F3,B.G1,D;231,D.43,B.64,A.76,8.97.A1,D.B4,7,A.D4,A.F3,B.G1,D;331,D.G1,D:JGUD7N8XB89UU0MRUVBQ319X8M612IIH67QJCNBPR8HJXNY4NIV9WYW3ZBMCGV2FLMP7FZQ4XRV1HE0CW46GE3G1LEAPPLLA6Y4W9RE7HD2YCI3JQ2", "hasFlower": 0, "hasSeason": 0}, {"id": "198", "q": "000,2,6,8,C,E.20,2,4,6,8,A,C,E.40,2,4,7,A,C,E.60,3,5,7,9,B,E.80,2,5,7,9,C,E.A0,2,6,8,C,E.C2,5,7,9,C.D0,E.E3,5,7,9,B.F0,E.G2,4,6,8,A,C.H0,E.I2,5,7,9,C;101,D.20,E.32,C.44,A.60,E.82,7,C.90,E.B2,7,C.D0,E.E4,A.G2,C.H0,E.I2,C;260,E.90,E.D0,E.I2,C;360,E.D0,E.I2,C:PB3TWA7V7IY3IME0EPNFMNXAPFT0LL34FALTWY0B91RUX7Z3E29QEKQ95RU1MRT1R9BFJJ442NVI58KPWWJ24K7ZAMUZKQQ0IVUN1Y8VLJ2YZB", "hasFlower": 0, "hasSeason": 0}, {"id": "199", "q": "000,3,5,7,9,B,E.20,2,4,A,C,E.36,8.40,2,4,A,C,E<PERSON>56,8.62,C.70,E.91,7,D.B1,D.D2,5,7,9,C.F0,2,4,6,8,A,C,E.H0,2,4,A,C,E.I7;100,3,6,8,B,E.22,4,A,C.30,6,8,E.42,C.70,E.91,D.D5,7,9.F1,3,5,7,9,B,D.H2,4,A,C;200,E.13,B.30,E.70,E.D6,8.F1,D.H2,4,A,C;370,E.D6,8.H2,C:N0MYN04TQ9211A3EPV2GKXED34V1DX9DQ59ZYPH3AOSFH3F0EK8S458IV8OY0VY5MKX6U22OPIU6PHQTH9QNDUZI5GO8ASMSIKNUAMFX4FE1", "hasFlower": 0, "hasSeason": 0}, {"id": "200", "q": "000,2,4,6,8,A,C,E,G,I.20,2,4,6,8,A,C,E,G,I.40,2,4,6,9,C,E,G,I.60,2,4,7,9,B,E,G,I.80,2,4,6,8,A,C,E,G,I.A0,2,4,6,8,A,C,E,G,I.C0,2,4,6,8,A,C,E,G,I.E0,3,5,7,9,B,D,F,I.G0,2,4,6,8,A,C,E,G,I.I0,2,5,7,9,B,D,G,I;104,E.39.70,I.92,6,C,G.B1,H.F9:E9EJ2JFB0K62OZO7RSFJYDPX9HTNVHZ0PHVLTOXF05798GT06OCIIBNCJT9GAI8MQMZ62V5DSFZDS5LIRAVGCUYDHQAQ7LY7USK6YQAGCL25", "hasFlower": 0, "hasSeason": 0}]