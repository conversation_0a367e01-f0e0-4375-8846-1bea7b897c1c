[{"id": "1", "q": "000,3,5,7,9,B,E.20,2,5,9,C,E.37.40,3,5,9,B,E.57.61,4,A,D.77.80,2,C,E.94,6,8,A.A2,C.B0,4,7,A,E.D0,2,5,9,C,E.E7;100,3,B,E.21,5,9,D.43,5,9,B.57.61,4,A,D.92,4,7,A,C.D1,5,9,D:6AVJZ78WWV6AAVHHW6IC5IZJV5W8JJH5JI8HJV2A8266775VAI65HA2C82CCCI7ZZ7C857ZZHI", "hasFlower": 0, "hasSeason": 0}, {"id": "2", "q": "001,3,6,9,B.21,5,7,B.40,3,9,C<PERSON>55,7.61,3,9,B.76.80,C.93,9.A0,5,7,C.C2,5,7,A;102,A.21,5,7,B.43,9.55,7.61,3,9,B.80,C.93,9.A0,5,7,C.C2,5,7,A;253,6,9.61,B.80,C.A6.C5,7:YVLUT9F37VWJLS8JFMP7MCAHACC8FASDJ3RW332YMG1TH278FJGP829R3U7RCM32RAD1", "hasFlower": 0, "hasSeason": 0}, {"id": "3", "q": "001,3,5,7,9,B.20,4,8,C.40,2,4,8,A,C.56.61,4,8,B.80,4,6,8,C.A0,3,5,7,9,C.C0,3,5,7,9,C.E0,4,8,C.G0,3,5,7,9,C;104,6,8.20,4,8,C.42,A.56.64,8.85,7.A3,9.B0,C.C6.F0,4,8,C.G6:0RBPR7RDW63P3IPC0P0BDRXWDVR6I3R03I0DBIC63BCW6WPW0VDXPIX76XC6IWBC7CX3XD7B", "hasFlower": 0, "hasSeason": 0}, {"id": "4", "q": "000,2,5,7,9,C,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,2,4,A,C,E.81,4,A,D.A0,2,4,6,8,A,C,E.C0,2,4,6,8,A,C,E.E1,4,6,8,A,D;100,E.12,6,8,C.37.43,B.64,A.94,A.B3,6,8,B.E6,8:9AG45TEHTTYRA5EJMB9CGEFWTHYCP8TEURFAGR4RZEMZL8GH3XX8JOPAOWHPP68BYL3EIUYIT6", "hasFlower": 0, "hasSeason": 0}, {"id": "5", "q": "001,3,5,7,9,B.21,3,5,7,9,B.40,3,5,7,9,C.61,3,5,7,9,B.80,2,5,7,A,C.A0,2,4,6,8,A,C.C1,3,5,7,9,B.E2,4,8,A;102,4,6,8,A.21,3,9,B.40,6,C.61,5,7,B.82,A.90,5,7,C.B6.C1,3,9,B.E3,9:15SWLFUI5VCHCI0KGCESFLOGHX3H3V92RA9I5MAJUWG8QA4EK68VY65I04QDYEGRAE12CHJDXVOM", "hasFlower": 0, "hasSeason": 0}, {"id": "6", "q": "001,3,6,8,B,D.20,4,6,8,A,E.32,C.40,4,7,A,E.60,2,4,6,8,A,C,E.80,2,5,9,C,E.A1,4,6,8,A,D.C1,3,5,7,9,B,D.E1,3,6,8,B,D.G0,2,4,6,8,A,C,E;127.33,B.61,D.85,9.A4,A.B1,D.D6,8.E3,B.F1,7,D.G5,9:MQAZW0CB2W1M18EEXTSX2DRHZJV4ORZR8ARDWTR6IYZ5RWHG5TLXYYBYXJ6OKQ4VYLCYK3GSH30I6TH6", "hasFlower": 0, "hasSeason": 0}, {"id": "7", "q": "000,2,4,6,8,A,C.20,2,5,7,A,C.40,2,4,8,A,C.62,4,6,8,A.70,C.83,5,7,9.A2,4,8,A.B0,C.C2,4,6,8,A.D0,C.E2,4,8,A;102,5,7,A.22,A.30,C<PERSON>44,8.52,A.66.74,8.A2,4,8,A.C0,2,5,7,A,C.E3,9;230,C.A3,9.C0,C:5LHBLNQ765U4K4ABUHOD6P9QL2EPN7R9XJRKDRELY4COERWDUJ7YRPENLNLYWR5U4N5YX7UNUP9AD2C9", "hasFlower": 0, "hasSeason": 0}, {"id": "8", "q": "002,4,6,8.20,2,4,6,8,A.40,2,4,6,8,A.61,3,5,7,9.80,2,4,6,8,A.A0,2,4,6,8,A.C1,3,5,7,9.E1,4,6,9;102,8.15.22,8.34,6.42,8.62,4,6,8.84,6.90,2,8,A.B2,8.C4,6.E1,5,9;202,8.25.42,8.B2,8.D5:2B3JF79JXWJFX9J93HWZEWUWXW3ZJF92O7OFUBZ6A7LZ327ZEFAA7JT3232HXZTX9AW67LXFU2U9", "hasFlower": 0, "hasSeason": 0}, {"id": "9", "q": "000,2,6,8,C,E.20,3,5,7,9,B,E.40,2,4,7,A,C,E.60,2,4,6,8,A,C,E.80,2,4,6,8,A,C,E.A0,2,5,7,9,C,E.C2,4,7,A,C.D0,E.E3,5,7,9,B;124,6,8,A.40,4,7,A,E.52,C.67.71,4,A,D.87.91,D.A6,8.D4,A;224,A.67.97.D4,A:Z6S6W4F7PT6F4DBWHZFXITIPHNIDCNR3ZMIK67BTSWPC7PHC4HQQ3RHDTWCT40BHXMDZS34BTC3CN740SFKN", "hasFlower": 0, "hasSeason": 0}, {"id": "10", "q": "000,C.12,4,6,8,A.20,C.32,4,6,8,A.40,C.56.61,B.73,5,7,9.80,C.92,A.A0,6,C<PERSON>B3,9.C0,6,C.E0,2,4,6,8,A,C;112,4,6,8,A.30,3,9,C.46.90,C.B0,6,C.D6.E2,4,8,A;213,5,7,9.30,C.B0,C.E3,9:WXZM1MXB1MTZEEDCDTHCHHZTXEZMEDR1BRHHZCCBDEDRZCXRDFBWFRCRFWTTBXXFFBWEF1TH", "hasFlower": 0, "hasSeason": 0}, {"id": "11", "q": "000,2,5,7,A,C.21,3,6,9,B.41,4,8,B.56.60,3,9,C.75,7.80,2,A,C.94,6,8.A0,C.B3,5,7,9.C1,B.D4,8.E1,B.F3,6,9.G0,C;100,2,5,7,A,C.31,B.44,8.56.75,7.80,C.A5,7.D1,4,8,B;231,B.56.76.A6:50SC85NOX6HN4P4Q3CGVMWJ4QFP6Z8T3TWGVL5AXOT9J5LB4D2KMAF9DSEH1T8K8WZ12WEB0", "hasFlower": 0, "hasSeason": 0}, {"id": "12", "q": "000,3,5,7,9,C.20,2,4,6,8,A,C.41,4,6,8,B.60,2,4,6,8,A,C.80,2,5,7,A,C.A0,2,6,A,C.B4,8.C0,2,A,C.D4,6,8.E0,2,A,C.F4,6,8.G1,B;103,9.24,8.41,B.54,8.72,A.A2,A.C4,8.D1,B.F4,8:XB7KKROBJB4KDMQVX2GEVMJ7VOG8RWJXB2RX4EKG6JV90CKLHJG0RHCLCQJKWDUCC896UTCT", "hasFlower": 0, "hasSeason": 0}, {"id": "13", "q": "001,3,5,7,9,B,D.20,3,5,7,9,B,E.40,2,7,C,E.54,A<PERSON>66,8.70,3,B,E.85,7,9.93,B.A0,5,7,9,E.B2,C.C0,4,7,A,E.E4,6,8,A;105,9.13,B.20,6,8,E.40,7,E.73,B.95,9.B0,7,E.D4,A;213,B.95,9.D4,A;3D4,A:0XP8CWS6PHYKJVR8HN4NBV74OYJCJX7Y80IYXJ4IK1JHJTTR6THX6HHB4VSXNS6S81NY0TVWYX0O", "hasFlower": 0, "hasSeason": 0}, {"id": "14", "q": "001,3,6,9,B.20,3,5,7,9,C.40,3,5,7,9,C.60,3,5,7,9,C.81,4,6,8,B.A0,2,4,6,8,A,C.C0,3,5,7,9,C.E0,2,4,6,8,A,C.G0,3,5,7,9,C;103,9.25,7.33,9.45,7.50,C.63,9.A2,A.C0,C.D5,7.E3,9.F5,7:G7U1FSSDZ84QGPZ7ZTFS1QDFQTZ1TPU14W7G8Z8WF4PPS48BZW77SFVWT1P1QVD8QUUDSPB7GF8Q", "hasFlower": 0, "hasSeason": 0}, {"id": "15", "q": "001,5,7,B.13,9.20,5,7,C.33,9.40,5,7,C.53,9.60,5,7,C.72,A.80,4,6,8,C.92,A.A0,C.B2,4,8,A.C0,6,C.D2,4,8,A.E0,C;101,5,7,B.20,4,6,8,C.43,6,9.50,C.65,7.70,C.91,B.B3,9.C1,6,B.D4,8;201,5,7,B.36.56.60,C.C6;301,B:1R2AF801AFFOL83JAND8F22RVS0D0DVLJ08SN3NCAV1V2JFRNO3O8ROFOOL0SA6RVDCLVD136D011RLSLAJ8", "hasFlower": 0, "hasSeason": 0}, {"id": "16", "q": "000,2,A,C.14,6,8.20,2,A,C.34,6,8.40,2,A,C.54,8.60,2,A,C.74,6,8.80,2,A,C.94,6,8.A0,2,A,C.B4,8.C1,B.D3,5,7,9.E0,C.F2,4,6,8,A.G0,C;100,C.12,6,A.31,4,8,B.51,4,8,B.75,7.95,7.B1,B<PERSON>C4,8.D6.E4,8.F1,6,B:PNZOH6D56JWHZYDDMOGZRM4UIGUL5FZH0YJ2D47MLZO448H72OIS0V91RFW954E4OZPIS8YNV1EOYM5IXX", "hasFlower": 0, "hasSeason": 0}, {"id": "17", "q": "000,2,4,7,A,C,E.21,6,8,D.33,B.40,6,8,E<PERSON>53,B.60,7,<PERSON><PERSON>72,C<PERSON>80,6,8,E.93,B.A1,6,8,D.B3,B.C0,6,8,E.D2,C.E0,6,8,E;100,E.27.33,B.50,3,B,E<PERSON>70,E.93,B.A6,8.C7.D2,C.E6,8;200,E.33,B.50,E.D2,C;350,E:IIOAPRAPATD059CPADLD8LOOIB50DFOIS5LR6J64TS0DL6B6JB6C8I4L06UB4LP4D00JJI5U9FRR", "hasFlower": 0, "hasSeason": 0}, {"id": "18", "q": "000,3,6,9,C.20,2,4,6,8,A,C.40,2,4,6,8,A,C.60,2,4,8,A,C.76.80,2,4,8,A,C.A0,3,5,7,9,C.C0,3,5,7,9,C.E0,2,5,7,A,C;126.40,3,5,7,9,C.63,9.93,9.B0,3,5,7,9,C.D6:C6UFXMHHGM910FZHPZQ649YP4CTFHQQ8I1CMCUS9MA4STG0TA7M444CCXYFQYT8YI7M9", "hasFlower": 0, "hasSeason": 0}, {"id": "19", "q": "000,4,6,A.20,2,4,6,8,A.40,5,A.53,7.60,5,A.73,7.90,2,4,6,8,A.B0,5,A.D0,2,4,6,8,A;100,5,A.20,4,6,A.40,5,A.53,7.60,A.73,7.90,2,8,A.A5.B0,A.D0,4,6,A;220,A.50,A.A0,A.D0,A:T0H803P22EYAQAJ4OC76CCVVYNHZ70M8MXTBNDT6BQH838PE4PJDLCT0KNH4VKZNOV4LXP", "hasFlower": 0, "hasSeason": 0}, {"id": "20", "q": "000,3,5,7,A.20,2,4,6,8,A.40,2,4,6,8,A.60,2,4,6,8,A.80,4,6,A.92,8.A0,5,A.B2,8.C0,4,6,A.D2,8.E0,5,A;100,A.14,6.21,9.34,6.40,A.52,5,8.60,A.90,5,A.A2,8.B0,A<PERSON>C4,6.D1,9.E5;221,9.60,A.90,A.D1,9:ZDUCJH9F17C5NLGBHVHGPJGG8YZF6083BV8YT23FMYF69A7UQHCM0QP1GDPLT8P9NCG852FD8FDAY9", "hasFlower": 0, "hasSeason": 0}, {"id": "21", "q": "000,3,6,8,B,E.22,4,7,A,C.41,4,7,A,D.62,4,6,8,A,C.70,E.83,6,8,B.A0,2,4,7,A,C,E.C2,4,6,8,A,C.E0,2,4,7,A,C,E;100,3,6,8,B,E.22,C.41,7,D.63,5,9,B.83,B.A1,D.B3,7,B.D2,C.E0,4,A,E;200,3,B,E.63,B;303,B:OE4BAVBYA4V9YAL8IO4VZKIOZOVRRERA9VIRKLYS8ZLSKKKI4BSZEYOAIZB8LO89EVKSYS4BIEZSE411AB9Y", "hasFlower": 0, "hasSeason": 0}, {"id": "22", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,2,4,8,A,C.56.60,2,4,8,A,C.76.81,3,9,B.95,7.A0,2,A,C<PERSON>B4,8.C0,2,6,A,<PERSON><PERSON>D4,8.E2,A<PERSON>F0,4,6,8,C.G2,A;112,A.25,7.43,9.55,7.60,C.B0,C<PERSON>C5,7.D3,9.F5,7.G2,A:SHQ8WTT916OQ92OS91QO567TA1WXHH5SS89O1HA1XF276XWT67SFA1T26S92H7W97TFXFQOHO7A6", "hasFlower": 0, "hasSeason": 0}, {"id": "23", "q": "000,3,5,7,9,C.22,4,6,8,A.40,2,4,6,8,A,C.60,2,5,7,A,C.80,2,4,6,8,A,C.A0,2,4,6,8,A,C.C0,2,5,7,A,C.E1,4,6,8,B.G2,4,6,8,A;114,8.35,7.42,A.50,6,C.70,6,C.82,A.A0,3,6,9,C.C0,6,C.E5,7.G4,8:GG2RSGBOE4PRMRSLFX28XZTPWNVFCW1BF2NNFZGNTVRBFSXLMTAAFN9WWCCWC1X21891O4TTNSBWMEMT", "hasFlower": 0, "hasSeason": 0}, {"id": "24", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.41,3,9,B.55,7.60,2,A,C.75,7.83,9.90,5,7,C.B1,3,9,B.C6.D0,2,4,8,A,C;104,8.11,6,B.33,9.52,6,A.60,C.83,9.90,5,7,C.C3,9.D0,C;216.33,9.56.83,9.95,7.C3,9;316.33,9.C3,9:T5ZM3DIYY0TEIEDI55EMI70DSSTNTEEXM7NM7XNE0YIYINXNZ30TZ5ZY7YN7ZZS37MSSSD35X0MDT5D0", "hasFlower": 0, "hasSeason": 0}, {"id": "25", "q": "001,5,7,B.13,9.20,6,C.32,4,8,A.50,2,4,6,8,A,C.70,2,4,6,8,A,C.90,2,4,8,A,C.A6.B2,4,8,A.C0,C.D2,4,6,8,A.E0,C;113,6,9.33,9.50,4,8,C.62,A.74,8.81,B.A4,8.B2,A.D3,5,7,9.E0,C:I89IGBUN7R6PTURVZ4KGVR6JE0ZN70RPC9IHHGFX81ICGFXPYKPJ4E89NFN01Y09F8H9TB9H", "hasFlower": 0, "hasSeason": 0}, {"id": "26", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,2,5,7,A,C.61,4,6,8,B.80,3,5,7,9,C.A1,3,5,7,9,B.C0,3,5,7,9,C.E0,4,6,8,C;103,9.10,C.24,8.30,C.64,6,8.84,8.96.A4,8.C0,C.D4,8.E0,C;210,C.64,8.84,8.E0,C:CV5JPT5IVJDTPFTW72IMK4CD5F3WVX7Q1VRCXJMU7E7P3TR22P2NEU2JPE2T7N31545CK3VVET7Q5P", "hasFlower": 0, "hasSeason": 0}, {"id": "27", "q": "001,3,5,7,9,B,D.20,2,4,7,A,C,E.40,2,5,7,9,C,E.60,2,4,7,A,C,E.81,3,5,9,B,D.97.A0,2,5,9,C,E.B7.C0,2,4,A,C,E.D7.E0,3,B,E;101,5,7,9,D.22,C.37.42,C.60,E<PERSON>72,C.95,9.A1,D.B7.C2,C.E0,E:PG42KXS2BSM2FOCI142VFD2RS0LGI2LB1FGHKHCK8RGOGX1D9IH11IGMKFFV9IRSKP0FR10HKI08", "hasFlower": 0, "hasSeason": 0}, {"id": "28", "q": "000,2,4,7,A,C,E.20,3,5,9,B,E.41,3,5,7,9,B,D.61,3,5,7,9,B,D.80,2,4,6,8,A,C,E.A0,2,4,6,8,A,C,E.C0,2,4,6,8,A,C,E.E1,3,6,8,B,D.G1,3,5,7,9,B,D;100,E.35,9.41,D.57.74,6,8,A.A4,6,8,A.C7.D1,D:YXDRK5EAKM6PX8YMRDY8CRKF8FPAA8OA9QEV65AD29YQRDQ5QVT5YAEO9N8PE9PKR26FC66NLRTK8FY6KL", "hasFlower": 0, "hasSeason": 0}, {"id": "29", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,2,4,A,C,E.60,2,4,7,A,C,E.80,2,5,9,C,E.97.A0,2,4,A,C,E.C0,3,B,E.D5,9.E0,2,7,C,E;110,6,8,E.31,D.43,B.50,E<PERSON>62,C<PERSON>70,E<PERSON>85,9.91,D.B3,B.D0,5,9,E.E7:I0RNXHQH04GPA4Y23A3DID4XNXAHVH54J34ZXDOAJI5SVHGXJIHCXOAJVO3PVRZUSQC22ODA42CCUY", "hasFlower": 0, "hasSeason": 0}, {"id": "30", "q": "000,2,4,6,8,A,C.20,2,4,8,A,C.36.40,C.52,5,7,A.60,C.72,4,6,8,A.80,C.92,4,8,A.A6.B0,C.C4,6,8.D0,2,A,C.E4,6,8;101,3,9,B.20,4,8,C.40,C.55,7.70,2,A,C.93,9.A6.B0,C.D0,4,6,8,C:7J7I677BD5TV3Y2UJ4GR6IFU4IYJNVBJ5J44TD254YGGD3T622IIN56NJIFHT4NR55DTGYTH", "hasFlower": 0, "hasSeason": 0}, {"id": "31", "q": "000,2,5,7,A,C.20,3,5,7,9,C<PERSON>41,5,7,B<PERSON>53,9.65,7.70,2,A,C<PERSON>85,7.92,A.A0,5,7,C<PERSON>B2,<PERSON><PERSON>C4,6,8.D0,C<PERSON>E2,4,6,8,A;101,5,7,B.20,4,8,C.53,9.65,7.95,7.A0,<PERSON>.B2,A<PERSON>C4,8.D0,C;224,8.53,9.66.96:CRR69ZZYW477964UYZXC9U9967A4R4RU964WCCXW64WC7YXZZXYXZ7UAXYRCAARUUYWW76AA", "hasFlower": 0, "hasSeason": 0}, {"id": "32", "q": "001,3,7,9.20,2,5,8,A.43,5,7.50,A.62,4,6,8.92,4,6,8.A0,A<PERSON>B3,5,7.C0,<PERSON><PERSON>D2,5,8;101,9.22,8.43,5,7.50,A.62,4,6,8.92,4,6,8.A0,A.B3,5,7.D2,8;201,9.43,5,7.64,6.94,6.B3,5,7;301,9.64,6.94,6:CGE9FN77IEBCDCZ7D9B913J55UY7GZ6FPCQI63A4E6GJG6GU4BU4NA7BU1ZY9QE6EZ4E7GPBB6", "hasFlower": 0, "hasSeason": 0}, {"id": "33", "q": "000,3,6,9,C.20,3,5,7,9,C.40,2,5,7,A,C.60,2,4,8,A,C.80,2,5,7,A,C.A0,2,4,6,8,A,C.C0,2,5,7,A,C;106.26.42,5,7,A.61,B.81,B.95,7.A3,9.B1,6,B;206.42,5,7,A.61,B.95,7.B1,B:JVJK4HZI26U1ZBVL4SKLC2JB6JII4ZJVCH4B2AS61SCELCBU4BCV2AZESI6AKKAJIIBL4C", "hasFlower": 0, "hasSeason": 0}, {"id": "34", "q": "000,2,6,8,C,E.14,A.21,D.33,6,8,B.40,E.52,4,7,A,<PERSON><PERSON>60,E.72,4,7,A,C.90,2,4,A,C,E.A7.B0,4,A,E.C2,6,8,C.E1,3,5,9,B,D;106,8.14,A.33,7,B.60,4,7,A,E.83,B.90,E.B4,7,A.E2,4,A,C;206,8.33,7,B.B7;333,B:UZ6Y7B8YCGC2T2ACY6YG6BGAZUB6OCR73BA8OG38N6ZR373RZ2RABGWBORZRNZCNTWOW76GW8AN33A2C", "hasFlower": 0, "hasSeason": 0}, {"id": "35", "q": "001,3,5,7,9,B,<PERSON>.20,6,8,E<PERSON>32,<PERSON><PERSON>44,7,A<PERSON>50,E<PERSON>62,4,7,A,<PERSON><PERSON>70,E.82,4,6,8,A,C.90,E.A2,4,6,8,A,C.B0,E<PERSON>C4,7,A.D1,<PERSON><PERSON>E3,5,9,B.F0,E.G2,5,9,C;101,7,D.27.44,7,A.50,E<PERSON>62,C.70,E.85,7,9.90,E<PERSON>A2,5,9,C.D1,4,A,D.G5,9:ZLOGL3WGSTSJZT8SL1CEC1J3L1ZAZWC881CRW4MRF41WNFW8ATGTWSZFZNEPOML34OFPA4A3RJGO1LEJARAE", "hasFlower": 0, "hasSeason": 0}, {"id": "36", "q": "000,2,4,6,8,A,C.20,2,A,C.34,6,8.40,2,A,C.56.60,2,4,8,A,C.80,5,7,C.92,A.A0,6,C.B2,4,8,A.C0,6,C<PERSON>D2,<PERSON>.E0,4,8,C;100,3,5,7,9,C.20,2,A,C.35,7.51,B.63,9.85,7.A1,B.B3,9.C1,6,B:9KZJH14CJ9136YY94D7YBQPNDP1KIHIJJ7BEO5KC92H1ZHGQGTV4FO6249V93ZK7N7TY5EFZ", "hasFlower": 0, "hasSeason": 0}, {"id": "37", "q": "000,4,8,C.12,A.20,C.32,5,7,A.40,C<PERSON>53,5,7,9.61,B<PERSON>73,9.85,7.90,C<PERSON>A2,5,7,A.B0,C.C2,4,8,A;104,8.12,A.20,C.32,6,A.40,C.54,6,8.61,B.73,9.86.90,C.A2,6,A.B0,C.C2,A;212,A.31,B.46.96.A1,B.C2,A;331,B.A1,B:U1VM3M7O2F5T95W9AGH8QVWCDE432G3EKCT17MKHMS5L5FNQS803XE0RPDP04RT0DUOLDTAIVXEVIN", "hasFlower": 0, "hasSeason": 0}, {"id": "38", "q": "002,4,6,8,A,C.10,E.22,4,6,8,A,C.40,3,5,7,9,B,E.60,2,4,6,8,A,C,E.80,3,5,7,9,B,E.A0,2,4,6,8,A,C,E.C1,3,5,7,9,B,D.E0,2,6,8,C,E;107.11,D.23,7,B.40,4,A,E.73,5,9,B.A4,A.D7.E1,D:VYINZR84OOHF30Y8HFU48YVU9N0C0IROC9NCUVV8UURY8I0N9SUNC4IFGZH66H4RGFFO9N8EFE3S", "hasFlower": 0, "hasSeason": 0}, {"id": "39", "q": "000,2,5,7,9,C,E.20,2,4,6,8,A,C,E.40,4,6,8,A,E.60,2,4,6,8,A,C,E.81,3,5,9,B,D.97.A0,3,5,9,B,E.C0,2,4,7,A,C,E.E0,3,5,7,9,B,E;102,C.15,9.20,2,C,E.35,7,9.62,C.82,C.C2,7,<PERSON>.D0,E<PERSON>E5,9:8G20NSG76E2YWL7SLY75S82V6ZJZA4880F6YV4G4YE7PJLPPOSN6N04A4NIFTPK52G0TKO2L2HHWI4", "hasFlower": 0, "hasSeason": 0}, {"id": "40", "q": "000,2,6,A,C.14,8.21,B.34,6,8.40,2,A,C.56.61,3,9,B.75,7.81,3,9,B.95,7.A0,2,A,C.B4,6,8.C0,C.D2,4,8,A.E0,6,C;102,6,A.36.41,B.61,3,6,9,B.81,5,7,B.A1,5,7,B.D3,9.E6;236.A5,7.D3,9.E6;3A6.D3,9:R9X98QA96A24VQFOBRRX59RH67J4H005F778DV49FJF2AQ6Q6VZ4D594XZ6BA57RFROFVAA4X565", "hasFlower": 0, "hasSeason": 0}, {"id": "41", "q": "000,2,4,8,A,C.20,4,6,8,C.40,2,4,8,A,C.56.60,4,8,C.72,A<PERSON>85,7.90,C.A3,5,7,9.B0,C.C3,5,7,9.D0,C.E2,A;114,8.34,8.40,2,A,C.55,7.60,C.72,A.90,6,C.A3,9.B0,5,7,C.E2,A;242,A.55,7.96.E2,A;355,7:ZTMG5ZTUYZUMW7XM1TRX6GT1UU0G7R0UXY0WXZMYG16I61TWGZWY5071XWYIMM0V7XUGW0YV61ZT", "hasFlower": 0, "hasSeason": 0}, {"id": "42", "q": "001,5,7,9,D.20,3,6,8,B,E.40,2,4,A,C,E.56,8.61,3,B,D.75,7,9.80,E.94,A.A0,2,6,8,C,E.C1,3,5,7,9,B,D.E0,2,5,7,9,C,E.G0,4,6,8,A,E;106,8.23,6,8,B.53,B.61,6,8,D.94,A.A1,6,8,D.C3,5,9,B.E7.G5,9;227.67.A7.C4,A.G5,9:R1UIROP3K404G1GF94S1U21SPO12RU4CPUVSKP4V3I09CVVG4PKVGCP9KRVGGUR09O033FOCC2SSOUORS12C2299", "hasFlower": 0, "hasSeason": 0}, {"id": "43", "q": "000,3,5,7,9,B,E.20,2,7,C,E.35,9.40,2,7,C,E.54,<PERSON><PERSON>60,6,8,E<PERSON>72,C.80,5,7,9,E<PERSON>92,<PERSON>.A6,8.B0,3,B,<PERSON><PERSON>C5,9.D0,2,7,C,E.E5,9.F0,2,7,C,E.G4,A;100,5,7,9,E.32,5,9,C.47.67.85,7,9.B3,<PERSON><PERSON>C5,9.D2,7,<PERSON><PERSON>E5,9;206,8.57.C5,9:CTW9TG2NS0GWZIA010NCD2W89TIZW1FIS9FSD26IZLIN0PZ28CD1TLP06IX2FDF6AX9NOOGZGN260CNFF1SZ", "hasFlower": 0, "hasSeason": 0}, {"id": "44", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.41,5,7,B.60,3,5,7,9,C.80,3,5,7,9,C.A0,3,6,9,C.C0,2,5,7,A,C.E0,2,4,6,8,A,C.G0,2,4,8,A,C;106.13,9.25,7.60,C.73,9.80,C.A3,9.B0,C.D0,C.E3,6,9.G3,9:U9L1YBF2OK86CC12FT789WNBKKFI9LNBN7BILN9WK8A168NTSA7SUO8AAKLWNWFW51WY7L5K8L66", "hasFlower": 0, "hasSeason": 0}, {"id": "45", "q": "000,3,5,7,9,C.20,2,4,6,8,A,C.40,2,4,6,8,A,C.60,2,4,8,A,C.76.80,2,4,8,A,C.96.A0,3,9,C.C1,3,5,7,9,B.E1,3,5,7,9,B.G1,3,5,7,9,B;110,3,9,C.40,2,A,C.62,A.75,7.D2,A.G3,9:PSOGM4S8IM6HYS7L29EBFF7WT9LZUGAOKU9W0WVQWHVINYTP9R2K90SXBOE6AZ1R9XNO41Q8", "hasFlower": 0, "hasSeason": 0}, {"id": "46", "q": "001,3,5,7,9,B,D.22,4,6,8,A,C.30,E.43,6,8,B.50,E<PERSON>62,6,8,C.70,4,A,E.82,6,8,C.94,A.A0,2,6,8,C,E.C0,3,5,7,9,B,E.E0,2,4,7,A,C,E.G1,3,5,7,9,B,D;104,A.12,C.33,B.60,7,E.86,8.A7.B0,E<PERSON>C3,B.E1,3,B,D.G1,D:IRAIVWNK4SNRKBBUBN7DITIFO8D9B7C0SWULOKO292SLUCF0VKTOFN07O29479N0B0UBA044ROVFI2NRS8VI", "hasFlower": 0, "hasSeason": 0}, {"id": "47", "q": "000,2,4,6,8,A,C,E.20,5,7,9,E.33,<PERSON><PERSON>46,8.50,3,B,E.70,2,6,8,C,E.90,2,4,A,C,E.A6,8.B2,4,A,C.C6,8.D0,E.E2,4,6,8,A,C;100,2,5,9,C,E.17.25,9.37.50,E.71,6,8,D.91,4,A,D.A6,8.B2,C.C7.E3,6,8,B;202,C.17.50,E.91,D.D7;350,E:VBZUZOSXB43OAMA8E2M52Y1UU2ARF7IX32WECFKXVI3CDAWZYDIEMXIC18G8O838U4A9OARO57OBGCRK9SZ8BMRE", "hasFlower": 0, "hasSeason": 0}, {"id": "48", "q": "000,2,4,8,A,C.16.20,2,4,8,A,C.40,2,4,6,8,A,C.61,3,5,7,9,B.80,2,5,7,A,C.A1,3,5,7,9,B.C0,2,4,6,8,A,C.E1,4,8,B.F6.G0,2,4,8,A,C;112,4,8,A.40,4,8,C.56.64,8.81,B.B4,8.C6.D4,8.F1,5,7,B:ICZC0MC0MZM9IZ2C29QRC9SS0G0MI2QGRJQIMWQIRZZ9SJG92J2JRWJCWRGZIWGWGSJM92QSQWSR00", "hasFlower": 0, "hasSeason": 0}, {"id": "49", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,2,4,6,8,A,C.60,2,5,7,A,C.80,2,4,6,8,A,C.A0,2,4,8,A,C.C2,4,8,A.D0,6,C<PERSON>E3,9.F0,5,7,C.G3,9;101,5,7,B.13,9.33,9.45,7.50,C.62,A.70,C.83,5,7,9.A0,2,A,C.C3,9.D6.E3,9.G3,9:L5L4S5QEQXX5RIML5QIRYSSMLY34IGAXSAMSGRG3M5YM43AAG5SIQL44AIEY3GUELQIY34XUEQGXEYAMX3RRER", "hasFlower": 0, "hasSeason": 0}, {"id": "50", "q": "000,2,4,7,9,C,E,G.20,2,4,6,A,C,E,G.38.40,3,5,B,D,G.57,9.60,3,5,B,D,G.77,9.80,2,4,C,E,G.97,9.A0,2,4,C,E,G.B6,A.C0,3,8,D,G.D5,B.E0,3,7,9,D,G.F5,B.G0,2,7,9,E,G;107,9.11,F.34,C.48.55,B.70,G.91,F.D3,8,D.F7,9.G1,F:5NXUVTWZQO5QJTHIRHT7TYVEBTENWVKOMGUVJQL81MZ9098UHGXJGFBAHEIURTG4QF41XAX00I7JPRXXY0KLPJIJRE", "hasFlower": 0, "hasSeason": 0}, {"id": "51", "q": "001,4,8,B.20,2,4,6,8,A,C.41,4,6,8,B.64,6,8.71,B.86.94,8.A0,C.B4,6,8.C0,2,A,C.D4,6,8.E0,C;101,4,8,B.20,2,5,7,A,C.44,6,8.71,B.A0,4,8,C.B6.C0,C.D5,7;220,5,7,C.45,7.A0,C.D5,7;325,7.D5,7;425,7.D5,7:7VW4CH2CE2WOO93P1ME1BCGYN54HGWJXJNVNZMZVDNFPYILPMKZEMQXVM9F3DDGMBDZC4GE54QKIW7PL", "hasFlower": 0, "hasSeason": 0}, {"id": "52", "q": "001,3,5,7,9,B.20,3,5,7,9,C.41,3,5,7,9,B.60,2,4,6,8,A,C.81,3,6,9,B.A0,2,5,7,A,C.C1,5,7,B.D3,9.E1,B.F3,5,7,9.G0,C;103,5,7,9.23,6,9.41,B.55,7.63,9.83,6,9.C5,7.D1,B.F3,6,9;204,8.41,B.63,9.86.D1,B;304,8:16YM939VILIGMG01SJ55U20TAAOLL61Y6TRJSGM2I0MRS3JJV1A33ILILMSO0S765LTRS72IGU23TMAG53GR", "hasFlower": 0, "hasSeason": 0}, {"id": "53", "q": "000,2,4,6,8,A,C.22,4,8,A.40,3,5,7,9,C.63,5,7,9.71,B.84,6,8.A3,5,7,9.B0,C.C2,5,7,A.E0,2,4,8,A,C;100,3,5,7,9,C.23,9.40,3,5,7,9,C.65,7.71,B.85,7.A5,7.B0,C<PERSON>C5,7.E0,2,4,8,A,C;223,9.55,7.A5,7:2CHJULBP2O5OQPQL8C0TC4HJJTPOC4P528JTUP00DTO8HLD5ULHO0J5T4U8CHJQLL02T4C2P0DDQ2BHO", "hasFlower": 0, "hasSeason": 0}, {"id": "54", "q": "000,2,4,6,8,A,C.22,4,6,8,A.40,2,5,7,A,C.60,2,4,6,8,A,C.80,3,5,7,9,C.A0,2,5,7,A,C.C0,3,5,7,9,C.E1,4,6,8,B.G0,2,4,8,A,C;104,8.23,6,9.45,7.50,C.73,5,7,9.80,C.A2,5,7,A.C0,4,6,8,C.E4,8;245,7.74,8.B5,7:XS3GE2LSIAF6ZCARTFZA3XEK1RDLJ7WJ0WFI70UGVELRI6YNUJKKDVDFKYP4WDO3N4CTPA0UEI02LWBB1JOU3R", "hasFlower": 0, "hasSeason": 0}, {"id": "55", "q": "000,3,6,8,B,E.20,2,5,9,C,E.41,3,6,8,B,D.61,6,8,D.74,A<PERSON>80,6,8,E<PERSON>92,<PERSON><PERSON>A0,7,E.B2,4,A,<PERSON><PERSON>C0,E<PERSON>D2,5,9,<PERSON><PERSON>E0,7,E;100,E.22,5,9,C.46,8.51,D.67.74,A.86,8.90,2,C,E.B0,E.C2,C<PERSON>D5,9;222,C.51,D.67.C2,C;351,D:9I5H3N6IXSWIME93S3Y5EXES939J1WK90W6Q6WG671GVM7YKMBV0E97KMIKSJ7YBQ7HXXNV71VNN6Y61", "hasFlower": 0, "hasSeason": 0}, {"id": "56", "q": "001,3,5,7,9,B.20,2,4,6,8,A,C.41,3,5,7,9,B.60,2,5,7,A,C.80,3,5,7,9,C.A2,4,8,A.B0,C.C2,5,7,A.E0,2,4,6,8,A,C.G0,2,5,7,A,C;103,5,7,9.21,B.33,9.41,5,7,B.61,5,7,B.B1,B.D5,7.E3,9.F1,B.G5,7;205,7.31,B;305,7;405,7:DP8EMMGO5CX9LDCBBP4CBBGUDKK8OM5POUDMPDKPMOTKUGPLRRGGB48TO498LX4EGMB9U84CRDLTLR48R99OLTR9", "hasFlower": 0, "hasSeason": 0}, {"id": "57", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.41,3,5,7,9,B,D.60,2,4,7,A,C,E.80,3,5,7,9,B,E.A2,4,6,8,A,C.B0,E.C3,5,7,9,B.D1,D.E3,5,9,B.F0,7,E.G2,4,A,C;102,C.10,6,8,E.32,C.47.54,A.A2,C.B5,9.G3,B:MASLTEDD5WN959ALNSSB50LDAFW0EDZLFZ75B7WT99ESW7MBNZD470WTATA4MZWN9EM9FDNEN0LABFEL", "hasFlower": 0, "hasSeason": 0}, {"id": "58", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.42,4,6,8,A,C.60,2,4,6,8,A,C,E.80,2,4,7,A,C,E.A1,3,5,7,9,B,D.C0,3,B,E.D6,8.E1,3,B,D;101,4,A,D.23,B.42,6,8,C.60,3,B,E.80,E.93,B.C0,E.D3,B.E1,D;263,B.70,E.93,B.E1,D:Z9G2QUYWCW2Q0VZ0UWFZOG09C2CBFZOUCYB9WBZYG2WUNOCNYYUFGVG99NUZ0V2WQC9BGBVFFVB2QF00VOOOYN", "hasFlower": 0, "hasSeason": 0}, {"id": "59", "q": "000,3,5,7,9,C.20,2,5,7,A,C.40,2,4,6,8,A,C.60,2,4,6,8,A,C.80,2,4,6,8,A,C.A0,3,5,7,9,C.C0,2,5,7,A,C.E1,3,6,9,B;100,C.15,7.20,C.42,4,8,A.60,2,4,8,A,C.80,2,4,8,A,C.A5,7.C0,C.E1,B:RVH9X9V6Z6FN6OHF2MZH2VNRXMR9HZZNFVX66RZ9NO2ONMBBO22OMBXOBZ6RVRNHFFH9XFX9VBM2BM", "hasFlower": 0, "hasSeason": 0}, {"id": "60", "q": "000,2,6,8,C,E.14,A.20,6,8,E.32,4,A,C.40,6,8,E.52,C.60,4,6,8,A,E.72,C.80,4,6,8,A,E.A2,5,7,9,C.B0,E.C2,4,6,8,A,<PERSON><PERSON>D0,E.E2,4,7,A,C;110,E.24,A.42,C.50,7,E.63,5,9,B.71,D.95,9.A7.B2,C<PERSON>D0,4,A,E;263,B.D0,E:7V27WHYUY8ISD5H9I5A7S82Y4JKJG8DRGJAR920UIQ4Y3K9Y9HRBHBG8I3BGBV99VVH0744GGHQR2VV8YW8J", "hasFlower": 0, "hasSeason": 0}, {"id": "61", "q": "001,3,6,9,B.20,2,4,8,A,C.40,2,4,6,8,A,C.63,6,9.71,B.83,6,9.A1,3,6,9,B.C2,4,8,A.D0,C.E2,5,7,A;101,B.20,2,4,8,A,C.41,3,5,7,9,B.63,9.93,9.B3,9.D0,2,A,C;222,4,8,A.46.63,9.93,9.D2,A:KH3VNSFXFJNK5LNQJNRMGASOANOOHP93HMY3LPMV99JA76QXD7B3JYVV1R60NZGHA1BOZCM9CD50", "hasFlower": 0, "hasSeason": 0}, {"id": "62", "q": "000,2,4,6,8,A,C.20,2,5,7,A,C.40,3,5,7,9,C.60,2,4,6,8,A,C.80,2,4,6,8,A,C.A0,2,5,7,A,C.C0,3,5,7,9,C.E0,2,6,A,C.G0,3,5,7,9,C;101,4,6,8,B.21,5,7,B.46.62,4,8,A.83,9.C5,7.E0,2,6,A,C.G3,6,9;216.36.E6.G6:20Q82XBTG4JTCGW85CXLT0BQC8EXTB45TFQGL258EQ2224D4QCXX8X5J5FQBJWL0EB08JB0CLGL5LEJWDJT0CW", "hasFlower": 0, "hasSeason": 0}, {"id": "63", "q": "000,3,5,7,9,B,E.21,4,7,A,D.41,3,6,8,B,D.60,2,6,8,C,E.74,A.80,6,8,E.A3,5,9,B.B0,<PERSON><PERSON>C3,B.D1,6,8,<PERSON><PERSON>E3,<PERSON><PERSON>F1,7,D.G3,B;100,4,7,A,E.24,A.31,7,D.43,B.51,6,8,D.70,4,6,8,A,E.A4,A.D2,6,8,C.F3,B;204,A.24,A.37.41,D.56,8;324,A.47;447:8QDP0WIPPWWX0Z7U3N5WU0XGN97MKZ70P8BJN7GB0X9BW6KXRSPP7JU5KRJDW0N868D47SRBMRI65DJ4DUJDJ3UUKQ56", "hasFlower": 0, "hasSeason": 0}, {"id": "64", "q": "000,4,6,8,C.21,3,5,7,9,B.40,6,C.52,A.60,4,6,8,C.80,3,9,C.96.A0,2,A,C.B6.C0,3,9,C.D5,7.E0,3,9,C;100,5,7,C.22,4,6,8,A.56.60,4,8,C.90,C.A6.D3,5,7,9.E0,C;205,7.23,5,7,9.56.60,C.90,C.A6.D3,5,7,9.E0,C;305,7.24,6,8.D4,6,8;426.D6:LOHXYQLUOL882FNI5LTNIY2U57H4UT8NTXLQI5RXZTHN2IOTRPYHTY2F4YOZFD2UPQXLQOPIFPRN78OR5HYRHDN2RIUU", "hasFlower": 0, "hasSeason": 0}, {"id": "65", "q": "000,2,4,6,8,A,C,E.20,3,6,8,B,E.40,2,5,7,9,C,E.60,2,5,9,C,E.80,2,4,A,C,E.A1,4,6,8,A,D.C0,2,4,7,A,C,E.E0,2,4,6,8,A,C,E;101,5,9,D.20,3,6,8,B,E.50,5,9,E.62,C.A5,9.C2,C.D0,4,7,A,E;226,8.D4,A;327:T5LWDFMWMQZWW75FPTPQTMBHDYZX5FR8QWR8QRPQTLTPP8YWTLD8SP299HQA86LAXSB65ZLZSF55LDR728SM", "hasFlower": 0, "hasSeason": 0}, {"id": "66", "q": "000,2,4,7,A,C,E.20,4,6,8,A,E.32,C.40,4,6,8,A,E.60,3,6,8,B,E.81,3,5,7,9,B,D.A0,3,5,9,B,E.B7.C1,3,5,9,B,D.E0,2,7,C,E;101,4,A,D.20,4,6,8,A,E.32,C.44,6,8,A.76,8.81,D.B4,6,8,A.C1,D.E0,2,C,E;201,D.26,8.33,B.76,8.D1,D:8XOO6Q3M8VGFIGDXMDS6GG3NMN8DQV8536DI3FN3FXDPMPI1VFFDNVW1O55GP1OMVMQ6SQWP13S66SXVWF58XXOWGI8O", "hasFlower": 0, "hasSeason": 0}, {"id": "67", "q": "000,2,4,6,8,A,C,E.20,2,5,9,C,E.40,2,4,7,A,C,E.62,5,9,C.70,7,E.82,4,A,C.96,8.A0,2,4,A,C,E.B7.C3,5,9,B.D0,E.E3,6,8,B;103,5,9,B.20,E.40,4,A,E.62,5,9,C.70,7,E.92,4,6,8,A,C.B4,<PERSON><PERSON>D0,E<PERSON>E3,<PERSON>;204,A.65,9.95,9:VIJ38YNXG21X33ITXNTATGR8G3SGN5J2RINXY2YAJ8385GATNVYSJ1155TV1JST55YVG183ASVNJR2IX1VYXR8", "hasFlower": 0, "hasSeason": 0}, {"id": "68", "q": "000,4,6,8,A,E.12,C.25,9.30,2,7,<PERSON>,<PERSON><PERSON>44,A<PERSON>51,6,8,<PERSON><PERSON>63,<PERSON><PERSON>70,6,8,E<PERSON>83,B.90,7,<PERSON><PERSON>A2,C.B0,4,A,E.C2,6,8,C.D4,A.E0,2,6,8,C,E.G0,2,4,6,8,A,C,E;106,8.12,C.32,C.56,8.87.A2,C.C6,8.D3,B.F6,8.G0,2,4,A,C,E;256,8.87.C6,8.F6,8.G1,3,B,D:NO9U3NBNACJH9MKO9IOHN133UD1ZKOUT2TWTMIFEAXXNKEXNWCJU28FU1F13HZDFSS1UBFKBABSBC8VCHSCA19VFTBXC", "hasFlower": 0, "hasSeason": 0}, {"id": "69", "q": "000,2,4,6,8,A,C,E.21,4,7,A,D.40,2,4,6,8,A,C,E.61,4,6,8,A,D.80,2,5,7,9,C,E.A0,2,4,6,8,A,C,E.C1,4,6,8,A,D.E0,2,4,6,8,A,C,E;103,B.31,D.44,A.51,6,8,D.64,A.77.95,9.A0,3,B,E.B5,9.C1,D.E0,4,6,8,A,E;231,D.77.A3,B.C1,D.E5,9:NTHE00868KVKHNTE3TN1DKNNP6HA22TS6WADARQSWV2Q0EP3VVBDVBQB2W238DERKAHD06KTTVN102S6WEEDWK8S3QWB60", "hasFlower": 0, "hasSeason": 0}, {"id": "70", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,2,4,8,A,C.61,3,5,7,9,B.81,3,6,9,B.A1,3,5,7,9,B.C0,3,9,C.E2,4,6,8,A.F0,C.G2,5,7,A;100,2,6,A,C.20,2,4,8,A,C.40,2,4,8,A,C.64,6,8.72,A.93,9.D3,9.F0,2,5,7,A,C;201,B.23,9.31,B.43,9.D3,9;333,9:839VTQKR7BHBVV1TFV7VRC5B9KOH9UMOCKA798HCQ3H9AFUMR9A1FCRCUF3K7M8VA35MTTUC71HQ8MMR37UBT8H8FU3TFRQ1", "hasFlower": 0, "hasSeason": 0}, {"id": "71", "q": "001,4,6,8,A,D.20,3,5,7,9,B,E.40,2,5,7,9,C,E.62,4,6,8,A,C.81,3,6,8,B,D.A1,3,6,8,B,D.C1,3,5,7,9,B,D.E0,2,4,7,A,C,E.G0,4,6,8,A,E;105,9.23,7,B.30,5,9,E.67.86,8.B7.C1,3,B,D.E0,E.F7;227.C1,D.F7;327.F7:RBI6PGPBUXDX2WIJO29F22GVRE2MROOMS6EOVDWZK3S43NVFKHB4J07EBUH0PPNR4EH42ZJ4VHQ47VTV9JQT", "hasFlower": 0, "hasSeason": 0}, {"id": "72", "q": "000,2,4,6,8,A,C,E.20,2,5,7,9,C,E.42,5,9,C.50,E.64,6,8,A.80,2,6,8,C,E.A0,2,5,7,9,C,E.C7.D1,4,A,<PERSON><PERSON>E6,8.F2,C<PERSON>G0,4,6,8,A,<PERSON><PERSON>H2,C.I6,8;100,5,7,9,E.12,C.25,7,9.42,C.67.80,E.97.A5,9.D7.F2,<PERSON><PERSON>G0,E.H2,6,8,C;200,E.15,9.A5,9.H2,C;300,E.15,9.A5,9;415,9:PJPY7CRXT5YV15HDAUMUCJ5J4NNAZ4PQM1U1XXVKVRVMZ8M68U4Y99NKX6Z8TKP5Y1DR7CJ8QAYRAHKUPHYZCU1Q91X9JP4NJHQX", "hasFlower": 0, "hasSeason": 0}, {"id": "73", "q": "000,2,4,6,8,A,C.20,2,5,7,A,C.42,6,A.50,4,8,C.62,6,A.70,4,8,C.82,6,A.94,8.A0,2,6,A,C.C1,3,5,7,9,B.E1,B;100,2,A,C.22,5,7,A.42,6,A.63,5,7,9.85,7.93,9.B1,6,B.C3,9.E1,B;222,6,A.46.B6;346.B6:W8DEERRDLH88NUN9E5EO9HR95OA9R9C0DWDOEL89F0R5FUHWL0COXO80LFRHWFL0A55FFO50E8LUXU", "hasFlower": 0, "hasSeason": 0}, {"id": "74", "q": "000,2,5,7,9,B,E,G.21,3,5,8,B,D,F.40,3,5,7,9,B,D,G.60,2,4,6,A,C,E,G.78.81,3,5,B,D,F.97,9.A0,2,5,B,E,G.B7,9.C0,2,4,C,E,G.D6,8,A.E0,3,D,G.F5,8,B.G0,2,E,G;108.24,C.43,D.65,B.73,D.96,A.C4,8,C.F5,B:V2P9B3QL3LL4BAVX14H9E19PKYHGK8AG2PEKGRXHL3SP9YBPQYK4AVBY8A81YVAAE4R8BGSGY3GHKLLKBEP1", "hasFlower": 0, "hasSeason": 0}, {"id": "75", "q": "002,5,7,9,C.10,E.23,5,7,9,B.30,E.42,4,7,A,C.61,3,5,7,9,B,D.80,4,6,8,A,E.A1,4,7,A,D.C1,3,6,8,B,D.E0,2,5,7,9,C,E.G0,5,9,E;105,7,9.10,E.27.43,B.64,A.80,4,A,E.A1,4,A,D.C1,3,B,D.D6,8.E2,C.G0,E;210,E.A1,D.C1,D.D6,8.G0,E:RX6PRZBUIW9XXW6DODWYB2JIUUO7PZ2WP4YI9SZLX8B9N9LZ6WSID24794AA28A6ADRW96BZ27BU7D4DPN46ZRRRJ2B4", "hasFlower": 0, "hasSeason": 0}, {"id": "76", "q": "001,4,8,B.20,3,5,7,9,C.40,2,4,6,8,A,C.60,3,5,7,9,C.80,2,4,8,A,C.A1,3,5,7,9,B.C0,2,4,6,8,A,C.E0,2,4,8,A,C.F6.G0,2,4,8,A,C;101,4,8,B.20,C.40,6,C.64,8.81,B.A1,B.B4,8.D0,C.F0,2,A,C;230,C.E0,C.F2,A:CH4MT850GMCL54ILSN4DTBMWY9R0ZWODOI4CYGIECO5OTSN9RWIOABURO599TTCACZ48TH4W9R9MIUEIMUUM", "hasFlower": 0, "hasSeason": 0}, {"id": "77", "q": "000,2,5,7,A,C.20,2,4,6,8,A,C.40,2,5,7,A,C.61,3,6,9,B.80,3,5,7,9,C.A2,5,7,A.C0,2,4,6,8,A,C.E0,3,5,7,9,C.G1,3,5,7,9,B;106.11,B.23,6,9.31,B.46.61,6,B.84,8.A2,6,A.C4,8.D0,6,C.F3,6,9.G1,B:6C0WR6Y82U65YGMNXGMSKAMR8O8IQA8M43ESXNY4QPU3OY8NNE5JW2IM3NNG0XP3S6US8TUJKYTCMYGX", "hasFlower": 0, "hasSeason": 0}, {"id": "78", "q": "000,3,5,9,B,E.17.20,E.35,7,9.41,D.53,5,9,B.61,D.73,5,9,B.81,D.93,6,8,B.A1,D.B5,9.C0,7,E.E1,4,6,8,A,D;103,B.10,E.35,7,9.41,D.61,D.73,B.91,6,8,D.C0,7,E.E1,5,9,D;235,9.41,D.61,D.73,B.91,7,D.E1,D;351,D.97;497:VBFR3Z2PIMM4FPI2RM3V32RBS4NR33VCIMP2CF4VZMINVMBFSZCZC2B3RNNFCRIB4SPNZSVPN2FPBSSZ44IC", "hasFlower": 0, "hasSeason": 0}, {"id": "79", "q": "000,3,5,7,9,C.20,6,C.34,8.40,6,C<PERSON>53,9.60,5,7,C<PERSON>72,<PERSON><PERSON>80,5,7,C.93,9.A0,5,7,C.B3,9.C0,5,7,C<PERSON>D3,9.E0,6,C.G1,4,6,8,B;100,3,5,7,9,C.20,6,C.34,8.40,6,C.53,9.60,5,7,C.72,A.85,7.93,9.A5,7.B0,3,9,C.D0,6,C.F6.G1,4,8,B;246.50,C.75,7.A3,5,7,9.C0,C.D6:14Y6MINUT2I7T7PM8FL2RSY05SXF2L5EQNTAFD1L5NNU6XE07H4JP2P7TMHQ2PRBLBFG8ABQPFSUU2D1QGTFXPSDXN1WTNJDWBM5", "hasFlower": 0, "hasSeason": 0}, {"id": "80", "q": "001,3,6,9,B.20,2,5,7,A,C.41,3,5,7,9,B.60,2,6,A,C.74,8.80,2,A,C.A4,8.B0,2,6,A,C.D1,3,5,7,9,B.F0,2,5,7,A,C;102,6,A.20,5,7,C.41,4,8,B.62,A.80,2,A,C.B2,A.D1,4,8,B.F0,5,7,C;202,A.41,B.72,A.D1,B;341,B.72,A.D1,B;441,B.D1,B:R6VYGJA6RLCCG828269WNOMR1MK8PJ8MMRFACYKNRA1PFYPW866LJN1WW22PMY89P9RAYNVJ212GLJAO6PCAWGJYWM9L", "hasFlower": 0, "hasSeason": 0}, {"id": "81", "q": "000,3,5,7,9,B,E.20,3,5,7,9,B,E.41,3,6,8,B,D.61,3,5,9,B,D.80,2,5,7,9,C,E.A0,3,5,9,B,E.C1,5,9,D.D3,7,B.E0,5,9,E.F3,7,B.G0,E;100,4,A,E.16,8.20,E.33,B.41,6,8,D.64,A.71,D.87.A3,B.B5,9.D4,6,8,A.E0,E.G0,E;246,8.A3,B.B5,9.D6,8;346,8.B5,9.D6,8;446,8.D6,8:SAQ5P84S3K5LORY5PMK5WM6RT9VTEDFLYNJ6VKEHID8ZP3I6VVADWMSAD3WEOAITZJ6HC0F8Y34IVLVMMWP9C8SEYWS9NT3SKQ3W0LM9", "hasFlower": 0, "hasSeason": 0}, {"id": "82", "q": "000,3,6,9,C<PERSON>20,C<PERSON>33,9.40,C<PERSON>52,5,7,A<PERSON>60,C<PERSON>72,4,6,8,A<PERSON>80,C.93,5,7,9.A0,C<PERSON>B3,9.C0,5,7,C.E1,3,9,B.G0,3,5,7,9,C;100,3,9,C.50,5,7,C<PERSON>73,9.85,7.93,9.B0,<PERSON><PERSON>C5,7.E1,B.F3,9.G0,C;255,7.85,7.B0,<PERSON><PERSON>C5,7.E1,B;355,7.B0,<PERSON><PERSON>C5,7;455,7.C5,7:FGC5G5FG5KR23CO5JF7DRKHJRRZJNTPFFN3GIGOD9ABM4VYV7109ORCPN0QW2J1E4E3BWB5MGCTF5ARIYWZWNQBO3H", "hasFlower": 0, "hasSeason": 0}, {"id": "83", "q": "003,5,7,9,B,D.10,G.22,5,7,9,B,E.30,G.42,4,6,8,A,C,E.50,G.63,5,8,B,D.70,G.83,7,9,D.90,G.A2,4,6,8,A,C,E.B0,G.C3,5,8,B,D.D0,G.E2,4,6,8,A,C,E.G0,2,6,A,E,G;103,D.15,7,9,B.20,2,E,G.37,9.42,E.73,D.80,8,G.A3,<PERSON>.C3,D.E3,7,9,D;215,B.38.E3,8,D:6R8HFW6FTDQVRQZ7WJ0DV6XBB8WDJBRH7UTFVJZUF0ZTWDF068IR28WHZ6FT0JRKJD51HIT267ZJKB25BI8W7H2Z8XB221HVTIRD", "hasFlower": 0, "hasSeason": 0}, {"id": "84", "q": "000,4,6,8,A,E.21,3,5,7,9,B,D.40,2,4,6,8,A,C,E.62,4,6,8,A,<PERSON><PERSON>70,<PERSON><PERSON>82,<PERSON><PERSON>90,<PERSON><PERSON>A2,6,8,C.B4,A<PERSON>C0,6,8,E.D2,4,A,C.E0,E.F2,4,6,8,A,C.G0,E;105,7,9.21,5,9,D.40,4,7,A,E.62,C.80,E<PERSON>A2,C<PERSON>C4,<PERSON><PERSON>D0,E.F1,5,9,D;215,9.21,D.40,7,E.D0,E.F1,D:OVLKOJX9SV17ZUW89ZYSL8US89UEV76E431Y7S25YDWPDKN6ULMVV5VKUBQX3QE9UZKPNJP7GLQ2PR4GZ8OMYABROE0QA0", "hasFlower": 0, "hasSeason": 0}, {"id": "85", "q": "001,3,5,7,9,B.21,3,5,7,9,B.40,2,5,7,A,C.60,2,4,6,8,A,C.80,3,5,7,9,C.A0,2,5,7,A,C.C0,3,5,7,9,C.E0,3,5,7,9,C.G0,2,4,6,8,A,C;104,8.25,7.40,C.55,7.70,6,C.A0,2,6,A,C.C5,7.D0,<PERSON><PERSON>F5,7.G0,C;240,C.55,7.C5,7.D0,C:BNB4NZHO5PJNYVQ1D2ZHK29YKRTBJP3YKH3YZVD5PY666350QSP93S1NYZ1DJ5019BPKJBR2H5H5HO602DT90PB4", "hasFlower": 0, "hasSeason": 0}, {"id": "86", "q": "000,5,7,9,E.12,C.24,7,A.32,C.40,4,A,E.56,8.60,2,C,E.75,9.80,3,7,B,E.A1,3,5,9,B,D.C1,3,6,8,B,D.E2,5,7,9,C.G2,4,6,8,A,C;100,5,9,E.12,7,C.24,A.32,C.44,A.57.60,2,C,E.75,9.80,E.A1,4,A,<PERSON>.C3,7,B.E2,5,9,C.F7.G2,C;205,9.12,C.24,A.G2,C:7RTSS3SUXBTNC03NVJRUJAB7ANCUT5AH0RXKMST00XBNVH3XMM603KJTK3K37N0VKUC5B6JTAUX7SOSACC6ABMKXCNUO6RVB", "hasFlower": 0, "hasSeason": 0}, {"id": "87", "q": "001,3,6,8,B,D.20,3,5,7,9,B,E.41,3,5,9,B,D.60,6,8,E.72,4,A,C.86,8.90,E.A3,5,9,B.B1,D<PERSON>C3,5,7,9,B.D0,E.E3,6,8,B;103,6,8,B.20,3,B,E.43,5,9,B.60,E.72,6,8,C.90,E.A5,9.B3,B.C7.D0,3,B,E;213,B.44,A.C7.D3,B:FVEEXFWS1JVK9JPZ9WS9WZFJSF1W1WUJUGU9UJKFPXUWXPVXKZ9GPEKS1GGSVUG9ZSJXEVEX1ZFZP4E1PV4G", "hasFlower": 0, "hasSeason": 0}, {"id": "88", "q": "000,3,5,7,9,B,E.20,2,5,7,9,C,E.41,3,6,8,B,D.60,2,4,6,8,A,C,E.80,2,4,7,A,C,E.A0,3,5,7,9,B,E.C0,3,5,9,B,E.D7.E0,3,5,9,B,E;103,B.15,9.20,E.42,C.56,8.60,2,C,E.74,A.80,E.97.C5,9.D0,E.E3,5,9,B;203,B.61,D.C5,9:7Z7OU8RZ8UOU1BAPBOH8B7TY1U18A1JOPAT1QAPBQ4RUNQQ7BNYQ4PT8YQ4417OZNNT8OZ47TJNBYUN4ATAYYRHR", "hasFlower": 0, "hasSeason": 0}, {"id": "89", "q": "000,3,5,7,9,B,E.20,3,6,8,B,E.40,3,6,8,B,E.60,2,4,6,8,A,C,E.80,2,4,6,8,A,C,E.A1,4,6,8,A,D.C2,5,7,9,C.D0,E.E2,4,6,8,A,C.F0,E.G4,6,8,A;104,A.10,6,8,E.23,B.30,6,8,E.43,B.60,3,5,9,B,E.A1,6,8,D.C2,C.E0,3,5,9,B,E.G6,8;265,9.A6,8.C2,C:WOP5BB31HR73WZLSI3PWUHSY5SP1RW4JAB1YLIHUH54AXX3ZIHJRPLGGB535J7A4UOS3POR1SOIRZS1BUZJG1G4LHZOABROZIPI5", "hasFlower": 0, "hasSeason": 0}, {"id": "90", "q": "000,2,5,7,9,C,E.20,2,5,7,9,C,E.41,3,5,7,9,B,D.60,2,4,6,8,A,C,E.80,2,4,6,8,A,C,E.A0,3,5,7,9,B,E.C0,2,4,6,8,A,C,E.E1,3,5,7,9,B,D.G1,5,9,D.H7.I2,5,9,C;100,5,9,E.17.21,D.35,9.42,7,C.54,A.60,2,C,E.82,6,8,C.A7.C2,C.D4,A.E1,7,D.G5,9.I5,9:QAJUMYLYEK1LP968E6J1O6995Q8FKM1OU5J1FK8AEY03QKMQ5A909YP1UFJQ30OAUK3L333MML00656PFOOJPE0ME6OL19LJEFQKF8", "hasFlower": 0, "hasSeason": 0}, {"id": "91", "q": "000,2,4,6,8,A,C,E,G.22,5,7,9,B,E.30,G.42,6,A,E<PERSON>50,4,C,G.66,A.70,2,E,G.85,8,B.90,2,E,G.A5,7,9,B.B0,G.C3,6,A,D.D0,G.E2,6,A,E.F8.G1,3,5,B,D,F;101,5,7,9,B,F.25,8,B.42,E.50,4,C,G.70,2,E,G.85,8,B.92,E.A0,5,7,9,B,G.C0,G.E6,A.F8.G3,5,B,D;206,A.50,G.A5,B.B0,G:WYDFPGSSDZ5YTX7KVSL2PG8ZZEWCLSDG5C1O5J73VBXGL81LZMF3I1XBP7RYG7ZI7XJT8ESFXWJGK2828MEDOX5EP521O7ZY2J5RLWO8SF2L", "hasFlower": 0, "hasSeason": 0}, {"id": "92", "q": "000,2,4,7,A,C,E.21,4,6,8,A,D.40,2,4,6,8,A,C,E.61,3,B,D.77.80,3,5,9,B,E.A0,2,4,7,A,C,E.C0,2,4,6,8,A,C,E.E0,2,4,6,8,A,C,E.G0,2,4,6,8,A,C,E;100,4,7,A,E.21,D.34,A.41,D.61,D.77.84,A.A0,2,7,C,E.B4,A.C1,D.E4,A.F0,E;231,D.84,A.A2,C.B4,A.F0,E:BW6P4PXK0XHRCU0WOZ4PR40N71CNY4KKDV3RHW4A9U9RUBYS1KJG0WOOGT4YBJT8ZAOYMHRJU0FYJ8MAEDYP063MBHR7MFJOOESVJA", "hasFlower": 0, "hasSeason": 0}, {"id": "93", "q": "001,3,5,7,9,B,D,F.20,2,4,8,C,E,G.40,3,7,9,D,G.60,2,4,6,A,C,E,G.80,2,4,6,A,C,E,G.A0,2,4,6,A,C,E,G.C1,3,7,9,D,F.E1,4,7,9,C,F.G0,3,5,B,D,G;102,8,E.21,4,8,C,F.60,3,5,B,D,G.82,5,B,E.A0,2,5,B,E,G.C8.E4,7,9,C.G3,D;202,E.18.24,C.82,5,B,E.A0,G.D8;382,5,B,E:JNEK64LF05PXAJA7I4PTXOE4CGFNOKE4AKTFQYT06RNLD83Y8RKDUXQEYTENCAKJTVJN6KU0CUPX6PT06XFALU4VIU6CL38O8OPAYN7GE4XU5P", "hasFlower": 0, "hasSeason": 0}, {"id": "94", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.41,3,5,7,9,B,D.60,2,4,6,8,A,C,E.80,5,7,9,E.93,B.A0,5,7,9,E.B2,C.C0,4,6,8,A,E.E0,2,4,6,8,A,C,E.G2,4,6,8,A,C.I0,3,6,8,B,E.K0,3,5,7,9,B,E;111,D.32,C.53,6,8,B.80,E.D0,E.E2,C.G3,6,8,B:BY4KCCB4T3Y4BFQSUTTS6S4WR13MWB8WFRRUKQFU8Q1QM6TC4S3UK3MT4SRYYFRBY3FCWUT1688CM1Y61WU6KR18WS3FCB86", "hasFlower": 0, "hasSeason": 0}, {"id": "95", "q": "000,2,4,7,9,C,E,G.20,2,4,6,8,A,C,E,G.40,2,4,8,C,E,G.56,A.60,2,E,G.74,6,8,A,C.81,F.96,A.A0,2,4,8,C,E,G.C2,4,6,8,A,C,E.D0,G.E3,5,7,9,B,D.F0,G.G2,4,7,9,C,E;102,4,8,C,E.10,G.24,7,9,C.51,F.74,8,C.A4,8,C.D6,A.F4,7,9,C.G2,E;224,7,9,C.F4,7,9,C:XWVCXOVCU7WH07U0ZEHD0ZPO7RE7RERGPPDZF4H3UVUIRZW4Z70XCDIDW0GIPIPEOF4EUCVGFOGHUDHF4CDX0IVWGV7ZCERW3PHIGOOR", "hasFlower": 0, "hasSeason": 0}, {"id": "96", "q": "000,2,4,6,8,A,C,E.20,2,7,C,E.34,A.40,6,8,E<PERSON>52,C<PERSON>60,4,6,8,A,E<PERSON>72,C.85,7,9.91,3,B,D.A6,8.B0,2,4,A,C,E.C6,8.D0,4,A,E.E2,6,8,C.F0,E.G2,5,7,9,C;100,3,5,7,9,B,E.22,C.40,7,E.62,7,C.75,9.87.93,B.A1,D.B3,7,B.C0,E.D4,7,A.F2,C;205,9.62,C.87.A1,D.C0,E:I3XOYRCIZOL56HFT3P5SOJICTZ1TUQFIFT6SPCFJKLH5JP1OTXF2H9O313PUCJ5Q2KEO5QN2UL3INFZPPH31YKX2QRTE65U9KL6XZI", "hasFlower": 0, "hasSeason": 0}, {"id": "97", "q": "000,2,4,6,8,A,C,E.20,3,5,9,B,E.37.42,5,9,C.50,7,E<PERSON>63,B.70,6,8,E.84,A.90,2,6,8,C,E.B4,6,8,A.C0,E.D3,6,8,B.E0,E.F2,5,9,C.G0,7,E.H3,5,9,B.I0,7,E;100,E.15,9.36,8.42,C.50,7,E<PERSON>63,B.86,8.A6,8.D3,7,B.E0,E.F2,C.G6,8;242,C.86,8.F2,C;342,C.86,8.F2,C;442,C.F2,C:H7TJVAKV84MJAFX9FBABXVK0IJXWKRX0KAGKWG8GMKGJV9MTIBX88T4T55MW0VG485TAJBFM8WS7FTSVFSSBFAHH5J40W4G0MR04XWBH", "hasFlower": 0, "hasSeason": 0}, {"id": "98", "q": "001,3,5,8,B,D,F.20,2,4,6,8,A,C,E,G.40,2,5,7,9,B,E,G.60,2,5,7,9,B,E,G.80,3,5,B,D,G.A0,2,4,6,8,A,C,E,G.C0,2,4,8,C,E,G.D6,A.E0,3,8,D,G.F5,B.G1,7,9,F;101,3,8,D,F.26,A.45,8,B.60,7,9,G.A1,F.B3,D.D8.G1,F;203,D.B3,D:KHBS5OXD3JFOOODCN55YK1CP5H3TBJFCO4TBYXKBBC75ENY4XC1TFP1YCHD1FBZ3YSDT3F7HUEZT1OTKHF5D1UYX9D9H", "hasFlower": 0, "hasSeason": 0}, {"id": "99", "q": "000,2,6,8,C,E<PERSON>14,A<PERSON>21,D<PERSON>33,7,B.40,5,9,E<PERSON>52,7,<PERSON><PERSON>65,9.70,2,7,C,E.84,A.90,2,6,8,C,E.A4,A<PERSON>B1,6,8,D<PERSON>C3,<PERSON><PERSON>D0,5,7,9,<PERSON><PERSON>E3,<PERSON><PERSON>F1,D.G3,5,7,9,B;100,6,8,E.40,E<PERSON>52,C.67.70,2,C,E.90,5,9,E.A7.D0,3,B,E<PERSON>F2,C.G5,9;207.67.70,E.A7.D3,B:04BXBPCCB3APCLNMNC8NA00086SN444GFIFP9CI934S87PSAB7I66MH87A66AHS3IGXHFGGLI87CBIH467BA7P83FP", "hasFlower": 0, "hasSeason": 0}, {"id": "100", "q": "000,2,6,A,C.20,2,4,6,8,A,C.40,2,6,A,C.54,8.60,2,A,C<PERSON>74,8.80,6,C.94,8.A2,A.B0,4,8,C.C2,6,A.D0,C.E2,4,6,8,A<PERSON>F0,C.G2,6,A;101,B.20,3,6,9,C.42,A.62,4,8,A.85,7.B2,4,8,A.C6.D2,A<PERSON>E6.F0,C.G2,A;201,B.42,A.62,4,8,A.B2,4,8,A.D2,A;364,8.B4,8:FQECWVXODECQ88WED85G8KUKKGXYK18DKUYFGFCQOD1KD7V7CWX1Y77YQ11EOXV8W5FUCOFGEYF1OQVXYGXQC7VEODG557UV", "hasFlower": 0, "hasSeason": 0}, {"id": "101", "q": "000,2,4,6,8,A,C,E.20,4,6,8,A,E.42,6,8,C.50,E.62,4,6,8,A,C.82,6,8,C.A1,3,5,7,9,B,D.C0,2,5,9,C,E.E1,4,6,8,A,D.G3,6,8,B;106,8.20,5,9,E.42,C.50,6,8,E.62,4,A,C.76,8.97.A1,3,B,D.C0,5,9,E.E4,A;220,E.52,C.65,7,9.A3,B.E4,A;320,E.65,7,9.A3,B;420,E;520,E:LSCBDIWKIYJLBBLS8YXDIH1SHAKQK03X1P0CK184I5O6VGH7XVOQ1I4AXVUEPDF417A8I86WJDGOQ77EF9LAAD4OSDJUH5BVE1EQ3J9A", "hasFlower": 0, "hasSeason": 0}, {"id": "102", "q": "000,2,4,8,C,E,G.20,3,5,8,B,D,G.41,4,7,9,C,F.60,2,7,9,E,G.74,C.80,6,A,G.94,C.B0,3,5,7,9,B,D,G.D0,2,7,9,E,G.F1,4,7,9,C,F.H0,3,5,8,B,D,G;100,3,D,G.18.23,D.47,9.51,F.84,C.A4,C.B8.D0,G.E8.H3,D;213,8,D.47,9.51,F.84,C.B8.D0,G;313,8,D.47,9.84,C;447,9.84,C;548:8H4H8J4RQOS8USNNQPNWJUE77QSNW4KKJ4EORR7VPVJQPEVWS8WVW7RHKPFHFR7KFJOPUSLOKQ8LJNVNU4HER8OLPLSEF4EFLFWQLKVHO7", "hasFlower": 0, "hasSeason": 0}, {"id": "103", "q": "000,3,6,A,D,G.22,5,8,B,E.30,G.42,5,8,B,E.61,4,7,9,C,F.80,3,5,8,B,D,G.A0,4,6,A,C,G.B2,E<PERSON>C4,7,9,C.D0,2,E,G.E4,7,9,C.F2,E.G0,6,A,G;100,3,6,A,D,G.30,G.42,5,B,E.58.61,4,C,F.78.85,B.A4,6,A,C.C2,4,8,C,E.E3,D.G6,A;261,F.A4,C.C4,C.E3,D;3E3,D:C5UXM22521DSC2CXLN00DS5G8SSUX58LNASCAN1XDK1DKA11A9NKN0KK9DC0X2UW5MK0GW8GMM9M1GG0A99GU8N2UAUXS5MWDC9W", "hasFlower": 0, "hasSeason": 0}, {"id": "104", "q": "010,G.30,2,E,G.50,2,4,C,E,G.66,8,A.70,2,4,C,<PERSON>,G.86,8,A.90,2,4,C,E,G.A6,8,A.B0,2,4,C,E,G.C6,8,A.D0,2,4,C,E,G.F0,2,E,<PERSON>.H0,G;120,G.40,G.52,E.60,4,6,8,A,C,G.72,E.80,5,7,9,B,G.92,E.A0,5,7,9,B,G.B2,E.C0,6,8,A,G.D3,D.E1,F.G0,G:ZQVZ2GQTU7NG7PQGZIHVTV7JPH7I77UIVKU2J6NBNNUJBKB6HGB6PVPQ62JKTHKKQUBT6BZIUZPNJGI26ZHTHVQGI2J2PTKN", "hasFlower": 0, "hasSeason": 0}, {"id": "105", "q": "002,4,7,A,C.20,3,5,9,B,E.40,3,6,8,B,E<PERSON>64,7,A<PERSON>71,<PERSON><PERSON>84,A.90,7,E.A5,9.B0,2,C,E<PERSON>C6,8.D4,A<PERSON>E0,6,8,E<PERSON>F3,B.G1,D.H3,5,9,B.I7;102,4,7,A,C.20,3,B,E.40,3,7,B,E.67.71,D.90,E.B0,E.C7.E7.F3,B.H3,B.I7;203,7,B.20,E.33,B.47.67.90,E.B0,E.C7.G3,B.I7;303,B.20,E.I7;403,B.I7;5I7:1NQWAKB44P9P84BAINWNWE9HL8W9HHLR4UNQLNBBA81KE4U94BR8IRHFEK5NP1LUBAFPIEK1I1EKIW95LUR898UYUFRRPEKIP1YWLFAA", "hasFlower": 0, "hasSeason": 0}, {"id": "106", "q": "001,4,A,D.17.20,2,4,A,C,E.36,8.40,2,4,A,C,E.56,8.60,4,A,E.77.81,4,A,<PERSON>.96,8.A0,2,C,E<PERSON>B4,7,<PERSON><PERSON>C0,<PERSON><PERSON>D4,7,<PERSON><PERSON>E0,E<PERSON>F2,4,6,8,A,C.H0,2,4,7,A,C,E;101,4,A,D.23,B.35,9.40,E.54,6,8,A.87.B7.E0,4,A,E.G2,4,A,C;204,A.45,9.57.87.B7.E0,E.F4,A.G2,C;304,A;404,A:906SYZDCEY7JTOTGTNN43MEXGSEN74G2XSOD78T6ZS24TYN0AJFXCTB39GAS3AAJ4J9EF3MMNCOQGDNYFYDE8GSC7AMXQAOYBE9F", "hasFlower": 0, "hasSeason": 0}, {"id": "107", "q": "000,2,4,6,8,A,C,E.20,5,9,E.32,<PERSON><PERSON>44,A<PERSON>51,6,8,D.71,3,5,9,B,D.87.90,2,4,A,C,E.A6,8.B0,2,C,E.C4,6,8,A.D1,<PERSON><PERSON>E3,6,8,B.F0,E.G2,4,6,8,A,C;101,7,D.15,9.32,C.44,A.51,7,D.72,C.87.90,2,C,E.B0,2,6,8,C,E.D6,8.F6,8.G3,B;232,C.87.91,D.B2,C.C6,8;332,C.C7:N032W0W0KSEP87889PSDE11LMLN0ZBU26JLMZDN1E0K2P1G669Y8Y3GTWYA9TEYAV8P8C5GN9JTBUTT7Y6CWELUYN2E2VGT02N5U", "hasFlower": 0, "hasSeason": 0}, {"id": "108", "q": "000,2,4,A,C,E.16,8.21,3,B,D.35,7,9.40,2,C,<PERSON><PERSON>54,A<PERSON>61,7,D.75,9.80,7,E.92,4,A,C.B0,4,7,A,E.D1,4,7,A,D.F0,2,5,7,9,C,E.H1,3,5,9,B,D.I7;100,2,C,E.16,8.21,D.35,9.40,2,C,E.54,A.61,D.75,9.93,B.C4,A.D1,D.E7.F0,2,5,9,C,E.H1,4,A,D;202,C.42,C.F2,C;342,C.F2,C:SHRRF3YIHP7ICN2SQNY2KBQB8ZINSP7H8K8LI6837PISLNR7B8SZRQ6ZLBC8FPPQF33QRLZR6QC6KCFB3N7H2ZL6FNLHI2Z36KH7FPSB", "hasFlower": 0, "hasSeason": 0}, {"id": "109", "q": "001,3,5,7,9,B,D.21,3,5,9,B,D.40,3,5,7,9,B,E.60,2,4,6,8,A,C,E.81,3,5,7,9,B,D.A0,2,4,7,A,C,E.C0,2,4,6,8,A,C,E.E1,3,5,7,9,B,D.G0,3,5,9,B,E.I0,2,6,8,C,E;101,6,8,D.14,A.34,A.46,8.50,E.63,7,B.83,B.A3,B.B0,E<PERSON>C6,8.D3,B.F3,5,9,B.H0,E.I6,8:60KMMZQMH4E93H43ME9U9ZQRUOH943354RY6RY53B5M4Z0KEOHORQ90YUYBXX5QXXQE5ZZ3QO5BUU0KM9YBH4RKHZ6X6EUEBYKBRXK", "hasFlower": 0, "hasSeason": 0}, {"id": "110", "q": "000,2,4,7,A,C,E.20,3,5,7,9,B,E.41,4,6,8,A,D.60,2,4,A,C,E.77.80,3,5,9,B,E.97.A0,3,B,E.C0,2,6,8,C,E.D4,A.E1,6,8,D.F3,B.G7;101,3,B,D.24,7,A.44,6,8,A.51,D.64,A.87.C1,D.D4,6,8,A.E1,D.F7;203,B.24,A.37.44,A.64,A.D4,A.E1,7,D;303,B.24,A.37.64,A.E1,7,D;424,A.64,A:0DHOKZAMD7IPQT48CILZI4G5WQORY7400JDY3MDBK1ZHS1L0T2HVK8T7IJJBS3G4IBJ2PRYYB2YOK57WDTA3327MCMHZVYLOGTPDT7IGPL", "hasFlower": 0, "hasSeason": 0}, {"id": "111", "q": "001,3,6,8,A,D,F.20,2,4,6,8,A,C,E,G.40,3,7,9,D,G.55,B.61,3,7,9,D,F.75,B.80,2,7,9,E,G.94,C.A0,2,7,9,E,G.B4,C.C0,6,8,A,G.D2,4,C,E.E0,6,8,A,G.F3,D.G0,5,8,B,G.H2,E.I4,6,8,A,C;118.20,G.54,6,A,C.68.76,A.80,2,E,G.A0,3,D,G.B7,9.C4,C.D0,8,G.E6,A.G5,B.I8:RNIYIUGLO7I9JLNT4C4BXHC29XLA1FEUK0TWENIELQ18WXNLJE9NU97TYXCR4CJ8JFRAT8RQUUO41W4G7COCNK4OKWQ8LJ21JQ79BK0H9U", "hasFlower": 0, "hasSeason": 0}, {"id": "112", "q": "000,3,5,7,9,B,E.20,2,4,6,8,A,C,E.45,7,9.50,3,B,E<PERSON>66,8.70,2,4,A,C,E.86,8.90,3,B,E.A5,7,9.B1,3,B,D.C6,8.D1,4,A,D.E6,8.F1,3,B,D.G6,8.I0,2,4,6,8,A,C,E.K0,2,4,7,A,C,E;103,B.10,E.22,5,9,C.37.45,9.53,B.73,6,8,B.C1,D.D4,A.E6,8.F2,C.I0,2,7,C,E.K0,E;210,E.73,B.I0,E.K0,E:NLVQDIMW2ACFALPAF7UVVMDLQAYMP8OBDVO2Z2DOLBY2VBOWQLPCMW8FUII8QOVN28F82UDYAIY8WPKQONBYUDJBLNCBMF7CNCCYMUPQFKNJZAUP", "hasFlower": 0, "hasSeason": 0}, {"id": "113", "q": "000,3,5,8,B,D,G.20,2,5,8,B,E,G.41,3,5,7,9,B,D,F.61,3,6,A,D,F.80,2,5,8,B,E,G.A0,2,5,7,9,B,E,G.C0,2,4,6,8,A,C,E,G.E0,2,4,6,A,C,E,G.F8.G3,6,A,D;105,B.18.20,G.38.41,6,A,F.61,3,6,A,D,F.80,G.B0,5,8,B,G.D1,6,A,F.E4,C;241,6,A,F.80,G.B0,G.D1,6,A,F;346,A.D6,A:EX5IJUSZ5PBAMXUU5N6ASA5X2I5BYFZF2EJZPZYJPEE1XMF1FJYC2DYBIWYPPM16CIEPCND6BEMD6BFICIANZ66WD1NJDX5AZYFCMMACJ2BXDU", "hasFlower": 0, "hasSeason": 0}, {"id": "114", "q": "000,2,5,7,9,C,E.20,2,5,9,C,E.41,3,5,7,9,B,D.60,2,4,6,8,A,C,E.81,3,5,7,9,B,D.A0,2,4,6,8,A,C,E.C0,2,6,8,C,E.E0,2,4,6,8,A,C,E.G1,4,A,D;101,D.43,B.56,8.64,A.72,7,C.A0,2,4,7,A,C,E.C2,6,8,C.D0,E.E6,8.F4,A.G1,D;2A0,E.D0,7,E:KOBZ0OZO4RRDKPTMXG4KB90OTW88OAXXMARR8P0WXMD4DZ9880MKCC8ZGCZP0GCGPK0W9TGCORRAWMGADPZC99K9PXMXWTW4", "hasFlower": 0, "hasSeason": 0}, {"id": "115", "q": "000,2,5,7,9,B,E,G.20,2,4,7,9,C,E,G.40,2,4,7,9,C,<PERSON>,G.61,3,6,A,D,F.81,4,7,9,C,F.A0,3,5,7,9,B,D,G.C3,5,7,9,B,D.D0,G.E2,5,8,B,E.F0,G.G2,4,6,A,C,E;100,2,5,7,9,B,E,G.27,9.47,9.87,9.A7,9.C7,9.E8.F5,B;202,6,A,E.47,9.88.A7,9;348.88;448.88;548:ZZJQT6Y3Y4B64LFQYJYCTQLQLALFZ6FA4JI3XQ76I7CTC44CEXT47I3B6EECBXJTBC7IJ7AE3QF733XTYEIZ6LYZZIEFLABAFJAB", "hasFlower": 0, "hasSeason": 0}, {"id": "116", "q": "000,2,5,7,9,C,E.20,3,5,7,9,B,E.40,2,4,7,A,C,E.60,2,5,7,9,C,E.80,4,6,8,A,E.92,C.A0,4,7,A,E.B2,C.C0,4,7,A,E.E2,4,6,8,A,C.F0,E.G3,5,9,B.H0,7,E.I2,4,A,C.J0,7,E.K2,4,A,<PERSON>;116,8.40,E.60,E.75,7,9.93,B.B3,B.E3,5,7,9,B.F0,E.H0,E;250,E.G0,E:06K6NH8JICV0U7LRL9TNUZRYDHXZBOSTCOI0YYPOBMBJ1X8PH3IBELB9HRY6MOR3T8CSEDP0YCBID8IXLE8618TYRJEPIVJDKXR7", "hasFlower": 0, "hasSeason": 0}, {"id": "117", "q": "000,3,5,B,D,G.17,9.20,4,C,G.37,9.40,2,E,G.54,6,8,A,<PERSON><PERSON>60,G.73,6,A,D.81,8,F.93,5,B,D.A0,7,9,G.B3,5,B,D.C7,9.D0,2,E,G.E4,7,9,C.F0,G.G3,5,7,9,B,D;100,3,5,B,D,G.17,9.20,4,C,G.41,7,9,F.55,B.60,G.76,A.82,8,E.94,C.A6,8,A.B3,D.C7,9.D1,F.E4,C.F0,7,9,G.G3,5,B,D:8OM1WFYNWDYBZ3XAUJ9OSBM1KZ8UVMSUW6V3S85JD2XAIDJB57ONDVSIWOZ0IB1JOMZ4N295ZW53S16S11ODJANV45F38XUN0WXDJZ5I7KNA", "hasFlower": 0, "hasSeason": 0}, {"id": "118", "q": "000,4,6,A,C,G.20,4,6,8,A,C,G.32,E.40,4,6,A,C,G.52,8,E.60,5,B,G.72,7,9,E.80,4,C,G.92,6,8,A,E.A4,C.B0,2,6,A,E,G.C4,8,C.D0,2,6,A,E,G.E4,8,C.F0,6,A,G.G4,C;106,A.24,C.30,2,E,G.45,B.50,2,E,G.68.87,9.C0,2,4,C,E,G.D6,A.E0,G.F4,C;224,C.42,E.50,G.C0,G.F4,C;342,E;442,E:IMARMLH46EJSM798PEIJIHXMT59PJXFI7FE7S5457SRD6FHP65DG9X6DEEIJ8XPE9SYTR0HIMPH0Y6D007D3BGMH7BSA9F4BSB3T9LTO6P4ORD", "hasFlower": 0, "hasSeason": 0}, {"id": "119", "q": "000,3,5,7,9,B,D,G.20,4,C,G.32,6,A,E.40,4,8,C,G.52,6,A,E.60,8,G.72,4,6,A,C,E.80,8,G.92,5,B,E.A0,8,G.B2,5,B,E.C7,9.D0,2,4,C,E,G.E6,A.F0,4,C,G.G2,6,A,E;103,7,9,D.24,C.32,E.50,2,6,A,E,G.68.70,2,6,A,E,G.A0,2,E,G.B8.C2,E.E4,C.G6,A;207,9.32,E.52,6,A,E.76,A.C2,E:G5RDWH7VV3O5QWSHXPZDQ76Y8YMC8SINGI5PO56OQYSC36XMPJWIIRC8ZGC23VWI0V0PMP8W2YRCXDSJ3G5YXO0RN7O5WCSMQD06P7OIYMSXXM", "hasFlower": 0, "hasSeason": 0}, {"id": "120", "q": "000,3,6,8,A,D,G.20,2,7,9,E,G.34,C.41,6,8,A,F.53,D.61,8,F.74,C.80,6,A,G.93,D.A0,7,9,G.B2,E.C0,5,7,9,B,G.D2,E.E4,6,A,C.F0,2,E,G.G5,7,9,B;107,9.20,G.34,C.41,7,9,F.53,D.74,C.80,G.93,D.B2,7,9,E.C5,B.D2,E.E4,C.F0,G.G5,8,B;207,9.20,G.48.74,C.B2,8,E.C5,B.F0,G;374,C.B2,E;4B2,E:BCZFL6VWVFCY3AGXXOTO2YH9OXY7ILVCEHUUIVL3GX3CDEDAWOYE7GTVJQ8E27QQZ92UJ2QZ2GFEYOFZ3LLJU7ZJ79OL9C6U9872CWV9YJJZUBWE", "hasFlower": 0, "hasSeason": 0}, {"id": "121", "q": "000,4,6,8,A,C,G.12,E.20,4,7,9,C,G.40,2,4,6,A,C,E,G.63,5,7,9,B,D.70,G.83,8,D.91,F.A3,6,A,D.B8.C1,3,5,B,D,F.D7,9.E0,3,5,B,D,G.F7,9.G0,2,4,C,E,G;106,8,A.10,G.24,7,9,C.30,G.42,4,6,A,C,E.63,7,9,D.83,D.A6,A.B3,D.D4,6,A,C.E0,G.F3,7,9,D.G0,G;227,9.63,7,9,D.B3,D.F7,9:PJ7S1SSQ63BZMKO02NLWU23QBKN7WRUVNGWZS0KHUG4ZM0JHLJV4C77NGBX84QKU2K37KOVLCB6BX7PX8GUJL3933UW0ZEVSL4L9R1XEQAT2STBA", "hasFlower": 0, "hasSeason": 0}, {"id": "122", "q": "000,2,6,A,E,G.14,8,C.21,6,A,F.33,8,D.40,5,B,G.52,7,9,E.60,5,B,G.72,E.80,6,8,A,G.92,E.A4,6,A,C.B0,2,8,E,G.C4,6,A,C.D0,8,G.E3,D.F1,6,8,A,F.G3,D;100,6,A,G.18.21,F.38.50,6,8,A,G.62,E.70,G.86,A.A2,4,C,E.B0,7,9,G.C4,C.D8.F1,3,7,9,D,F;238.50,7,9,G.E8.F2,E;350,G:S42DJOEI5UAGJ1KFXCW6D2MM4XAY1RIU005OS1MEON06NW9O172EWK0S2TDTQE261MI627RSOXOCHFWU6G01496Q0BYEHXAJURI4WBRDAJEW", "hasFlower": 0, "hasSeason": 0}, {"id": "123", "q": "000,E.12,4,6,8,A,C.36,8.40,3,B,E<PERSON>55,7,9.63,B.70,5,7,9,E<PERSON>82,C.90,5,9,E.B0,2,4,7,A,C,E.D3,<PERSON><PERSON>E5,7,9.F0,3,B,<PERSON><PERSON>G6,8.H1,D.I3,6,8,B;100,E.14,6,8,A.40,6,8,E.64,6,8,A.70,E.90,5,9,E<PERSON>B0,4,A,<PERSON><PERSON>E6,8.F0,E<PERSON>H6,8;216,8.46,8.E6,8.H6,8;316,8.47.E7;416,8.E7;5E7:RH38J5KO0JU0WL8TRZJLGLOVVHHRLWDUL6189O059V0WUFJHEPD9CGQGAZ16HWA193EOHRFTBOZQGVDO1TZFEFCBKT8IPLIGGUED", "hasFlower": 0, "hasSeason": 0}, {"id": "124", "q": "000,3,5,7,9,B,E<PERSON>21,3,6,8,B,<PERSON><PERSON>44,7,A.50,E<PERSON>63,B<PERSON>70,6,8,E<PERSON>84,A.90,7,E<PERSON>A4,A<PERSON>B1,<PERSON><PERSON>C3,B.D7.E0,2,4,A,C,E.F6,8.G0,3,B,E;100,7,E.26,8.44,7,A.50,E<PERSON>63,B.70,6,8,E.84,A.A4,A<PERSON>B1,D.C3,B.D7.E1,D.F6,8.G0,E;260,E.76,8.84,A.A4,A.B1,D.G0,E;360,E.77.A4,A.B1,D;460,E:PP5N8KKH46RG9G8PP8ML14LH082XS5KH6X1XM8KNHDF5JRRRLXMW7WPLG937SADXVV2F2YSJR5MY2OP3ER0LWAJ8EGX5OE5ESWJL", "hasFlower": 0, "hasSeason": 0}, {"id": "125", "q": "000,4,6,8,C.12,A.24,6,8.30,2,A,C.44,6,8.50,2,A,C.64,6,8.71,B.83,5,7,9.A0,2,5,7,A,C.C0,2,4,6,8,A,C.E2,5,7,A;100,5,7,C.30,2,5,7,A,C.50,2,4,8,A,C.66.71,B.85,7.A0,2,A,C.B5,7.C0,2,A,C.E5,7;205,7.36.40,C.52,4,8,A.A0,2,A,C.E6;306.36.52,A.A0,2,A,C;4A0,C:7IFJDI2JGETMDVI2TC5V41CNGKWJM194G4K14VSCS4GIKVJ62KM269J41N7EO79O7UAOW5K9COUA9ZOGU2VVKCFCZJFF2IA9OMAHIUGH", "hasFlower": 0, "hasSeason": 0}, {"id": "126", "q": "002,4,7,A,C.20,3,B,E.35,7,9.40,2,C,E.55,7,9.60,2,C,E.77.80,3,B,E.96,8.A0,2,C,E.B5,7,9.C0,2,C,E.D4,7,<PERSON><PERSON>E1,<PERSON><PERSON>F3,<PERSON>;104,7,A.23,B.30,5,7,9,E.50,5,7,9,E.77.80,E.96,8.A2,C.B0,6,8,E<PERSON>D4,7,A.E1,D.F3,B;235,9.40,7,E.55,9.77.96,8.D7;335,9.96,8;435,9;535,9:6E40DQOKCDKKZZL1YQYMV4CI6BZHDRZ9QZH1H9HZ4RRBHRVQI06VDOYH6CIE4CYODLV0OKA6MCA19LQN10OONV6DL9QTTVLLC9I9", "hasFlower": 0, "hasSeason": 0}, {"id": "127", "q": "000,2,4,7,9,C,E,G.20,2,4,6,8,A,C,E,G.40,2,4,C,<PERSON>,G.57,9.60,2,5,B,E,G.77,9.80,3,5,B,D,G.98.A0,2,4,6,A,C,E,G.B8.C0,2,4,6,A,C,E,G.D8.E0,2,E,G.F4,C.G0,6,A,G.H2,4,8,C,E.I0,6,A,G;108.12,4,C,E.20,6,A,G.42,E.50,G.62,8,E.70,G.93,D.A1,F.C0,3,D,G.D8.E0,2,E,G.H0,4,6,A,C,G:NU370L5FFQKN1ROLC5MM5B3UE3QU15OAPFCFOWS1KFIKDJHK9K8RESIJ2K1C2MIWD00FM2LSDJL9M3289E52W07CA9DQUPJSOEBIU15W00WWQ1MHU2", "hasFlower": 0, "hasSeason": 0}, {"id": "128", "q": "000,2,4,7,A,C,E.20,3,5,7,9,B,E.41,3,5,7,9,B,D.61,3,5,9,B,D.77.80,2,4,A,C,E.96,8.A0,2,4,A,C,E.B6,8.C1,4,A,D.D7.E2,5,9,C.G0,2,4,A,C,E.H7.I1,4,A,D.J7.K1,3,5,9,B,D;102,C.10,4,7,A,E.36,8.43,B.51,D.73,B.81,D.93,B.A6,8.F2,C.H7.I1,D.J4,<PERSON><PERSON>K1,D;202,C.17.J1,4,A,D;302,C.J4,A:PV13BEP2ITBURIGKV6O1C8HILOI7WT0GYY5UIPKETQOZRRS1CF3W5HHGOV7H0E5WB8GTVQO72ZNRLFFV1GF0Q5IQ8OBLV08GREQP7R6S2LLQNW2L", "hasFlower": 0, "hasSeason": 0}, {"id": "129", "q": "000,2,5,7,9,C,E.20,3,6,8,B,E.41,4,A,D.56,8.61,4,A,<PERSON><PERSON>76,8.80,2,C,E.97.A0,E.B2,6,8,C.D1,4,6,8,A,D.F1,4,A,D.G7.H0,3,B,E.I5,7,9;100,5,7,9,E.23,B.55,7,9.61,D.77.80,E.B7.D1,6,8,D.G7.H3,B.I7;256,8.61,D.77.80,E.B7.D1,6,8,D.G7.I7;356,8.B7.D7.I7;4B7.I7;5I7:AB2BYH0BF4HZWEI1PZDVLTFKRNY3G71LNGPVKA4FQTY0ZZ6VAYP4YFQ332S2H6RKLR0LQIVYDHSI8K8APFGI3QB3372HHWRGE0F4", "hasFlower": 0, "hasSeason": 0}, {"id": "130", "q": "000,3,6,8,B,E.21,4,6,8,A,D.40,2,5,9,C,E.57.62,C.70,5,7,9,E.82,C.94,6,8,A.A0,2,C,E.B6,8.C0,3,B,E.D5,9.E2,7,C.F4,A.G0,2,C,E;103,6,8,B.24,7,A.40,2,C,E.62,C.70,5,9,E.87.93,B.A0,E.B6,8.F2,4,A,C;203,7,B.40,E.52,C.87.93,B.F2,C;303,7,B;403,B;503,B:WGHR33RT1MGX7PTMR41MEHVWW1UGDXFM12F43XF7BVR2DU4U28FMXE347E8V1GE2WDVTHRDWPB3RPUXWMHXP8T1DDVH247H82V34", "hasFlower": 0, "hasSeason": 0}, {"id": "131", "q": "000,2,5,7,9,B,E,G.21,3,6,8,A,D,F.40,2,4,6,8,A,C,E,G.60,2,5,7,9,B,E,G.80,3,5,7,9,B,D,G.A1,3,5,8,B,D,F.C1,5,7,9,B,F.D3,D.E0,8,G.F2,4,6,A,C,E.H1,3,6,8,A,D,F;102,7,9,E.21,3,6,A,D,F.41,F.56,A.62,E.76,8,A.80,G.98.B8.C6,A.D3,D.E0,G.G6,A.H1,3,D,F;223,D.88.B8.H3,D:CQQVB7RR4N2FX4K0WE046WYP2OR7AMPOQNGZ1QK4P2PWEVNY4GVY9ZOVPH0NKVFZAAMM2Y4X6M10FMWVOMZ5R2H27PKWFRCUUXNJAW59RNOOJXB7", "hasFlower": 0, "hasSeason": 0}, {"id": "132", "q": "000,2,4,6,8,A,C,E,G.21,7,9,F.34,C.41,6,A,F.54,8,C.61,6,A,F.73,8,D.85,B.98.A0,3,5,B,D,G.B7,9.D5,7,9,B.E0,2,E,G.F4,6,8,A,C.G1,F.H3,5,7,9,B,D.I0,G.J2,7,9,E.K0,5,B,G;103,6,8,A,D.11,F.28.31,4,C,F.56,A.61,8,F.73,D.85,8,B.A3,D.B7,9.D5,7,9,B.E0,G.F4,7,9,C.H3,5,7,9,B,D.J8.K0,G:3I9SO1PUWVT5F2KWS2CZCT1C454EPH74U4U33Q146EEK09DFC2R7RC8PDZCONI3KSIS1DIFP208IEH43UV5FV1WS6VWS5H3OOQZFZ5IPFND9TPHK1T95", "hasFlower": 0, "hasSeason": 0}, {"id": "133", "q": "000,2,5,7,9,C,E.22,4,7,A,C.30,E.52,4,6,8,A,C.60,E.72,4,6,8,A,C.90,3,5,9,B,E.A7.B0,2,5,9,C,E.D3,5,9,B.E0,E.F3,5,7,9,B.H0,E.I3,5,7,9,B.K2,4,6,8,A,C;100,2,5,7,9,C,E.24,7,A.57.61,D.77.83,B.90,5,9,E.B0,2,5,9,C,E.D3,B.F7.H0,E<PERSON>I3,7,B.J5,9.K7;************,D.A0,E.I3,7,B:QX1S8SSQYTWZGDF7LU59KR7QWDU1X8KNUYXZF7DFKYLZG7XF5XNLU11UVWRYL5DKRDT1DFNAUWYSR1QZYVKXATGT7K97ZQNSALAG88ZQ5N9LF9SN", "hasFlower": 0, "hasSeason": 0}, {"id": "134", "q": "000,2,4,6,8,A,C,E.21,3,B,D.36,8.40,2,4,A,C,E.57.64,<PERSON><PERSON>72,6,8,C.84,A.90,2,7,C,E<PERSON>A4,A.B0,6,8,E.C2,4,A,C.D7.E2,4,A,C.F7.G0,2,4,A,C,E.H6,8.I0,3,B,E.K2,4,7,A,C;101,D.21,3,B,D.37.54,A.77.84,A.97.B5,7,9.D4,7,A.F2,<PERSON><PERSON>G4,A<PERSON>H6,8.I0,<PERSON><PERSON>J3,<PERSON><PERSON>K7;237.77.97.B5,7,9.D7;397.C7;497.C7:R44ZFID6058EU5N51TJW3RSBCS4NO3CZRWE1K3K5OP3SNPN5ZSIODBJPZE4QRS6EB0JC3K036H11OTE0Q8JB6EPFMHUMBS0MM0JPJ9B9P5KC", "hasFlower": 0, "hasSeason": 0}, {"id": "135", "q": "001,5,7,9,D.13,B.20,5,9,E.32,7,C<PERSON>44,A.50,2,6,8,C,E.70,2,4,6,8,A,C,E.90,2,4,6,8,A,C,E.B1,3,6,8,B,D.D0,2,5,7,9,C,E.F0,2,5,9,C,E.H1,4,6,8,A,D.J0,2,4,A,C,E.K6,8;107.15,9.32,7,C.50,6,8,E.77.82,C.94,A.B3,B.D2,6,8,C.F1,D.J2,C;256,8.D6,8.F1,D;3D6,8:ECGAPQ6KWMUMQUUNZHWNN2EPOM8KQFPGE6AIHMP8O8O2UGGF9K88OZF9HOWNKIFHQN8OWMMGH62E6UPGPCKC2Z2KHI2ZFQUQFCIEEN66", "hasFlower": 0, "hasSeason": 0}, {"id": "136", "q": "000,2,6,8,C,E.20,2,4,6,8,A,C,E.40,2,5,7,9,C,E.60,3,5,7,9,B,E.82,5,7,9,C.A1,4,6,8,A,D.C0,2,6,8,C,E.D4,A.E1,6,8,D.F3,B.G0,6,8,E.H2,4,A,C.I6,8;100,E.16,8.20,2,C,E.35,7,9.42,C.50,5,7,9,E.86,8.B1,6,8,D.D5,7,9.G0,6,8,E.H2,C.I6,8;222,C.55,9.H2,C:KF55GFN4TVOBVN53Y4UWIG3V8HQKH7FQZIYWTBABW3ZZQ868GBG5U64KKVAB8FZZFWUUA7V4AYAK5QOVKNAN46WW3IZOBQFO4H56HIQY", "hasFlower": 0, "hasSeason": 0}, {"id": "137", "q": "000,2,5,7,9,C,E.20,2,4,6,8,A,C,E.44,7,A.51,D.63,5,7,9,B.70,E.85,9.90,3,B,E.A6,8.B1,D.C3,5,9,B.D0,7,E.E2,5,9,C.F7.G0,4,A,E.H2,6,8,C.I4,A;101,6,8,D.20,5,7,9,E.44,7,A.70,E<PERSON>85,9.90,3,B,E.A6,8.C3,B.E2,C.F7.G4,A.H7;220,5,9,E.37.80,5,9,E.A6,8.G4,7,A;337.85,9.A6,8.G7;4A7:385L6OBMHHA2UVEL6QLD7OIXMM8CDTCT5DDESIQ6EF1AQXC5F66VP127B8TC51PX9UC1VCUAP4DQE8TMVPSE9FNHA2MLFXN4FF21D36HTM1UET", "hasFlower": 0, "hasSeason": 0}, {"id": "138", "q": "000,2,4,A,C,E.16,8.20,2,4,A,C,E.40,2,4,7,A,C,E.60,2,4,7,A,C,E.82,4,6,8,A,C.A1,3,5,7,9,B,D.C1,4,6,8,A,D.E0,3,5,7,9,B,E.G2,4,6,8,A,C.H0,E.I2,5,9,C.J0,7,E.K3,5,9,B;100,2,4,A,C,E.16,8.20,2,4,A,C,E.J0,5,7,9,E.K3,B;204,A.10,6,8,E.22,C.J7;316,8.J7:FV31ZO7P1OJ1ZX5VXQTQPKX9BLT5E1GYVFN0RGPFQFPGQVEVBK3OPVTKOYEFUYFT0CE079O30JCGK50G0ZXTTSUOL5QXXZZRGQZNS3WPWY", "hasFlower": 0, "hasSeason": 0}, {"id": "139", "q": "000,3,5,7,9,B,D,G.20,2,5,8,B,E,G.40,3,6,A,D,G.58.60,2,4,6,A,C,E,G.80,2,6,A,E,G.A2,5,7,9,B,E.B0,G.C3,6,8,A,<PERSON>.D0,G.E2,6,8,A,E.F0,G.G2,5,7,9,B,E;107,9.15,B.43,D.56,A.60,3,D,G.80,G.A5,7,9,B.B0,G.C6,A<PERSON>E6,8,A.G5,B;207,9.A7,9.D6,A;3A7,9;4A8:LNYNFDGYTF705M5O7G7KV8152RBUKHX5CUC1G2K1TH1RH5VQTXABAMLBDCXYLBFNJ8SRRCKYRJ1LGGXGT7IAHUO057UFQIA7NRS1", "hasFlower": 0, "hasSeason": 0}, {"id": "140", "q": "000,4,6,8,A,E.12,C.20,4,6,8,A,E.32,C.40,4,7,A,E.60,2,7,C,E.75,9.80,3,B,E.95,9.A0,3,B,E.B6,8.C0,E.D2,7,C.E0,5,9,E.F2,7,C.G0,4,A,E.H2,6,8,C.I0,E;107.14,A.20,7,E.32,C.44,A.67.70,E.85,9.90,E.B0,E.D7.E1,D.G0,2,7,C,E;207.27.70,E.85,9.G0,7,E;317.70,E.G7;470,E;570,E:TJDDG8U51474TUUGFB5XNR7RE92FQNLX1Z4UYUA2BO1R414XWYKSFO9SSYSAJQ61T2YE0ETKRUXS682EW8TT70ZZFZL7KD4D7F81676SKF", "hasFlower": 0, "hasSeason": 0}, {"id": "141", "q": "000,2,4,6,C,E,G,I.19.20,2,5,7,B,D,G,I.40,2,5,7,9,B,D,G,I.60,3,7,9,B,F,I.81,3,6,8,A,C,F,H.A0,3,6,8,A,C,F,I.C1,4,6,8,A,C,E,H.E0,3,6,8,A,C,F,I.G1,6,8,A,C,H.H3,F.I1,6,9,C,H;100,6,C,I.20,I.41,7,9,B,H.60,8,A,I.93,9,F.C1,5,8,A,D,H.E9.F7,B.G1,9,H.H3,F:96EPLKMN8R6FI4F85BM1UKBTELRB44T9CRQHOHQOU9FNC0BCOF4OPW35CK1NPDIUPK3TO9CQBIHI184H1LUPWQBLFDRF1C1TUKL3UK80O3L4PN", "hasFlower": 0, "hasSeason": 0}, {"id": "142", "q": "001,3,5,7,9,B,D,F.21,3,5,7,9,B,D,F.41,3,5,7,9,B,D,F.60,2,4,6,A,C,E,G.80,2,4,6,8,A,C,E,G.A0,2,5,7,9,B,E,G.C0,2,4,7,9,C,E,G.E0,2,4,7,9,C,E,G.G0,2,5,8,B,E,G.I0,3,5,8,B,D,G;115,B.27,9.34,C.46,A.63,D.81,F.A0,G.B2,E.D3,D.F0,G.I0,5,B,G;228.63,D.81,F.A0,G.D3,D:59QPQN4AU7YM4LAHMZWD89ZHKYDE04KZINEZDFW1GPAHE8TLL68TYEIL154Y8F01FAM6IQK6ND1EAAURNOI4EZZ7L1HNM7FQOGKL6M1RMNHH0704", "hasFlower": 0, "hasSeason": 0}, {"id": "143", "q": "000,4,6,8,A,E.20,2,4,7,A,C,E.40,2,5,9,C,E.57.61,3,B,D.77.80,2,4,A,C,E.97.A0,E.B2,6,8,C.C0,4,A,E.D2,6,8,C.E0,E.F2,4,7,A,C.G0,E;105,9.20,2,4,A,C,E.41,D.63,B.77.84,A.A7.C4,A.D1,7,D.F0,2,4,A,C,E;221,3,B,D.41,D.D1,D.F1,3,B,D;341,D.D1,D:5YQA013XHL0GG4N2WXKE1LZIV1EWYQZC2UHQJKEBPVCRD4LQKEL0GXUA1S0QR5U6IPGC232UXIO0DXB0DO6CJKANYIQYACDXCS", "hasFlower": 0, "hasSeason": 0}, {"id": "144", "q": "001,3,6,A,D,F.20,2,4,6,8,A,C,E,G.41,3,7,9,D,F.55,B.61,7,9,F.73,D.85,7,9,B.90,G.A4,6,8,A,C.B2,E.D1,5,7,9,B,F.E3,D.F1,7,9,F.G3,D.H0,5,7,9,B,G.I2,E;101,3,6,A,D,F.21,5,8,B,F.42,8,E.61,F.73,8,D.85,B.90,8,G.A4,C.D1,F.E3,7,9,D.G8.H5,B;201,F.21,F.78.85,B.E3,7,9,D;3E7,9:RBFFEM71RZWCWHGSBZG75RENMRSMHGFWE7G7UHIR5ZNZMNFEFM9SICETPLBCLLG5T9CI9NL0S1S1UIHW7519FTISIHRTT7PE5WZBHG0T11Z5LLWM", "hasFlower": 0, "hasSeason": 0}, {"id": "145", "q": "000,2,4,6,8,A,C,E.21,6,8,D.33,B.40,5,9,E<PERSON>52,7,C.60,5,9,E.72,7,C.80,4,A,E.92,6,8,C.A0,E.B2,4,6,8,A,C.C0,E.D2,5,9,C.E0,7,E.F2,4,A,C.G6,8.H1,D.I3,B;101,4,7,A,D.21,7,D.33,B.61,5,7,9,D.82,C.96,8.B2,5,9,C.D1,5,9,D.F2,4,A,C.H1,D.I3,B;204,A.17.33,B.96,8;396,8;497:IRO5SIDPB87N5LVPWGLIHEQDS5BT5SLZE7GGFIE87GKFDFPKYAVEDKRGPVH0NNBTEZVZV88XPZZVHLEI0W7LBDPFDIAKBLNFBHFWXSWOQZYG", "hasFlower": 0, "hasSeason": 0}, {"id": "146", "q": "000,3,5,8,B,D,G.20,2,4,6,A,C,E,G.38.43,5,B,D.50,8,G.62,4,C,E.77,9.81,5,B,F.97,9.A1,3,5,B,D,F.B8.C0,3,5,B,D,G.D7,9.E0,2,5,B,E,G.F7,9.G0,2,4,C,E,G;108.13,5,B,D.21,F.43,5,B,D.50,G.78.97,9.A1,5,B,F.C0,G.D5,7,9,B.E0,2,E,G.F8.G1,3,D,F;213,D.45,B.78.98.D5,B.E8.G3,D:3D1CVU42W7ZTAX3BWD3YSYD217UPW7BDBXRRZ42S165SUW76PXXTGE382C8I2YSTC06EB3X8C14WC9PXWSVY8CB1URU6772STU9A4P5GBI130R", "hasFlower": 0, "hasSeason": 0}, {"id": "147", "q": "000,2,4,A,C,E.16,8.21,3,B,D.35,7,9.43,B.50,E<PERSON>62,5,7,9,C.70,E.83,6,8,B.90,E.A3,5,7,9,B.B1,D.C3,6,8,B.D1,D.E3,B.F5,7,9.G0,3,B,E.H5,9.I3,7,B;102,4,A,C.16,8.21,3,B,D.37.43,B.61,5,7,9,D.83,B.90,E.A3,B.B1,D.D1,D.E3,B.F5,9.G3,B.H5,9.I7;216,8.21,3,B,D;317;417:3THAQAIBWGQ1VMZAEMDMUU9WL16ZLFL985V65Z988Z6GMEN5IWDWDUMGBLY4TM6EHI5U2DZ4U2HWH58DT5I3AZVTDVF4GYWNUOY4YOE9", "hasFlower": 0, "hasSeason": 0}, {"id": "148", "q": "000,3,5,7,9,B,D,G.20,4,6,A,C,G.40,2,5,7,9,B,E,G.60,5,7,9,B,G<PERSON>72,E.80,6,8,A,G.92,E.A5,7,9,B.B0,2,E,G.C4,6,8,A,C.D0,G.E2,5,8,B,E.F0,G.G4,6,A,C.H0,G.I2,4,7,9,C,E;103,7,9,D.10,G.24,C.30,G.42,5,7,9,B,E<PERSON>66,A.71,F.86,A.98.A6,A.B0,G.C6,A.E8.F5,B.G0,G.H4,C;208.20,G.86,A.B6,A:EU63UJ870Y0927LDUOSZ87W4O6Y4J2JXEEANPJE6N36XJD2A47LMYYSJ3X398O3USD2N4EOM8X7X9Y4DOS62N2NWW949ODSW9PWPY7EXPSUW6D3UZN", "hasFlower": 0, "hasSeason": 0}, {"id": "149", "q": "002,4,6,8,A,C.22,4,A,<PERSON><PERSON>30,E<PERSON>45,7,9.50,2,C,<PERSON><PERSON>65,9.71,<PERSON>.84,6,8,A.90,<PERSON>.A5,9.B0,E<PERSON>C2,4,6,8,A,C.E0,2,5,9,C,E.G1,7,D;103,5,9,B.22,4,A,C.45,7,9.50,E.84,7,A<PERSON>B0,E<PERSON>C3,5,7,9,B.E1,5,9,D;203,B.23,B.45,7,9.50,E.87.C6,8.E1,D;303,B.87.C6,8.E1,D;4E1,D;5E1,D:KYK33O0QG67UFNUQNLK3NLOACL1AYSQOY1FSFA1YK1CGLCGA76AOACUSSY6301ULC6SQ3NF0K0N7077K1LUCG0UYFS36NG67FQGQ", "hasFlower": 0, "hasSeason": 0}, {"id": "150", "q": "000,4,6,8,A,E.12,C.25,7,9.30,2,C,E.45,9.50,2,7,C,E<PERSON>64,A<PERSON>70,2,7,C,E.84,A.91,6,8,D.A4,A.B0,2,7,C,E.D0,2,4,7,A,C,E.F5,9.G0,2,7,C,E.H5,9.I2,7,C;112,7,C.25,9.31,D.45,9.51,D.63,B.70,E.87.91,5,9,D.B0,7,E.D3,<PERSON><PERSON>F5,9.G1,7,D.H5,9.I2,7,C;217.45,9.95,9.F5,9.H7;395,9:QRLQXO61QW2DHOJVF4BA3V46ADZ6WB46ZWEOZBHWLDFLFHA5RB9ARV5ZKBXWL959REX4DVLWOOZAFF2234QJXOX1FB25X4E99ZK955LAHE", "hasFlower": 0, "hasSeason": 0}, {"id": "151", "q": "000,5,7,9,E.12,C.24,6,8,A.32,C.40,4,A,E<PERSON>52,6,8,C<PERSON>60,<PERSON><PERSON>73,5,7,9,B.80,E<PERSON>95,9.A1,D.B4,6,8,A.C1,D.D6,8.E0,2,4,A,C,E.G2,4,6,8,A,C.I2,4,6,8,A,C;100,5,9,E.22,C.41,4,A,D.56,8.60,E.73,5,9,B.B1,D.D6,8.E0,2,C,E.F4,A.G2,C.I4,A;200,5,9,E.57.B1,D.D7.E1,D.F3,B;3E1,D:NTOLMVREIH1L8HC0G8JWDTLT8H600S90HIWLCII1M62MDWIVCUN2DRMPRCJNTB8DIN8QZOQEZLNSQNRBGV66UE9Q8020STVLESUCC2TUPW", "hasFlower": 0, "hasSeason": 0}, {"id": "152", "q": "003,9.10,C.22,4,6,8,A.40,2,4,8,A,C.56.61,3,9,B.75,7.81,B.94,6,8.A0,C.B3,5,7,9.C0,C.D2,4,8,A.E6.F1,3,9,B;103,9.10,C.23,9.40,2,4,8,A,C.62,A.76.81,B.95,7.B3,9.D2,4,8,A.E6.F1,3,9,B;210,C.23,9.40,3,9,C.62,A.81,B.D3,9.F1,3,9,B;381,B.F1,B;481,B;581,B:M89MH5KHBERDKFKAKAF7EKEDU8VHFVKVRRDABDTTHFXP8QXYYTMP9XFTD87PAQUYVPHRQXFEUA57VMQ7UQDAPE7PTYEHQT585V87", "hasFlower": 0, "hasSeason": 0}, {"id": "153", "q": "000,2,4,7,A,C,E.20,3,5,7,9,B,E.40,2,4,6,8,A,C,E.60,3,5,9,B,E.77.80,3,B,E.95,7,9.A0,3,B,E.B5,7,9.C0,3,B,E.D7.E0,2,4,A,C,E.F6,8.G0,2,4,A,C,E.H6,8.I0,2,4,A,C,E.J7.K1,3,B,D;100,2,4,7,A,C,E.20,E.K1,3,B,D;200,E.K1,3,B,D;3K1,3,B,D;4K1,3,B,D;5K1,D:KGPY9DOEN0KB2XUB9OJ2HUJKHYFSYLBVHR22HBOE3IDPGE4BKXHNP9FF2TC3OD3NXIVFGNFPH9KPOU3CEDRDVUTFLODV2S0XPYK94G9B", "hasFlower": 0, "hasSeason": 0}, {"id": "154", "q": "001,3,7,B,D.20,2,6,8,C,E.43,5,9,B.50,E.62,7,C.70,4,A,E.86,8.90,2,4,A,C,E.B1,3,5,9,B,D.D0,3,5,9,B,E.F0,2,7,C,E.G4,A.I2,5,9,C.J0,7,E;102,7,C.20,7,E.43,B.50,E.70,7,E.85,9.90,2,C,E.B1,3,B,D.C5,9.D0,E.G4,A.I2,C.J0,7,E;220,E.60,E.80,E.92,C.B1,D.D0,E.J0,E;392,C.B1,D;4B1,D;5B1,D:PNY5RWLKREKI3W6EP4FPRHWPNQEO4VT7WNZ76HHH8D5DH5EBZ5R85FUP84VNYQ1RE48DTZROIDIOKZPUB77KDHIYEKIBYY7I8378LFNOFB3ND1Y5K3", "hasFlower": 0, "hasSeason": 0}, {"id": "155", "q": "000,2,4,6,8,A,C,E,G.20,2,4,7,9,C,E,G.41,3,5,7,9,B,D,F.60,2,5,7,9,B,E,G.80,3,5,7,9,B,D,G.A0,2,4,7,9,C,E,G.C2,5,7,9,B,E.D0,G.E2,4,6,8,A,C,E.F0,G.G2,4,6,A,C,E.H8.I0,2,4,6,A,C,E,G.K0,2,5,7,9,B,E,G;104,6,8,A,C.27,9.48.68.88.A8.C8.E8.H8.K6,8,A;205,8,B.27,9.58.K6,A:SX6FTKAA8RK51OQB1OVEJX8V5KSA5FJ7S8QJTVT468MAO6RAQQEOBVKQSOCGEVRQL4M77Y77HEXTAEJKHCXLOVK7MHHMJHGCY54CX88XCR4EJCRHR6", "hasFlower": 0, "hasSeason": 0}, {"id": "156", "q": "000,2,4,6,8,A,C,E.20,2,5,9,C,E.40,2,4,A,C,E.57.60,3,B,E.75,7,9.80,2,C,E.94,7,A.A0,2,C,E.B6,8.C0,4,A,E.D2,C.E0,4,6,8,A,E.F2,C.G6,8;100,4,6,8,A,E.25,9.30,E.50,E.70,5,7,9,E.91,7,D.B6,8.C0,E.E0,6,8,E.G6,8;204,A.25,9.40,E.76,8.A7.C0,E.E7.G6,8;340,E.E7:L5ND2DBP979SMOGASS9TJNHOGB2EMKPS5KEON2NO68LTH2JNKH9APTGPAAHOLKHEJHJAP2S6BOK25AGJTLKE59ELN9PM87S8L85EM5BJ", "hasFlower": 0, "hasSeason": 0}, {"id": "157", "q": "002,4,6,8,A,C.10,E.24,A.31,7,D.45,9.50,2,7,C,E.70,4,7,A,E.90,3,6,8,B,E.B1,5,7,9,D.D0,3,6,8,B,E.F0,2,5,7,9,C,E.H0,4,A,E.I2,7,C;102,4,6,8,A,C.10,E.41,D.60,7,E.74,A.87.93,B.A6,8.C6,8.D0,E.E6,8.F1,D;202,4,A,C.60,E.A6,8.C6,8.D0,E.E7;304,A.C7;404,A:M0QLU11BUL02UAV3XGRRTA4LJ3DO0GKBK6UDI9IOQU5GMJIWRG9BMNR3ACVDWBOVDDJ9IO0A91ONZ4CM6ZRD69MV63LRJ2MU9OT15X", "hasFlower": 0, "hasSeason": 0}, {"id": "158", "q": "000,3,5,7,9,C.22,6,A.30,C.42,4,8,A<PERSON>50,6,C.62,4,8,A<PERSON>70,C.82,4,6,8,A.A0,2,4,8,A,C.B6.C0,2,4,8,A,C.E0,3,5,7,9,C.G0,3,5,7,9,C;105,7.26.32,A.50,3,9,C.70,3,9,C.93,9.A0,C.B6.C0,3,9,C<PERSON>E5,7.F0,C.G4,8;205,7.53,9.C3,9.E5,7.G4,8;353,9.C3,9:DD8NN1ZMETL6ZULOLIN6D6ETOZ3M160M1MO030TIEELO3MBNNIDZE0IB60ITIOU118UODUDB088BU3BLTZT81U6M3ZEL8BN3", "hasFlower": 0, "hasSeason": 0}, {"id": "159", "q": "001,3,6,8,B,D.24,7,A.32,C.40,4,7,A,E.62,4,A,C.77.80,2,4,A,C,E.96,8.A1,3,B,D.B5,9.C0,E.D2,4,7,A,C.F4,7,A.G0,2,C,E;106,8.24,A.32,C.40,4,7,A,E.81,3,7,B,D.B5,9.C0,E.D3,7,B.F4,A.G0,2,C,E;206,8.24,A.32,C.81,3,B,D.B5,9.F4,A.G0,E;306,8.32,C.81,D.G0,E;432,C:5G1JC35YE1PD435834N8W8UULZMPS5DGLLYNBHRS164C8J11GS8RDJJGDAYW53CAYHEMGACPE6BZUPLZZYYZJ5SAZS814J3ABBASUG3E", "hasFlower": 0, "hasSeason": 0}, {"id": "160", "q": "000,2,5,7,9,B,E,G.22,4,6,8,A,C,E.40,3,5,8,B,D,G.61,3,6,8,A,D,F.80,2,4,6,A,C,E,G.A0,2,4,7,9,C,E,G.C1,4,6,8,A,C,F.E1,3,8,D,F.F6,A.G1,3,8,D,F;106,A.22,E.38.43,5,B,D.61,3,6,A,D,F.85,B.A1,8,F.B4,C.E2,8,E.G1,F;206,A.A8.E2,E.G1,F;306,A:FC629AIPV8FRDP6L2RZI86DPMR7AZDZE8CZ7R2A7MECMV9D87V78FDMEDALC9L9RR2ECA9FL676I92V2PIZFIZE8AIMFLL6ECPPM", "hasFlower": 0, "hasSeason": 0}, {"id": "161", "q": "001,4,6,8,A,D.20,3,6,8,B,E.40,2,4,A,C,E.56,8.60,4,A,E.72,7,C.80,4,A,E.96,8.A1,4,A,D.C0,2,4,7,A,C,E.E0,2,4,6,8,A,C,E.G0,3,B,E.H6,8.I0,3,B,E;120,3,6,8,B,E.40,2,C,E.54,6,8,A.60,E.72,C.84,A.96,8.A4,A.C0,2,C,E.E4,6,8,A.F0,E.G3,B.H0,6,8,E.I3,B;256,8.60,E.72,C.95,9.C0,2,C,E.E6,8.G3,B;360,E.C1,D;4C1,D:9AOWTES7W738P4P8ZN9LWL24XLRWUO6LP0CHTK3R7EU2XH76XSCNUR996EOYAVR60KSEULAWY4PKK6XOSWRX3ZPZVZH99Y7L87YRXA440Y603KK42N200NUUGG8YPH", "hasFlower": 0, "hasSeason": 0}, {"id": "162", "q": "000,2,4,A,C,E.17.21,3,B,D.35,7,9.42,C<PERSON>54,A.61,7,D.73,5,9,B.87.92,C.A4,6,8,A.B1,D.C3,5,9,B.D7.E1,3,5,9,B,D.G0,3,5,7,9,B,E;101,4,A,D.23,7,B.35,9.42,C.61,D.73,6,8,B.A4,6,8,A.B1,D.D6,8.E4,A.G3,5,9,B;201,4,A,D.27.73,6,8,B.A4,6,8,A.D6,8;373,6,8,B.A6,8;473,B;573,B:DFCCJS6NTLURQ4EEXD9FKEAWTS6SCA06BWR2EW39LGUD00XO56INW349IOYKDYBDEXQBBQLQE404XK69YR4SK45YDSUSQUWLF2QKG6JCKFRW", "hasFlower": 0, "hasSeason": 0}, {"id": "163", "q": "000,2,4,7,A,C,E.20,2,4,7,A,C,E.41,3,5,9,B,D.57.62,4,A,C.70,6,8,E<PERSON>82,C<PERSON>95,7,9.A0,2,C,<PERSON><PERSON>B4,<PERSON><PERSON>C0,7,E.D2,4,A,<PERSON><PERSON>E0,E.F2,4,7,A,C;102,4,A,C.17.20,2,4,A,C,E.41,4,A,D.67.82,C.95,9.B0,E.D1,4,A,D.F2,4,7,A,C;204,A.17.21,D.41,D.95,9.D1,D;395,9;495,9:WZ1C43PIXRXF524KA1I3W9PX5Z3GLISURB9TAXN65PBOJPEJEYHIS8F2Y89NPJ631N9FNCCKPR1ITBZJ9VGHEFEVOB1U15CRI9ZL", "hasFlower": 0, "hasSeason": 0}, {"id": "164", "q": "004,6,8,A.10,2,C,E.25,7,9.30,2,C,E.44,6,8,A.50,E.62,4,6,8,A,C.81,3,7,B,D.A2,4,6,8,A,C.C0,3,5,7,9,B,E.E0,2,4,6,8,A,C,E.G0,2,4,6,8,A,C,E;104,6,8,A.10,E.31,5,9,D.50,E.62,4,7,A,C.82,7,C.A2,5,9,C.B7.C0,E.E1,3,5,9,B,D.G0,5,9,E;204,A.31,D.E1,D:HY2HTBILQK73VB1DY22IH3KPCK8V1BL5IQ2TQF8FY6BQ76I0P0050MC39DU11HTTVM5LY250VYTDCMYA813DP7LKCT0V21M87V9UJJPA", "hasFlower": 0, "hasSeason": 0}, {"id": "165", "q": "000,2,4,6,8,A,C,E.21,3,5,9,B,D.37.43,5,9,B.57.61,4,A,D.76,8.80,2,4,A,C,E.A0,2,5,7,9,C,E.C0,4,A,E.D2,7,C.E5,9.F1,D.G4,7,A.I2,4,6,8,A,C.K0,2,6,8,C,E;100,5,7,9,E.12,C.24,A.37.43,B.67.74,A.80,E.A1,5,9,D.C0,E.E5,9.H7.I2,5,9,C.J7.K2,C;243,B.67.A1,D.C0,E.E5,9.I5,9;367.A1,D.C0,E.E5,9.I5,9;4C0,E:IRTS83HIP64HYJK9A9IO1RS6TS3P659T83984NOH4Y4THPY481KN291K5JVHHPI6P61SNOJRR255K255YKJY3RK19RPA246TOV1SAOANSTVAJLAI2JYOIVL2", "hasFlower": 0, "hasSeason": 0}, {"id": "166", "q": "001,5,7,9,B,F.13,D.20,6,A,G.33,8,D.40,5,B,G<PERSON>52,<PERSON><PERSON>65,7,9,B.70,2,E,G.84,8,C.90,6,A,G.A3,8,D.B0,G.C2,4,7,9,C,E.E0,2,4,C,E,G.F8.G0,3,5,B,D,G.H7,9.I0,3,D,G;105,8,B.20,3,D,G.38.45,B.52,E.65,7,9,B.70,2,E,G.88.A8.C2,7,9,E.E1,F.G0,8,G.H3,<PERSON>.I0,G;220,G.52,5,B,E.67,9.C7,9.H0,G;368.C7,9;468.C7,9;568:0YEZ600AENCDKTMZLY8DL8ARYAG8TGYLE9EKGTR5VG5D0Z9L0L56CVVEK5GR9R9NAK3R6C19T69RGMDT01YVDM68TZV3Z1MLCC5KM18A3Z3EYC6D38M3A5VK", "hasFlower": 0, "hasSeason": 0}, {"id": "167", "q": "003,5,7,9,B,D.10,G.22,5,7,9,B,E.30,G.42,4,6,8,A,C,E.50,G.63,5,8,B,D.70,G.83,7,9,D.90,G.A2,4,6,8,A,C,E.B0,G.C2,5,7,9,B,E.D0,G.E2,4,6,8,A,C,E.G0,2,5,7,9,B,E,G;104,7,9,C.27,9.30,G.54,8,C.60,G.88.A4,7,9,C.B0,G.C6,8,A.E8;260,G.A4,7,9,C.B0,G.C7,9:E15R2GRQ48WG8JXHHPIW7NGQ8TWQN447JTKSK258WOBNB4SEAE5JNEPHA2A8XNT2HK414I2AI758E2WJEOBGXQSXKBNIK7WSHSSXTXHK", "hasFlower": 0, "hasSeason": 0}, {"id": "168", "q": "003,5,7,9,B.11,D.24,7,A.30,E<PERSON>42,4,6,8,A,C<PERSON>50,E<PERSON>62,<PERSON><PERSON>74,6,8,A.90,3,5,9,B,E.B3,6,8,B.C1,D.E0,2,5,9,C,E.F7.G0,4,A,E<PERSON>H2,7,C.I5,9;103,6,8,B.30,4,A,E.42,6,8,C.62,C.74,7,A.90,3,5,9,B,E.B7.C1,D.E1,5,9,D.F7.G0,4,A,E.I5,9;207.47.52,C.77.95,9.C1,D.E5,9.F7.I5,9;377.I5,9;477;577:LZE03ESWXU4CC4XXXW8WGED0VPZGEVZPJG3CJNSVZNEUEVCPLMNUML8350Z2456L0X6JS3ZDJWJVUUU6W5M26CMVS2GX4J44SG3GP3SNC222W5", "hasFlower": 0, "hasSeason": 0}, {"id": "169", "q": "000,2,5,7,9,B,E,G.20,3,6,8,A,D,G.40,2,4,6,8,A,C,E,G.62,4,7,9,C,E.80,4,6,8,A,C,G.A3,5,7,9,B,D.C0,2,5,7,9,B,E,G.E0,2,4,7,9,C,E,G.G1,3,6,8,A,D,F;102,7,9,E.10,G.23,7,9,D.30,G.43,5,8,B,D.63,7,9,D.84,6,A,C.A7,9.C0,8,G.E0,4,C,G.F7,9.G2,E;227,9.44,C.86,A.F7,9:EEM7G5GTN4KRSKE53ALRNR5LAHHEY5AI4LRIT3H4KKKKIL7VWY7NM75SAJTM4HJA7JELETNN5YY7W333SJLMWVMIGJVISSMWJTHRVAGTH43NISR4", "hasFlower": 0, "hasSeason": 0}, {"id": "170", "q": "000,2,4,6,8,A,C,E,G,I.20,2,4,6,8,A,C,E,G,I.47,9,B.E9.F7,B.G0,2,4,9,E,G,I.H6,C.I0,2,4,8,A,E,G,I;100,3,5,7,B,D,F,I.19.20,2,4,6,C,E,G,I.39.47,B.F7,9,B.G0,3,F,I.H5,9,D.I0,2,G,I;200,3,5,D,F,I.20,3,6,9,C,F,I.F8,A.G0,3,F,I.H9.I0,I;300,4,E,I.20,6,9,C,I.G0,3,9,F,I.I0,I;404,E.G9:UGFW145FUTN32B2B2KNDR86RUYR184G4RUG85RTUF2K4F4Y5YG8W23LNDRB2L1DAGALAW5BLNA86316D614Y5LK1FWT3TNBUNDTAYWKWYAFTGBK665D8KL", "hasFlower": 0, "hasSeason": 0}, {"id": "171", "q": "000,3,5,7,9,B,D,G.20,2,5,8,B,E,G.40,3,5,7,9,B,D,G.61,4,6,8,A,C,F.80,2,5,7,9,B,E,G.A0,2,4,6,8,A,C,E,G.C1,5,7,9,B,F.D3,D.E0,5,7,9,B,G.F2,E.G4,6,8,A,C.H0,G.I3,5,8,B,D.J0,G.K2,4,6,8,A,C,E;103,D.15,B.28.47,9.66,A.80,8,G.D8.F6,A.G8.I4,C.J8.K5,B;203,D.38.66,A.F6,A:EGUASFKYR9RTMF1TFIXJ11KGMCYM9P2YYRG9CMECJPRK9S3FTUJPP2KY1FSG3JCTXPA212XRSIR3U8M3EG9FP12XG8EAAUA8EEJC9YM8CJUXAU2X", "hasFlower": 0, "hasSeason": 0}, {"id": "172", "q": "000,2,4,7,A,C,E.20,2,5,9,C,E.40,2,5,9,C,E.57.60,4,A,<PERSON><PERSON>76,8.81,4,A,D.97.A2,C.B4,6,8,A.C0,E<PERSON>D4,7,A.E0,2,C,E<PERSON>F5,9.G0,2,C,E.H5,9.I1,3,B,D;102,7,C.10,E.22,5,9,C.41,5,9,D.60,4,A,E.84,7,A.B4,6,8,A.C0,E<PERSON>D4,<PERSON><PERSON>E0,<PERSON><PERSON>F5,9.G1,D.H5,9.I1,D;202,C.45,9.60,E.D0,E.F5,9.I1,D:ZZ3NPE7C33CHXYXUC354CNLXNNOH7UPYK43TAYEZH7H7TZ5EPYKLZ1OK1ELL1AY5N4NU1AHO1ZOYUTEELPCA5OTXAU7CULP7HPT4K4O34A1T", "hasFlower": 0, "hasSeason": 0}, {"id": "173", "q": "001,3,5,9,B,D.17.20,2,4,A,C,E.36,8.41,D.53,7,B.60,5,9,E.80,2,4,6,8,A,C,E.A0,4,6,8,A,E.B2,<PERSON><PERSON>C4,7,A.D1,<PERSON><PERSON>E3,5,9,B.F0,7,E.G2,4,A,C;101,4,A,D.20,3,7,B,E.53,B.70,5,9,E.83,B.96,8.A0,4,A,E.B2,C.D4,A.F0,3,7,B,E;220,E.75,9.83,B.A4,A.D4,A.F0,E:DKYN89WD0X5BJ8IM2OSTXDBMCO8N7P0PC8IMHB59GB7HKJKKUN6X009SMXWSMHWT5GW58BMSW0856QDY0UUIHPNCN5UNICBW29PQ", "hasFlower": 0, "hasSeason": 0}, {"id": "174", "q": "002,4,6,8,A,C.20,2,4,7,A,C,E.41,6,8,D.54,A.60,6,8,<PERSON><PERSON>72,C<PERSON>84,A.91,7,D.B1,3,B,D<PERSON>C5,9.D0,3,7,B,E.F1,6,8,D.G3,B<PERSON>H0,5,7,9,E.I2,C;104,6,8,A.21,3,7,B,D.41,7,D.67.72,C.B3,B.C5,9.D7.F1,7,D.H7;205,7,9.23,7,B.41,7,D.B3,B.F1,7,D.H7;307.23,B.47.F7;407;507:3MCM399UZ4D34CLHZLKCU7MUO9JJK4IUO5OC5DMKHK9OIVUKIIDJIC9UH445M5OJJOLLK5J7ZZ33CD73ZIT7A4TTVA597THDD7ZM", "hasFlower": 0, "hasSeason": 0}, {"id": "175", "q": "001,3,7,9,D,F.15,B.20,3,7,9,D,G.42,4,6,A,C,E.50,G.62,4,6,A,C,E.80,3,7,9,D,G.A1,4,7,9,C,F.C0,2,4,6,A,C,E,G.E0,3,6,8,A,D,G.G0,3,5,7,9,B,D,G;103,7,9,D.15,B.28.50,2,E,G.73,D.80,8,G.C0,2,E,G.E8.G4,7,9,C;208.28.50,G.88.C0,G.F8;350,G.C0,G:4D515H61Z2UMHQFE4E0K6KD02MDH35331MJ63Y0U3DZNSY4266142N3U4J6YHQIXUZ0FQFNSD2QXI70ZINZQ7EFB0IYB24QMZE5D", "hasFlower": 0, "hasSeason": 0}, {"id": "176", "q": "000,2,4,7,A,C,E.20,2,4,6,8,A,C,E.42,4,6,8,A,C.50,E.64,6,8,A.70,2,C,E.84,6,8,A.90,E<PERSON>A3,7,B.B0,5,9,E<PERSON>C2,7,C<PERSON>D0,4,A,E.E7.F2,4,A,C.G6,8.H0,2,4,A,C,E.I6,8;102,7,C.21,5,9,D.42,7,C.55,9.60,E.72,4,7,A,C.A3,B.B5,7,9.C1,D.E7.F2,C.H1,5,9,D.I7:HPFDMC1EFT3I7B7W2A2RH61QPKO3DMQBAKUW2D1457IF14KXDKM7BBMAGGBET3W2ERAFOA63SBW33D55CM566IXIUFEADS5MF544", "hasFlower": 0, "hasSeason": 0}, {"id": "177", "q": "000,3,5,7,9,B,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.61,3,5,7,9,B,D.80,3,5,7,9,B,E.A0,4,7,A,E.C1,3,6,8,B,D.E0,2,4,7,A,C,E.G0,2,5,9,C,E;100,4,6,8,A,E.23,B.30,6,8,E.42,C.55,7,9.61,D.73,6,8,B.80,E.A4,A.C1,3,6,8,B,D.E0,4,A,E.G1,D:F4E3TTOWJ40X0AIUH0GWLT1X25O5XTJ3ETJTZRILI1ZULRGUQXUYOE4Y0UZ2PBJ5HRRLIOAR5LOHIX1ELO3PB00IHX3FR455Q1ZU", "hasFlower": 0, "hasSeason": 0}, {"id": "178", "q": "001,3,5,7,9,B,D.21,4,6,8,A,D.43,6,8,B.50,E.63,6,8,B.71,D.83,6,8,B.91,D.A3,7,B.B0,E.C6,8.D0,3,B,E.E5,9.F3,7,B.H1,4,6,8,A,D;102,4,A,C.16,8.21,D.50,6,8,E.71,3,B,D.91,3,B,D.B0,E.D0,E.E4,A.H1,6,8,D;204,A.50,6,8,E.73,B.91,D.B0,E.D0,E.H7;3B0,E.D0,E;4D0,E:KO9H8LGKMJF9Y1BL6FA429BTYKJM1AKX102EESHJQJ4AT90F2H0CGFV37N6G0PHQPALKJF8F0KBV4C0452NOL1RSRO3B5HOGEH7J4XE4", "hasFlower": 0, "hasSeason": 0}, {"id": "179", "q": "000,4,8,C,G.12,E.20,5,7,9,B,G.32,E.40,4,6,8,A,C,G.52,E.60,5,7,9,B,G.72,E.80,4,6,8,A,C,G.A0,3,7,9,D,G.C0,2,4,6,8,A,C,E,G.E0,2,4,6,8,A,C,E,G.G0,2,4,6,8,A,C,E,G.I1,7,9,F;130,6,A,G.48.50,G.62,8,E.70,5,B,G.88.90,G.A8.C0,5,B,G.D2,8,E.E0,G.F8.G0,6,A,G;260,8,G.90,G.C0,G.D8:SS2SEHUWRI3OH71UFEALSEJUX6ETH33997AFBJ6HH8YIBLFT326J6IN3FT28WAUW67WUWT77LYRS6FNJBWTAA7EB89UB2NOLA9H8J1IT3NJS8F8NEXBN", "hasFlower": 0, "hasSeason": 0}, {"id": "180", "q": "000,2,5,7,9,B,E,G.21,4,7,9,C,F.40,2,4,6,8,A,C,E,G.60,2,5,B,E,G.78.83,D.90,6,A,G.B3,7,9,D.C0,5,B,G.D2,E.E0,7,9,G.F2,4,C,E.G0,6,8,A,G.H4,C.I1,8,F;101,5,8,B,F.21,7,9,F.34,C.42,8,E.61,5,B,F.83,D.96,A.B3,D.D1,F.E8.F2,E.G4,7,9,C.I1,8,F;205,8,B.11,F.38.65,B.E8.G8.I1,F;365,B.E8;4E8:LCQPC41MQKKWTY003OY2HJ2J880B9Y26O3OK2LTW603XZT8EE4REPDMOITJ4Z0MRY707OP8MSTBOH79DWXL8ESXJR1SR9GKWSZGPL39X2C42KCIKT78Z", "hasFlower": 0, "hasSeason": 0}, {"id": "181", "q": "000,2,4,6,8,A,C,E,G.20,3,6,8,A,D,G.40,3,5,7,9,B,D,G.61,5,7,9,B,F.73,D.80,5,7,9,B,G.92,E.A0,6,8,A,G.B2,E.C0,5,7,9,B,G.D2,E.E0,5,7,9,B,G.F3,D.G1,5,7,9,B,F.H3,D.I0,7,9,G;103,5,B,D.28.43,7,9,D.55,B.61,F.76,A.96,8,A.B2,E<PERSON>C6,A.D1,F.E5,B.F3,7,9,D.H8;243,7,9,D.86,A.F3,7,9,D:575U0N0HGHG6BM6XGHM65QMGOIQE90HR1AM6STOISSASUX8C27XTM92S3UAN9M98BIGEIB6B7USQ8A75BNCXUENU7OBE81AIC70RQ2T52AI3GTO56CXX", "hasFlower": 0, "hasSeason": 0}, {"id": "182", "q": "000,4,7,9,C,G.20,2,6,8,A,E,G.42,4,8,C,E.56,A.60,3,8,D,G.80,3,5,8,B,D,G.A0,2,7,9,E,G.B4,C.C1,6,A,F.D4,8,C.E2,E.F0,6,8,A,G.G3,D;107,9.10,G.22,6,8,A,E.42,8,E.60,3,8,D,G.80,3,5,B,D,G.A0,8,G.C1,F.D8.F6,8,A.G3,D;207,9.27,9.48.73,D.D8.F7,9.G3,D;307,9.27,9.E8.G3,D;417,9.E8:DEUYBQDG94BKG93R7BZU8K89HR9CL4V300QK9DRQ4WRK4GVU8AYLQ883R7C8V0QLBS34EWRCGK04HAAEU3AVCQ9WKZBHEVS3G0CDCGBVEHHW0LEH", "hasFlower": 0, "hasSeason": 0}, {"id": "183", "q": "000,3,5,7,9,B,D,G.20,3,6,8,A,D,G.40,2,5,B,E,G.57,9.60,2,5,B,E,G.77,9.80,3,D,G.96,8,A.A2,4,C,E.B0,6,8,A,G.C3,D.D0,5,8,B,G.F0,2,5,B,E,G.G7,9.H0,3,D,G.I5,7,9,B;100,G.17,9.20,3,D,G.40,2,E,G.57,9.65,B.70,G.93,D.B0,G.C3,D.D5,8,B.E0,G.F2,E.G0,8,G.H3,D.I7,9;265,B.C3,D.D5,B:LIXHJAH1CCHUHLLKK1AXXFZCMKGA0GKHF3F80ZGL185U53M5LF8MACJFC35UC5II12XJG8M0IM023I20G8G12LAZZ5JKUIHAZFJXK8UJ10M3UXZ322", "hasFlower": 0, "hasSeason": 0}, {"id": "184", "q": "002,4,7,9,C,E.20,2,7,9,E,<PERSON><PERSON>35,B<PERSON>42,E<PERSON>50,6,8,A,<PERSON><PERSON>62,<PERSON><PERSON>70,5,7,9,B,<PERSON><PERSON>82,E.90,4,7,9,C,<PERSON><PERSON>A2,E.B5,7,9,B.C0,2,E,G.E2,5,7,9,B,E.F0,G.G2,6,A,E;102,7,9,E.27,9.35,B.42,E.50,G.62,7,9,E.75,B.93,D.A8.B2,5,B,E.C0,G.D2,E.E5,7,9,B.G2,E;207,9.27,9.51,F.68.C1,F.E6,A;307,9.27,9.51,F.C1,F;407,9;508:JGIETNX4MMGMPP0B54V63E5TV9FYPGYAJMUJ3EUI7V7KSVIPYF9R55CTBXX1U4H8K4YJ0N76KG6DV61U7YU1NXIK081SMYVNE15CEFRFMETAH5107U7D", "hasFlower": 0, "hasSeason": 0}, {"id": "185", "q": "003,7,9,D.10,5,B,G.22,E.30,4,8,C,<PERSON><PERSON>46,A<PERSON>52,8,<PERSON><PERSON>66,<PERSON><PERSON>72,8,E.80,4,C,G.92,8,E.A5,B.B0,2,7,9,E,G.D2,6,A,E.E8.F1,3,6,A,D,F.G8.H0,2,E,G.I4,6,8,A,C;103,7,9,D.10,5,B,G.30,8,G.46,A.58.72,E.80,4,8,C,G.A5,8,B.B0,G.C2,E.E7,9.F3,D.G8.H0,G.I5,8,B;203,7,9,D.10,G.30,8,G.58.80,4,8,C,G.B0,G.E8.G8.H0,G:2WC8NWIYI91ZLD8QZ98THBGUH4M7FBF1TVV3JZ8P71J74DW1U3GW937Q7DLCHT9M3WQMW1C8IHC2YU5YLG8Q5BFHTIBIM4C4ZDN9VMYGGFIG1LUCQH7QM9PV", "hasFlower": 0, "hasSeason": 0}, {"id": "186", "q": "000,3,5,7,9,B,D,G.21,3,5,7,9,B,D,F.43,5,8,B,D.60,2,5,7,9,B,E,G.80,3,5,8,B,D,G.A0,4,6,8,A,C,G.B2,E.C0,4,6,8,A,C,G.E0,3,5,7,9,B,D,G.G0,2,7,9,E,G.H5,B.I3,8,D.J5,B.K0,3,D,G.L5,7,9,B.M0,2,E,G;100,7,9,G.22,8,E.58.62,E.78.80,3,D,G.95,B.B6,A.D4,6,A,C.F0,7,9,G.G2,E.H8.I4,C.L0,8,G:JKQG0Q90GS6TX96UEKG263427EQ2P9P3836V8JPIPSQHXRXY6VAG9J6CUEIACK40873PS9YTCAYIC9GPCLG2R4L7ATL1RYXLRJT7Y4E0CUQS4K2YU8H12Q4I", "hasFlower": 0, "hasSeason": 0}, {"id": "187", "q": "000,3,5,7,9,B,D,G.23,5,7,9,B,D.30,G.42,4,7,9,C,E.64,8,C.70,2,6,A,E,G.84,C.91,6,8,A,F.A3,D.B0,G.C2,5,7,9,B,E.E3,5,7,9,B,D.F0,G.G5,B.H3,7,9,D.I0,G;104,6,8,A,C.23,6,A,D.30,8,G.44,C.58.70,3,5,B,D,G.91,F.A3,D.B0,G.C5,7,9,B.E3,5,8,B,D.F0,G.H3,7,9,D.I0,G;206,8,A.48.73,D.A3,D.C8.D5,B.H7,9;348.H7,9;4H8;5H8:AUIVNAKT5DNBEK1197BMX36YGB8CMM6UZ41T4IMEINYACIDMLET76C2E18ATLEZU65M7Z9BK8YUH9W5JNVDJDBUHG6VGCB38N1DX315V8K67W8Z935NU5CTYDE2GCT", "hasFlower": 0, "hasSeason": 0}, {"id": "188", "q": "000,2,4,6,8,A,C,E.21,4,6,8,A,D.40,3,5,7,9,B,E.60,2,4,7,A,C,E.81,3,7,B,D.95,9.A0,2,7,C,E.B4,A.C0,2,C,E<PERSON>D4,7,A.E2,C.F0,4,7,A,E.G2,C.H0,4,6,8,A,E.I2,C.J4,6,8,A.K0,E;100,3,6,8,B,E.25,7,9.40,5,9,E.57.61,D.77.92,5,9,C.B2,C.D7.E2,<PERSON><PERSON>G0,7,E<PERSON>H5,9.J5,7,9;200,3,B,E.57.A2,C.G7;300,E:IA5NI2BN8280F93Y0N4NMZ68N3M069O0K215T40IHJTKTMPTPYTYM89AOHFPEXDFP2601GK1I8O4KZQIHSU4KKD6QUOUUF5OSYGJ2H8BXX2NIOE19TX5", "hasFlower": 0, "hasSeason": 0}, {"id": "189", "q": "000,2,5,7,9,C,E.20,2,4,7,A,C,E.41,5,7,9,D.63,5,9,B.70,7,E.82,5,9,C.90,7,E.A2,C.B5,7,9.C0,E.D3,5,9,B.E0,E<PERSON>F5,7,9.G1,D.H3,7,B.I1,D;100,5,7,9,E.12,C.20,4,A,E.37.41,5,9,D.65,9.86,8.B6,8.D0,5,9,E.F5,9.G7.H3,B;207.12,C.20,E.41,D.65,9.D0,5,9,E.H3,B;320,E.65,9.D5,9:0Y3J1R4ABHRT5NWWBNTIIKT5HJE8NBFEGORUO0FUJYJCRGUKII00LKGZHLURUUKLF3A3TGK8HGNAR3J4WLKF3WOEB10ZTNGLO03HANTHJECL", "hasFlower": 0, "hasSeason": 0}, {"id": "190", "q": "000,2,4,7,A,C,E.20,2,5,7,9,C,E.42,4,6,8,A,C.60,2,4,6,8,A,C,E.80,4,7,A,E.A1,3,6,8,B,D.C0,4,6,8,A,E.D2,C.E4,6,8,A.F2,C.G4,6,8,A.H0,2,C,E.I4,7,A;103,7,B.11,D.25,9.37.42,4,A,C.56,8.60,3,B,E.77.A2,C.B6,8.C0,E.D3,6,8,B.F2,4,6,8,A,C.H0,4,A,E.I7;203,B.11,D.42,4,A,C.F2,4,6,8,A,C.H4,A:RX3AX0UJAXHE0T8V0MA0RKR43UVXPY4SFOR0TEBUM6OMFOKALEM9SKQJOBNPKEHBME7O5ABU4KPL18B7FRD6F4YQE82MPBNPRTS1DQTAPN6SKD9ODQ85N026", "hasFlower": 0, "hasSeason": 0}, {"id": "191", "q": "000,2,5,7,9,C,E.20,2,C,E.34,6,8,A.41,D.54,6,8,A.60,2,C,E.75,9.80,3,7,B,E<PERSON>95,9.A0,E<PERSON>B3,7,B.C0,5,9,E.D2,C<PERSON>E4,6,8,A.F1,D.G3,5,7,9,B.H0,E.I3,5,7,9,B;101,5,9,D.20,E.34,A.41,7,D.55,9.60,2,C,E.75,9.80,E.95,9.B0,E<PERSON>C5,9.D2,C.E5,9.F1,7,D.G3,5,9,B.H0,E.I3,6,8,B;251,5,9,D.85,9.E5,9.I7:Q0RXHEGV3QWX3OWXHS4H77CJL4LHCJQL2RY20OWMEWF0EQX3LRORWFXBJ4SEBB7TG7WQJ7N3ELLSGMGOYH7SGRY40X0NE4PQS4OYNCBRJTOGVNPC0SHJ", "hasFlower": 0, "hasSeason": 0}, {"id": "192", "q": "000,2,5,7,9,C,E.20,4,6,8,A,E.32,C.40,5,9,E.60,2,4,A,C,E.77.81,4,A,D.97.A2,4,A,C.B6,8.C1,4,A,D.D6,8.E0,4,A,<PERSON><PERSON>F2,C.G0,4,6,8,A,E.H2,C.I0,5,7,9,E.K2,6,8,C;100,6,8,E.24,7,A.31,D.45,9.50,E.62,4,A,C.A4,7,A.C1,D.D6,8.E4,A<PERSON>F0,2,C,<PERSON><PERSON>G5,7,9.I7.K6,8;206,8.27.A7.D6,8.E4,A.I7.K7;3D6,8;4D6,8:8K3GG1U7UU265Q0U71QG3TXLK9T6VV3T5X60TP32TVKP7769PX32LV6PI80U2QKFIGXPL9L011L9T1X0GVVX7QG623FFI78UQKFK2F819LQ9F8PIII08", "hasFlower": 0, "hasSeason": 0}, {"id": "193", "q": "000,2,4,6,8,A,C,E.20,2,4,7,A,C,E.40,2,4,6,8,A,C,E.60,2,4,7,A,C,E.80,2,4,A,C,E.96,8.A1,3,B,D.C2,4,6,8,A,C.D0,E.E3,5,7,9,B.F0,E.G2,5,7,9,C.H0,E.I3,6,8,B.J0,E.K2,6,8,C;102,C.21,4,A,D.37.50,2,C,E.67.92,C.C2,5,9,C.E5,9.F7.G0,2,C,E.I0,7,E.K2,6,8,C;250,E.67.C5,9.F7.G0,E;367.F7:R2ZLA0HZ0UAIVJ1N4FCQJXWKRR0FYGA7GKG9H6NXUT3NCFKARK6P9J0F8ZXJCLWC6VGR38ENI11Y6EEAZKXFEJPZ484JXX6A46K8Q1U8TF78NPZP2UNR", "hasFlower": 0, "hasSeason": 0}, {"id": "194", "q": "000,2,4,7,A,C,E.20,2,5,7,9,C,E.40,2,5,7,9,C,E.62,7,C.70,4,A,E.82,6,8,C.90,4,A,E.A2,6,8,C.C0,2,4,A,C,E.D6,8.E0,E.F2,5,7,9,C.G0,E;101,7,D.20,E.32,6,8,C.52,7,C.70,4,7,A,E.82,C.95,9.A7.C2,C.D6,8.E0,E;201,7,D.32,7,C.57.82,C.D6,8;382,C;482,C:9QXI5IFYMCKJA9JP6WVIXCNKXL2X4L242YTZR6I7JCRK1XK9WYFJSUH4ESE676F7CC9MQ6LS5SE7VMOLUEHYJXMZ1TAFPN42CJ6O", "hasFlower": 0, "hasSeason": 0}, {"id": "195", "q": "000,2,4,A,C,E.16,8.20,4,A,E.32,6,8,C.40,E<PERSON>53,6,8,B.70,7,E<PERSON>82,C.94,6,8,A.B3,5,7,9,B.C1,D.D3,7,B.E0,E<PERSON>F2,5,7,9,C.G0,E.H2,5,7,9,C.J0,4,7,A,E.K2,C;100,3,B,E.17.37.56,8.70,7,E.94,6,8,A.B4,7,A.C1,D.E0,7,E.G0,6,8,E.H2,C.I7;203,B.37.77.94,7,A.B7.C1,D.E7.H7;394,A;494,A:MYVD9U7M3ZSCLYAK3U9AHSA5GU8HKJJWE7Y3A7UVWSDL784D2ZWZ83WDJJ2DPV46HHDUE9SGS3E9G18Y6HY4JS8V5J93AH981GYEZC4UWAPW", "hasFlower": 0, "hasSeason": 0}, {"id": "196", "q": "000,6,8,A,G.13,D.20,6,8,A,G.32,E<PERSON>44,8,C.51,F.63,6,8,A,D.70,G.82,4,6,8,A,C,E.A0,2,4,6,A,C,E,G.B8.C1,3,D,F.D5,8,B.E1,3,D,F.F6,8,A.G0,3,D,G;100,G.16,8,A.32,8,E.44,C.51,F.67,9.73,D.85,7,9,B.92,E.A4,C.B8.C1,F.D4,8,C.E1,F.F8.G0,G;217,9.32,E.51,F.88.C1,8,F.F8.G0,G;317,9;418;518:3NDQMPARVHS0AODYVF1H869WT4SAPQBHCBHNB568CQI4XXMW5C1T68549IO7MA3Q7TNNDY1I0DC1I7S0BFM2WYTJ55YTJTY564B8260WB7WYS6WR", "hasFlower": 0, "hasSeason": 0}, {"id": "197", "q": "000,2,5,7,9,B,E,G.20,4,6,8,A,C,G.32,E.44,8,C.51,6,A,F.68.70,3,D,G.85,7,9,B.90,3,D,G.A5,7,9,B.B0,3,D,G.C7,9.D0,2,E,G.E4,6,A,C.F1,8,F.G3,5,B,D.H0,7,9,G.I5,B;100,2,5,B,E,G.18.25,B.32,E.48.51,F.73,8,D.86,A.93,8,D.A5,B.B7,9.D0,G.F1,5,8,B,F.G3,D.H5,7,9,B;202,E.48.51,F.87,9.A8.F8.G5,B;387,9;488;588:ZML1UVONZCK4E46SBRPFW8H6AUZM0UNEBF15BQ6VRNWE8ULBM98O6Z9TSWE2BICIL4A6LS2RJVW0DSRTPE5NK95SLAWUBAN8VAUC6JSE9AMFH58NF5DW54QC8L", "hasFlower": 0, "hasSeason": 0}, {"id": "198", "q": "000,2,5,7,9,C,E.20,2,7,C,E.35,9.40,2,7,C,<PERSON><PERSON>54,<PERSON><PERSON>60,7,<PERSON><PERSON>74,A.81,6,8,D.93,B.A1,6,8,D.C0,4,6,8,A,E.E0,2,4,6,8,A,C,E.G0,2,5,7,9,C,E.I0,3,5,9,B,E;102,C.20,2,C,E.35,7,9.42,C.50,E.67.86,8.91,D.A6,8.C0,E.D7.E0,2,5,9,C,E.G2,5,7,9,C.H0,E.I3,5,9,B;202,C.22,C.35,9.77.E1,D.G5,9:FZ0K9ZLD84A3WK7M8W0LY8OY4AYUUUW1HLWYZTDU79ZOXDABF9UZXBD0OQXYKH73W9FL78HHVMOX807V9DQ0YOTF2LHQK2QOXZ4QLHD4A70XU1W8Q9", "hasFlower": 0, "hasSeason": 0}, {"id": "199", "q": "000,3,6,8,B,E<PERSON>21,5,9,D.33,B.40,5,7,9,E<PERSON>53,B<PERSON>61,D<PERSON>73,5,9,B.80,7,E.93,5,9,B.A0,7,E.B2,5,9,C.C0,E.D3,B.E0,5,7,9,E.G0,3,B,E.H5,9.I0,2,7,C,E;103,6,8,B.35,9.40,E.61,D.80,3,5,9,B,E.B0,2,5,9,C,E.D0,E.E6,8.F0,E.I0,7,E;283,B.C0,E.E6,8;3C0,E.E6,8;4E6,8;5E6,8:V8OKMJURVVODW8KM1OVDOMP0POMRRUWOTTH86IMG1VGHZ1PHJ8PRPDTXHX0UUCI10JJK8CDDVKP6GXXHWZM0GRHTWRDIJIXX8CUWCUJW", "hasFlower": 0, "hasSeason": 0}, {"id": "200", "q": "000,6,8,A,G.12,4,C,E.20,7,9,G.33,5,B,D.41,7,9,F.54,C.60,6,A,G.72,8,E.84,6,A,C.91,8,F.A3,D.B0,7,9,G.C3,5,B,D.D1,8,F.E3,5,B,D.F0,7,9,G.G2,4,C,E.H7,9.I0,3,5,B,D,G.J7,9.K0,2,4,C,E,G;100,6,8,A,G.14,C.20,7,9,G.33,5,B,D.66,A.78.85,B.A8.B0,G.C5,B.D1,3,D,F.E8.F3,D.G8.H3,D.I5,8,B.J0,3,D,G:DGKOGGPHOZUV9U7NLW1BZY8BY0APB0044A3CB216H81LW17AVV9068SB8C7VY5U88V6R355702DLYMP23CY34SDPM34AS3CSBFRLY5G2KV06DUPNP5HC5HCF", "hasFlower": 0, "hasSeason": 0}]