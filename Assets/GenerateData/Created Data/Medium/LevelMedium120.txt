[{"id": "1", "q": "000,2,5,7,A,C.21,4,8,B.36.41,B.53,9.60,5,7,C.80,2,4,8,A,C.96.A0,2,A,C.B4,8.C0,2,A,C;101,5,7,B.21,B.41,B.53,9.60,5,7,C.83,9.90,C.B1,3,9,B;205,7.60,5,7,C.B3,9:R2IYVZWW2SXXOS0XZZCBJS2JBV2R0CRR9JOBIWVIYJ911GVWC1GY1ZX00IG9BGYC9S", "hasFlower": 0, "hasSeason": 0}, {"id": "2", "q": "000,3,7,A.15.21,3,7,9.35.42,8.50,4,6,A.71,3,5,7,9.90,3,5,7,A.B2,5,8.C0,A<PERSON>D3,7.E0,5,A;100,A.13,5,7.32,5,8.55.72,4,6,8.93,5,7.B2,5,8.D0,3,7,A.E5;213,7.55.73,7.93,7.A5.B2,8:22BM8JC0YVEWR0RQPVG48UXJW5TGE1KRDF0T13ABNKNWRTP5Q0ECXTAY48D8HXWMHEFX3U", "hasFlower": 0, "hasSeason": 0}, {"id": "3", "q": "000,3,7,A.15.20,2,8,A.34,6.40,A.52,4,6,8.71,5,9.90,4,6,A.B0,2,4,6,8,A;103,7.15.20,2,8,A.40,5,A.52,8.65.71,9.85.90,A.B0,2,4,6,8,A;203,7.30,A.55.85.90,A:CKDSYR9NIVTFN8Q4EEKO1L48SAI4PW2WZU0LDV4A7UO1082CAPF9MR7QZT8MAY", "hasFlower": 0, "hasSeason": 0}, {"id": "4", "q": "000,2,4,6,8.21,4,7.42,6.50,8.63,5.70,8.82,6.A0,2,4,6,8.C0,2,4,6,8;100,2,6,8.21,7.42,6.50,8.63,5.92,6.B0,8;202,6.21,7.42,6.63,5.92,6;302,6.21,7.63,5;421,7:173UFTDGBWZTGGQVFF739WVGUXBVQ9B97J1W3WVXUJ391SB1XQTD5TUQ7ZS5XF", "hasFlower": 0, "hasSeason": 0}, {"id": "5", "q": "001,3,6,9,B.20,2,4,6,8,A,C.40,2,4,6,8,A,C.60,2,4,6,8,A,C.80,3,6,9,C.A0,3,9,C.B5,7.C0,2,A,C;101,B.16.43,5,7,9.60,2,4,8,A,C.86.93,9.B5,7.C0,C;243,6,9.93,9.B5,7:QBV6G9JAPL5Z3CCYAU2SSKHQLPZW35JGRMYKHEA2GRW869GRBH9SRE0VVEM9HUES0A8V", "hasFlower": 0, "hasSeason": 0}, {"id": "6", "q": "000,2,4,8,A,C.16.20,3,9,C.35,7.41,3,9,B.55,7.62,A<PERSON>74,8.81,B.93,5,7,9.A0,C.B3,9.C0,5,7,C;100,C.20,3,9,C.42,4,6,8,A.93,5,7,9.B0,3,9,C;220,C.43,6,9.93,6,9.B0,C:I7FRPFZDNAZLFG9NQBSS4EIP3IYCGIYLQZRDXB9ZGBQHSA7J3UXB9J9SCFQH5E5UG4", "hasFlower": 0, "hasSeason": 0}, {"id": "7", "q": "000,2,4,6,8,A.21,3,7,9.35.40,3,7,A.55.60,A.75.81,9.93,7.A0,5,A.B2,8.C0,4,6,A;101,9.13,7.21,9.33,5,7.81,9.93,7.A0,5,A.B2,8.C5;201,9.13,7.34,6.B2,5,8;313,7.34,6:VBQJVZNO2WS22V5ZMX5NROANQ4BAPZNSKPJEXDZKUF6V20M0SA6QWRFXDA4EUQSX", "hasFlower": 0, "hasSeason": 0}, {"id": "8", "q": "000,A.13,7.31,3,5,7,9.52,8.60,4,6,A.81,5,9.A2,8.B5.C1,3,7,9.E0,3,7,A;100,A.23,7.42,8.60,4,6,A.81,9.A2,8.B5.C3,7.E0,A;223,7.42,8.81,9.A2,8;342,8.81,9:51POJJ814V847O1PINI9IJV5XV29FUDT9XX24X31H33D907UR4JNHRFT0VI3", "hasFlower": 0, "hasSeason": 0}, {"id": "9", "q": "000,4,6,8,C.20,3,5,7,9,C.40,2,5,7,A,C.60,C.73,9.86.92,A.A0,4,8,C.C0,2,4,6,8,A,C.E1,3,6,9,B.G0,4,6,8,C;100,5,7,C.20,3,5,7,9,C.40,2,5,7,A,C.86.A0,4,8,C.C3,6,9.E1,6,B.G4,8;205,7.10,C.26.40,C.A0,C.C6:71LP9O71ZI8C84WPTWBQH4S3Q1ZLHX0IQNZNEQCEMP8KZ0JSVEG4KORTGHM3H14GGPL9RBV9NE9X8ONJLOKK", "hasFlower": 0, "hasSeason": 0}, {"id": "10", "q": "000,4,6,A.12,8.20,4,6,A.32,8.44,6.50,2,8,A.64,6.71,9.85.90,2,8,A.A4,6.B1,9.C3,5,7.D0,A<PERSON>E2,4,6,8;100,5,A.12,8.24,6.32,8.44,6.50,2,8,A.64,6.71,9.91,5,9.B1,4,6,9.D4,6.E2,8:GH6XNIEUA88FMODOKZA7CXMJTVGC1FVHKIWT66S3YOUFYNUFJ1TAYXC7OU3ZTSDYAWCEX6", "hasFlower": 0, "hasSeason": 0}, {"id": "11", "q": "000,2,4,7,A,C,E.20,2,6,8,C,E.34,A<PERSON>40,6,8,E<PERSON>53,<PERSON><PERSON>66,8.70,3,B,E<PERSON>85,7,9.92,C.A5,7,9.B0,3,B,E.C5,7,9.D0,2,C,E.E4,7,A;103,7,B.10,E.22,C.34,6,8,A.63,7,B.86,8.B4,6,8,A.D1,D<PERSON>E4,7,A;207.63,B.86,8.B6,8.E7:1XDXVPP2EUU6CVDIBNI8DD7NTYRBTZVETM1OBYJHWU67PT8RO3FZ9LYW7JSSLHLZW7LY32IBWPICFVUZ9M", "hasFlower": 0, "hasSeason": 0}, {"id": "12", "q": "000,3,9,C.15,7.21,3,9,B.36.40,3,9,C<PERSON>55,7.60,2,A,C<PERSON>74,6,8.80,2,A,C.A0,2,4,6,8,A,C.C0,2,4,6,8,A,C.E2,6,A.F4,8.G0,6,C;100,C.36.43,9.56.72,4,8,A.91,B.A4,8.C6.D2,A.E6.F4,8.G0,C;243,9.56.72,A.C6.D2,A:W8T2IRD6ERQ8NDEPOT0N5FNQ6UV7TBQPOF2EHUVNIUD9G46H9L2EGOD4C5C6C2QU0ROL9IWR5CT5I9B7", "hasFlower": 0, "hasSeason": 0}, {"id": "13", "q": "000,2,4,6,8,A,C.20,2,5,7,A,C.40,2,4,6,8,A,C.61,3,9,B.81,3,9,B.A0,4,6,8,C.B2,A<PERSON>C4,6,8.D0,2,A,C.E5,7;100,3,5,7,9,C.20,6,C.40,2,6,A,C.62,A.81,B.A4,6,8.B2,A<PERSON>C4,6,8.D0,<PERSON><PERSON>E5,7:A5J7K7IGKSMKTPXMWSLFX67S3F8WX8MLHA84VP0H05PS78IGNG3G1NLN4X99LK6APOJVMTNA1O", "hasFlower": 0, "hasSeason": 0}, {"id": "14", "q": "001,4,6,8,B.24,6,8.30,2,A,C<PERSON>44,8.56.60,2,4,8,A,C.76.81,3,9,B.95,7.A2,A.B0,4,6,8,C.D0,3,5,7,9,C.F4,6,8.G1,B;101,4,6,8,B.25,7.33,9.55,7.62,A.76.82,A.95,7.B5,7.C0,C.D5,7.F5,7;204,8.26.C0,C.D5,7.F6:9I3SCVFIMDC0KUFVRSDREFUJDA4A0UKAYAEV3JFCI9043MPPYDO4SPVRJUKP9C3RMOOYEM4ISOYK0J9E", "hasFlower": 0, "hasSeason": 0}, {"id": "15", "q": "000,2,4,6,8,A,C.20,2,5,7,A,C.40,3,5,7,9,C.60,3,5,7,9,C.80,2,4,6,8,A,C.A0,2,4,6,8,A,C.C0,2,4,6,8,A,C.E0,2,5,7,A,C.G1,3,5,7,9,B;104,8.10,2,A,C.30,C.60,C.80,C.B0,C.D0,2,A,C.G1,3,9,B:BVGCGV8ECXJZT7H71BLRL8ZP1IS1C3YBY8JVPIISUOOULTOT1P8GRPXZXGLCUI77YBHVTXHUJZ3EJYHO", "hasFlower": 0, "hasSeason": 0}, {"id": "16", "q": "001,3,5,7,9,B.20,2,4,8,A,C.36.40,2,4,8,A,C.56.60,2,4,8,A,C.76.80,2,4,8,A,C.A0,2,5,7,A,C.C0,2,4,6,8,A,C.E0,2,5,7,A,C;111,B.40,5,7,C.61,B.91,B.B0,5,7,C.D1,B;240,C.B0,C.D1,B;340,C.B0,C:EZ8FEQ5NBOAZYEQNSOA8GNUHYFBPHTL19GMX5TIMUHIMJAHUXRR8L33YM3AXBBUGX89YPTENGTJ3S1", "hasFlower": 0, "hasSeason": 0}, {"id": "17", "q": "000,2,5,7,9,C,E.20,5,9,E<PERSON>32,<PERSON><PERSON>44,<PERSON><PERSON>51,6,8,D.63,B.70,5,7,9,E.90,2,4,6,8,A,C,E.B1,3,B,D.D0,5,9,E.E2,7,C;101,5,9,D.20,E.32,C.51,6,8,D.63,B.76,8.90,3,6,8,B,E.D0,E.E7:PSDUGCOKPD6TNAXHD0LJHT44NOP4K1PCRGX6SBJWB8LG4UBXH8W3WWNSEA1NSEG3HBXRD0", "hasFlower": 0, "hasSeason": 0}, {"id": "18", "q": "001,4,7,A,D.20,3,5,9,B,E.37.40,3,B,E<PERSON>57.64,A<PERSON>70,6,8,E<PERSON>94,6,8,A.A0,<PERSON><PERSON>B3,<PERSON><PERSON>C0,6,8,<PERSON><PERSON>D3,<PERSON><PERSON>E0,5,7,9,E;114,A.20,E.37.40,3,B,E.67.87.A0,E<PERSON>B3,B.C7.D0,3,B,E.E5,9;230,E.A0,E.D3,B<PERSON>E5,9;3A0,E.D3,B.E5,9;4D3,B:G9UE58SGZ3JHWCAVA3Z91NMQ0SEJVIZMGC8NWSA9NAUHV550UF5FMNGUZFHH81ROR70QO0SO9MOI87VF", "hasFlower": 0, "hasSeason": 0}, {"id": "19", "q": "000,3,5,7,A.20,2,5,8,A.40,2,8,A<PERSON>54,6.70,2,4,6,8,A<PERSON>93,7.A1,5,9.C1,4,6,9.E0,3,5,7,A;100,4,6,A.25.30,A.42,8.54,6.71,3,7,9.A1,5,9.C4,6.E0,3,5,7,A;215.30,A.54,6.A1,9.D4,6;330,A.54,6;454,6:YIKNNS0AJTD0EBJRMJR27CRSDSTWVKT0IE7UV55UMTA2BDU2WRJUVICD5Y0KSAN7N275AWBVBIWK", "hasFlower": 0, "hasSeason": 0}, {"id": "20", "q": "000,2,4,6,8,A,C.21,4,6,8,B.41,3,5,7,9,B.60,2,4,6,8,A,C.81,3,9,B.95,7.A0,C.B2,4,6,8,A.C0,C.D2,4,6,8,A.E0,C;100,3,6,9,C.24,8.36.51,5,7,B.72,A.A5,7.C0,2,4,6,8,A,C.E0,C:K9QEL7MVC9VFJTGI9M9VOTIULFOGGKYI27FLQ2OKE7MEMYFKTC2VJ72CQUCLYJOQTJGUUIYE", "hasFlower": 0, "hasSeason": 0}, {"id": "21", "q": "000,2,4,6,8,A.23,5,7.30,A.42,4,6,8.50,A.63,5,7.70,<PERSON><PERSON>82,5,8.90,A<PERSON>A2,8.B4,6.C0,2,8,A.D4,6.E0,2,8,A;104,6.23,7.35.41,9.53,7.60,5,A.81,9.A2,8.B4,6.C1,9.D3,7;223,7.35.B4,6.D3,7:Y27D2QP4322TVN4EFK04V5PGXPEDF9CKNXIRTRA1WDWQHA9H1EDE34Z6GP5QZ76C99Q0IY", "hasFlower": 0, "hasSeason": 0}, {"id": "22", "q": "000,2,4,6,8,A,C.21,4,6,8,B.40,2,4,6,8,A,C.60,2,4,6,8,A,C.82,4,6,8,A.90,C.A3,5,7,9.B1,B<PERSON>C3,5,7,9;100,2,5,7,A,C.25,7.40,C.53,6,9.61,B.73,6,9.90,C.A5,7.B3,9.C6:PR8BZ4FBYGOXBPTLKGHFZZH33XFCZL820KPJJ9E4Y2GUU1DMLB0TO1RGMEDL96CMP6MF", "hasFlower": 0, "hasSeason": 0}, {"id": "23", "q": "000,3,6,8,B,E.20,2,4,6,8,A,C,E.41,3,6,8,B,D.60,2,4,7,A,C,E.80,3,6,8,B,E.A0,2,4,6,8,A,C,E.C3,5,7,9,B.D0,E.E4,6,8,A;110,7,E.41,6,8,D.62,C.83,6,8,B.A0,6,8,E.C5,9.E7;210,E.41,D.62,C.86,8:OB5Y2HZAWGDALYSG6EB8D1X10G6RLLYR51EAWHJIO30W9JYFJRG0FTX90J7LIS38ZAZZEORSEO2WT1S7", "hasFlower": 0, "hasSeason": 0}, {"id": "24", "q": "003,5,7.10,A.22,4,6,8.30,A.42,4,6,8.50,A.62,4,6,8.70,A.82,4,6,8.90,A.A3,5,7.C0,2,5,8,A.E0,2,4,6,8,A;103,7.10,A.25.32,8.45.70,2,8,A.A4,6.C2,8.D5.E0,A;225.32,8.A4,6.C2,8.D5:0XSN9ITZ8UNJPR699FT4MCCJMHSUU8HC0F6ZHWC46XJTIRQU6HZFLLBSWPSM22FZ9BTJQM", "hasFlower": 0, "hasSeason": 0}, {"id": "25", "q": "002,5,7,A.10,C.23,5,7,9.30,C.45,7.50,2,A,C<PERSON>65,7.70,2,A,C.84,6,8.90,2,A,C.A5,7.B1,B<PERSON>C4,6,8.D0,C<PERSON>E2,4,8,A;102,A.15,7.36.52,5,7,A.70,5,7,C.A5,7.C6.D4,8;226.52,6,A.76.A6;352,A;452,A:V8S13PDDXJAQ8FOL59CFR2VAVLUDVF8XSEPWPCUXEH2PHWS15W5ALXSWJUDFQ5E9E3LRA8OU", "hasFlower": 0, "hasSeason": 0}, {"id": "26", "q": "000,3,5,7,9,C.20,2,4,6,8,A,C.41,3,5,7,9,B.60,3,5,7,9,C.80,2,4,6,8,A,C.A1,4,6,8,B.C0,2,6,A,C.D4,8.E1,6,B;100,3,5,7,9,C.21,3,9,B.36.44,8.56.64,8.70,6,C.82,4,8,A.A4,6,8.C0,2,6,A,C.E1,B:ZMCT7OOA78FIA9OMZCR3E2CVBC5TF6I6H9QGTGRXEES5328ZOSVGZRJJFHHEXAF3PAMMGHBPSVRTVS3Q", "hasFlower": 0, "hasSeason": 0}, {"id": "27", "q": "000,2,4,6,8,A,C.20,3,5,7,9,C.40,2,5,7,A,C.60,4,8,C.80,2,4,6,8,A,C.A0,3,5,7,9,C.C0,2,4,8,A,C.E0,2,4,6,8,A,C.G0,2,4,8,A,C;102,4,6,8,A.24,6,8.41,6,B.60,4,8,C.80,3,5,7,9,C.A0,C.B3,9.C1,B.E2,5,7,A.G1,B:322LZVPJ76AUUD17F7VKLO9PRCOZQN5HKL92L9KIY7MFM905A0IM3GHCPJ1VTHAWQMY2Z6RTHDKRVYWG4PYRA4ZN", "hasFlower": 0, "hasSeason": 0}, {"id": "28", "q": "000,2,4,6,8,A,C.21,4,6,8,B.43,5,7,9.50,C.62,4,6,8,A.81,3,9,B.95,7.A0,C.B3,5,7,9.C0,C.D2,4,6,8,A.E0,C;100,2,5,7,A,C.24,6,8.44,6,8.50,C.66.72,A.96.A0,C.B4,6,8.D3,6,9.E0,C:0CJ7NSA3EY61SHJGK0AKO4HAWMCVKTOQPTPQZWM33YOKZ41OQZM7G1VY3QVYE170V67NAMZ0", "hasFlower": 0, "hasSeason": 0}, {"id": "29", "q": "000,2,4,6,8,A,C,E.20,3,5,9,B,E.37.41,3,B,D.55,7,9.60,3,B,E.75,7,9.80,2,C,E.95,7,9.A0,3,B,E.B6,8.C0,3,B,E.D5,9.E0,2,C,E;101,5,9,D.13,B.20,5,9,E.33,B.47.53,5,9,B.76,8.95,9.A3,B.B7.C3,B.D0,5,9,E.E2,C:5AAC3HEJDNEP6H1MXANNCQKT38C6HDGQI5329N1EM6S2KP6GKJG3SIJ88952TSPTIJCG1APD82XDT51EKISH", "hasFlower": 0, "hasSeason": 0}, {"id": "30", "q": "000,2,5,8,A.22,5,8.30,A.43,5,7.50,A<PERSON>62,8.70,4,6,A.90,2,8,A.A4,6.B0,A<PERSON>C2,4,6,8.E0,2,8,A;100,5,A.25.40,3,7,A.62,8.74,6.92,8.A0,4,6,A.C4,6;200,5,A.25.74,6.A4,6.C4,6;305.74,6.C4,6:JC35INDK0NT0M6QVK181WD7VFKN8EJB12QYIM2WK8HQZNEO3HZ8H7B446VFHV1TO3MQ3CM5Y", "hasFlower": 0, "hasSeason": 0}, {"id": "31", "q": "000,2,4,6,8,A.20,2,4,6,8,A.40,2,4,6,8,A.61,3,5,7,9.82,4,6,8.A1,4,6,9.C1,3,5,7,9.E0,3,5,7,A;104,6.21,9.34,6.40,A.61,9.82,8.A1,9.B4,6.C1,9.E4,6;205.40,A.A1,9.C1,9.E4,6:5IZPESTVJMPYJEBCIGCXVO5IVCWGHM1I5GEAOOJ5WOZSHWFM7BZCTFVA7S1HGMXYWSHEJZ", "hasFlower": 0, "hasSeason": 0}, {"id": "32", "q": "000,2,6,A,C.14,8.20,6,C.32,4,8,A.46.50,2,A,C.64,6,8.70,C.83,5,7,9.90,C.A2,5,7,A.C0,2,4,6,8,A,C.E0,2,4,6,8,A,C;106.10,C.25,7.32,A.50,C.64,8.76.83,9.95,7.B2,A.D1,5,7,B:LOVNCUCLQULBYRFBCFD9AUUN70A0PRFGL7Y9ROD2CW7X2FQG1VGP9GN7VXJJYDRW1NDV9Y", "hasFlower": 0, "hasSeason": 0}, {"id": "33", "q": "001,4,6,8,B.20,2,5,7,A,C.42,4,6,8,A.60,2,5,7,A,C.80,3,6,9,C.A1,4,8,B.B6.C0,2,4,8,A,C.E0,3,5,7,9,C;111,5,7,B.32,6,A.44,8.60,2,6,A,C.A4,8.C2,4,8,A.E3,5,7,9;211,B.60,C.C3,9.E3,9:NDXZMDIOKGA31YIEG7MMKK1X7RRY3Z2GTL135R2I7XTDKL5ING7ZOOEZMX6A55Y31LRA6EOYDELA", "hasFlower": 0, "hasSeason": 0}, {"id": "34", "q": "000,2,6,A,C.14,8.20,6,C.32,4,8,A.46.52,A.60,4,8,C.82,5,7,A.A0,2,4,6,8,A,C.C0,3,5,7,9,C;100,2,A,C.16.20,4,8,C.32,A.85,7.A1,5,7,B.B3,9.C0,6,C;201,B.24,8.32,A.C0,C:3O2QLTA7ZNQ313OL7A4N1XL9K4ZA2C27TXDNFO2AWDT3NCFC4WFZCWZ94D7TWFOQDLQK", "hasFlower": 0, "hasSeason": 0}, {"id": "35", "q": "000,2,4,8,A,C.16.20,3,9,C.35,7.40,2,A,C<PERSON>54,6,8.60,2,A,C.74,8.86.90,2,A,C.A4,6,8.B0,C.C2,5,7,A;101,B.16.20,3,9,C.36.40,C.55,7.62,A.74,8.90,C.A4,6,8.B0,C<PERSON>C5,7;201,B.30,6,C.56.90,C.A6:B8PJ5S4CM4RBPFGXWR8VUPR65AG954EJUE1XOO14C9NFTR1SN6NF1YP68JMWT68AJYY05NF0VY", "hasFlower": 0, "hasSeason": 0}, {"id": "36", "q": "000,2,4,6,8,A,C.20,5,7,C.32,A.40,4,6,8,C<PERSON>52,A<PERSON>60,5,7,C.73,9.80,5,7,C.92,A.A4,6,8.B0,2,A,C.C4,6,8.D0,C<PERSON>E2,4,6,8,A;102,A.15,7.20,C.42,6,A.50,C<PERSON>66.73,9.85,7.B2,6,A<PERSON>C0,C<PERSON>E5,7:RACM6CI1LHB8EXYXXYCOOEU1II1JUB85BOMTJQ81RQ5MA0IMO5600CHPLPTX85L0EEBLRRPP", "hasFlower": 0, "hasSeason": 0}, {"id": "37", "q": "004,6,8.10,C.25,7.32,A.40,4,6,8,C<PERSON>52,A<PERSON>65,7.71,B.83,6,9.91,B.B1,5,7,B.D0,4,6,8,C<PERSON>E2,<PERSON><PERSON>F5,7.G0,3,9,C;105,7.26.32,A.40,5,7,C.65,7.71,B.91,B.B1,5,7,B.D0,5,7,C.E2,A.F6.G3,9;205,7.55,7.81,B.B1,B.C5,7:Z19ZRMSBNLP7G6TRWGABHB9UMZ3ZN6WCAYEBKPKCFF4OPW1ANOG91H1TXU7P9SNE4GX376H6YHALYWY7", "hasFlower": 0, "hasSeason": 0}, {"id": "38", "q": "000,2,4,6,8,A,C.20,2,5,7,A,C.41,3,5,7,9,B.60,2,6,A,C<PERSON>74,8.81,6,B.94,8.A0,6,C<PERSON>B2,<PERSON><PERSON>C0,5,7,C<PERSON>D2,<PERSON><PERSON>E4,6,8.F0,2,A,C.G4,6,8;101,5,7,B.35,7.42,A.60,C.74,8.A0,C<PERSON>C2,A<PERSON>D5,7.G5,7;201,B.35,7.42,A.60,C.D5,7.G5,7;342,A.D5,7:0AWWQ4GPFJKT24FR3K31Q6EDAW3MRVDGGCDAQTMD6JKCKLERBB9TS022BXC6CVQ68XJEW238BLPGATSREVVLJ9L1", "hasFlower": 0, "hasSeason": 0}, {"id": "39", "q": "000,2,4,6,8,A,C.20,2,5,7,A,C.41,3,5,7,9,B.60,2,4,6,8,A,C.80,2,5,7,A,C.A0,4,6,8,C.C2,4,8,A.D0,6,C.E3,9;106.20,2,5,7,A,C.41,3,6,9,B.63,6,9.71,B.85,7.A4,8.C3,9.D0,C;220,2,5,7,A,C.41,B.76.D0,C:XHE0UPU7GTKAKXB2H76BRB0X6LDIKADOFJRM3QE58ZGPQAFNT7U7FRO0CZ4230UIB84NLFCSHASMKJ5HXR", "hasFlower": 0, "hasSeason": 0}, {"id": "40", "q": "000,3,9,C.15,7.20,2,A,C<PERSON>35,7.43,9.50,5,7,C.63,9.70,5,7,C.91,3,5,7,9,B.B3,5,7,9.C1,B.D3,5,7,9.E0,C;122,5,7,A.43,6,9.63,6,9.70,C.85,7.93,9.B3,6,9.D3,5,7,9;225,7.56.63,9.85,7.93,9.D5,7:8JB385UYIOGE729FUX5J2GB4YI9FDG8ZGHU3FB4YWZYW9Z3K5V5U834DHWSZFKOBJ7JVSW4EXI9I", "hasFlower": 0, "hasSeason": 0}, {"id": "41", "q": "000,2,4,6,8,A,C.21,3,6,9,B.40,2,5,7,A,C.60,4,6,8,C.80,3,6,9,C.A3,6,9.B0,C.C2,5,7,A<PERSON>D0,<PERSON><PERSON>E4,6,8.F1,B.G3,5,7,9;100,3,5,7,9,C.22,A.55,7.60,C.76.83,9.96.B0,C.C2,5,7,A.F5,7.G3,9;200,C.55,7.C2,5,7,A:AHYK7X0CRFKKRG7KQ0I7HXCVEYG0XJQ0ARVJGQI78WSFRVUJS3SJGW3SQI3V5UHU55CC38H5FEEFEUIAXA", "hasFlower": 0, "hasSeason": 0}, {"id": "42", "q": "000,2,4,6,8,A,C.20,3,6,9,C.40,3,5,7,9,C.60,2,4,8,A,C.80,2,4,6,8,A,C.A0,3,5,7,9,C.C0,5,7,C;100,2,6,A,C.20,C.36.43,9.63,9.81,5,7,B.93,9.A6.B0,C.C5,7;202,6,A.36.63,9.A6.C6:3UM266Q8K11SMAGG8MLC1AACDIC217XKID44K7LU778M42426KD3X8LCDXXAUQIGLGQ6UQSI", "hasFlower": 0, "hasSeason": 0}, {"id": "43", "q": "000,2,4,8,A,C.16.23,9.35,7.40,2,A,C.56.62,A<PERSON>70,C<PERSON>83,5,7,9.90,<PERSON><PERSON>A2,A.C0,2,6,A,C.E2,5,7,A.G0,2,4,6,8,A,C;113,9.35,7.40,C.56.62,A<PERSON>70,<PERSON><PERSON>85,7.90,<PERSON><PERSON>B2,<PERSON><PERSON>C6.E5,7.G3,5,7,9;236.70,C.85,7.90,C<PERSON>E6;370,C.85,7;470,C:VALK9X6Z5JCQ725J6BJ3B5RXKNL9AM0Z037QY9A3GVJ2KYXZ2QGY9RZR5KBIXAQNOC6B6RLI00Y3IILOM2", "hasFlower": 0, "hasSeason": 0}, {"id": "44", "q": "000,2,C,E.14,7,A.21,D.36,8.41,D.53,6,8,B.74,A.92,4,6,8,A,<PERSON>.A0,E.B2,5,7,9,C.D1,3,5,7,9,B,D;100,2,C,E.17.31,6,8,D.53,B.92,4,7,A,C.B2,5,9,C.D4,A;200,E.31,6,8,D.97.D4,A;336,8.D4,A:R8QKCE4JI2IEBAS2HU94RW24LQIXXUSXCFF8FLOA3WU7EKRFWCHOXU9BSWHKJS3KQCQ2HRI47E", "hasFlower": 0, "hasSeason": 0}, {"id": "45", "q": "000,3,5,7,9,C.20,4,6,8,C.40,2,5,7,A,C.60,3,5,7,9,C.80,2,6,A,C.A0,2,4,6,8,A,C.C0,2,5,7,A,C.E1,4,8,B.F6.G0,2,4,8,A,C;103,6,9.26.41,5,7,B.60,4,8,C.81,B.A2,A.B0,5,7,C.D1,B.F6.G3,9;203,9.16.60,C.B0,C.F6;303,9.16:HYZJORV24YWPAPMF54YO7N5VHMROM9IIEAPXXZ4A9EXQQNHVWRX4F752WVEAJIMF5NIWPZOHE2R2QJFQNJ7ZY799", "hasFlower": 0, "hasSeason": 0}, {"id": "46", "q": "000,2,4,6,8,A,C.20,2,5,7,A,C.40,2,5,7,A,C.60,3,6,9,C.80,2,4,6,8,A,C.A1,3,5,7,9,B.C3,5,7,9;101,3,5,7,9,B.21,5,7,B.56.60,3,9,C.76.84,8.A3,6,9.C3,5,7,9;202,5,7,A.66.A3,9.C6:Q4ULI9LKUPLYTZ3CBK0USGT2NVBBKWOKNPPD32CGN6RPDJ4N2I02SWRJWF4BWFLU634ZQ3O9VY", "hasFlower": 0, "hasSeason": 0}, {"id": "47", "q": "000,2,4,6,8,A,C,E.20,2,4,7,A,C,E.41,4,6,8,A,D.63,6,8,B.70,E.82,5,7,9,C.90,E.A2,4,6,8,A,C.C2,5,7,9,C.E0,3,5,7,9,B,E.G1,3,5,7,9,B,D;100,4,6,8,A,E.12,C.31,4,A,D.47.63,B.77.80,E.97.A3,5,9,B.C2,7,C.E4,A.G1,5,7,9,D:1EFCI76QYLR8K7EK07AT9EB548W6GQ56KW5Q7IH9UUBRFUGTIYGC0YRABHLBA8E9QWCTYG10WUCTAS684FR54SIK90F4", "hasFlower": 0, "hasSeason": 0}, {"id": "48", "q": "000,2,4,6,8,A,C.21,3,5,7,9,B.41,4,6,8,B.60,2,4,8,A,C.76.80,3,9,C.95,7.A0,2,A,C.B4,8.C0,2,6,A,C.D4,8.E0,2,A,C.F4,6,8.G0,2,A,C;101,3,6,9,B.23,6,9.51,B.B0,C.D2,A.F3,6,9.G0,C:38303N90ZFCLALRUL8AN2TFCZWY0I2NUC2KWZAYK0RTMRLK93YKT82WFFMMMWZAIUN8IUYCTIR", "hasFlower": 0, "hasSeason": 0}, {"id": "49", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,2,4,8,A,C.56.60,2,A,C.74,6,8.80,C.93,5,7,9.A0,C.B2,5,7,A<PERSON>C0,C.D2,4,8,A<PERSON>E0,C.F2,4,6,8,A.G0,C;104,8.12,6,A.20,C.33,9.41,B.56.62,A.80,C.93,9.B2,6,<PERSON><PERSON>C0,C<PERSON>E3,9.F0,5,7,C:5KCU74O1PR14ODGW336CZTBB387I1RL2CX2G8Q2LTPBLMDN7UIZDEDNLXNWK5OB2UM6IIVUX37ONCKX1QEVK", "hasFlower": 0, "hasSeason": 0}, {"id": "50", "q": "001,3,5,7,9,B.22,4,8,A.36.40,2,4,8,A,C.61,3,5,7,9,B.80,2,4,6,8,A,C.A0,2,4,8,A,C.C0,2,4,8,A,C.E1,3,6,9,B.G0,4,6,8,C;106.24,8.36.42,A.63,6,9.A0,C.B3,9.D2,A.E6.G0,C;242,A.63,9.A0,C.B3,9.D2,A.G0,C:HXSQE7HP7AAMJOXY5OFRSKPOH4JB5B4KY6KFA9SEBT9S7Q4RQAMO9TXJEBQ0R5670PYTEFRX4F5KJTPHY9", "hasFlower": 0, "hasSeason": 0}, {"id": "51", "q": "001,3,5,7,9,B.20,4,6,8,C.41,3,5,7,9,B.61,4,6,8,B.80,2,4,6,8,A,C.A0,2,4,8,A,C.B6.C0,3,9,C.D5,7.E0,3,9,C.F6.G0,3,9,C;104,6,8.51,5,7,B.75,7.80,3,9,C.A3,9.C0,3,9,C.E3,9;275,7.A3,9.C0,C.D3,9:JH5JC9UUDCIGWQ57W1RWK1IFJGEQSQOCH7FDE7RKDD99SAGUGA1EO15R5UIHIQHFEFCWSOAKA97KJOSR", "hasFlower": 0, "hasSeason": 0}, {"id": "52", "q": "000,2,4,6,8,A,C,E.20,4,7,A,E.32,C.44,6,8,A.50,2,C,E.64,6,8,A.70,2,C,E.84,7,A.90,E.A2,4,7,A,C.C0,2,4,6,8,A,C,E.E0,3,5,7,9,B,E;103,6,8,B.33,7,B.51,5,7,9,D.70,2,7,C,E.90,E.A7.B3,B<PERSON>C5,7,9.E5,7,9:PSFO22O2MAGJUUU8N98VNPSHGNCCAJGMSAFIFFPR9823SMCTO37938TROCNIVT7JR9HMIGVHIVJ3RUP7ATH7", "hasFlower": 0, "hasSeason": 0}, {"id": "53", "q": "000,2,4,A,C,E.17.20,2,C,E<PERSON>35,9.40,7,<PERSON><PERSON>53,B.65,7,9.71,<PERSON><PERSON>85,7,9.93,B.A0,7,E<PERSON>B5,9.C1,D.D3,7,B.E0,E;100,3,B,E.17.20,2,C,E.35,9.40,7,E<PERSON>53,B.65,9.85,9.93,B.A0,7,E<PERSON>B5,9.C1,D.D3,7,B;200,E.22,C.30,E.93,B.B5,9.D3,B;300,E.D3,B:O847BL4RIECBOSXKWSO8DPPRE1DXNNL1TTK8JWVNISI22T4EA41CNDAUJ7MCFXADVMSXKRAOP8J1RVEJIPTUKCFV", "hasFlower": 0, "hasSeason": 0}, {"id": "54", "q": "000,3,6,9,C.20,2,5,7,A,C.40,3,5,7,9,C.61,3,6,9,B.80,3,5,7,9,C.A0,3,5,7,9,C.C1,3,6,9,B.E0,2,4,6,8,A,C;110,C.30,6,C.44,8.62,A.A0,5,7,C.C1,3,6,9,B.E0,C;230,C.44,8.A0,C.C1,B:6EXZA0HBL0OXYS1ZJRMBO6AMRZL10RHHH7DE0J6DAYP6IZXOY3WDDL3WEVRVV7SIEOLXYAPBBV", "hasFlower": 0, "hasSeason": 0}, {"id": "55", "q": "000,2,4,6,8,A,C.20,2,4,8,A,C.40,2,6,A,C.61,4,6,8,B.81,4,6,8,B.A0,2,5,7,A,C.C0,2,5,7,A,C.E0,2,4,8,A,C.G1,4,6,8,B;100,2,4,8,A,C.20,2,4,8,A,C.61,5,7,B.81,5,7,B.A0,2,6,A,C.C0,C.E1,4,8,B.G1,4,8,B:7F6U90GEE7ETY3PP54D9VUY2496CT9DF7UC3M2TNJ4R5H0JPGCYH74MOTC2GP5OOMRJUN03GV2V5OK3EJM0KVY", "hasFlower": 0, "hasSeason": 0}, {"id": "56", "q": "001,3,5,7,9,B.20,3,6,9,C.40,2,5,7,A,C.61,3,5,7,9,B.80,2,4,6,8,A,C.A0,2,4,6,8,A,C.C0,2,4,6,8,A,C.E0,2,4,8,A,C.F6.G0,2,A,C;103,6,9.26.42,A.55,7.62,A.74,8.A0,4,8,C.B2,A<PERSON>C5,7.D2,A.F2,6,A:2LJDJBNKP703EFF10M7P194WXSENHA21DHW9QSK2LMAP5337EB7F5SD10WMM92X0AD4JQSN9AWNFP3EJ", "hasFlower": 0, "hasSeason": 0}, {"id": "57", "q": "000,3,9,C.15,7.20,2,A,C<PERSON>35,7.40,C<PERSON>52,4,8,A.60,C<PERSON>73,6,9.80,C.93,5,7,9.A0,C.B2,4,8,A.D0,4,6,8,C.E2,A<PERSON>F0,4,6,8,C.G2,A;103,9.10,6,C.50,2,A,C.70,3,9,C.A0,3,9,C.D5,7.E2,A.F4,8.G2,A;203,9.50,2,A,C.E2,A:4AIFJFCLR6T0GJV4C373H7WV0EIMZIA3WEV96ZEB7UU1FLVBA6G3479FGMU1OB5H6IU1A9REG1B9T5T4TO", "hasFlower": 0, "hasSeason": 0}, {"id": "58", "q": "000,2,6,A,C.14,8.20,2,A,C.34,6,8.41,B.54,6,8.60,2,A,C<PERSON>75,7.80,3,9,C.95,7.A2,A.B0,4,6,8,C<PERSON>C2,<PERSON><PERSON>D5,7.E1,3,9,B.G2,4,6,8,A;101,B.14,8.34,6,8.51,4,6,8,B.80,4,8,C.A2,A.B0,4,6,8,C.E2,A.G4,6,8;244,6,8.B4,8.E2,A.G5,7:38RN1RO649SSOW4VP6ZZK5XGL6KHU6UIRCG5Q8CZW1XFHVNRN5WOQ58TIHCHP773LVKQC9KDWVI8DZN1FUOQ1ITU", "hasFlower": 0, "hasSeason": 0}, {"id": "59", "q": "000,2,4,7,A,C,E.21,4,6,8,A,D.40,2,4,A,C,E.60,2,4,6,8,A,C,E.80,4,7,A,E.92,C.A4,A<PERSON>B0,2,C,<PERSON><PERSON>C5,7,9.D1,<PERSON><PERSON>E4,A;102,4,A,C.25,7,9.40,E.61,5,9,D.80,E.B0,E.E4,A;202,C.25,9.40,E.80,E.B0,E.E4,A;325,9.40,E.B0,E.E4,A:5WA1Q5AK3FPF910JTGM308HPLEXYXEZIOONG8HOKXJPAXLZQF71DT7YD5W369MILIHB1ELOHIJFB3KPJKEN5A6", "hasFlower": 0, "hasSeason": 0}, {"id": "60", "q": "000,3,5,7,9,C.20,2,4,6,8,A,C.40,2,5,7,A,C.61,4,6,8,B.80,3,5,7,9,C.A0,2,4,6,8,A,C.C1,3,9,B.D6.E0,2,A,C;104,8.10,C.23,5,7,9.30,C.51,B.64,6,8.80,5,7,C.A0,3,9,C.C2,A.D6.E0,2,A,C:56PPQPKNNIUUDMS0JIGL4UJL40I5QKCLM2SSG6KGQ2654246SC20UL5JYMYG0QCYNDYKINPMDJDC", "hasFlower": 0, "hasSeason": 0}, {"id": "61", "q": "000,2,5,7,A,C<PERSON>23,6,9.31,B.43,6,9.50,C<PERSON>62,6,A.70,4,8,C<PERSON>82,A.90,6,C<PERSON>A2,4,8,A<PERSON>B0,6,C<PERSON>C3,9.D6.E2,4,8,A;102,5,7,A.31,3,9,B.50,C.62,6,A.82,A.90,6,C<PERSON>A4,8.C3,9.E3,9;233,9.62,A.82,A.90,C<PERSON>A4,8.C3,9:JKG8IQRA3NEN7GLG5N1T71641178495EFKESUU868JGRO93TLOELL794QRARTN95JDYS4WTWDYFI5J", "hasFlower": 0, "hasSeason": 0}, {"id": "62", "q": "000,2,4,6,8,A,C,E.20,2,5,7,9,C,E.40,2,4,6,8,A,C,E.62,4,6,8,A,C.70,E.85,7,9.92,C.A0,4,7,A,E.B2,C.C0,4,6,8,A,E.D2,C.E4,6,8,A;101,5,9,D.17.35,9.41,D.54,6,8,A.77.A1,4,A,D.C3,5,9,B.E7:HUX89838N9NQUH7F3DPZL1E55H33ZENNBQMP5L7MXUPX98HDEQBF7B5FLE10M9PZM7F00X110QZLBU", "hasFlower": 0, "hasSeason": 0}, {"id": "63", "q": "000,3,5,7,9,B,E.20,2,4,6,8,A,C,E.42,4,A,C<PERSON>50,7,E.62,4,A,C.76,8.81,4,A,D.96,8.A0,2,C,E.B4,A<PERSON>C1,6,8,D.D3,B.E0,6,8,E;100,5,9,E.22,5,9,C.44,A.51,D.67.81,6,8,D.A1,D.B4,A.C1,7,D.E0,E:BHHM2VYYUDMOPBGWW1BLUV8IADUVYDLTPG5MHQ9I8E8XOE15QAX2FOLNTODMEQUNBFVQYIHI89EL", "hasFlower": 0, "hasSeason": 0}, {"id": "64", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,2,4,6,8,A,C.60,2,4,6,8,A,C.81,4,6,8,B.A1,4,6,8,B.C0,2,4,8,A,C.D6.E0,2,A,C.F4,6,8.G1,B;100,4,6,8,C.21,6,B.42,A.54,6,8.61,B.75,7.A5,7.B1,B.C3,9.E0,C.F6:19394TM9MBKOGZFFSVS3V4ZTMEJ8KEB4IGK8S1I8VPRJKBGP43JFF1TP0SP5ZZMT5B9R0V0GJ821O203", "hasFlower": 0, "hasSeason": 0}, {"id": "65", "q": "000,2,4,7,A,C,E.22,4,6,8,A,C.40,2,6,8,C,<PERSON><PERSON>54,<PERSON><PERSON>61,<PERSON><PERSON>73,5,7,9,B.80,E<PERSON>95,7,9.A0,3,B,E.C0,3,5,7,9,B,E.E1,5,7,9,D.F3,B.G0,5,7,9,E;100,2,7,C,E.14,A.27.32,C.46,8.51,4,A,D.73,5,9,B.87.95,9.A3,B.B0,E<PERSON>C4,6,8,A.E1,6,8,D.F3,B.G0,5,7,9,E:KMBRB4UUFS7UER6P5S95NP3JJ9J46FY1HI3BVKV57Y90NAXNIJK082FP4822S10QWYSHKIVQBWAMQEXPI2AW9Y5UAVHN0QWFH4", "hasFlower": 0, "hasSeason": 0}, {"id": "66", "q": "000,2,5,7,9,C,E.20,2,5,7,9,C,E.43,5,7,9,B.50,E.62,5,7,9,C.70,E.82,4,6,8,A,C.90,E.A4,6,8,A.B1,<PERSON><PERSON>C3,6,8,B.E0,3,5,9,B,E.F7.G0,4,A,E;100,6,8,E.26,8.45,7,9.62,6,8,C.80,3,6,8,B,E.A5,9.C3,6,8,B.F7;217.46,8.62,6,8,C.87;317.46,8.66,8:66PUZ5R5205ZS5TOTPKQJITQRIW8LSO90K7NQ97JNQUP46A4O8UZZ9K6O82SS2JWL8AFAFFN27NRKLP7UIT4LFW0WRJA094I", "hasFlower": 0, "hasSeason": 0}, {"id": "67", "q": "000,3,6,8,B,E.20,2,4,7,A,C,E.41,6,8,D.53,B.65,7,9.71,3,B,D.86,8.90,3,B,E.A7.B4,A.C1,D.D6,8.E1,3,B,D.F7.G0,3,B,E;100,3,6,8,B,E.20,3,7,B,E.47.63,5,9,B.77.83,B.97.C1,D.D7.E1,D.F3,7,B;203,7,B.10,E.23,7,B.63,B.83,7,B.C1,D.F3,7,B:OTIAOG9SED12ICJ6UHPQK423IBUKIVGAAT7BHMLEMDLTOP96KM32A6YDUXTE2OSQ9DPKCHVX4MUG9SXJRP7ERSYJH6JXG1", "hasFlower": 0, "hasSeason": 0}, {"id": "68", "q": "000,3,5,7,9,B,E.20,2,6,8,C,E.34,A.40,2,C,E.54,6,8,A<PERSON>62,C.70,5,7,9,<PERSON>.82,C.90,5,7,9,E.A2,C.B0,4,6,8,A,E.D2,4,6,8,A,C.E0,E.F2,4,A,C.G0,6,8,E.H2,<PERSON><PERSON>I4,A;103,B.22,6,8,C.34,A.40,2,<PERSON>,E<PERSON>56,8.70,2,7,C,E.90,6,8,E.B6,8.E0,2,C,E.G1,D:X47IMSMFBG6DHXOC4A8F99L3UNFDDQAW4KIVHO6RBS74C1BVIZ1SQVOWN7F3Q1AWTG3HST6H2UVD6Y8KLAIUR2UY3ZQ71BWO", "hasFlower": 0, "hasSeason": 0}, {"id": "69", "q": "000,3,5,7,9,B,E.20,2,4,6,8,A,C,E.40,2,4,7,A,C,E.60,3,5,9,B,E.77.80,2,4,A,C,E.96,8.A0,4,A,E.B2,6,8,C.C0,4,A,E.D6,8.E0,3,B,E.F5,7,9.G0,2,C,E<PERSON>H4,6,8,A.I1,D;106,8.14,A.20,6,8,E.33,B.41,7,D.60,E.77.81,D.95,9.B1,5,9,D.C7.D0,E.E7.H1,6,8,D:5XU6U3BX9LGTB478R8ZK5S2Z36D4RYHDJ2DKL7O5WS864WTKWCJ3TZYPCSXCJK7GUBG24Z26DWOPLCJL3UP9SX7GYRY5HTRPO8BO", "hasFlower": 0, "hasSeason": 0}, {"id": "70", "q": "001,3,6,8,B,D.20,3,5,7,9,B,E.42,4,6,8,A,C.50,E.62,4,7,A,C.80,3,5,7,9,B,E.A2,4,6,8,A,C.B0,E.C2,4,6,8,A,C.E0,2,4,6,8,A,C,E.G0,2,4,6,8,A,C,E;103,B.25,9.43,7,B.87.A6,8.C4,A.D7.E2,C.F5,9.G0,E;2A6,8.E2,C.G0,E;3E2,C:JXLYMGONUJ6XFSRAB5ENNHAE2LX2DOSMADN12YIJ66I417MDGALF51YG4JF7LMHIUHRUD21I4FXEEBBH647SYUGBS7", "hasFlower": 0, "hasSeason": 0}, {"id": "71", "q": "000,3,5,7,9,B,E.20,2,4,7,A,C,E.40,2,4,7,A,C,E.60,3,5,7,9,B,E.80,2,4,6,8,A,C,E.A0,2,4,A,C,E.B7.C0,2,4,A,C,E.D7.E1,5,9,D;100,3,B,E.22,C.42,C.60,E.73,5,7,9,B.90,E.B2,C.E1,D;260,E.77.90,E.E1,D:NGTLCIAS0YA0OGZ9BABBS5YI7QXT90F7XW9YLBZFMQWS0ZOW5G5CLGM9MMUNWLCXAXZCOU6SE67FF5YO7E", "hasFlower": 0, "hasSeason": 0}, {"id": "72", "q": "000,2,4,6,8,A,C.20,2,6,A,C.34,8.41,6,B.53,9.65,7.71,3,9,B.85,7.90,2,A,C.A6.B2,4,8,A.C0,C.D2,4,6,8,<PERSON><PERSON>E0,C<PERSON>F2,6,A.G4,8;101,3,6,9,B.31,4,6,8,B.63,5,7,9.82,A.A2,6,A.C2,4,8,A.D0,6,C;203,9.31,5,7,B.64,8:HP9C08WNBU0BDYZCY9ZJ9WPCE64BUCMJVPP6HKEEDZ88U4V3M7KK7LMH3DL2W7TDB0KET79QQUNWHY28Z0MY", "hasFlower": 0, "hasSeason": 0}, {"id": "73", "q": "000,2,4,7,A,C,E.20,3,5,7,9,B,E.42,4,7,A,C.50,E.63,5,7,9,B.70,E.83,6,8,B.90,E.A2,4,6,8,A,C.C0,2,5,9,C,E.D7.E1,4,A,D.F6,8.G0,2,4,A,C,E;101,D.13,7,B.34,A.50,E.67.83,6,8,B.A2,7,C.C0,2,5,9,C,E.E4,A.G0,2,C,E:2W3WBG379ZVO27IEW6PVSDTDZQEADPQY7DVLZ5TG53O5FL9OS6VREQORLYRWF2FIL9Q9EGBTSPYGY523A7RTSZFP", "hasFlower": 0, "hasSeason": 0}, {"id": "74", "q": "002,4,6,8,A.21,4,6,8,B.41,3,5,7,9,B.61,3,5,7,9,B.80,3,9,C.95,7.A0,3,9,C.B5,7.C0,3,9,C<PERSON>D5,7.E2,A;102,A.21,4,6,8,B.43,5,7,9.62,4,8,A.93,5,7,9.A0,C.B3,5,7,9.D5,7.E2,A;202,A.26.34,8.62,A.A6.B3,9.D6:B5YARN4CNV7E1FQRZW4CI95SGDKYBGQZVC2YTQ6P2ISTAQOYKR7PRG16WIHBFP0FIH0G9JOJPD88SSCFBE", "hasFlower": 0, "hasSeason": 0}, {"id": "75", "q": "000,2,5,7,9,C,E.20,2,4,6,8,A,C,E.40,3,5,9,B,E.60,3,6,8,B,E.80,2,4,6,8,A,C,E.A0,4,7,A,<PERSON><PERSON>B2,C.C0,5,7,9,<PERSON><PERSON>D3,<PERSON><PERSON>E0,5,9,<PERSON><PERSON>F3,<PERSON><PERSON>G0,5,7,9,E<PERSON>H2,<PERSON><PERSON>I5,7,9;105,9.12,C.26,8.44,A.50,E<PERSON>66,8.70,E.84,6,8,A.A4,7,<PERSON><PERSON>C0,5,9,E.E0,5,9,E.G5,9:3VBC079AHOI1T6MZXT96735765CBOLVHQQFL8PVHBI31YXYWXDZBAON1R6QI8F5XN8OKDI1C57FP9WK0H2E3VFR89AA2QEMC", "hasFlower": 0, "hasSeason": 0}, {"id": "76", "q": "000,2,4,6,8,A,C,E.20,3,5,7,9,B,E.41,3,5,7,9,B,D.60,2,6,8,C,E.80,2,5,7,9,C,E.A0,3,6,8,B,E.C0,5,9,E.D3,7,B.E5,9.F0,3,7,B,E.G5,9;106,8.13,B.35,9.41,7,D.62,7,C.81,7,D.D5,7,9.F3,B;206,8.77.81,D.D5,9.F3,B:J04FF1BS8QTLEWON3MTVW99MTONV08P3J3NPZTEZBZJEB1FQL8QN4MQ1L9FSBS779EV70SWMVOZL8WP1O073JP", "hasFlower": 0, "hasSeason": 0}, {"id": "77", "q": "003,5,9,B.10,7,E<PERSON>23,B.31,5,7,9,D<PERSON>43,B.50,6,8,E<PERSON>62,<PERSON><PERSON>70,5,7,9,E<PERSON>82,<PERSON><PERSON>95,7,9.A0,2,C,E.B4,6,8,A.C1,<PERSON><PERSON>D3,6,8,B.E0,E;105,9.10,3,7,B,E.31,5,9,D.66,8.70,2,C,E.85,9.97.B1,4,A,D.C6,8.E0,<PERSON>;205,9.13,B.35,9.72,C.C7;305,9.C7:EC47AV76K8T3HWUNFX9EL9KTAAVNILMCNL2G2CI2TM394XGEDDE8FM6NMIGUHQ2QV88IHXTUV6CHUXLW7A79G446", "hasFlower": 0, "hasSeason": 0}, {"id": "78", "q": "002,5,7,9,C.10,E.22,4,7,A,C.30,E.42,4,7,A,C.60,2,5,7,9,C,E.83,5,9,B.A1,3,5,7,9,B,D.C1,5,7,9,D.E0,2,4,7,A,C,E.G0,2,4,6,8,A,C,E;102,5,9,C.20,3,7,B,E.43,B.83,B.A7.E2,4,A,C.F0,7,E.G3,B;202,C.20,E.F0,E.G3,B:UKJCJLL1WABKH75HTLRRKQRXQABNNT52Y51HXWJ1NBCBCA92U227CWU3Y57WQYK7YTR1XN8J9389UXH8TA83L39Q", "hasFlower": 0, "hasSeason": 0}, {"id": "79", "q": "000,2,4,6,8,A,C.22,4,6,8,A.42,4,6,8,A.60,2,4,6,8,A,C.81,3,9,B.96.A1,3,9,B.B5,7.C3,9.D5,7.E0,2,A,C;101,4,6,8,B.25,7.33,9.45,7.61,3,6,9,B.82,A.96.B3,5,7,9.D5,7.E1,B;206.26.45,7.B5,7.D6.E1,B;306.36.C6;436.C6:JOALN5GOE74EMG33SWALJ33H61XPEL7VE7P9G9X6JJ6AGNU6U58N5S5VS7L4CYMNO4P1WSMVAYMHW4OWCV8P", "hasFlower": 0, "hasSeason": 0}, {"id": "80", "q": "000,2,5,9,C,E.17.20,2,4,A,C,E.36,8.40,4,A,E<PERSON>56,8.60,2,4,A,C,E.76,8.80,2,4,A,C,E.96,8.A1,3,B,D.B5,7,9.C0,2,C,E.D4,6,8,A.E0,2,C,E.F4,6,8,A.G0,E<PERSON>H4,6,8,A.I2,C.J0,4,6,8,A,E.K2,C;100,E.17.22,C.30,7,E.62,6,8,C.81,6,8,D.C7.D1,D.E3,B.F6,8.I3,7,B:BU7HPMX7GSADXPV74EVQ4BESCDMQUHZTCZTAS3V1FMQ06G3D9UM40O3B6DAOETAQH1O41R3B160E9Z9RCFGPX9HOCR60XUG7VSPZRT", "hasFlower": 0, "hasSeason": 0}, {"id": "81", "q": "000,2,4,6,8,A,C.21,3,6,9,B.41,3,5,7,9,B.60,2,4,6,8,A,C.81,3,5,7,9,B.A6.B0,2,4,8,A,C.D1,3,5,7,9,B.F1,3,6,9,B;100,4,6,8,C.21,6,B.41,3,6,9,B.61,6,B.83,9.B1,B.D1,3,6,9,B.F1,6,B;200,C.42,A.61,6,B.B1,B.D2,A:CGUF1UVCTH3CN5UEL9K2GNL96731KJSS3OXYVWKHFEH97V5U5JNV2DQCGPYQ8NPS775GY1HXTDKW1X6YDXS839OD", "hasFlower": 0, "hasSeason": 0}, {"id": "82", "q": "000,2,5,7,A,C.21,3,5,7,9,B.40,2,4,6,8,A,C.60,2,4,6,8,A,C.81,3,5,7,9,B.A0,3,5,7,9,C.C0,2,4,6,8,A,C.E2,6,A.G0,2,4,8,A,C;101,5,7,B.22,5,7,A.41,3,9,B.61,3,9,B.75,7.83,9.A0,5,7,C.B3,9.C0,C.F2,A.G0,C;222,A.53,9.76.83,9.A6.F2,A:QFX0Q1F6BENPZXM4ZB68K7X3T6HD2HSIW12ML8PTNL838FU22WGN7LEYLG0U0VZDVS0IHYFORNG3HKBUOM6YB4YUQMGQ3RZX", "hasFlower": 0, "hasSeason": 0}, {"id": "83", "q": "001,3,5,7,9,B,D.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,3,5,7,9,B,E.80,2,7,C,E.A0,2,4,6,8,A,C,E.C0,2,4,7,A,C,E.E2,4,6,8,A,C.F0,E.G2,4,7,A,C;102,C.22,C.36,8.40,E.60,3,B,E.B0,3,B,E.D7.F2,C;236,8.63,B.B3,B:U4QWDR2H6FBGHMM9D3EGGM6WJM225B0FJ3UCBDKF5R3U44KQ3WJ2YH50G9J4RE6N5F0YRCKDUYC0EY99KBN6HCEW", "hasFlower": 0, "hasSeason": 0}, {"id": "84", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,2,4,7,A,C,E.60,2,5,7,9,C,E.81,3,5,7,9,B,D.A0,2,4,6,8,A,C,E.C0,2,C,E.D4,6,8,A.E0,2,C,E;101,4,7,A,D.23,B.30,E.42,C.50,E.65,7,9.84,A.91,7,D.A4,A.B2,<PERSON><PERSON>C0,E.D3,B.E0,E:YUVG78JP4MJENEO8AXO1BEX83I45J3BP0ID9LK4PLE91UJMOM5G8XD0YKBSVSKU16Y1NK7PM6Y42G2OXVDAUBDGV", "hasFlower": 0, "hasSeason": 0}, {"id": "85", "q": "004,8.10,2,A,C<PERSON>25,7.30,C.42,4,6,8,A<PERSON>50,C<PERSON>63,9.70,6,C<PERSON>82,A.90,5,7,C<PERSON>B1,3,9,B<PERSON>C5,7.D2,<PERSON><PERSON>E0,4,8,C.F6.G0,2,A,C;104,8.11,B.25,7.30,C.42,4,8,A.60,3,9,C.76.80,2,A,C.96.B3,9.C5,7.D2,<PERSON><PERSON>E0,4,8,C.G1,B;204,8.30,C.43,9.80,C.C6.E0,C:EMU1C8W46U08GR5RDGOA23HQJ0HJ7W7VNC5ODDHV2WJ9AWEOLI9GAHL31LLCN5GZ0NQV6FMJVIZC04F5RDNSRPOSPA", "hasFlower": 0, "hasSeason": 0}, {"id": "86", "q": "000,3,7,9,D,G.21,5,7,9,B,F.43,5,7,9,B,D.61,3,5,8,B,D,F.80,2,4,6,8,A,C,E,G.A0,2,4,7,9,C,E,G.C2,4,6,A,C,E.D8.E0,4,6,A,C,G.F8.G2,6,A,E;121,6,8,A,F.43,7,9,D.55,B.61,F.78.80,2,4,C,E,G.97,9.A0,G.C4,6,A,C.E5,B.F7,9:UUA2PU5LVC5HWTLPD376FMI26UROV4PSKILHC4LE6OS3PT1GJ7RF3Y1AK2JSMH8VJRYOK7J2KO0DVWH63EZZ0DDGR7S8", "hasFlower": 0, "hasSeason": 0}, {"id": "87", "q": "000,3,6,8,B,E.27.30,2,4,A,C,E.46,8.51,D.63,7,B.80,2,4,A,C,E.A4,6,8,A.B0,2,C,E.C5,7,9.D0,3,B,E.E7.G2,4,6,8,A,C;100,3,6,8,B,E.27.30,2,C,E.51,7,D.63,B.81,4,A,D.A4,6,8,A.B0,2,C,E<PERSON>C5,7,9.D0,E.E7.G2,5,7,9,C;207.31,D.57.81,D.A5,9.C7.G2,5,9,C:L5GJWV5G2A0CERF1SKQU6LABKE1832FG3LDU3CDLCQWFJ6JMFSB2K1QR3J1ND5BMN640BSKAD0A8G0WISEE5HOHZ24WOQIV69ZC9", "hasFlower": 0, "hasSeason": 0}, {"id": "88", "q": "000,3,5,7,9,B,E.20,2,5,7,9,C,E.41,3,5,7,9,B,D.60,3,5,7,9,B,E.81,3,5,7,9,B,D.A0,2,5,9,C,E.B7.C1,3,5,9,B,D.D7.E0,2,5,9,C,E;103,B.15,7,9.35,7,9.57.60,E.81,D.A2,C.B6,8.C4,A.E2,5,9,C;236,8.57.60,E.81,D.B7:0SYU93M568U6I26G7W3W9ST94SIE145AHEAPTEY4ET8JJ7DT7H6KHMKJ4KU2YGPD21882705WKIWSJIU9YHGG5", "hasFlower": 0, "hasSeason": 0}, {"id": "89", "q": "000,2,6,A,C.14,8.20,C.32,4,6,8,A.50,2,4,6,8,A,C.70,2,4,8,A,C.A0,2,4,8,A,C.C0,2,4,6,8,A,C.E1,4,6,8,B.G1,3,6,9,B;106.14,8.32,A.46.50,3,9,C.70,2,4,8,A,C.A0,2,4,8,A,C.C0,3,9,C.D6.F1,B.G6;232,A.63,9.70,C.A0,3,9,C.F1,B:Y5F796ENNX1W7DIMYOY5Q1Y3SP8HJ1KJFHPERJC22NRVXW88HVC9SSOF8UEMOJHIMLFIV5IKKN2OLD52KB1QE3UBM6SV", "hasFlower": 0, "hasSeason": 0}, {"id": "90", "q": "001,3,6,9,B.20,3,5,7,9,C.40,3,5,7,9,C.61,4,8,B.76.81,3,9,B.96.A0,2,4,8,A,C.C0,2,4,8,A,C.D6.E1,3,9,B.F5,7.G1,3,9,B;113,6,9.30,5,7,C.76.81,3,9,B.96.A0,2,4,8,A,C.C1,B.E6.F1,4,8,B;213,9.35,7.A1,4,8,B.C1,B.F4,8:PXXPU0REEC8WWIVPQY7CNJN2V4I80W4RSAYJVAS5P4W2X7R0YSV2Q7UI733XSNQ5C584U8JAAR0J3IQEEY3C5U2N", "hasFlower": 0, "hasSeason": 0}, {"id": "91", "q": "000,2,5,7,9,C,E.21,3,5,7,9,B,D.40,3,7,B,E.55,9.60,2,C,E.75,7,9.80,2,C,E.94,7,A.A0,2,C,E.B4,6,8,A.C0,E.D2,4,6,8,A,C.F0,3,7,B,E.G5,9.H1,3,7,B,D.I5,9;100,2,5,9,C,E.26,8.43,B.55,9.70,5,7,9,E.82,C.A1,D.B6,8.C0,E.D3,6,8,B.F3,B.G5,9:TZLH7204GB1K5FE1WLWHJX2S0BBDKS8ZAWLVUDMXHKGOAEKTEWNYJT2O7M7YEYM8GF52BSMZH4FCDXCXSU8LGF8VN5IDY7IZT5", "hasFlower": 0, "hasSeason": 0}, {"id": "92", "q": "000,3,5,7,9,C.20,3,5,7,9,C.40,2,4,8,A,C.56.60,2,4,8,A,C.76.80,2,4,8,A,C.96.A0,2,4,8,A,C.B6.C0,4,8,C.D2,<PERSON><PERSON>E4,8.F0,C<PERSON>G2,4,8,A;103,5,7,9.20,3,6,9,C.52,A.65,7.72,A.84,8.91,B.A6.F0,4,8,C.G2,A;216.20,C.72,A.A6.F0,C:RGR2G3XKF25SF7JXNXK31PI19YHI7ASRJW4AKQ1J3SI142NHP44WHWAJY9P7Q92QNH5YFYA59KX57GN3GQIRPSWF", "hasFlower": 0, "hasSeason": 0}, {"id": "93", "q": "000,2,4,6,8,A,C,E.20,3,5,9,B,E.37.40,2,C,E.55,7,9.61,D.73,7,B.80,E.92,4,6,8,A,C.A0,E.B2,4,A,C.C0,7,E<PERSON>D4,A<PERSON>E0,2,6,8,C,E.G0,2,4,7,A,C,E.I0,4,A,E;101,5,7,9,D.20,4,A,E.37.40,2,C,E.57.61,D.83,B.95,9.B3,B.E0,7,E.F2,C.G0,4,7,A,E.I0,4,A,E:ZBRO9Y6LKIZCJX6FEEBDJ69OPK71V9M7CLZCML7EFFYMIK6OLH4QTYZH8XG8I1DXFQJCJHA8K45P018HY5A179EG2M20IOXBTRBV", "hasFlower": 0, "hasSeason": 0}, {"id": "94", "q": "001,3,5,7,9,B,D.21,4,6,8,A,D.40,2,4,6,8,A,C,E.61,4,6,8,A,D.82,4,6,8,A,C.A0,2,5,9,C,E.B7.C0,2,4,A,C,E.D6,8.E0,2,C,E;102,7,C.25,9.31,D.47.66,8.82,6,8,C.A2,C.B7.C1,4,A,D.D6,8.E1,D;202,C.57.82,C.A2,C.D1,6,8,D;392,C.D6,8:GAC63IP1U6GYIZMQ73BKG3UZNF9ZJQ63ANB09DKTD8PY8JK7U8UEZFLFKBL8MDGN7S171TJPQ0BC9EDC9QJPCE6SN1EF", "hasFlower": 0, "hasSeason": 0}, {"id": "95", "q": "000,2,4,6,8,A,C.21,3,9,B.36.40,2,A,C<PERSON>54,8.60,6,C.81,3,5,7,9,B.A1,5,7,B.B3,9.C0,C.D2,5,7,A<PERSON>E0,C<PERSON>F2,4,8,A.G0,6,C;100,3,5,7,9,C.22,A.40,2,A,C.66.82,4,8,A.A5,7.C0,C.D2,A.F0,2,4,8,A,C;203,9.41,B.82,4,8,A.A5,7.C0,<PERSON><PERSON>D2,A.F3,9;303,9.A6.C0,C:WTZZ3F873QHPJ4GI0HW302HVI8AOHRZTJ2YP48OOPYZGIU4V3VTU7KAOA9769YJK9UVJFGFUAQ2Y6GT642IR7K8RW6PLFKRLW9", "hasFlower": 0, "hasSeason": 0}, {"id": "96", "q": "000,2,4,6,8,A,C,E.21,3,5,7,9,B,D.40,3,5,7,9,B,E.61,3,5,9,B,D.81,3,5,9,B,D.97.A0,2,4,A,C,E.B6,8.C0,3,B,E.D5,9.E0,2,7,C,E.F5,9.G0,2,C,E.H4,6,8,A.I0,2,C,E;104,7,A.23,B.61,4,A,D.91,4,A,D.B0,6,8,E.D5,9.E7.G1,D.H3,B;223,B.64,A.B6,8.H3,B:EQKKVE1S1CBQCKO0LZNZ5NR5RTI6072MA6WDWENDO1TGOKGQTC58IE8NBDPS7OWPGGW7B3LPV325PV2DCQB29AR693S3TMR1S7V6", "hasFlower": 0, "hasSeason": 0}, {"id": "97", "q": "000,3,5,7,9,B,E.20,3,5,7,9,B,E.40,2,4,6,8,A,C,E.60,4,7,A,E.80,3,5,9,B,E.97.A0,3,B,E.B5,9.C1,7,D.D4,<PERSON><PERSON>E1,7,D.F3,5,9,B.G1,7,D.H3,5,9,B.I7;115,7,9.20,E.33,B.46,8.50,E.67.74,A<PERSON>90,<PERSON><PERSON>C1,7,D.E1,D.F4,6,8,<PERSON><PERSON>H6,8;217.46,8.C1,7,D.F6,8.H7:9RK9GN48ZJAJJM9GZ8FENMPXAQAU8ITAYFDESLVPQ71R1P9ZRM7PY1EQURXI8SLVK2MGES3VTS3KLUT2TDLGV41NJINKZUQI", "hasFlower": 0, "hasSeason": 0}, {"id": "98", "q": "000,2,4,6,8,A,C.21,4,6,8,B.41,3,5,7,9,B.60,2,5,7,A,C.80,2,5,7,A,C.A2,4,8,A.B0,C.C2,5,7,A.E1,3,6,9,B.G1,3,5,7,9,B;114,8.21,B.36.55,7.62,A.70,C.86.A2,A<PERSON>C5,7.E1,3,6,9,B.G2,4,8,A;214,8.55,7.C5,7.G4,8;355,7.C5,7:NGOPA4IPVN106AYSGRB29YE1KRFLLX5MYKF6DGHPQYTEDN0BF59U44MO26KSQZNPTHXM6EV5QIER45KUZFQGZMZR", "hasFlower": 0, "hasSeason": 0}, {"id": "99", "q": "000,3,5,7,9,B,E.20,4,6,8,A,E.32,C.40,4,6,8,A,E.52,C.60,4,7,A,E.80,2,6,8,C,E.A0,3,5,9,B,E.B7.C1,4,A,D.D6,8.E2,4,A,C.F0,6,8,E.G3,B;104,A.10,E.24,7,A.30,2,C,E.45,7,9.52,C.64,7,A.81,D.A3,5,9,B.B7.C4,A.D6,8.E2,C.F0,7,E.G3,B:U1UVT67997K8F4874ZB2H592VZULFTYTASBCI148SBS5AIBIMVKLAK7VY6U8GFLGHY06SHLH46M0G2AXZIYCKF12TXG91Z", "hasFlower": 0, "hasSeason": 0}, {"id": "100", "q": "002,5,7,9,C.20,2,4,6,8,A,C,E.40,2,4,7,A,C,E.61,3,5,7,9,B,D.80,3,5,7,9,B,E.A0,2,4,6,8,A,C,E.C2,4,7,A,C.D0,E.E2,4,A,C.F0,6,8,E.G2,4,A,C;112,7,C.24,A.40,3,7,B,E.63,6,8,B.80,4,A,E.A2,C.C2,7,C.D0,E.F4,6,8,A.G2,C:ASX8MJNIVT9H7LFHV204T6BKN0HPK5UJZ6ZUUAWSHW295NSFMIDZFJA281IN8BC4FI2P7B3D71E7J38BCUSAVZEKLXKV", "hasFlower": 0, "hasSeason": 0}, {"id": "101", "q": "001,4,6,8,A,C,F.20,3,5,7,9,B,D,G.40,3,5,B,D,G.60,2,4,6,8,A,C,E,G.80,2,4,7,9,C,E,G.A0,2,4,6,8,A,C,E,G.C1,4,6,A,C,F.E1,4,6,8,A,C,F.G0,2,4,6,A,C,E,G;101,8,F.24,8,C.45,B.53,D.65,B.78.81,F.97,9.A4,C.C5,B.F4,C;218.45,B.81,F.97,9:Z0GNUIEXVOOCQH0G0NTX83UVCYE1ES8WGT7FFHMXY7FTOWWVYC93GHNWC2MYHS0LFBEBQ9L42LI884XI9SUOZB93I3ULV1BTNS", "hasFlower": 0, "hasSeason": 0}, {"id": "102", "q": "000,2,5,9,C,E.20,2,4,7,A,C,E.40,3,6,8,B,E.61,3,7,B,D.80,2,4,6,8,A,C,E.A0,2,5,7,9,C,E.C0,2,5,7,9,C,E.E0,2,4,6,8,A,C,E;102,5,9,C.10,E.27.30,E.47.61,7,D.80,4,A,E.97.B0,5,7,9,E.D0,7,E.E4,A;205,9.47.61,D.80,4,A,E.B5,7,9.E4,A:EUILPOEGWC24JPFM02OI99NLW0R9JPZUJ3OAAN6MDFALD3J06DH0OYSZCCZ344DNLMHUSR2RGH9HR6Y4WCSMZ3SN6UFPAW2F", "hasFlower": 0, "hasSeason": 0}, {"id": "103", "q": "000,3,5,7,9,B,E.20,3,5,7,9,B,E.40,2,4,6,8,A,C,E.60,3,7,B,E.75,9.80,2,7,C,E.A0,3,B,E.B6,8.C0,E.D3,5,7,9,B;100,3,6,8,B,E.20,3,5,9,B,E.41,4,7,A,D.75,7,9.82,C.A0,3,B,E.B7.C0,E.D3,5,9,B;203,B.20,4,A,E.41,7,D.75,9.B7.D4,A;324,A.75,9.D4,A:H46NQK7V9FBTXF5SKQY2SY55OAVS67LS2L57K1CE7J1EH9G3GJF9ZFJJ9NBO4RHOCZX1ZGRZ2XC4YN4AVET10GOQCN2KV3XHQEY0", "hasFlower": 0, "hasSeason": 0}, {"id": "104", "q": "001,3,5,7,9,B,D.21,3,5,7,9,B,D.41,4,6,8,A,D.61,3,5,7,9,B,D.80,2,4,A,C,E.97.A0,2,4,A,C,E.C2,4,6,8,A,C.E0,3,5,7,9,B,E.G0,2,4,6,8,A,C,E.I0,4,7,A,E;105,9.11,D.23,B.56,8.63,B.71,D.84,A.91,7,D.A3,B.C3,B.D7.E3,B.F0,E.G3,B;211,D.71,D.91,D.C3,B.E3,B:X2UVNDNRMHC8ZRJ2G56XCZL0950BT70AVJC5O9FAAPYH6CG140F7UXMXBPPLVYH9L7OZBJAF4F3RGT491R3Z4DBHJYM6865DYVM7GDPL", "hasFlower": 0, "hasSeason": 0}, {"id": "105", "q": "002,5,7,9,C.10,E.23,5,7,9,B.30,E.42,4,7,A,C.50,E<PERSON>62,5,7,9,C.70,E<PERSON>82,7,C.90,E.A3,5,7,9,B.B0,E<PERSON>C3,6,8,<PERSON>.D0,E<PERSON>E2,5,7,9,C.F0,E.G6,8;102,5,7,9,C.10,E.26,8.30,3,B,E.61,5,7,9,D.82,7,C.A0,3,5,7,9,B,E.C3,6,8,B.D0,E.E7.G6,8:KK63EYHK6S74QAXMTDYIRE8PWTG8JG843H36HJZRADA8HINMICQTSJE7WF7MY4DIZMX6WX7NAZRJCP4WYFDKRZSSTEX3", "hasFlower": 0, "hasSeason": 0}, {"id": "106", "q": "001,3,5,7,9,B,D.21,3,5,9,B,D.41,4,6,8,A,D.60,2,4,6,8,A,C,E.80,2,4,7,A,C,E.A0,3,5,7,9,B,E.C0,2,5,7,9,C,E.E2,5,9,C.G0,2,4,6,8,A,C,E;101,4,6,8,A,D.22,5,9,C.46,8.51,D.64,6,8,A.70,2,C,E.A0,7,E.B5,9.C1,7,D.E2,5,9,C:0TXRM4A3KMVEREN3NISG37JTWEVA9NQ9FF78KG45XYWSJISIT5GRFMYECNCWH7M3RG8HJ89IHUSFX09UJT8UXUAHAC7CWQ", "hasFlower": 0, "hasSeason": 0}, {"id": "107", "q": "000,2,5,7,9,B,E,G.20,3,5,8,B,D,G.41,3,5,7,9,B,D,F.60,3,5,7,9,B,D,G.80,3,5,7,9,B,D,G.A0,2,4,8,C,E,G.C1,4,6,A,C,F.D8.E1,3,D,F.F5,8,B.G1,3,D,F;101,F.18.20,3,D,G.42,6,8,A,E.64,7,9,C.83,6,8,A,D.A1,4,8,C,F.C5,B.D8.E2,E.F8.G1,3,D,F:534FXB9NL2QJAG2NHFU6MCXTMPGCVQPYJIBWTGA5INWTXCYYHT8X86ZNW02AYC42QU8EFDD8QZVVL0KIBDIFWM3BGHAMVDK9EHLL", "hasFlower": 0, "hasSeason": 0}, {"id": "108", "q": "000,2,4,6,8,A,C.23,5,7,9.42,4,6,8,A.50,C.63,9.70,5,7,C.82,A.90,5,7,C.A2,A.B0,4,8,C.C6.D2,4,8,A.F3,5,7,9.G0,C;100,4,6,8,C.23,5,7,9.42,4,8,A.60,C.75,7.80,C.96.A1,B.B4,8.C6.D2,4,8,A.F3,5,7,9.G0,C;206.24,8.60,C.75,7.80,C.E3,9.F5,7;376.80,C.F5,7:OB8BKP3L0T9G0CIBO61EW0ZJ5K9KP5XS2TKLJ1MO3HGMQTTQICSU6MZXIW15Q0N7SE71G2I5N7HS98RONJRJQUMC9CP27N2P3B3G", "hasFlower": 0, "hasSeason": 0}, {"id": "109", "q": "000,3,5,7,9,B,E.21,3,6,8,B,D.42,4,6,8,A,C.50,E.62,5,7,9,C.70,E.82,5,7,9,C.90,E.A3,5,9,B.B0,7,E.C2,4,A,C.D6,8.E0,E.F2,4,6,8,A,C.G0,E;103,5,9,B.17.21,3,B,D.36,8.42,4,A,C.50,6,8,E.70,2,C,E.85,9.A0,4,A,E.B7.C3,B.D6,8.E0,E.F2,4,A,C:9FJZE33FO8MPCBXXJLZVS8NLRJY7T6VFXY688RDBPJ6P6ZC7LDN44F9M7SOL1NZD4SEY17TB9CE93OMRBPXTSMDRYT1C4NEO13", "hasFlower": 0, "hasSeason": 0}, {"id": "110", "q": "000,3,5,8,B,D,G.21,3,6,8,A,D,F.41,3,5,7,9,B,D,F.60,3,5,7,9,B,D,G.80,6,8,A,G.A0,2,5,7,9,B,E,G.C1,3,5,8,B,D,F.E1,3,7,9,D,F.G0,2,5,7,9,B,E,G;100,5,B,G.13,8,D.26,A.31,F.53,6,A,D.60,8,G.76,A.88.A1,6,8,A,F.C1,3,D,F.E1,F.F7,9.G2,E:N2U5LIJE0G6LR38C4E5X29C1AZA1P2EWYGOYW80NPBQG9ZUCI75CPJXIOG3QAZB0X50IX3A496QE3B91N1O8OURQ7JNWBU82WJZP", "hasFlower": 0, "hasSeason": 0}, {"id": "111", "q": "000,2,4,6,8,A,C,E,G.23,6,8,A,D.31,F.43,7,9,D.55,B.61,3,8,D,F.81,3,5,B,D,F.A0,2,4,7,9,C,E,G.C2,5,7,9,B,E.D0,G.E2,4,7,9,C,E.G1,5,7,9,B,F;101,4,6,8,A,C,F.23,6,A,D.31,8,F.55,B.63,D.81,4,C,F.A2,E.C2,5,7,9,B,E.E2,8,E.G1,5,7,9,B,F:DL0N6O0AXBHYF4HEEO8QB8NZE05GC7Y3Z25OEFDXLYHL7CQLBX4NZF3GAF2DDO6I36AB5IX39IQZA7QCH4GP6K05KN924PY7GIC2", "hasFlower": 0, "hasSeason": 0}, {"id": "112", "q": "000,2,4,6,8,A,C,E.21,3,6,8,B,D.40,2,4,6,8,A,C,E.60,2,5,9,C,E.77.82,4,A,C.90,6,8,E.A3,B.B0,5,7,9,E.D2,4,7,A,C.F0,2,5,9,C,E.H0,2,4,6,8,A,C,E.J1,3,6,8,B,D;101,3,B,D.17.31,6,8,D.50,2,C,E.72,C.90,E.A6,8.D2,<PERSON>.F2,C.G0,E.I1,6,8,D:OJA1EIN54CT8SJQPLQOSMDAKPTG1Z1D4MNL9WWC8Y9GVRZ91STDR4P0DZGLXA4IYZEQ0CSMT9LOCYKPGXR77ROYA5WW78MQV78", "hasFlower": 0, "hasSeason": 0}, {"id": "113", "q": "000,2,5,B,E,G.18.20,2,E,G.34,6,A,C<PERSON>41,F.53,7,9,D.60,G.72,4,7,9,C,E.80,G.92,4,7,9,C,E.A0,G.B2,4,6,A,C,E.C0,8,G.D2,6,A,E.E0,4,C,G.F2,E.G0,4,8,C,G;100,2,5,B,E,G.18.20,G.35,B.60,3,7,9,D,G.83,8,D.A1,F.B3,6,A,<PERSON>.C0,G.D2,6,A,<PERSON>.E0,G.F3,D.G8:CK79LK3Z6I8PPQGCOI8MGZRU0ASTT6B7LQ51HD8S1AC6U0DFQC7EQLHY73DHR3Y9BFMAFK1ZLBFHIYETPKE86O1EYRZ5DPT3IARB", "hasFlower": 0, "hasSeason": 0}, {"id": "114", "q": "000,3,6,8,B,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,2,5,9,C,E.77.80,2,4,A,C,E.97.A0,3,5,9,B,E.C0,2,4,6,8,A,C,E.E0,2,4,7,A,C,E.G1,5,7,9,D;100,7,E.13,B.25,7,9.30,3,B,E.47.51,5,9,D.70,E.82,7,C.90,E.B0,3,B,E.C5,9.D0,2,7,C,E.F7:PY299J6F0GG8C5SZNOUCUULJ1YL8EQE5UV0Z3F686BJPS3QY9QOB1CVSAJ56YPPFZ19GAEAV5NO32LOVN8213SLAZ00GQEFN2C", "hasFlower": 0, "hasSeason": 0}, {"id": "115", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,2,5,7,9,C,E.60,3,5,7,9,B,E.81,4,6,8,A,D.A1,3,6,8,B,D.C0,2,5,9,C,E.D7.E1,3,5,9,B,D.F7.G0,2,4,A,C,E;103,B.10,E.23,B.30,7,E.57.60,3,B,E.76,8.81,D.A3,6,8,B.D1,6,8,D.E3,B.F1,D:UCHC5IO85Z0GMVFN9RXDQYF08UZB3CF5I6NMEG3BM6OV5HEM6KQZUHD38FZCNDOR8RL6BR3UDYSHK0OQELSBX9EQ0LLN", "hasFlower": 0, "hasSeason": 0}, {"id": "116", "q": "000,2,4,7,A,C,E.20,2,5,9,C,E.37.42,4,A,C.50,6,8,<PERSON><PERSON>62,<PERSON><PERSON>70,7,E<PERSON>85,9.91,7,D.A3,B<PERSON>B0,5,9,E<PERSON>C2,7,<PERSON><PERSON>D4,<PERSON><PERSON>E1,7,D.F5,9.G0,2,7,C,E;100,2,7,C,E.21,5,9,D.37.43,B.50,6,8,E.70,7,E.85,9.91,7,D.C2,7,C.D4,A.E1,7,D.F5,9.G1,7,D:4GC2CLRQ7NLT6PBSB96EXXA0V2PN3OV1QCESX39LYL7XGKKSW4BRETIBEA9AIOVHWF96STTQH6FQCOWD10OYDFFWAV", "hasFlower": 0, "hasSeason": 0}, {"id": "117", "q": "000,2,4,6,8,A,C,E,G.20,2,4,7,9,C,E,G.40,3,5,7,9,B,D,G.60,2,5,7,9,B,E,G.80,2,4,C,E,G.A0,2,5,7,9,B,E,G.C1,3,5,8,B,D,F.E0,2,4,6,A,C,E,G.F8.G0,2,4,C,E,G;102,4,C,E.18.20,3,D,G.45,B.50,G.67,9.71,F.A1,F.C2,E.D5,B.E0,2,E,G.G1,3,D,F:YD0HAYTJD1H62MVUZAUYVAIXUTEBT5EGBCWH9JPQB6H8QZT8GQJDDSBSVECXK5A6Q0XZ21L8XIE4PU33SZJK3LY6MWI3845I9SV5", "hasFlower": 0, "hasSeason": 0}, {"id": "118", "q": "000,2,4,6,8,A,C,E.20,6,8,E.33,B.40,5,9,E<PERSON>52,7,C<PERSON>60,<PERSON><PERSON>72,4,7,A,C.80,E.92,4,7,A,C.A0,E.B2,6,8,C.C0,4,A,E.E0,3,5,7,9,B,E.G1,6,8,D;100,4,A,E.16,8.20,E.33,B.40,5,9,E.52,7,C.60,E<PERSON>72,4,A,C.92,4,A,C.A0,E<PERSON>B2,7,<PERSON><PERSON>C4,<PERSON><PERSON>D0,<PERSON><PERSON>E3,B.G1,6,8,D:9729R3YBFGBWIIZAHUD1SQ3Y7Q01ZGUWBOSK2SHQUUSKW33KN4NAK04R616OY7N91Z4O9GO04I0HWGIXF7ZXXB2DQHAYNXDA2D", "hasFlower": 0, "hasSeason": 0}, {"id": "119", "q": "000,3,5,7,9,B,E.21,5,9,D.40,3,5,7,9,B,E.60,2,4,6,8,A,C,E.80,2,4,7,A,C,E.A1,3,5,7,9,B,D.C0,2,5,7,9,C,E.E0,2,6,8,C,E.G0,2,4,A,C,E;100,4,A,E.21,D.40,3,6,8,B,E.61,5,9,D.73,7,B.92,C.A5,7,9.B1,D.C7.D0,2,C,E.E6,8.F1,D.G3,B:S865A9C6X0SRUE3AOBC6O9LZMAYI9W1MU7MMACSUCBR15ZL80055ZZBX7O9SIN0EPUWY62T1YXN38PEXR283RBOTT3T1EYPP", "hasFlower": 0, "hasSeason": 0}, {"id": "120", "q": "000,2,4,6,8,A,C,E.20,2,5,7,9,C,E.41,4,A,D.62,6,8,C.70,4,A,E.82,7,C.90,4,A,E.A2,7,C.B0,5,9,E.C2,7,C.E0,2,4,A,C,E.G1,4,6,8,A,D.I0,3,5,7,9,B,E;103,B.10,5,9,E.22,C.44,A.66,8.80,E.97.B0,E.E0,2,4,A,C,E.G1,6,8,D.I0,3,5,9,B,E:EKQ7SCNB1PCTQVUX3EYQP349S8Y97YX5UYT41K7580N440QH3IHPAZKE0CXI8AIHNVA5CKX71B0HT9B9EN1VTSBS583IVZPA", "hasFlower": 0, "hasSeason": 0}]