[{"id": "1", "q": "000,2,4,6,8,A.21,3,5,7,9.40,2,4,6,8,A.60,3,5,7,A.80,3,5,7,A.A1,3,7,9.B5.C0,2,8,A;112,5,8.32,8.54,6.84,6.A2,8.B5.C0,2,8,A:8JRL6SC5YXGV00J6GC8JJ809XV8RVSGRF5LYS6RM0M9MFCGSC6MV", "hasFlower": 0, "hasSeason": 0}, {"id": "2", "q": "000,2,5,8,A.20,2,8,A.34,6.40,2,8,A.54,6.60,2,8,A.74,6.90,2,4,6,8,A.B0,2,8,A.C4,6;102,5,8.20,A.32,8.44,6.62,8.92,4,6,8.B0,A:3QOJDBSY0PQVF5Q24UTW7CFHO37I5P8VDW2M41U1MTCH0JBIQSY8", "hasFlower": 0, "hasSeason": 0}, {"id": "3", "q": "001,3,5,7.20,2,6,8.40,2,4,6,8.60,3,5,8.80,2,4,6,8.A0,2,6,8.C0,3,5,8;101,4,7.21,7.42,4,6.50,8.63,5.70,8.83,5.A0,2,6,8.C3,5:U4VIY0VZRU00VCI5IMZCRM8498CR3YC8M4RVU0ZZUY9Y53548IM5", "hasFlower": 0, "hasSeason": 0}, {"id": "4", "q": "000,2,4,6,8,A.20,2,4,6,8,A.42,4,6,8.50,A.62,4,6,8.70,A.82,8.94,6.A1,9.B3,5,7.C0,A;103,7.15.20,A.33,7.45.61,9.94,6.A1,9;215.45.95.A1,9;3A1,9:GLE6OFCST2EGUNU55TO1N6C15GLFS526FC26RO39RR2R9EEGOLUL3UFC", "hasFlower": 0, "hasSeason": 0}, {"id": "5", "q": "000,2,4,6,8,A.20,2,4,6,8,A.40,3,5,7,A.60,2,5,8,A.80,2,4,6,8,A.A2,4,6,8.B0,A.C2,4,6,8;114,6.33,7.61,9.81,9.A2,4,6,8.C4,6:FLC908TRPOFG0KG02TP4ODSAL8O9GRFFGA44K0LC24DLOD2S2KKD", "hasFlower": 0, "hasSeason": 0}, {"id": "6", "q": "001,3,7,9.15.21,9.35.40,A.52,5,8.60,A<PERSON>72,8.85.90,A.A5.B1,9.C3,5,7;101,3,7,9.21,5,9.40,A.60,2,8,A.90,A.A5.B1,9.C3,7;201,3,7,9.21,9.60,A.B1,9;321,9.B1,9:6XR4RIX0Z53EE36GFTY565IRCQ8F8PNZ5QIRAANU6GV17UH201YCHP2IV47T", "hasFlower": 0, "hasSeason": 0}, {"id": "7", "q": "001,3,5,7,9.20,3,5,7,A.41,3,5,7,9.60,2,4,6,8,A.82,4,6,8.A0,2,4,6,8,A.C0,3,5,7,A;103,7.41,4,6,9.60,5,A.85.A0,2,8,A;203,7.45.65.A2,8:CVY8BCE4YY5ZUR8S7BUBE4SUZVVCBR84RLV5SURES5Y77LZE7L8L5C4Z", "hasFlower": 0, "hasSeason": 0}, {"id": "8", "q": "000,4,6,A.12,8.20,A.34,6.41,9.53,7.61,9.73,5,7.91,3,7,9.B1,3,5,7,9.D0,A<PERSON>E2,4,6,8;112,8.20,A.34,6.52,8.91,3,7,9.B2,8.D0,<PERSON><PERSON>E2,8;234,6.52,8.91,3,7,9:BI7LSYAZPS74YK9F32ZPATTPCHR1VLIYF071B13CE0MTK2UMPRE1YVTUH947", "hasFlower": 0, "hasSeason": 0}, {"id": "9", "q": "000,3,5,7,A.20,3,5,7,A.42,4,6,8.50,A.62,8.70,5,A.82,8.90,A.A2,4,6,8.C0,2,5,8,A.E0,2,5,8,A;103,7.10,A.25.44,6.52,8.60,A.90,A<PERSON>A2,5,8.C5.E0,2,8,A:2XAZ59G31S60OOC59Z851A08VV6SBV4804C52A3UZV1HB9X0AZG8MMU661H9", "hasFlower": 0, "hasSeason": 0}, {"id": "10", "q": "000,2,4,6,8,A,C.22,A.35,7.40,2,A,C.56.60,2,4,8,A,C.76.80,2,A,C.94,6,8.A1,B.B3,9.C0,6,C;100,2,4,8,A,C.35,7.40,C.56.62,A.76.80,C.94,8.C6;204,8.36.40,C.62,A.C6:VQWFCK85MBW8CGREL1UQMGCNF9OS6V6XY15IUPSARZL707AYK9BICTOE40PT4NXZ", "hasFlower": 0, "hasSeason": 0}, {"id": "11", "q": "000,3,6,9,C.20,2,4,8,A,C.41,3,5,7,9,B.60,2,4,8,A,C.80,4,6,8,C.A3,5,7,9.B0,C.C3,5,7,9;100,6,C.13,9.20,C.32,A.45,7.53,9.60,C.74,8.86.A3,5,7,9.B0,C.C3,9:O8NICVR2BEVXOA24OBPIRVFFFNNIUUZDXXDRAJE8OHCAZ4RBFDNDITAJPXTHB44V", "hasFlower": 0, "hasSeason": 0}, {"id": "12", "q": "000,3,5,7,A.21,3,5,7,9.40,3,5,7,A.62,8.70,5,A.82,8.A0,A<PERSON>B3,5,7.D1,3,5,7,9;103,7.21,3,7,9.35.62,8.70,5,A.82,8.A0,A<PERSON>B4,6.D1,3,7,9;203,7.22,8.70,A.A0,A.D2,8:WBNFMJF8HBLNTVMTDQFGF3KP5YQVPSD861M7ISRCO2ZJ1MWL6G29KIHYC9ZO35R7", "hasFlower": 0, "hasSeason": 0}, {"id": "13", "q": "001,3,6,9,B.20,3,5,7,9,C.42,5,7,A.50,C.62,4,8,A.70,6,C.82,4,8,A.90,C.A2,4,6,8,A.C1,3,6,9,B.E0,3,5,7,9,C;120,3,6,9,C.45,7.51,B.63,9.75,7.82,A.90,4,8,C.D3,6,9:PBF86SYGLU2WXVDNGVH23OBKQ6FMLS0YTODGXUQMWFW5PNZZ5JFA130TG7A9WKJ97H18", "hasFlower": 0, "hasSeason": 0}, {"id": "14", "q": "000,3,5,7,A.20,2,5,8,A.40,3,5,7,A.60,2,4,6,8,A.80,2,4,6,8,A.A0,2,4,6,8,A.C0,2,4,6,8,A.E0,3,7,A;120,5,A.43,7.50,A.70,3,7,A.91,9.A3,7.B1,9.C3,7.D0,A:TW6AK24H7D6TL8VN2FNS5AUU1M0H6I31SWMLDH4FXVAIRU5KR0E9A7U3XP96PEH8", "hasFlower": 0, "hasSeason": 0}, {"id": "15", "q": "001,3,6,9,B.20,2,5,7,A,C.40,3,5,7,9,C.60,2,4,8,A,C.76.80,3,9,C.95,7.A1,B.B3,5,7,9.C0,C.D2,5,7,A.E0,C;103,6,9.11,B.54,8.60,C.80,C.A1,5,7,B.D1,B;260,C.80,C.A1,6,B:KKYPKJQEOK8SL8UD6JVL47NA6V3ZZSCZBAJD0E2LULCQGW47PIWBW490GIJONZ2439YW", "hasFlower": 0, "hasSeason": 0}, {"id": "16", "q": "000,3,5,7,9,C.22,5,7,A.40,2,A,<PERSON><PERSON>54,8.60,C<PERSON>73,9.80,C<PERSON>92,A.A5,7.B2,A<PERSON>C0,6,C;100,3,5,7,9,C.22,A.42,A.50,C<PERSON>70,C<PERSON>92,A<PERSON>A5,7.B2,A<PERSON>C6;200,C.22,A.42,A.92,A.B2,6,A;332,A.A2,A.B6:GQSB1I6L3E9ZNTWBWZHCA5NGIQ75YE2AV2AL7UA8T8OHOU0G4916SDPD4G4V3CPM04YM", "hasFlower": 0, "hasSeason": 0}, {"id": "17", "q": "000,3,5,7,9,C.21,4,8,B.36.40,2,A,C<PERSON>54,6,8.60,C.72,5,7,A.80,C.94,6,8.A1,B.B3,5,7,9.C0,C.D4,8.E2,6,A;105,7.40,2,A,C.60,C.75,7.94,8.B3,5,7,9;205,7.41,B.B3,5,7,9:QHJ5TOZMH7BVSIEG5KAL1JSGXEF7B792XOID4Q2A5CWF54V1KRKTWHCLZBKH7DMRB9", "hasFlower": 0, "hasSeason": 0}, {"id": "18", "q": "000,2,4,6,8,A,C.21,4,6,8,B.40,3,6,9,C.60,2,5,7,A,C.80,4,6,8,C.92,A.A5,7.B0,3,9,C<PERSON>C5,7.D1,B.E3,5,7,9;100,2,A,C.15,7.21,B.40,3,9,C.66.96.B0,3,9,C.D1,B.E3,5,7,9:TE9ENP5LSX49P8OSB8CFHA9ELPEHX60RK5ALJB747FRT967KJPKQOQVJ0J3RR3KNCL7V", "hasFlower": 0, "hasSeason": 0}, {"id": "19", "q": "000,2,5,8,A.23,5,7.30,A.42,5,8.60,2,4,6,8,A.80,2,4,6,8,A.A1,4,6,9.C0,3,7,A.D5.E1,3,7,9;100,A.23,7.35.42,8.60,2,8,A.74,6.82,8.A4,6.C3,7.E2,8;223,7.35.42,8.60,A.74,6.A4,6:M88CKF47SA7S9K629BATABLRIBRF4QE03LH31MIETFPJP0I2EZBN1VZHJFED3INDAQ6C3V", "hasFlower": 0, "hasSeason": 0}, {"id": "20", "q": "000,3,9,C.15,7.20,2,A,C.35,7.41,3,9,B.56.60,3,9,C.75,7.80,2,A,C.94,8.A2,A.B0,4,8,C.C2,A;100,C.16.21,B.41,3,6,9,B.63,6,9.80,C.92,A.A4,8.B1,B;221,B.42,A.80,C.92,A.B1,B:P135H1WSLKNRTSHNQ34SVTSJQWHLRJJ43PP0T17L7553QQJ1V0PN7470T4L05WRKRNHW", "hasFlower": 0, "hasSeason": 0}, {"id": "21", "q": "000,2,4,6,8,A,C.20,4,6,8,C.32,A.40,4,8,C.52,A<PERSON>66.71,B.84,8.90,C.A2,5,7,A.B0,C.C2,4,6,8,A;101,3,5,7,9,B.20,6,C.34,8.42,A.66.84,8.A6.B0,C.C6:UNIGY9OD1SC6MES9NHHU1YB14ZXVE1CONGVSMIZJ4SJPNWTXTBWDCPTCT6", "hasFlower": 0, "hasSeason": 0}, {"id": "22", "q": "000,3,5,8.21,7.33,5.40,8.52,4,6.60,8.72,4,6.80,8.92,6.A0,4,8.C0,2,4,6,8;103,5.21,7.34.50,2,6,8.71,3,5,7.A4.C0,2,6,8;204.21,7.34.50,2,6,8.74.A4.C1,7:NSOOZ5YF3R4SX42FG50YR5L5ZXSZLSJ0FH2VF3E8GIR6N6VERCJN668CZIHN", "hasFlower": 0, "hasSeason": 0}, {"id": "23", "q": "000,2,4,6,8.20,3,5,8.41,3,5,7.61,3,5,7.80,2,4,6,8.A0,2,4,6,8.C1,3,5,7;101,3,5,7.20,4,8.42,4,6.61,4,7.81,4,7.A2,4,6;220,4,8.43,5.61,7.81,4,7.A2,6:FF5479WFEVOCWV2CS7AEOUM094TAXQ3VYJZUVJ5M0WYZQW2XTP1R1RRF3EPESR", "hasFlower": 0, "hasSeason": 0}, {"id": "24", "q": "000,2,4,6,8,A,C.21,3,5,7,9,B.40,2,4,6,8,A,C.60,3,5,7,9,C.80,2,5,7,A,C.A0,3,5,7,9,C.C0,3,5,7,9,C;102,4,8,A.26.32,A.40,4,6,8,C.65,7.80,C.96.A0,C.B6:MXMMYK71771RRAPWMZ4QATUDXCQY3JRXPFW31FCLUJ2X2I6ITC67D1DBLCBRK4ZD", "hasFlower": 0, "hasSeason": 0}, {"id": "25", "q": "001,9.13,5,7.21,9.33,7.41,5,9.53,7.60,5,A.72,8.80,4,6,A.92,8.A0,4,6,A.B2,8.D1,3,5,7,9;111,9.23,7.31,9.45.60,A<PERSON>72,8.80,A.93,7.A0,A<PERSON>D3,7;211,9.45.71,9.93,7.A0,A:I686O4U1XOG7XHJL4UX23L2U8JXIG3G2Q1GV3JL67QL4V2VU46HHQ8HJ8O37Q7OV", "hasFlower": 0, "hasSeason": 0}, {"id": "26", "q": "000,3,7,A.20,2,4,6,8,A.42,4,6,8.50,A.62,4,6,8.70,A<PERSON>84,6.90,2,8,A.A4,6.B2,8.C0,4,6,A.D2,8.E4,6;103,7.10,A.22,8.42,4,6,8.62,8.70,4,6,A.92,8.B2,4,6,8.D2,8:HFXHWRV2HPLMFXT5S9AHE1VW0E64VL2VFW60D94FBDNSTR34M6W84R538N6BA1PR", "hasFlower": 0, "hasSeason": 0}, {"id": "27", "q": "000,3,9,C.16.21,B.33,5,7,9.40,C<PERSON>52,5,7,A.60,C.74,6,8.81,B.94,6,8.A1,B.B3,6,9.C0,C.D2,5,7,A.E0,C;100,C.21,6,B.33,9.46.50,2,A,<PERSON><PERSON>65,7.81,6,B.94,8.A6.B3,9.D1,5,7,B:DNRETW8OLG432FB9LQ7A0A3DI8I9MRB4INOCZQJ4MFJU46C162EUWVZRV750TRI15G", "hasFlower": 0, "hasSeason": 0}, {"id": "28", "q": "000,2,5,7,A,C.20,2,4,6,8,A,C.40,2,4,6,8,A,C.60,2,4,8,A,C.81,3,5,7,9,B.A0,2,4,6,8,A,C.C2,4,8,A;101,B.21,B.35,7.40,2,A,C.61,3,9,B.82,A.94,8.A0,2,6,A,C:K0LZBG0N6ITT48FSG4DA4EYM63FGXIB4IB36YICAECL2H06XGKMBQ0NSYHZD2YCC8Q", "hasFlower": 0, "hasSeason": 0}, {"id": "29", "q": "004,6,8.10,2,A,C.30,3,5,7,9,C.50,2,5,7,A,C.70,2,4,6,8,A,C.91,3,5,7,9,B.B1,3,9,B.C6;130,3,5,7,9,C.52,A.60,C.72,A.93,5,7,9.B1,B;262,A.94,6,8.B1,B:Q3INW1GZEVJJ4L8CE9QYA86NTC64KSRYIT3B4O7KRA9HW5HDVB7Z10O0DSL54G", "hasFlower": 0, "hasSeason": 0}, {"id": "30", "q": "000,2,4,6,8,A,C.21,4,6,8,B.43,5,7,9.50,C.62,4,6,8,A.81,4,8,B.96.A0,3,9,C.B5,7.C0,2,A,C.D4,6,8.E0,C;104,6,8.11,B.25,7.43,9.62,4,6,8,A.81,4,8,B.96.B0,C.C5,7.E0,C:HFKW2WAD84ZUV5UTOK2BTS9BPZYFPFH1SB04KOWZ9ODVRWF0RIL8I1GA5ZGKIYOL5BI5", "hasFlower": 0, "hasSeason": 0}, {"id": "31", "q": "000,2,5,8,A.21,3,5,7,9.43,5,7.60,3,5,7,A.80,3,5,7,A<PERSON>A2,4,6,8.C0,2,4,6,8,A.E0,2,4,6,8,A;105.11,9.23,5,7.43,5,7.65.73,7.85.A2,5,8.C0,2,4,6,8,A.E1,3,5,7,9:CWAUMFWFX4U8J9B7ARPTTU5N0HP47S9HU8QJVMOVEVN6VXCB3Q1372AAS1O6ER0275", "hasFlower": 0, "hasSeason": 0}, {"id": "32", "q": "000,2,4,8,A,C.16.20,3,9,C.36.42,4,8,A.56.61,3,9,B.76.80,2,4,8,A,C.96.A2,A.B4,6,8.C1,B.D3,5,7,9.E1,B;100,C.16.20,3,9,C.43,6,9.63,6,9.86.B6.C4,8.D1,6,B:PZLCR1VR7S5PIZJ8W7O5V80KLY7J6BN2NFWK2ZDPI9FZEE1YC9NS0RPNR6D7BO", "hasFlower": 0, "hasSeason": 0}, {"id": "33", "q": "001,3,5,7,9.20,5,A.33,7.40,5,A.52,8.64,6.70,A.83,5,7.A0,2,5,8,A.C1,3,5,7,9.E0,2,4,6,8,A;102,4,6,8.20,A.35.40,A.55.A5.B2,8.C5.E1,9;203,7.30,5,A.55.A5.C5:GIJM8KJS2QC4BMA42SQBVRCDVXS9Q57K2O8VUQ15R72GAPSCOGVW1XGWDP9CIU", "hasFlower": 0, "hasSeason": 0}, {"id": "34", "q": "000,3,5,7,A.20,3,5,7,A.41,3,7,9.60,2,4,6,8,A.81,9.93,7.A0,5,A<PERSON>B3,7.C0,A;103,7.15.33,7.41,9.61,4,6,9.A0,3,7,A;203,7.15.33,7.41,9.64,6.A3,7;315.33,7.64,6.A3,7:0HVGONTE4DJ986ORKPVUPA0I91S6YRNF4JEDAGW13MITHHS3XYFU8QWKQLMHZZXL", "hasFlower": 0, "hasSeason": 0}, {"id": "35", "q": "000,3,7,A.15.20,2,8,A.34,6.42,8.54,6.60,A.73,7.80,A.94,6.A2,8.C0,3,7,A.D5.E3,7;100,A.22,5,8.42,4,6,8.60,A.80,A.94,6.C0,3,7,A.D5;225.45.70,A.C0,A.D5;325.45.D5;425.D5:73HL369B9P4H8IWLN0O4S46O9IP034PPLIN8O77N06H3H7S8UUIO69SW8SWBWL0N", "hasFlower": 0, "hasSeason": 0}, {"id": "36", "q": "001,3,5,7,9.20,3,5,7,A.40,2,8,A.55.60,2,8,A.74,6.81,9.A0,2,5,8,A.C1,3,7,9;104,6.30,A.42,8.55.60,2,8,A.74,6.81,9.A0,A.B2,8;230,A.65.81,9.A0,A;330,A.A0,A:A6KIKCKE6W6WPRVO3422TUIOTWKRZOVO599CCV4QR6Q4WVUNN54APQ93ZQE9RC", "hasFlower": 0, "hasSeason": 0}, {"id": "37", "q": "000,3,5,7,9,C.23,6,9.40,4,6,8,C.52,A.60,4,6,8,C.72,A.80,5,7,C.A2,4,6,8,A.C0,2,4,8,A,C;100,4,8,C.36.40,C.53,9.61,B.96.A2,A.C2,4,8,A;200,4,8,C.36.96.C4,8:QFYPLMHY4PZHFB5YEMZ8VV5E1HBMHBWQXXWLTPZ8ZWQPYTBE1WXTFEF4LQTXL8M8", "hasFlower": 0, "hasSeason": 0}, {"id": "38", "q": "002,4,6,8.10,A.22,8.30,5,A.51,3,5,7,9.71,3,5,7,9.92,4,6,8.B1,4,6,9.D0,2,8,A;104,6.10,A.51,3,7,9.65.71,9.94,6.B1,4,6,9.D0,A;204,6.61,5,9.95.B1,9;304,6.61,9;404,6:ZTR1<PERSON><PERSON><PERSON><PERSON>HDKEEEHSGA5SRK1LHXH1VO19MJYVPQWBJB8EX7LWB7AO5CYBM89QTDCXD", "hasFlower": 0, "hasSeason": 0}, {"id": "39", "q": "000,3,5,7,9,C.20,2,4,6,8,A,C.40,2,5,7,A,C.61,3,5,7,9,B.80,4,6,8,C.A0,3,6,9,C.C1,3,5,7,9,B.E1,3,5,7,9,B.G0,3,6,9,C;103,9.22,4,8,A.36.42,A.55,7.80,C.A0,C<PERSON>C5,7.D2,A.E6:ANLASPPLZU8RHITZCR8HG4Y3XNPXNG8M3ISI4COTHNAU3GOYOOGLY8UA4XI4PRTYM3UTLHRX", "hasFlower": 0, "hasSeason": 0}, {"id": "40", "q": "000,2,4,8,A,C.16.20,2,A,C.34,6,8.40,C<PERSON>52,5,7,A.70,3,6,9,C.90,2,5,7,A,C.B2,4,6,8,A.C0,C.D3,6,9.E1,B<PERSON>F3,5,7,9.G1,B;101,B.26.34,8.52,5,7,A.70,6,C.A6.B2,<PERSON><PERSON>C0,<PERSON><PERSON>E1,B.F3,5,7,9:ED523GKAKXJPOOWZODUQT3N6RPL6C78IC9GGBL2AW89QRP3XQEVHGOP7NJNZTUH5QN36I6BV", "hasFlower": 0, "hasSeason": 0}, {"id": "41", "q": "000,2,4,6,8,A.21,3,7,9.35.40,3,7,A.60,2,5,8,A.80,2,5,8,A.A1,3,7,9.C0,2,5,8,A.E1,3,5,7,9;100,2,4,6,8,A.33,5,7.40,A.60,2,5,8,A.80,2,8,A.A1,3,7,9.C5.E1,3,7,9:JRFXOS9JNN9V2QXSCC0OVLQNX8VVFX8O3TFR20RLQOGBFS0HIYJUUTR3HNBJQIY07GS7", "hasFlower": 0, "hasSeason": 0}, {"id": "42", "q": "001,3,5,7,9.20,3,5,7,A.41,3,7,9.55.60,3,7,A.75.80,2,8,A.94,6.A0,A.B2,4,6,8.C0,A.D3,5,7.E0,A;102,5,8.23,7.41,3,7,9.63,5,7.70,A.82,5,8.A0,4,6,A.C0,4,6,A.E0,A:0SY7TMNFD73LQHQL36VQZYDPEHZYFZQMN0BGGPCZSVGNYB63G3S6CV7C7SCTHVEN6H", "hasFlower": 0, "hasSeason": 0}, {"id": "43", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,3,5,7,9,C.60,2,4,8,A,C.80,2,4,8,A,C.A0,2,6,A,C.B4,8.C0,2,6,A,C.D4,8.E1,B;100,6,C.12,A.34,8.40,C.61,B.73,9.81,B.A0,2,6,A,C.B4,8.E1,B:6HPNNNQYR0QA88UY1S9029RJN7UUOJ2ES1377QAFKPUT8TDQ87DEYV5OCO3F6ZCO5BKBZHYV", "hasFlower": 0, "hasSeason": 0}, {"id": "44", "q": "000,3,5,7,A.20,A.32,4,6,8.40,A.52,5,8.60,A.75.80,A.93,5,7.A0,A.B2,4,6,8.C0,A<PERSON>E0,2,5,8,A;104,6.30,4,6,A.42,8.50,A.70,A.90,3,7,A.B1,5,9;205.30,4,6,A.50,A.90,A.B1,5,9:OG5EIDKPDG7E1XAEISE1P19ZJ8G7A7IK8D5J9SOS2OWF2WD7XAKWGF0KO2199Z2WS0IA", "hasFlower": 0, "hasSeason": 0}, {"id": "45", "q": "000,2,4,6,8,A,C.20,3,5,7,9,C.41,3,5,7,9,B.61,3,6,9,B.80,2,5,7,A,C.A0,3,6,9,C.C0,4,6,8,C;102,4,8,A.10,C.25,7.33,9.41,B.61,3,9,B.80,C.A0,3,9,C.C0,4,8,C:QTU9UT2E3902RRE059HL5XJT2X8288G3H5XQUQ5JE3GXH03ERP0LJPPRQ9TGHJGPU8", "hasFlower": 0, "hasSeason": 0}, {"id": "46", "q": "000,3,5,7,9,C.20,2,4,6,8,A,C.40,2,4,6,8,A,C.61,3,6,9,B.80,2,5,7,A,C.A0,2,4,8,A,C.B6.C1,4,8,B;103,5,7,9.10,C.22,6,A.34,8.51,B.72,A.80,C.A2,4,8,A.B6.C1,4,8,B:E1BPVEMXIVY5X9N01RQLI9DWHRZY0BKNA2Z753V5V3NL03BZ37HW05GDSMPSZQGBKN2A", "hasFlower": 0, "hasSeason": 0}, {"id": "47", "q": "000,2,6,A,C.14,8.20,6,C.32,4,8,A.46.52,A.60,4,8,C.82,5,7,A.A0,2,4,6,8,A,C.C0,3,5,7,9,C;100,C.16.32,4,8,A.52,A.60,C.82,5,7,A.A0,2,4,8,A,C.C6;200,C.16.34,8.85,7.A4,8.C6:2FCGMNF2B4QQRCCEYSVVJONN24FVK43BMSY3KDOZJMCRQV42OPO3RMWRN3WGUQZPJFDJUE", "hasFlower": 0, "hasSeason": 0}, {"id": "48", "q": "000,4,6,8,C.12,A.20,6,C.32,A<PERSON>40,6,<PERSON><PERSON>52,A.60,4,8,C<PERSON>72,6,A.84,8.90,<PERSON><PERSON>A2,5,7,A.B0,C.C2,6,A<PERSON>D0,C<PERSON>E2,5,7,A;104,8.10,C.22,A.30,6,C<PERSON>42,A<PERSON>61,3,9,B.84,8.A6.B2,A<PERSON>C0,6,C.D2,A<PERSON>E5,7:36LYG5SMYSULMULMUYKGBAMIXTK7I3TTH7XI7X3OAKGK6O565LHBTBY6O5AGSISBAXO7U3", "hasFlower": 0, "hasSeason": 0}, {"id": "49", "q": "000,3,5,7,A.20,2,4,6,8,A.41,3,5,7,9.61,3,5,7,9.83,5,7.91,9.A5.B1,3,7,9.C5.D0,2,8,A<PERSON>E4,6;105.21,3,5,7,9.41,9.54,6.61,9.73,7.91,5,9.B1,9.D1,5,9;223,7.54,6.61,9.73,7.91,9:QQXFWSBP59BFPVEBQFR5PREPSDIE9IDQW1BW9559YKIRRSEYSXVKX1FVDXINNKYDWNYKVN", "hasFlower": 0, "hasSeason": 0}, {"id": "50", "q": "000,2,4,6,8,A.20,2,5,8,A.40,2,5,8,A.60,2,8,A.80,2,8,A.A1,3,5,7,9.C1,3,5,7,9.E0,2,4,6,8,A;101,9.31,9.50,A.62,8.70,A.92,8.B4,6.C1,9.D3,7.E1,9;231,9.50,A.62,8.92,8.C1,9:3US88BHS5RYV06RP3O78VR7BVW62OE2EE76WUVEU0UYMP8DD70530WP5RWP6M5SOS22O3H", "hasFlower": 0, "hasSeason": 0}, {"id": "51", "q": "000,3,6,9,C.20,4,6,8,C.32,A.44,6,8.51,B.63,6,9.71,B<PERSON>84,6,8.A1,3,6,9,B.C0,2,5,7,A,C.E1,6,B;100,6,C.20,4,8,C.44,6,8.62,6,A.84,6,8.A3,9.B6.C1,B.E1,6,B;200,C.20,4,8,C.46.62,A.76.B6:0HB3SJW6ZSK9QBUBERPDWKENWUWUPD3HMGIFLGNOI9CLVQOZXCVMTMDMQKJKXBDNTF06QGNGRU", "hasFlower": 0, "hasSeason": 0}, {"id": "52", "q": "000,2,4,8,A,C.16.20,2,4,8,A,C.40,3,5,7,9,C.63,6,9.70,C.82,4,8,A.96.A2,4,8,A.B0,C.C3,5,7,9.D0,C.E2,4,6,8,A;115,7.22,A.40,C.53,9.66.92,6,A.B0,<PERSON><PERSON>C3,9.E5,7;215,7.22,A.40,C.92,A.B0,<PERSON><PERSON>E5,7:YQ3HAQBB29PTCQDNHK97NW41VHHYYYBQCDPCVNV2KW21070KOA11DNAPRW7P7W9KTC4VDR2O9B3A", "hasFlower": 0, "hasSeason": 0}, {"id": "53", "q": "000,2,5,7,A,C<PERSON>21,3,9,B.35,7.40,2,A,C<PERSON>54,6,8.60,C.72,4,6,8,A.80,C.93,9.A5,7.B0,2,A,<PERSON><PERSON>C4,6,8.D1,B<PERSON>E3,5,7,9;101,5,7,B.35,7.41,B.74,8.B1,<PERSON><PERSON>C5,7.E3,9;206.35,7.C5,7.E3,9:IPVDR5FD6VDJ7742XQIPRIK6Y5IXLFWQT7M7KLYR58T9JM2YUKOP8UWDMF9MKO4F5RYP", "hasFlower": 0, "hasSeason": 0}, {"id": "54", "q": "000,2,4,8,A,C.20,2,6,A,C.40,4,6,8,C.62,4,6,8,A.81,3,5,7,9,B.A0,3,5,7,9,C.C0,2,5,7,A,C.E0,2,4,8,A,C;103,9.10,C.30,C.45,7.76.A0,3,5,7,9,C.C0,C.E0,3,9,C:3ZR87RI2UYJJ1WVLNG483BUKIK42N8GMPWUWW4YZLL8GNKRUJM7GBMDMPJ41NLDKVR", "hasFlower": 0, "hasSeason": 0}, {"id": "55", "q": "000,2,4,6,8,A.21,4,6,9.40,2,4,6,8,A.60,3,5,7,A.80,2,5,8,A.A0,2,4,6,8,A.C0,2,4,6,8,A.E0,2,4,6,8,A;103,7.11,9.43,7.50,A.91,9.B3,7.D0,3,7,A;243,7.50,A.91,9.B3,7.D0,A:LMKTXY3Q3DMBYNTLBFXCB05MCL2F3N5KB6QFQ9VE9TXMVYK6KCYCN6DAF0ANXLE3DT62QD", "hasFlower": 0, "hasSeason": 0}, {"id": "56", "q": "000,3,5,7,9,C.20,3,5,7,9,C.42,5,7,A.63,9.70,5,7,C<PERSON>82,A<PERSON>94,8.A1,6,B.C1,3,5,7,9,B.E0,3,6,9,C;100,3,5,7,9,C.20,3,5,7,9,C.46.70,5,7,C.82,A.A6.B1,B.C5,7.D3,9;200,5,7,C.13,9.B1,B.C5,7:MI5DB9XZFU9IJRZ95VVOFQTQ2PJGMGVO88UR8QMEEUHPDSM8XZEFFNTXZVRHQSN2SUBX9PGRPSGE", "hasFlower": 0, "hasSeason": 0}, {"id": "57", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.42,6,A.60,2,4,8,A,C.80,2,5,7,A,C.A1,3,5,7,9,B.C0,2,4,6,8,A,C.E0,2,4,6,8,A,C;103,6,9.10,C.22,A.52,A.70,C.91,5,7,B.A3,9.C1,4,8,B.E0,C:ITO3RN9JJ6SUNUDNYHFTIOUVT23YHFS9R29SY2AGV4VDJ36UINDYA2GFSO9VO0IJ4DTF5350", "hasFlower": 0, "hasSeason": 0}, {"id": "58", "q": "000,2,4,8,A,C.20,2,5,7,A,C.40,2,4,6,8,A,C.62,4,6,8,A.70,C.82,5,7,A.A0,2,A,C.B4,6,8.C0,2,A,C.D5,7.E0,2,A,C;111,B.25,7.32,A.56.64,8.A0,C.C2,A.D0,5,7,C;225,7.64,8.A0,C.D5,7:ZAR7Y6X4RI788ZPA11ET8ZU684UUOZK4AUTRHEH7PTAEKO1IKOHTH6OYX6PP4XIIKEX7Y1YR", "hasFlower": 0, "hasSeason": 0}, {"id": "59", "q": "000,4,6,8,C.20,2,5,7,A,C.40,2,5,7,A,C.61,3,5,7,9,B.81,5,7,B.A0,2,4,6,8,A,C.C0,3,6,9,C.E0,2,4,6,8,A,C.G1,3,5,7,9,B;115,7.22,A.30,C.42,A.62,A.A0,4,8,C.E0,C.F2,A.G5,7:EQ11X2M213QHE1J09VW0DW2S2VETVSEFLQGL34JJSMG4P4FVGTDM09TFS30PXQFTJ3GM9H94", "hasFlower": 0, "hasSeason": 0}, {"id": "60", "q": "000,2,4,6,8,A,C<PERSON>21,3,5,7,9,B.41,5,7,B.53,9.60,C.72,5,7,A.91,4,8,B.A6.B0,2,A,C.C4,8.D1,6,B.E3,9.F1,5,7,B.G3,9;100,2,4,8,A,C.21,5,7,B.45,7.72,5,7,A.94,8.A1,B.C1,4,8,B.D6.E1,3,9,B.F5,7:1IP175GC9PWZH9CQZ98GCQNNUHU8E5ZHZH5GXQC8E5OGEOWO8X1PWIV6V69V1VOKXWX7UNUQNEPIKI", "hasFlower": 0, "hasSeason": 0}, {"id": "61", "q": "000,2,4,8,A,C.16.20,2,A,C.34,6,8.40,C.52,4,6,8,A.70,3,5,7,9,C.90,2,4,6,8,A,C.B1,3,5,7,9,B.D0,3,5,7,9,C.F0,2,5,7,A,C;104,8.40,C.55,7.75,7.A2,5,7,A.C5,7.D0,3,9,C.F5,7:PD16PL66SNSFBV6KT9YNMKRDO95AU79ON2ALTCUX5GZG74ZVXEW43W93C1FWME2R0WBKN0KY", "hasFlower": 0, "hasSeason": 0}, {"id": "62", "q": "000,3,5,7,9,C.21,3,5,7,9,B.40,3,9,C.55,7.61,3,9,B.75,7.80,C.92,5,7,A.A0,C<PERSON>B3,9.C0,6,C<PERSON>D3,9.E0,5,7,C;113,5,7,9.21,B.40,C.53,9.61,B.76.91,B.B0,C.D0,C.E5,7;215,7.21,B.D0,C.E5,7:EMYTKKUP1NXE1MYCERTUSMNXITNI8J7IU1QOQP7QKYMUEIC7SOQGX1J48DROYDNT4O4X74GK", "hasFlower": 0, "hasSeason": 0}, {"id": "63", "q": "003,6,9.11,B.25,7.30,C.42,5,7,A.50,C<PERSON>62,5,7,A.80,2,5,7,A,C.A0,5,7,C.C1,3,5,7,9,B;106.11,B.26.30,C.42,6,A.50,C.80,5,7,C.A0,5,7,C.C1,5,7,B;206.26.30,C.42,A.95,7.A0,C.B6;330,C.A0,6,C:BMRQYM05UFUD1L5TX5CC7VDYQ2VPDTTO4Q2BEOSR7RCP1081L4TNR980NX9ESC4UDEF4EU01Q5", "hasFlower": 0, "hasSeason": 0}, {"id": "64", "q": "000,2,4,6,8,A,C.20,3,5,7,9,C.40,3,5,7,9,C.62,4,8,A.76.81,3,9,B.95,7.A0,2,A,C.B4,6,8.C1,B.D3,5,7,9.E0,C;100,2,4,8,A,C.24,6,8.30,C.46.53,9.81,B.96.A2,A.B6.C1,B.D4,6,8:UBNIUIEEJ6J267YN7QKQINJQKO2OZCUL1ZL55QE1OUZO52BIBN17ECLBZ6JK7CL56CKY21", "hasFlower": 0, "hasSeason": 0}, {"id": "65", "q": "000,2,4,6,A,C,E,G.18.20,2,4,6,A,C,E,G.38.40,2,4,6,A,C,E,G.58.60,2,4,6,A,C,E,G.78.80,2,4,6,A,C,E,G.98.A0,3,5,B,D,G.B7,9.C0,2,4,C,E,G.D6,8,A.E1,3,D,F.F5,7,9,B.G0,2,E,G:DSSFDC6PNJWNP6B8GNGGJL199LF161LTLWWCGHIOM6WOMJOT4CDBIO4M81HIJ4ISFM4N9FDSC9", "hasFlower": 0, "hasSeason": 0}, {"id": "66", "q": "000,4,6,8,C.21,3,5,7,9,B.42,4,6,8,A.50,C.63,6,9.70,C.82,4,8,A.90,6,C.A3,9.B5,7.C0,2,A,<PERSON><PERSON>D5,7.E0,3,9,C;100,5,7,C.22,5,7,A.42,6,A.50,C.63,6,9.70,C.82,4,8,A.90,6,C.A3,9.B6.C0,2,A,<PERSON>.D5,7.E0,C:QRWH7CI6FOIPLG5NSYW7SIUZQ4ZP5N71WXX4OHDD29LG7IXESSUCB3CTKCL1U3BWK6FYBR29UTXEBRRL", "hasFlower": 0, "hasSeason": 0}, {"id": "67", "q": "001,3,6,9,B.20,2,4,6,8,A,C.40,3,5,7,9,C.61,3,5,7,9,B.84,6,8.A0,3,5,7,9,C.C0,3,6,9,C.E1,5,7,B.G0,3,5,7,9,C;102,A.16.24,8.30,C.53,5,7,9.61,B.84,6,8.C0,3,9,C.E1,B.F6.G3,9;202,A.16.24,8.54,8.84,8.E1,B:A7OMH4RCASERYY8KYILUBHMVOG0XFF5ILNSQW9AGRUCVQYJ9WS7T8L2FNBJFBAWDDLCBE2KRT4C45XS40WEE", "hasFlower": 0, "hasSeason": 0}, {"id": "68", "q": "000,2,5,7,A,C.20,2,A,C.34,6,8.40,2,A,C<PERSON>54,8.60,C.72,4,8,A.86.93,9.A5,7.B0,C<PERSON>C2,4,8,A.D0,6,C.E2,4,8,A.F0,C<PERSON>G2,4,8,A;110,C.22,A.30,6,C.42,A.60,C.72,A.B0,C.D0,2,A,C.F1,B.G3,9;230,C.42,A.72,A<PERSON>C0,C.D2,A.G3,9:VZPLEQ9P18QL7FEVE3WWAMT9XPFRZ3RTVZS1HSRCNX398PJ8HQEXJHV1NTA5J75Z95XM51JSHNCSQ38RNWTW", "hasFlower": 0, "hasSeason": 0}, {"id": "69", "q": "000,2,6,8,C,E.20,2,4,6,8,A,C,E.40,3,5,7,9,B,E.61,5,7,9,D.73,B.80,5,9,E.97.A0,3,5,9,B,E.B7.C0,5,9,E.D2,7,C.E5,9;101,D.16,8.21,4,A,D.36,8.40,3,B,E.55,7,9.74,A.90,5,7,9,E.A3,<PERSON>.B0,<PERSON><PERSON>C5,9.D7:DJALDW4JQY2XZAZAJMH01ZL26GQJ1Q0GMMFQH81X60LFHLHX24PM1WZOPD8DGOPP4WO6Y86O40A2G8XW", "hasFlower": 0, "hasSeason": 0}, {"id": "70", "q": "000,2,4,6,8,A,C.20,2,4,8,A,C.36.41,3,9,B.56.60,2,A,C.75,7.90,2,5,7,A,C.B1,3,5,7,9,B.D0,2,4,8,A,C;103,5,7,9.20,C.32,A.46.60,2,6,A,C.85,7.90,2,A,C.B4,6,8.C2,A.D0,C;205,7.46.60,C.90,C.B4,6,8:SITWURKZSVNECYCWNKBMIZRS2BYTVBTGNIKUZHEXCH5RI25UEWVZBM5CLHLKHURXGYMV5LGEYLNWTMSG", "hasFlower": 0, "hasSeason": 0}, {"id": "71", "q": "000,2,4,6,8,A,C.20,2,4,8,A,C.36.40,2,4,8,A,C.60,3,5,7,9,C.80,2,4,6,8,A,C.A0,2,A,C.B4,6,8.C1,B.D3,5,7,9.E1,B;130,2,6,A,C.44,8.73,5,7,9.91,B.B4,8.C1,6,B;231,B.44,8.B4,8.C1,B;331,B.C1,B:2JVJSF2ALPQYHYA92FZ11FTZRPDTMR8VQ1M7T2P79AMQ9RL1HMFVUSSY9TLSRJA8HHUJUDDDLVPUQY", "hasFlower": 0, "hasSeason": 0}, {"id": "72", "q": "000,2,5,7,A,C.24,8.30,2,A,C.45,7.50,3,9,C.65,7.70,2,A,C.92,4,8,A.A0,6,C<PERSON>B3,9.C0,<PERSON><PERSON>D4,8.E0,6,C;100,2,5,7,A,C.30,C.45,7.53,9.60,5,7,C.72,A.94,8.A6.C0,C.E0,C;200,2,A,C.45,7.60,C.A6.D0,C;360,C.D0,C:E1IXW5EKRRRIBMKP6XOC776WZAU4VCHEI944PCEGLK5L7RLGBKUZUU9HVGLWX96P1OC575G4A9MWXI6P", "hasFlower": 0, "hasSeason": 0}, {"id": "73", "q": "000,2,4,6,8,A,C.22,5,7,A.30,C<PERSON>43,5,7,9.51,B.65,7.70,2,A,C.85,7.90,C.A2,4,8,A.B0,C.C2,4,6,8,A.E0,6,C;102,5,7,A.22,A.36.51,B.66.70,C.86.A1,B.C4,6,8.E6;202,A.36.C4,6,8.E6;3C4,8.D6;4C4,8:7Z9BWKWRISDX1UKVWVMGA7O3IADYKZ0JXX6V0G0UOBJ3UNTXAM93SP1VAB23KP0YWNB2RU6THH", "hasFlower": 0, "hasSeason": 0}, {"id": "74", "q": "001,3,5,7,9,B,D.22,5,7,9,C.42,4,6,8,A,C.50,E.62,4,7,A,C.82,C.90,4,7,A,E.B2,4,6,8,A,C.D2,5,7,9,C.E0,E;103,B.15,7,9.22,C.37.51,3,7,B,D.90,4,7,A,E.B6,8.C2,C.D6,8.E0,E;216,8.22,C.37.53,B.B6,8.D6,8:Z86E87QHEZ8FQLGK2EJID41KDFNWIL6WZ7NKPOWBDSHMEVCOBGXVPDC40Z8UCXAS10CGF2UMW1AFNGNKJ1", "hasFlower": 0, "hasSeason": 0}, {"id": "75", "q": "001,3,5,7,9.20,4,6,A.42,4,6,8.60,2,4,6,8,A.81,4,6,9.A2,4,6,8.C1,3,5,7,9.E0,3,7,A;102,8.14,6.20,A.35.42,8.54,6.60,2,8,A.74,6.81,9.95.A2,8.C1,5,9.D3,7;202,8.14,6.35.52,4,6,8.74,6.95.A2,8.C5:MPWVQR79X4OV8YQ43I6T8SVC44CWT31FKP9QZ7XOS1CCOXIDFAM9GXRYOVEMNK679J7GENDMYJQAYZ", "hasFlower": 0, "hasSeason": 0}, {"id": "76", "q": "001,3,5,7,9,B.20,3,9,C.35,7.40,C.52,4,6,8,A.60,C.72,4,6,8,A.80,C.92,4,8,A.A0,6,C.C0,2,4,6,8,A,C.E0,2,4,6,8,A,C;101,3,6,9,B.35,7.50,3,6,9,C.72,A.93,9.A0,6,C.C3,5,7,9.E0,5,7,C:RFLN4J7RYOR06X3IP9GUOPLYFUBDFB35SN0SCXFVCUGEJ179LET7Y47GYIEEDUL28416GV284RT5", "hasFlower": 0, "hasSeason": 0}, {"id": "77", "q": "000,2,5,7,A,C.20,2,4,6,8,A,C.40,2,4,8,A,C.56.61,4,8,B.76.82,A.94,6,8.A0,2,A,C.B4,8.C6.D0,2,4,8,A,C;102,5,7,A.22,6,A.30,C.43,9.66.92,6,A.A0,C<PERSON>B4,8.D2,A;206.26.43,9.66.96.A0,C.B4,8;366.96.A0,C.B4,8:LBTA802L3WRR8MGYUIGWZPUELED3YULIITZB0GPV0RWIED8DZP0YDN8NM3GM2NA2PUNRVY2WAEM3VZVA", "hasFlower": 0, "hasSeason": 0}, {"id": "78", "q": "000,2,4,7,A,C,E.20,2,7,C,E.34,A.40,6,8,E.52,C.60,4,7,A,<PERSON><PERSON>72,C.85,9.90,2,7,C,E.A5,9.B0,3,7,B,E.C5,9.D0,2,7,C,E.E4,A;100,3,B,E.17.20,2,C,E.37.51,7,D.63,B.82,5,9,C.90,7,E.A5,9.C0,6,8,E.D2,C.E4,A:0J8T5Y4ATA1GMJGY07D1JTSQ4IE6IIXS1L2E2LQX5A6ZMBZ7Y1DASDG88G0TCILSJ8MYEE0XLXC77MDB55", "hasFlower": 0, "hasSeason": 0}, {"id": "79", "q": "001,4,7,A,D.20,3,5,7,9,B,E.41,3,5,7,9,B,D.60,3,5,7,9,B,E.80,2,5,7,9,C,E.A1,3,6,8,B,D.C0,2,5,7,9,C,E.E0,3,5,9,B,E;101,4,7,A,D.23,B.36,8.41,D.54,7,A.60,E.75,7,9.80,E.97.B1,6,8,D.E0,3,5,9,B,E:PERQGE78XG41A06F4QKLZ5TFR7LGC3FTQBBA59TNU48S4CZ1NL6GN3LPNUHOUBZT9Z8099WHUX8FQ3KBS3WO", "hasFlower": 0, "hasSeason": 0}, {"id": "80", "q": "001,4,6,8,A,D.20,2,4,6,8,A,C,E.40,3,5,9,B,E.57.60,2,4,A,C,E.76,8.81,3,B,D.96,8.A0,3,B,E.B5,9.C2,7,C.D0,5,9,E.E2,C.F0,4,6,8,A,E;101,5,9,D.17.21,4,A,D.40,3,5,9,B,E.60,4,A,E.76,8.82,C.97.B5,9.D0,5,9,E.F4,A:01102OCGC2L5ULDERULQEP6BDDSSPNMGTRGBT188O8PMWWW8CTMO5TCNLMV614SBVP66GRBQ2SWR4D20QQEO404E", "hasFlower": 0, "hasSeason": 0}, {"id": "81", "q": "000,2,4,6,8,A,C.21,3,5,7,9,B.40,2,4,8,A,C.56.63,9.70,5,7,C.83,9.90,5,7,C<PERSON>A2,A.B4,6,8.C0,C.D2,4,8,A.E0,C.F2,5,7,A.G0,C;100,3,5,7,9,C.21,3,5,7,9,B.40,2,A,C.56.75,7.83,9.A2,5,7,A.D0,2,A,C.F0,5,7,C:CCASBOFZZXTM3P00SRTAECL3RLUB24QS6QP6GZIB7IHCRG2FS3RIUKFZ3I76XJ8V8NOVA7AKEF7HMJ4NJB6J", "hasFlower": 0, "hasSeason": 0}, {"id": "82", "q": "000,2,5,7,A,C.21,3,5,7,9,B.40,2,5,7,A,C.60,2,4,8,A,C.76.80,2,A,C.95,7.A1,3,9,B.C0,2,4,6,8,A,C.E0,2,6,A,C.G0,3,5,7,9,C;100,2,5,7,A,C.22,5,7,A.42,5,7,A.50,C.70,2,A,C.92,5,7,A.C0,3,5,7,9,C.E1,B.G4,6,8:X0APC0D5LMX03CB64LRMBR3LMP6CTK2DA25X2P8RVW4A6KJWD85TM6WPKTU4KJU8XR0U2VUWAT3834BJBJC5DLVV", "hasFlower": 0, "hasSeason": 0}, {"id": "83", "q": "000,3,5,7,9,B,E.21,4,6,8,A,D.40,2,4,6,8,A,C,E.60,2,5,9,C,E.77.80,2,C,E.95,9.A0,7,E<PERSON>B2,4,A,<PERSON><PERSON>C0,7,<PERSON><PERSON>D4,<PERSON><PERSON>E0,6,8,E;103,6,8,B.26,8.31,4,A,D.46,8.52,C.60,E.72,C.90,E.B2,C<PERSON>C4,7,A.D0,E.E7;203,6,8,B.36,8.52,C.D0,E:CQETG1AAPSIX7XL7LXY6OIRSQTRPTO2K13PUK6WRS8OXDI806ILNY2GDRDCWAD8UU600A73NUETPL7B3EE3O08BS", "hasFlower": 0, "hasSeason": 0}, {"id": "84", "q": "000,2,6,A,C.14,8.21,B.33,5,7,9.50,2,4,8,A,C.66.73,9.80,5,7,C.93,9.A1,5,7,B.B3,9.C0,5,7,C;100,2,6,A,C.21,B.33,5,7,9.50,2,4,8,A,C.80,4,6,8,C.A1,3,5,7,9,B.C0,C;221,B.33,6,9.52,4,8,A.84,8.96.A3,9.C0,C:XX3E2WMMW3D6YYATTLIL6I2DAM7BHZUWYM8EUA1I3YG8XBUJJB7JDTWCL8ELZ76G8XIH6TCJUB711GGDAE13", "hasFlower": 0, "hasSeason": 0}, {"id": "85", "q": "000,2,4,6,8,A,C.20,2,5,7,A,C.40,4,6,8,C.60,2,4,6,8,A,C.80,2,4,8,A,C.A0,3,5,7,9,C.C1,3,5,7,9,B.E0,2,4,6,8,A,C.G0,3,5,7,9,C;101,4,8,B.21,B.36.40,C.65,7.70,C.82,4,8,A.A0,C<PERSON>B5,7.C2,A.E1,6,B.G5,7:4VGJTOS8OJP3GI4CZPODA5AWJ4S0KZWC3TZ88BJ0035PTMWKBADD0M8ACBDKSCPMBO5E4GVZSKVWIVGMT53E", "hasFlower": 0, "hasSeason": 0}, {"id": "86", "q": "001,3,5,7,9,B,D.20,2,4,6,8,A,C,E.41,3,6,8,B,D.60,3,5,7,9,B,E.80,2,4,6,8,A,C,E.A0,3,6,8,B,E.C0,2,5,9,C,E.D7.E0,2,4,A,C,E;102,C.15,9.27.32,C.46,8.63,5,9,B.81,4,6,8,A,D.A7.C2,5,9,C.D7:7CPM5Z730UQK89XTG8XDVKP351BLG93YMN880OSZNC24VU4CTO1YUQZ9X2RUBE439IRCMDIX4S7EM7ZL", "hasFlower": 0, "hasSeason": 0}, {"id": "87", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,2,4,7,A,C,E.60,2,6,8,C,E.80,2,4,A,C,E.A0,3,5,7,9,B,E.C0,3,5,9,B,E.D7.E0,3,B,E.F6,8.G1,D;100,E.13,5,7,9,B.21,D.34,A.52,C.60,E.A3,6,8,B.B0,E<PERSON>C3,B.F6,8.G1,D:L20E7RCJC97W3JYJZLW0ONM3ML632GGCJH3XTXB719O2SYNOQS20EMH5WB556R0XNMN1L7XGQ5CSOGZWTTTS", "hasFlower": 0, "hasSeason": 0}, {"id": "88", "q": "001,3,5,7,9,B,D.21,3,5,7,9,B,D.41,3,5,7,9,B,D.60,2,4,6,8,A,C,E.80,2,4,6,8,A,C,E.A2,4,6,8,A,C.C0,2,4,6,8,A,C,E.E0,2,4,6,8,A,C,E;105,7,9.13,B.37.43,B.51,D.73,5,9,B.A2,C.C7.D2,4,A,C:9PRYVO593U3K35RISJ6DOILLRKUVLZTGC229D0KFDRVZ1TVXYGTD29JYUW0GFUK2N1SWYFCPNTLFGX63", "hasFlower": 0, "hasSeason": 0}, {"id": "89", "q": "000,2,4,7,A,C,E.26,8.32,4,A,C.40,6,8,E.60,2,5,9,C,E.80,2,6,8,C,E.A0,5,7,9,E.B2,C<PERSON>C4,6,8,A.E0,2,4,6,8,A,C,E;100,4,A,E.17.36,8.40,E<PERSON>71,D.87.A5,7,9.B2,C<PERSON>C6,8.E2,4,6,8,A,<PERSON>;217.71,D.A5,9.B2,7,C.E5,7,9:I2SH7787PNCDTBAAZDE0TCJFSXJUP9QV3QD8F0EIA913EX14D116UC62CJSBXHLAHGX40ZL9NS0EJ79HGUUV", "hasFlower": 0, "hasSeason": 0}, {"id": "90", "q": "002,4,6,8,A.10,C.22,4,6,8,A.40,2,4,6,8,A,C.60,4,8,C.76.80,2,A,C.96.A0,3,9,C.B5,7.C0,2,A,<PERSON><PERSON>D4,6,8.E1,B<PERSON>F3,5,7,9.G0,C;103,5,7,9.22,5,7,A.40,2,4,8,A,C.64,8.76.80,C.A3,6,9.C0,2,5,7,A,C.E1,5,7,B.F3,9:NKLHFKRDENJZ0A41859VD70MFWLQNVO5QD30ESP310BP818HNP94MZA87XW51XBBFEOEU2F59R2D2JU2B9PS", "hasFlower": 0, "hasSeason": 0}, {"id": "91", "q": "001,4,6,8,B.20,3,5,7,9,C.40,3,5,7,9,C.60,2,5,7,A,C.80,3,5,7,9,C.A0,2,4,6,8,A,C.C1,3,5,7,9,B.E0,2,4,6,8,A,C;101,4,8,B.23,9.35,7.43,9.50,C.62,6,A.85,7.93,9.A0,C<PERSON>B3,5,7,9.D3,9.E1,5,7,B:HK3XJDS54B85UX9A1LKO8KJKQB1GU2XG3WLI4DH5TRVZ07X27GAV29J0QHN25CROSISSFTG9FHJZ9WCN", "hasFlower": 0, "hasSeason": 0}, {"id": "92", "q": "000,2,4,8,A,C.16.21,4,8,B.36.41,3,9,B.55,7.61,3,9,B.75,7.82,A.96.A1,3,9,B.B5,7.C0,3,9,C.D5,7.E2,A<PERSON>F4,6,8.G0,C;100,C.14,6,8.21,B.41,B.54,6,8.76.82,A.96.B4,8.C0,6,C.E2,A.F5,7;214,8.21,B.41,B.55,7.76.82,A.B4,8.C0,C.F5,7:ZT79KM0MB1ND34MUCE9OJBT7EB59JGUK8RYFC5YFDF1IMV1V29Y6BPD4L6ID778JZ3O0NGYAL4UIP841JI8A2FUR", "hasFlower": 0, "hasSeason": 0}, {"id": "93", "q": "000,2,4,7,A,C,E.21,4,A,D.36,8.40,2,4,A,C,E.61,3,7,B,D.75,9.80,2,7,C,E.A4,A.B0,2,6,8,C,<PERSON><PERSON>C4,A<PERSON>D1,<PERSON><PERSON>E4,7,A;100,4,A,E.24,A.37.40,2,4,A,C,E.61,7,D.81,D.B0,2,4,7,A,C,E.D4,A.E7;200,E.24,A.42,C.B2,C.D4,A:PNXK3RS5US1WHBPQVSLVNGQ6VSTCRCR9V7RU5Q61WOCHE2QD6JW3LEK7FL9DXZNUJNZGLWF6OCUXX2PHBPHT", "hasFlower": 0, "hasSeason": 0}, {"id": "94", "q": "000,2,6,A,C.21,3,5,7,9,B.40,3,5,7,9,C.60,4,6,8,C.80,2,5,7,A,C.A0,3,5,7,9,C.C0,2,5,7,A,C.E0,4,6,8,C.F2,A.G0,5,7,C;111,6,B.23,9.40,3,6,9,C.60,4,6,8,C.86.A4,6,8.B0,C.C2,5,7,A.D0,C.F0,2,5,7,A,C:LUQU1OE7GYR1RYFAGFQUHDLQLN8TTNXE6D49RW192VLJAXFCX33SXK2UR304T6FC1TVJ973JDHKJ80SOQWD9", "hasFlower": 0, "hasSeason": 0}, {"id": "95", "q": "002,7,C.10,4,A,E.26,8.30,2,4,A,C,E.46,8.50,2,C,E.64,6,8,A.71,D.83,5,9,B.90,E.A2,4,6,8,A,C.C0,2,5,7,9,C,E.E0,2,4,6,8,A,C,E.G0,2,4,6,8,A,C,E;120,5,9,E.32,C.40,6,8,E.71,4,A,D.94,A.B2,C.D0,2,6,8,C,E.F0,5,9,E:LDGKRW4WNZJA8D9FXJMKSCBQRCA10ONN1GZWGRJQGU49CZAUSK0DW98JNLQ0OQZ9FMYSM80ORBMKU8YLXDUXXCLASO", "hasFlower": 0, "hasSeason": 0}, {"id": "96", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.42,4,6,8,A.50,C<PERSON>64,8.71,6,B.84,8.90,2,6,A,C.A4,8.B0,C.C2,4,6,8,A.E0,2,4,6,8,A,C.G2,4,6,8,A;104,8.12,A.20,4,8,C.36.42,4,8,A.64,8.76.84,8.90,2,6,A,C.B0,4,8,C.C2,A.D4,6,8.F4,8.G2,A:NWYXTBGC8CRVI0VN0M1MJ55LBWGPKSZZRRMG4NXTGV6BPS24KI6LRWHT6ISK4Y6IXJ8XTV0H55WK8BZYN20YZ8M14S", "hasFlower": 0, "hasSeason": 0}, {"id": "97", "q": "000,3,6,8,B,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,2,5,7,9,C,E.80,2,5,7,9,C,E.A0,3,7,B,E.B5,9.C0,2,7,C,E<PERSON>D4,A.E1,6,8,D.F3,B.G0,E;113,B.21,5,7,9,D.41,4,A,D.56,8.62,C.80,6,8,E.A3,B.C7.D1,4,A,D.E6,8.F3,B:U4DI4WVHRFCALGH3FVNCZAGFUD77LIRATF41GPUHW611PT4NVTWHP6BXTZ3GZP6IBX7EACLB7IUEVEZE6WDLBDC1", "hasFlower": 0, "hasSeason": 0}, {"id": "98", "q": "001,3,6,8,A,D,F.20,3,5,8,B,D,G.40,3,5,B,D,G.58.61,3,5,B,D,F.78.80,2,5,B,E,G.97,9.A0,2,4,C,E,G.B6,8,A.C3,D.D0,5,B,G.E2,7,9,E.F0,4,C,G.G2,6,A,E;106,8,A.25,B.30,G.54,C.62,8,E.97,9.B3,6,8,A,D.D0,G.E2,E.G2,6,A,E:W8SVF12VPPZPA5ZXFQFXHQE061E2QLOOBHBZ28KRGKGYVOH7OSPE7L4LXBLSH1V6RYF52G1R6R08BEW84ZA7G7SX6Q", "hasFlower": 0, "hasSeason": 0}, {"id": "99", "q": "000,2,4,6,8,A,C.20,2,4,8,A,C.42,4,8,A.50,6,C.62,A.70,5,7,C.91,4,8,B.A6.B1,B.C6.D2,4,8,A.F0,2,4,8,A,C;112,4,8,A.34,8.42,A.56.60,2,A,C.91,4,8,B.C6.D2,A.E4,8;212,4,8,A.42,A.62,A.91,B.D2,A;312,4,8,A.52,A:Q3HYZMVVZ0VR7FX7B1KF3WHK4BXHCCWQV2WIQOTQEB8RSP0ZCI8PYD7ELMEH5182ZW5LMLL8CEMO734TS3BD", "hasFlower": 0, "hasSeason": 0}, {"id": "100", "q": "000,3,5,7,9,C.22,6,A.34,8.40,2,A,C.54,6,8.60,C.72,4,6,8,A.92,4,6,8,A.B0,3,5,7,9,C.D0,2,5,7,A,C.F2,6,A.G0,C;100,4,6,8,C.26.33,9.40,C.54,8.66.72,4,8,A.94,8.B3,5,7,9.D0,2,6,A,C.F6;200,5,7,C.26.73,9.94,8.D1,B.F6:BM8TIVKKYJBFCIH5KL8HAHO5RFFNB0CYHZ02JBNW1R28ZNKLTVM08TPA6PJCJWVX50MZXT6ACXAOM6FO1OVNZX65", "hasFlower": 0, "hasSeason": 0}, {"id": "101", "q": "001,3,6,9,B.20,3,5,7,9,C<PERSON>41,3,9,B.56.61,3,9,B.75,7.80,2,A,C.94,6,8.A2,A.B4,6,8.C0,2,A,<PERSON>.D4,8.E1,6,B.F3,9.G0,5,7,C;101,3,6,9,B.20,C.43,9.62,A.80,2,5,7,A,C.A2,A.B4,8.C0,C<PERSON>D4,8.E6.G5,7;202,6,A.80,6,C.C0,4,8,C.E6.G6;306.80,C.E6.G6:5R8INKY9GL3E2OFCPPKXBZVRA1RVX37AC1EOG3OXXQZQIYOUVF85FRC8WNE3UP1Y2LKWV7FUE9PU82BCB5B2959WQYKW1Q", "hasFlower": 0, "hasSeason": 0}, {"id": "102", "q": "002,7,C.10,5,9,E<PERSON>22,7,C.30,E<PERSON>42,5,7,9,C.61,4,6,8,A,D.80,2,4,6,8,A,C,E.A1,3,6,8,B,D.C3,6,8,B.D1,D.E7.F0,2,4,A,C,E.G6,8;102,C.10,5,7,9,E.31,7,D.45,9.61,4,6,8,A,D.80,4,7,A,E<PERSON>A2,7,C.C3,B.D1,D.E7.F0,3,B,E.G7:2DT3SGVNORIWLIL16G2627TSQ88Q3PDWC3WSLGS7IIN6J18PVTRV6D87XLVRRNQXO1XDTN3PCXCPWQOJGOJ2J7C1", "hasFlower": 0, "hasSeason": 0}, {"id": "103", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.41,3,5,7,9,B.60,3,5,7,9,C.81,3,5,7,9,B.A0,2,4,6,8,A,C.C0,2,4,6,8,A,C.E0,2,4,6,8,A,C.G0,4,6,8,C;102,A.10,4,8,C.33,9.46.63,9.86.A4,8.D2,6,A.G0,4,8,C;202,A.10,C.86.D2,A.G0,C:SZLDMH1GSOWYM4DYZKUX475KO3RUS8R8MGBQ4MQEG866RBPWGL33W15IU3WPIDZVPHASDP8ZAR2V092XE0UV4V79", "hasFlower": 0, "hasSeason": 0}, {"id": "104", "q": "005,7,9.11,3,B,<PERSON><PERSON>25,9.30,2,7,<PERSON>,<PERSON><PERSON>44,<PERSON><PERSON>52,C<PERSON>64,6,8,A<PERSON>70,E<PERSON>83,5,9,B.A2,4,A,C<PERSON>B0,7,E.C2,4,A,<PERSON>.D6,8.E1,3,B,D;106,8.11,3,B,D.25,9.31,7,D.52,4,A,C.67.74,A.93,B.B0,2,C,E.C7.D2,C;206,8.11,D.25,9.37.54,A.C7.D2,C:I9GGY73GWYTUGC097HK1QZ3FCAQ3W9B1SZ1KJ0P435YUIHJQJT4W5U41IF55U7ZSYHHPIV4ZKV7CJQBCAW9K", "hasFlower": 0, "hasSeason": 0}, {"id": "105", "q": "002,4,6,8,A.10,C.22,4,6,8,A.30,C<PERSON>43,5,7,9.50,C<PERSON>62,<PERSON><PERSON>70,6,C.82,4,8,A<PERSON>90,6,<PERSON><PERSON>A2,<PERSON><PERSON>B0,<PERSON><PERSON>C3,9.D5,7.E0,3,9,<PERSON><PERSON>F5,7.G0,2,A,C;111,B.23,5,7,9.40,3,9,C.70,C.85,7.90,C<PERSON>C3,9.E6.G1,<PERSON>;223,9.43,9.85,7.C3,9;333,9.86.C3,9;433,9:I9D69OURKJXXIC9GZADXG13IJ60IK6NJ0GX1N8RP1BJNYGYC1RRBAVDTCV3OANKVDY0YTT698PU0TZ3KCOVA3O", "hasFlower": 0, "hasSeason": 0}, {"id": "106", "q": "002,4,6,8,A.10,C.23,5,7,9.30,C.42,4,6,8,A.50,C.62,4,8,A.70,C.86.A0,2,4,8,A,C.C0,2,5,7,A,C.E0,2,5,7,A,C.G0,3,5,7,9,C;102,A.10,4,8,C.33,6,9.40,C.54,8.61,B.86.A0,4,8,C.B2,<PERSON><PERSON>C0,<PERSON><PERSON>D2,6,A.F5,7.G0,3,9,C:POAA4B0I8PA7EQM49MQKF9874FEL7O0Y7S2BQBR4N109EA5KR10MRY2E5FO89MOSXY2IX8KNKBP2PFRDLQYD", "hasFlower": 0, "hasSeason": 0}, {"id": "107", "q": "000,3,5,7,9,B,E.20,2,5,7,9,C,E.41,3,5,9,B,D.61,5,7,9,D.80,4,7,A,E.92,<PERSON><PERSON>A0,E.B3,5,9,B.C0,7,E.D2,5,9,<PERSON>.E0,7,E;107.21,5,7,9,D.41,4,A,D.61,5,9,D.77.84,A.91,D.B0,4,A,E.C7.D1,5,9,D;207.25,9.41,4,A,D.77.B4,A.D5,9:AXKCZJQ6TUBZNDXSQS53MXP8YY40R2CRTOJC9KGMO1Z20RZKPCO91445OPTBKY8Q3BNYAGP4S99DQRX6BNAUSNTA", "hasFlower": 0, "hasSeason": 0}, {"id": "108", "q": "002,4,6,8,A.10,C.23,5,7,9.30,C.42,4,6,8,A.50,C.62,4,8,A.70,6,C.96.A0,2,4,8,A,C.C0,2,5,7,A,C.E0,2,5,7,A,C.G0,3,5,7,9,C;113,5,7,9.20,C.33,9.50,2,4,8,A,C.70,C.A0,2,A,C.C0,2,5,7,A,<PERSON><PERSON>E0,C.F6.G3,9;223,9.43,9.70,C.A0,C.B2,A.F6:3OJ9XHG9GFKP219KD9FXX11TU563H6FNN7BX3B2S0UH05JOSU8Q6S52DTY07Y5T87HQFYDOS7G6DQQKB01OKJ3BJ2GTPYU", "hasFlower": 0, "hasSeason": 0}, {"id": "109", "q": "000,2,4,8,A,C.16.20,2,A,C.34,8.40,2,A,C.54,8.70,2,4,6,8,A,C.90,2,5,7,A,C.B3,9.C1,5,7,B.D3,9.E0,5,7,C.F2,A.G0,5,7,C;100,2,A,C.16.22,A.34,8.40,C.64,8.76.81,B.95,7.B3,9.D3,9.F2,A.G6;216.40,C.64,8.76.81,B.G6;340,C.81,B:WEINZG10N1GN2Q7RMQ2X87M5VIQY67S0ZUEHH1S86WV6WJFWFRPOMBTBSO2Y1FUT9XYI9JP9MZ97IZ52YNS6QFLL", "hasFlower": 0, "hasSeason": 0}, {"id": "110", "q": "000,3,6,9,C.20,3,5,7,9,C.41,3,5,7,9,B.60,2,4,6,8,A,C.80,2,5,7,A,C.A0,5,7,C.B2,A.C6.D1,3,9,B<PERSON>E5,7.F0,3,9,C.G5,7;100,6,C.23,5,7,9.41,3,5,7,9,B.60,2,4,6,8,A,C.80,2,6,A,C.B2,6,A.D1,3,6,9,B.F3,5,7,9:SLND1HV4PDG1F7N3RPSYGDJQLSQGVOM0FM7M474XPL18FFH7MY43GRR0QPRCQ0LN0JX8Z3D1ZCJOHEJSHEN3XX", "hasFlower": 0, "hasSeason": 0}, {"id": "111", "q": "000,2,6,8,C,E<PERSON>14,A<PERSON>22,C<PERSON>36,8.43,B<PERSON>50,5,9,E<PERSON>62,7,C.70,4,A,E<PERSON>86,8.90,3,B,E.A5,9.B0,2,7,C,<PERSON><PERSON>C4,A<PERSON>D7.E2,<PERSON><PERSON>F5,9.G0,2,7,C,E;101,D.13,B.36,8.43,B.55,9.60,E.77.93,B.B0,E<PERSON>C4,A.D7.F5,9.G0,2,7,C,E;237.60,E.77.B0,E.F5,9.G1,7,D:YZ4PNMAYHC5PY94A7JGG4KDM39N2J9ICVUZRNGRMO5MH3H25K3A3BT7D72JZ4RKZHC2OUGP7KYP5SVIACTBJ9SRN", "hasFlower": 0, "hasSeason": 0}, {"id": "112", "q": "001,3,6,9,B.20,2,4,8,A,C.40,2,5,7,A,C.60,4,6,8,C.72,A.80,5,7,C.92,A.A0,4,6,8,C.C0,2,A,<PERSON>.D5,7.E0,2,A,C.F4,8.G0,2,6,A,C;102,A.24,8.42,6,A.60,4,8,C.80,2,A,C.A4,8.B0,C.D2,6,A.F4,8;202,A.24,8.60,C.82,A.B0,C.F4,8:IPG1GKJMK4V2Z619YZK8ILE4C9KIQCH3CJ4YE7Z1Q5X9X7YP4OEH25LNX53J5R9LPRJ1P778MFQOVFQC6LIZYXEN", "hasFlower": 0, "hasSeason": 0}, {"id": "113", "q": "000,2,4,6,8,A,C,E.20,3,7,B,E.40,2,5,7,9,C,E.61,3,6,8,B,D.80,2,5,9,C,E.A0,2,4,6,8,A,C,E.C0,2,4,6,8,A,C,E.E1,4,6,8,A,D.G0,2,4,6,8,A,C,E;100,2,5,9,C,E.17.40,6,8,E.80,E.A5,7,9.C7.D1,4,A,D.G1,4,7,A,D:JCMA13AHDI76LMB9FAVGX0WV9U8IUG1LGUHLJK8B0DOO99V8MA1DZB7FZFZ0KCFUD6GMV07COZXW1RRKKLB7C83O", "hasFlower": 0, "hasSeason": 0}, {"id": "114", "q": "000,2,5,7,9,C,E.20,2,4,6,8,A,C,E.41,4,6,8,A,D.61,4,6,8,A,D.81,4,6,8,A,D.A0,3,5,9,B,E.B7.C0,2,5,9,C,E.E0,2,4,A,C,E;101,5,9,D.17.21,4,A,D.45,7,9.65,7,9.71,D.84,6,8,A.A3,B.B0,5,7,9,E.C2,C.E1,D:0ZMUIGAN0XIQQSOR57U6KK5RB39B12SIC728336ITRKKRJ78BAOZNTA11C6C52B4J50X4693M1GF072XCAFX", "hasFlower": 0, "hasSeason": 0}, {"id": "115", "q": "000,2,4,6,8,A,C.22,4,6,8,A.40,2,4,6,8,A,C.60,2,4,6,8,A,C.80,4,6,8,C.A1,4,6,8,B.C1,3,5,7,9,B.E2,5,7,A.G0,3,5,7,9,C;114,6,8.22,A.35,7.63,5,7,9.85,7.B5,7.E5,7.G0,4,6,8,C;235,7.64,8.85,7.E5,7:FZ47O4MAWW5Z1HW9HFTKXH1XK2R45ZNM91RHDXA9RFMRIZNEYTIYNTEW5FYA57X9YI726MA1NOI67T6D64", "hasFlower": 0, "hasSeason": 0}, {"id": "116", "q": "000,2,4,6,8,A,C,E.20,2,C,E.35,9.40,7,E<PERSON>52,4,A,<PERSON><PERSON>60,6,8,E.72,4,A,<PERSON><PERSON>80,7,E.93,B.A0,6,8,E.B2,4,A,C.C6,8.D0,4,A,E.E2,6,8,C.F0,E.G3,5,7,9,B;103,6,8,B.11,D.30,E.52,C.60,6,8,E.87.A6,8.B2,<PERSON><PERSON>C5,9.D0,E.E2,<PERSON>.F0,E.G4,A:ORJGCFWZNQRONQIR66XF6WOP47CVI3SUCNB6VJ451L5EXJDFELPPGUOZX7CBPXDFLN3B1BI5EG57SG7QSSLQJIER", "hasFlower": 0, "hasSeason": 0}, {"id": "117", "q": "001,3,5,7,9,B.22,4,6,8,A.43,5,7,9.50,C.66.70,2,4,8,A,C.86.93,9.A0,5,7,C<PERSON>C0,3,5,7,9,C.E1,3,5,7,9,B.G0,2,5,7,A,C;102,6,A.14,8.22,A.34,6,8.60,C.73,9.86.93,9.B0,5,7,C<PERSON>C3,9.D5,7.E1,3,9,B.G0,2,5,7,A,C:3CBA9I3AD9XJDBCLB3JOBIV02QT2JC8TJX12IT96VX0OY9Q11KK8QLVNK8DOYCL6ON2LD3K0Q860TYYVI1X6", "hasFlower": 0, "hasSeason": 0}, {"id": "118", "q": "001,4,6,8,A,D.20,2,5,7,9,C,E.42,4,A,C.50,6,8,E.64,A<PERSON>70,2,7,C,E.85,9.97.A0,2,4,A,C,E.C0,2,5,7,9,C,E.E3,6,8,B;101,4,6,8,A,D.20,5,7,9,E.43,B.50,E.64,A.70,E.A0,3,B,E.C6,8.E3,6,8,B;204,6,8,A.20,6,8,E.C6,8.E7:LSG2VL2UFR5OGFBT5OEQUPSHMMCYMRXIXIRZLVE2351EA6TZCH15993ULEJQ2CHAT8PCMKBPHNPRTKQQNY6JGG8U", "hasFlower": 0, "hasSeason": 0}, {"id": "119", "q": "000,3,5,7,9,C.20,3,5,7,9,C.40,5,7,C.61,3,9,B.75,7.80,2,A,C.95,7.A0,2,A,C.C0,6,C.E0,2,4,6,8,A,C.G1,3,5,7,9,B;106.20,4,8,C.40,5,7,C.72,A.80,C.A2,A.D0,C.E4,8.G1,B;206.20,4,8,C.40,5,7,C.72,A.80,C.A2,A.D0,C.E4,8.G1,B:5I6EFAN5N0SZLPU2VZFKENKZHKVXRJP8OUR7K6EVJD2297XZC9LQ1CC068VFADNIQH21CQ5IWSIH5QGOF1EHW1OO6G", "hasFlower": 0, "hasSeason": 0}, {"id": "120", "q": "000,2,4,6,8,A,C,E.23,5,9,B.30,E.43,7,B.60,3,5,7,9,B,E.80,2,5,7,9,C,E.A2,6,8,C.B0,4,A,E.C2,C.D5,9.E1,3,7,B,D;101,4,6,8,A,D.23,B.30,E.43,B.57.77.82,C.96,8.A2,C.B0,4,A,E.E1,3,B,D;201,D.23,B.43,B.92,6,8,C.B0,E:PM1HRWP1QLGWPJY2XSRL4AWWAKCITOIJ39AGPX27G1M6F3DQQFGS7V1INMODH70TA49LMS76NKY5I0JCQFSLJVF5", "hasFlower": 0, "hasSeason": 0}]