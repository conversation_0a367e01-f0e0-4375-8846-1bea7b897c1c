[{"id": "1", "q": "000,3,7,A.15.21,3,7,9.35.40,3,7,A.55.60,3,7,A.75.80,2,8,A.94,6.A0,A;103,7.15.22,8.34,6.53,7.75.94,6;234,6:AA8B62Z2BH6FTLH86AST1SAS61121ZFTLS8KKNN82T", "hasFlower": 0, "hasSeason": 0}, {"id": "2", "q": "000,2,4,6,8.20,3,5,8.41,7.61,4,7.81,7;100,2,6,8.20,3,5,8.41,7.61,7.81,7;200,8.23,5.41,7.61,7.81,7;341,7:2AKG2LKR88AULRAA524UF48UF545UL5R8L2KK4FRGF", "hasFlower": 0, "hasSeason": 0}, {"id": "3", "q": "000,2,4,6,8,A.20,2,4,6,8,A.40,2,4,6,8,A.60,3,5,7,A.81,3,5,7,9.A0,2,4,6,8,A.C0,2,4,6,8,A;102,8.35.55.63,7.85.A5:QEMH0KE0FOF2DOMEOS6HKE2SO2DDD2MQ0Q06MFFHHK6QSS6K", "hasFlower": 0, "hasSeason": 0}, {"id": "4", "q": "000,2,4,6,8,A.20,2,4,6,8,A.40,2,4,6,8,A.60,2,4,6,8,A.80,2,4,6,8,A.A0,2,4,6,8,A.C0,2,4,6,8,A:VFC4HHDHT7VTUD72CO7C72CDT4O24OUHVFF4V2DOFT", "hasFlower": 0, "hasSeason": 0}, {"id": "5", "q": "001,4,7.20,3,5,8.41,4,7.60,2,6,8.74.82,6.90,4,8;101,4,7.23,5.41,7.60,2,6,8.82,6.94;214.41,7.60,2,6,8.94;360,8:NGBRGLVLN9LNV8EN3B9BVHIRE3RIIGT8ITBVLERHGTTE", "hasFlower": 0, "hasSeason": 0}, {"id": "6", "q": "000,2,5,8,A.20,2,4,6,8,A.40,2,5,8,A.61,4,6,9.80,3,5,7,A.A1,3,5,7,9;110,A.23,7.35.41,9.61,4,6,9.83,5,7.A3,7;284,6:TNK3SD35FJ9JT7FMPJASM75S2NCKC9SDJ9CNCTDPNT7A27D9", "hasFlower": 0, "hasSeason": 0}, {"id": "7", "q": "000,2,4,6,8,A,C.23,5,7,9.30,C.42,6,A.61,3,5,7,9,B.81,5,7,B.93,9.A0,5,7,C.B3,9.C0,5,7,C;103,9.30,C.A0,3,9,C.C0,C;230,C.A0,C:BHYBB877QVQY5W5C333CC8HGQ530WY7WD5QVGHD80HVYVC8W0B07", "hasFlower": 0, "hasSeason": 0}, {"id": "8", "q": "001,3,7,9.20,A.32,4,6,8.50,2,5,8,A.71,3,5,7,9.90,<PERSON><PERSON>A2,8;101,3,7,9.20,A.52,8.71,9.90,A<PERSON>A2,8;201,9.20,A.90,<PERSON><PERSON>A2,8;301,9.20,A.90,A:8EKDN6P8LCN0D1PMMLDKG9HV1WXFDI0APCWSHGEMXMVIFP926AS2", "hasFlower": 0, "hasSeason": 0}, {"id": "9", "q": "001,4,6,9.20,2,4,6,8,A.40,3,5,7,A.60,A.73,5,7.80,A.92,4,6,8.A0,A;104,6.11,9.24,6.40,4,6,A.60,A.74,6.90,4,6,A:DYY822FWPKZ770KCITIRTVD90EMSP68SWRE6FVM89ZNNC8", "hasFlower": 0, "hasSeason": 0}, {"id": "10", "q": "000,2,4,6,8,A.20,2,4,6,8,A.40,2,4,6,8,A.60,3,5,7,A.80,2,4,6,8,A.A0,2,4,6,8,A;105.10,3,7,A.25.32,8.54,6.73,7.85.91,9.A3,7:484OITS8S2O4GM22THYX1SFVMSL40LM8FE1TVMTEG2XYF0LIFHL8", "hasFlower": 0, "hasSeason": 0}, {"id": "11", "q": "000,2,4,6,8.20,2,6,8.41,7.54.60,2,6,8.74.80,2,6,8.A0,2,6,8.C0,4,8;100,2,4,6,8.31,7.54.60,8.A1,7.C0,4,8;200,2,6,8.C4:40Q75QB0Q1V0U7JBXQVJ54KBKYXNUUP1KXKPBUX3043IYI4N", "hasFlower": 0, "hasSeason": 0}, {"id": "12", "q": "001,3,5,7,9,B.20,2,5,7,A,C.40,2,4,6,8,A,C.60,4,8,C.80,2,4,6,8,A,C.A2,4,6,8,A.B0,C.C2,4,6,8,A;111,6,B.30,6,C.43,9.82,4,8,A.A6.B0,C.C2,6,A:N2YDG56MEJPJMS0L36SY4ZV6E91Z7Z6075N4BI2LI0DTO9T21J3PBV2JG0ZO", "hasFlower": 0, "hasSeason": 0}, {"id": "13", "q": "003,5,7.10,A.22,5,8.30,A.42,4,6,8.60,2,5,8,A.83,7.90,5,A.A2,8.B0,5,A.C2,8;103,5,7.10,A.44,6.60,2,5,8,A.90,5,A<PERSON>B0,A<PERSON>C2,8;203,7.44,6.90,A.C2,8:IBHDDSGBS8GWX3GN8AL9H3UXAULBDIU3WN6NAB6WXAUSD8XN6G69S8WI3I", "hasFlower": 0, "hasSeason": 0}, {"id": "14", "q": "000,2,4,6,8,A.20,2,4,6,8,A.40,2,4,6,8,A.60,2,4,6,8,A.80,2,4,6,8,A.A0,2,4,6,8,A.C0,2,4,6,8,A.E0,2,4,6,8,A;104,6.11,9.55.63,7.83,7.A5.D1,9:CVD19QV7L0OO2QA88O77SJ02QDVS8ZZ0CX917A8QDCLFVO9LF910S1LJCXDS", "hasFlower": 0, "hasSeason": 0}, {"id": "15", "q": "000,2,4,6,8.20,2,4,6,8.41,7.60,3,5,8.80,2,6,8.A2,4,6.B0,8.C3,5;101,3,5,7.21,7.41,7.60,4,8.80,2,6,8.A4;202,6.21,7.41,7.80,2,6,8.A4;382,6:5KA58E3040OK2E19UIO9I833I2114OUEU5EI2148AU49OK2053890KAA", "hasFlower": 0, "hasSeason": 0}, {"id": "16", "q": "000,3,5,7,A.20,3,5,7,A.41,4,6,9.60,2,8,A.80,3,7,A.A0,2,4,6,8,A.C0,2,4,6,8,A;100,4,6,A.23,5,7.41,4,6,9.60,A.80,A.93,7.A0,A.B2,5,8:YD0GLZDWRXWR7DL0R95OZXNNO9ZL0Y7XT59OYNRE0E779GGTNLDOYZGX", "hasFlower": 0, "hasSeason": 0}, {"id": "17", "q": "000,2,4,6,8,A.20,2,4,6,8,A.40,2,4,6,8,A.61,3,5,7,9.80,2,4,6,8,A.A0,2,4,6,8,A.C0,2,4,6,8,A;102,8.24,6.32,8.44,6.61,9.83,7.91,9.A5.C1,9:R6QSB9Y6T36BCFCIEXMIDXTYDSHIRIEROX3ECM9HTECOF6QDDTF393XFR9", "hasFlower": 0, "hasSeason": 0}, {"id": "18", "q": "001,3,5,7,9,B.21,3,9,B.40,2,4,6,8,A,C.60,2,4,8,A,C.80,2,4,6,8,A,C.A0,2,A,C.B4,8.C1,6,B;101,5,7,B.43,9.61,3,9,B.82,4,6,8,A.A0,C.B4,8.C1,B:4VL4JDJSSUZZESPXBBPD1PVYEEO7XPDXBXVOU1UO1LU1SYEYLZVZLYOJ7BDJ", "hasFlower": 0, "hasSeason": 0}, {"id": "19", "q": "001,4,6,8,B.20,5,7,C.32,A.44,8.50,2,6,A,C.70,3,9,C.85,7.92,A.A0,4,8,C.B6.C0,2,A,C;104,6,8.25,7.32,A.44,8.50,C.70,C.85,7.A0,4,8,C.C1,B:IS4O9SH5F3G7543SI568HO6K8B394FBPPFH8GHP8KW34WKKIS5PGI7FG", "hasFlower": 0, "hasSeason": 0}, {"id": "20", "q": "000,2,5,7,A,C.20,2,5,7,A,C.40,2,4,6,8,A,C.60,2,4,6,8,A,C.80,2,6,A,C.94,8.A0,2,A,C.B5,7.C0,2,A,C;102,5,7,A.22,A.46.50,2,A,C.80,2,A,C.B2,A:RBZJN0FADEJ54B908LUNF9YZADV6P2HMT2OPQFU583H6TVII43ROLYEXFQXM", "hasFlower": 0, "hasSeason": 0}, {"id": "21", "q": "001,3,5,7,9.22,5,8.30,A.44,6.50,A.63,5,7.70,A.82,8.95.A0,2,8,A;101,5,9.22,8.30,5,A.50,4,6,A.70,A.92,8.A0,A;201,9.30,A.50,A:SUHPUUGE5THMHFHTFMSTPBFE5XVESBGAXWEFVUPTVASGXWPGVX", "hasFlower": 0, "hasSeason": 0}, {"id": "22", "q": "002,4,6,8.10,A.22,4,6,8.30,A.42,4,6,8.50,A.63,7.70,A.82,4,6,8.90,A.A2,4,6,8.B0,A.C2,4,6,8;123,7.35.40,A.63,7.84,6.90,A.A5.B3,7:7JQ7DCYG34VZDLGY73YVDDFG4QQL47JCVFJ4FZFCYZ3ZJCLQ3LGV", "hasFlower": 0, "hasSeason": 0}, {"id": "23", "q": "000,3,7,A.15.22,8.30,4,6,A.52,4,6,8.60,A.75.82,8.90,4,6,A.A2,8.B5.C0,2,8,A;125.30,A.44,6.52,8.82,5,8.90,A.A2,8.B5.C0,A;235.85.90,A:PTJ7ODVH62WG72FPC46F4JCN6O7YBN7OBTNGHYO1ZV1ZWP63N3PD", "hasFlower": 0, "hasSeason": 0}, {"id": "24", "q": "001,3,5,7,9.20,2,4,6,8,A.40,2,4,6,8,A.60,3,5,7,A.80,2,5,8,A.A0,3,5,7,A;111,3,7,9.31,9.43,7.50,A.63,7.81,9.A0,3,7,A:LAJRECHDH5LOFE8PZTR2PQ82J01IO58Y1CQEYTI0QFZ8ARDQER", "hasFlower": 0, "hasSeason": 0}, {"id": "25", "q": "000,3,7,A.15.22,8.34,6.40,2,8,A.55.60,2,8,A.74,6.92,4,6,8;100,A.22,8.34,6.40,2,8,A.61,9.74,6.92,8;200,A.33,7.41,9.74,6;333,7:GE7BNGYNZDIVAEAVVOISB7BSIASIY4G7GBDZO4DEOVYY7ODSAE", "hasFlower": 0, "hasSeason": 0}, {"id": "26", "q": "003,7.11,5,9.23,7.30,A.42,4,6,8.50,A.62,4,6,8.70,A.82,4,6,8.90,A.A2,4,6,8.C1,3,5,7,9;112,8.40,A.61,9.81,9.A4,6.B2,8;2B2,8:8UJWJSFX5G5556U4GTS4WK61UUBXX4HKR1HF2H80X1210B4THR", "hasFlower": 0, "hasSeason": 0}, {"id": "27", "q": "000,2,4,6,8,A.20,4,6,A.40,2,4,6,8,A.60,2,4,6,8,A.81,3,5,7,9.A0,2,4,6,8,A.C0,4,6,A<PERSON>E0,3,7,A;101,4,6,9.24,6.55.74,6.94,6.E0,A:H8L49LRX8PL2P1X0RIWD2IX787KZZ23HK391I74TL0237RDT8WIR3X", "hasFlower": 0, "hasSeason": 0}, {"id": "28", "q": "000,3,5,7,A.22,5,8.30,A.42,5,8.50,A.63,7.75.82,8.90,4,6,A.B1,5,9.D2,5,8;100,3,7,A.15.31,9.45.75.82,8.94,6.B1,5,9;215.94,6;395:XO3VZTIZO2H253BKSF91BFV6LKB938P54HLLWLTX6WBISP1834", "hasFlower": 0, "hasSeason": 0}, {"id": "29", "q": "001,3,5,7,9.21,3,5,7,9.40,4,6,A.52,8.60,4,6,A.80,2,4,6,8,A.A0,2,4,6,8,A.C0,2,4,6,8,A;112,8.25.40,A.54,6.81,4,6,9.B5.C0,2,8,A:1DOVBOVD23MVIUOYU7X0I10X7OVXEYD0YYU2XI201DB7EBIBU7123M", "hasFlower": 0, "hasSeason": 0}, {"id": "30", "q": "001,3,5,7.20,2,4,6,8.40,2,6,8.54.60,2,6,8.74.80,2,6,8.A1,3,5,7.C1,4,7;102,6.21,3,5,7.50,2,6,8.70,8.82,6.A3,5.B1,7;250,2,6,8.70,8.82,6;351,7:BM4800V55DATTVCNN0E5ALT7Q5IM84QBQ9QDLNNMCEAGAE47EM9C4T0CIG", "hasFlower": 0, "hasSeason": 0}, {"id": "31", "q": "000,3,5,7,9,C.20,3,5,7,9,C.40,2,4,8,A,C.60,2,4,6,8,A,C.81,3,5,7,9,B.A0,2,4,8,A,C.B6.C0,2,4,8,A,C;100,4,8,C.23,9.30,C.61,B.A0,4,8,C:BII9BRGGCMYZ0JX5F50JESVC5FTSV8NGCLTXGL69W5RR6OYMNWCREOYZY8", "hasFlower": 0, "hasSeason": 0}, {"id": "32", "q": "000,2,4,6,8,A.21,3,7,9.35.40,2,8,A<PERSON>54,6.60,2,8,A.74,6.81,9.93,5,7.A1,9.B3,7.C0,A;102,4,6,8.21,9.35.50,4,6,A.62,8.84,6.C0,A;205.21,9.50,A;321,9:DHY479PNNBPX0N1ZDO74HZN36HSR750K1O67VPVP9Y5YBSS3SRXHDTYKTD", "hasFlower": 0, "hasSeason": 0}, {"id": "33", "q": "000,2,5,8,A.20,3,5,7,A.41,3,5,7,9.61,3,5,7,9.80,2,4,6,8,A.A2,4,6,8.B0,A.C2,4,6,8;100,2,8,A.25.45.51,3,7,9.81,3,7,9.95.B1,5,9:HAZIWVWTFQZP88WGVWOMSIMAITFHAS6OGPFP6SMG6A6MQP8VVFI8GS", "hasFlower": 0, "hasSeason": 0}, {"id": "34", "q": "003,5,7,9.10,C.23,6,9.31,B.43,6,9.50,C.63,9.70,C.82,4,8,A.90,6,C<PERSON>A2,A.B0,4,6,8,C.C2,A;103,5,7,9.32,A.60,3,9,C.84,8.A2,A.B0,4,8,C.C2,A:N4NWSL21JBQ0116P064QP51J5493WWNW2LNQ54S3L0BB92P2J0BLPJQ5", "hasFlower": 0, "hasSeason": 0}, {"id": "35", "q": "000,2,4,6,8,A.20,2,4,6,8,A.40,2,4,6,8,A.60,2,4,6,8,A.80,2,4,6,8,A.A1,3,7,9.B5.C0,2,8,A;102,8.50,A.62,4,6,8.80,A;202,8.62,5,8:VIZI291Y0EYRY09925I191HIRCVCVPZHV27U5ZUZU7YBB1E77HU2HP", "hasFlower": 0, "hasSeason": 0}, {"id": "36", "q": "000,2,4,6,8,A.20,2,4,6,8,A.40,3,7,A.55.61,3,7,9.75.80,A.93,5,7.A0,A<PERSON>B3,7.C0,5,A.D2,8.E0,4,6,A;105.21,4,6,9.43,7.61,9.B3,7.D1,5,9:J4CDNTGQVC2SJC2W2EVQCJDZLBVG0XWSVE4ZRBLERNJO2044XOTYYE", "hasFlower": 0, "hasSeason": 0}, {"id": "37", "q": "000,2,5,7,A,C.20,2,4,6,8,A,C.40,2,4,6,8,A,C.60,3,5,7,9,C.81,3,5,7,9,B.A0,2,4,6,8,A,C.C0,2,5,7,A,C;105,7.23,9.36.53,9.65,7.83,9.95,7;266.96:ALG0UCYY6D8LDGC5AN593DDWRWP6PH5F3FH4YRY8AFG5NR4A4FHCCU4R9G0H", "hasFlower": 0, "hasSeason": 0}, {"id": "38", "q": "001,3,7,9.20,2,4,6,8,A.40,2,4,6,8,A.60,3,5,7,A.80,2,4,6,8,A.A0,2,5,8,A.C0,2,4,6,8,A.E1,4,6,9;102,8.20,2,5,8,A.81,9.A1,9.C2,8.D5.E1,9;202,8.20,A:Q5BF7ZXQI97595LDR519N8XXZIFXGLFB6L1R9LY6GTTKFNR6DRNY6H1Z81NZKH", "hasFlower": 0, "hasSeason": 0}, {"id": "39", "q": "001,3,5,7,9,B.21,5,7,B.33,9.40,5,7,C.61,4,6,8,B.81,4,6,8,B.A0,3,6,9,C.C2,5,7,A;102,6,A.34,8.40,6,C.61,B.81,4,8,B.96.C2,5,7,A;234,8.C5,7;334,8:2H13VN32A67HUKKULYNLAUF62EKAGQQKEAV2Q7T37GGYUV7FNLL11N3V1QGT", "hasFlower": 0, "hasSeason": 0}, {"id": "40", "q": "001,3,7,9.20,2,8,A.41,3,7,9.60,2,4,6,8,A.80,3,5,7,A.A0,2,4,6,8,A.C0,2,8,A.E0,2,4,6,8,A;103,7.11,9.43,7.62,4,6,8.83,5,7.B0,A<PERSON>D2,8.E0,4,6,A:WRRCYB4ER30B777IIBMU34C4W3EWE0ZH3HCIYZ0ZYHWU0CUHI7MB4YMEMURZ", "hasFlower": 0, "hasSeason": 0}, {"id": "41", "q": "002,5,8.20,4,6,A.40,4,6,A.52,8.60,4,6,A.72,8.80,4,6,A.A0,2,4,6,8,A.C0,3,7,A.D5.E1,3,7,9;120,5,A.50,3,7,A.73,7.94,6.A0,A.D5.E1,9;220,A.63,7:UZXV2DU5VQNB2XSBZR2XSQ6DN5CF2UZQD6KNRXSZ9CNS66F9DKQV7UKV7K", "hasFlower": 0, "hasSeason": 0}, {"id": "42", "q": "001,4,6,9.20,3,7,A.35.40,2,8,A.60,3,5,7,A.81,4,6,9.A0,2,8,A.B4,6.C0,2,8,A.D4,6.E0,A;101,4,6,9.23,7.35.41,9.63,7.75.91,9.B1,4,6,9.D4,6.E0,A:9HGB5A50MIX84AT7BECSK09RIETH4IWNYI3YCZDH8HUGD00MSU3XNRW7ZK", "hasFlower": 0, "hasSeason": 0}, {"id": "43", "q": "001,3,5,7,9.20,2,4,6,8,A.40,3,5,7,A.60,2,4,6,8,A.80,3,5,7,A.A1,4,6,9.C0,3,7,A.E0,2,4,6,8,A;111,3,7,9.30,5,A.53,7.74,6.A4,6.C0,A<PERSON>E1,3,7,9:POATKNBK2XJBQ7T20D7B231I3CNQEH161BH923C00DEEIPI39AOI16QQE0XJ", "hasFlower": 0, "hasSeason": 0}, {"id": "44", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,3,5,7,9,C.60,2,4,6,8,A,C.80,2,4,6,8,A,C.A0,2,4,6,8,A,C.C0,2,4,6,8,A,C.E0,3,5,7,9,C;150,C.66.73,9.96.A0,2,A,C:1XD1J904U9E4UJ5E5XI130GZJIDW7FAIF453T91D7JATE3G9ID75070UEU4X3WZX", "hasFlower": 0, "hasSeason": 0}, {"id": "45", "q": "000,2,5,7,A,C.21,4,6,8,B.40,3,5,7,9,C.60,2,6,A,C.81,3,5,7,9,B.A0,3,5,7,9,C.C0,2,4,6,8,A,C;102,5,7,A.24,8.36.50,C.71,B.83,9.96.B4,8.C1,6,B;206.71,B;306:8POHHRXAIYX7IYPZP144MP8A9R9CC1XYII77ZZRC4XRZ8MOY19AHOHAMO1MC9487", "hasFlower": 0, "hasSeason": 0}, {"id": "46", "q": "000,2,4,6,8,A.20,3,5,7,A.40,2,4,6,8,A.60,5,A.80,4,6,A.92,8.A4,6.B0,A.C2,4,6,8;100,2,8,A.14,6.20,A.33,7.40,A.55.60,A.85.92,8.B0,A.C4,6;201,9.20,A.B0,A;320,A.B0,A:74ERE62K647JQC37N32DXOSR3RQRQ7KN2X5SWD3OZS6WNNWJ5DDL2JEXXSJZWLC6QE", "hasFlower": 0, "hasSeason": 0}, {"id": "47", "q": "000,2,4,6,8,A.20,2,4,6,8,A.40,2,4,6,8,A.60,2,4,6,8,A.80,2,4,6,8,A.A0,2,4,6,8,A.C0,2,4,6,8,A.E0,2,4,6,8,A;115.35.54,6.71,4,6,9.94,6.C5.E5:0GOO1YEGK401T1VPK4AYXTWLZRYR88DUIEAPQZUI71QODLAVUPAYXV7UPOWV", "hasFlower": 0, "hasSeason": 0}, {"id": "48", "q": "000,2,4,6,8,A,C.21,4,6,8,B.40,2,4,8,A,C.56.60,2,4,8,A,C.76.80,3,9,C.95,7.A0,2,A,C.B4,8.C0,2,A,C.D4,6,8.E0,2,A,C;106.24,8.31,B.64,8.B0,C.C3,9:OCIGRN9T2NBY5RUEHVJAI0ZYHGXO9B26XE7DTUI0QWCAZW474IDF5QMJM6VF", "hasFlower": 0, "hasSeason": 0}, {"id": "49", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,2,4,8,A,C.60,2,4,6,8,A,C.80,2,4,8,A,C.A0,2,4,8,A,C.B6.C0,2,4,8,A,C;101,5,7,B.22,A.41,B.91,B.B2,A.C4,8:RQBOHVC6QWT11IVEK4S23OSIRJ34H52YGMK5GJDNEWBCC633CN9BDMI9TBIY", "hasFlower": 0, "hasSeason": 0}, {"id": "50", "q": "000,2,4,6,8,A.20,2,4,6,8,A.40,2,4,6,8,A.61,3,7,9.80,3,7,A.A0,2,4,6,8,A.C0,4,6,A.D2,8.E0,4,6,A;100,A.15.20,2,8,A.42,4,6,8.62,8.B4,6.D0,2,8,A.E5:9CENIRGNBSTRSUFGMYTK03B4CKXOQ5T5LU74730VH0IO29XTY1QVMH091E9L2F", "hasFlower": 0, "hasSeason": 0}, {"id": "51", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.80,2,4,6,8,A,C,E.A0,2,4,6,8,A,C,E.C0,2,4,6,8,A,C,E.E0,2,4,6,8,A,C,E.G0,2,4,6,8,A,C,E:5KNMR8882YUJ79WWPV9JMRYYI9RPSISNUKS5IVVNO2N5KRSKM8U5PO2IP2WO7JO7WMV79YJU", "hasFlower": 0, "hasSeason": 0}, {"id": "52", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,2,4,8,A,C.60,3,5,7,9,C.80,2,4,6,8,A,C.A0,3,9,C.B5,7.C0,2,A,C.D4,6,8.E0,2,A,C;112,4,8,A.32,A.53,9.65,7.A3,9.C2,A.E2,A;222,A.D2,A:M4ECQRJHPK5WWFSB0J6PTFASQD0XC1ZKXMFV9Q1VI0473DTER3G1AI5LBUZF16QG7L9HU0", "hasFlower": 0, "hasSeason": 0}, {"id": "53", "q": "001,5,9.13,7.20,A.32,4,6,8.40,A.52,4,6,8.60,A<PERSON>73,5,7.81,9.93,5,7.A0,A<PERSON>B2,4,6,8.C0,A.D2,4,6,8;101,9.13,7.20,A.32,4,6,8.40,A.52,8.60,4,6,A.83,7.B0,4,6,A.C2,8.D4,6;220,A.42,8.B4,6.D5;3B4,6:5A213MHPSOH5GMG94UJGLAPKNIOKI4LNM4A3UOQKAI2UJ1ROJQ5985RUPLK4HRISLH1M2RGP21J8", "hasFlower": 0, "hasSeason": 0}, {"id": "54", "q": "000,2,4,6,8,A,C.20,3,5,7,9,C.40,5,7,C.60,4,8,C.72,6,A.80,C.94,8.A0,<PERSON><PERSON>B5,7.C0,3,9,<PERSON><PERSON>D5,7.E1,3,9,B;101,3,5,7,9,B.20,3,9,C.35,7.40,C.B0,6,C.C3,9.E3,9;201,4,6,8,B.40,C.B0,C;301,B:XYLROBZ69TCVREHZYWPBDAZHO9CA46OV7LZG37DLL343G8EXJK3QCJE8TQ5KPQKWGEGQOYKCY5", "hasFlower": 0, "hasSeason": 0}, {"id": "55", "q": "000,2,5,7,A,C.20,3,5,7,9,C.41,4,6,8,B.60,2,5,7,A,C.80,2,6,A,C.A0,2,4,8,A,C.B6.C1,3,9,B.D5,7.E0,2,A,<PERSON><PERSON>F4,6,8.G2,A;106.20,4,6,8,C.46.51,B.A4,8.B2,A.C6.E2,5,7,A.G2,A:37XK4043MBR9LCHJLPKMC9E3KXACBTJ8LXZXO38ENH1CORGJKZG0N8GT5J7R1RLGPAII58", "hasFlower": 0, "hasSeason": 0}, {"id": "56", "q": "000,2,4,8,A,C.16.20,2,A,C.34,6,8.42,A<PERSON>54,8.60,C.72,4,6,8,A.80,C.94,8.A1,B.B5,7.C0,2,A,C.D5,7.E0,2,A,C;101,3,9,B.21,6,B.33,9.60,4,8,C.94,8.A1,B.C2,5,7,A.E1,B;2C2,5,7,A:JA2PLPYKM8C7P2LML9MF618643HJ8640J2VVIIKCM1IF86I0F2V4CAC7LH7F4P7A3VA9YJ", "hasFlower": 0, "hasSeason": 0}, {"id": "57", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,3,5,7,9,C.60,2,5,7,A,C.81,4,6,8,B.A0,2,4,6,8,A,C.C0,2,4,8,A,C.D6.E1,3,9,B;101,B.15,7.23,9.35,7.60,2,A,C.75,7.A1,B.C1,B.D3,6,9.E1,B:Y5RNJ43NH5U6A1PHYYPAHGGX3PJDRKSV0G0DITUS76EW4GF1UKN8UNXHVF8EYQQT3BI7PW3B", "hasFlower": 0, "hasSeason": 0}, {"id": "58", "q": "002,4,6,8,A.10,C.23,5,7,9.30,C.42,4,8,A.50,6,C.62,4,8,A.70,6,C.82,4,8,A.90,6,C.A2,A.B4,8.C0,2,6,A,C.E0,2,4,6,8,A,C;110,3,5,7,9,C.33,9.50,3,5,7,9,C.71,4,6,8,B.90,C.B2,4,8,A.D1,6,B.E3,9:E0FMFVJ635R219FQRVJG4QCKYC8C8QTDCJPVM60D58NT4EM6P2NKP35VK5J8IKQGYG1DD022PFGMI096", "hasFlower": 0, "hasSeason": 0}, {"id": "59", "q": "001,3,5,7,9,B,D.21,3,5,7,9,B,D.40,3,B,E.56,8.61,4,A,D.76,8.80,2,4,A,C,E.96,8.A0,2,4,A,C,E.C0,3,5,7,9,B,E.E1,3,5,7,9,B,D;103,5,9,B.21,4,6,8,A,D.92,C.C4,A.D6,8:G9E65KMIM9FEEAUXHPHWZCWDYP9O6DILWCD44Q0WFHY089E5V7UT5DT8QAVXLKZXGXH57O", "hasFlower": 0, "hasSeason": 0}, {"id": "60", "q": "000,2,4,8,A,C.20,5,7,C.33,9.41,6,B<PERSON>53,9.61,6,B.74,8.80,6,C.92,4,8,A.A0,C.B2,4,6,8,A.C0,<PERSON><PERSON>D5,7.E0,2,A,C;125,7.43,6,9.51,B.74,8.93,9.A0,C<PERSON>B3,6,9.D5,7;225,7.46.B6.D5,7;346.B6:IONWPQ06MS6A0WVZE2PD03DI3KDM77B9ZEJ1NH38JOHXMAISN4VPXM4DBI1A02P9Q3NK8A", "hasFlower": 0, "hasSeason": 0}, {"id": "61", "q": "001,3,6,9,B.20,3,6,9,C.40,3,5,7,9,C.60,2,4,6,8,A,C.90,3,5,7,9,C.B0,3,6,9,C;101,B.13,6,9.20,C.33,6,9.40,C.56.60,2,A,C.90,3,5,7,9,C.B0,3,6,9,C;213,9.20,C.46.61,B.96.B0,C:V4YXJ40P01OGIGEEMG2T1XJ83WTY7WWBW2X2IB1897XM51CK6DOOECT37E76GUKD9OV2TPU5", "hasFlower": 0, "hasSeason": 0}, {"id": "62", "q": "000,3,5,7,9,C.20,4,6,8,C.41,3,5,7,9,B.60,2,6,A,C.74,8.80,2,A,C.96.A0,3,9,C.B5,7.C1,3,9,B.D5,7.E0,2,A,C;104,6,8.20,4,8,C.42,A.56.60,2,A,C.90,6,C.A3,9.C1,3,9,B.E2,A;204,8.60,C.90,C.C3,9.E2,A:W5NB25CHVE6EDQL7LS8GAB47LRCLAR6NZJ7F5FHD3QYMYO7UPJXA3M6UPDGCZDIIYOVFXAW584SFC6Y2", "hasFlower": 0, "hasSeason": 0}, {"id": "63", "q": "000,2,6,A,C.14,8.20,2,A,C.35,7.40,2,A,<PERSON><PERSON>54,6,8.62,A<PERSON>70,6,C.82,A.94,6,8.A0,C.B2,4,6,8,A.C0,C.D2,4,8,A;102,A.22,A.36.41,B.53,5,7,9.70,C.96.B1,4,8,B.D2,A;212,A.36.53,5,7,9:2T2O44UN3RC73V0TXBB2RK0PPOBCAULP3OXPAC6URLOB1RAC7YANJU31YV81126S8HKHJS", "hasFlower": 0, "hasSeason": 0}, {"id": "64", "q": "000,2,5,8,A.20,2,5,8,A.42,5,8.50,A.62,4,6,8.90,2,4,6,8,A.B2,5,8.D0,2,5,8,A;101,9.15.21,9.35.42,8.50,A.64,6.94,6.A2,8.B5.D1,9;201,9.21,5,9.50,A.64,6.94,6.A2,8.B5.D1,9;301,9.50,A.65.95:MLO18JXPCX1M21G8UBA0SEGP80MUCSBE0S2RX8F0PH1OHJLFMZZRFBEP2OHUAE6OXS6FHU6B62", "hasFlower": 0, "hasSeason": 0}, {"id": "65", "q": "000,2,4,6,8,A,C.22,4,6,8,A.30,C.42,4,6,8,A.50,C<PERSON>62,5,7,A.70,C.82,5,7,A.A0,2,4,6,8,A,C.C0,2,A,C.D4,6,8.E0,C;100,2,4,6,8,A,C.23,5,7,9.43,5,7,9.50,C.65,7.71,B.85,7.A0,C.D5,7:QK0VVD7393H5RG5K5JW85NHYZ8XUO1TB0N0YW9V7J82UTLBXNQVB1NBXYYZGWAAR7O28DW0LX7", "hasFlower": 0, "hasSeason": 0}, {"id": "66", "q": "000,3,5,9,B,E.20,2,5,7,9,C,E.41,3,5,7,9,B,D.61,3,5,9,B,D.77.82,4,A,C.A0,2,4,A,C,E.B6,8.C1,4,A,D.D6,8.E0,3,B,E;105,9.20,6,8,E.32,C.44,A.63,B.77.82,4,A,C.B4,A.C1,6,8,D;244,A.B4,A:BWBWZPTRID43L9SUKLQKJKENA5ECTLSWOKZB8JDM8GR43ICRALW2NMOEMYBY3PY239R8YEUMG5Q8", "hasFlower": 0, "hasSeason": 0}, {"id": "67", "q": "001,4,6,9.22,4,6,8.30,A.42,4,6,8.50,A.64,6.70,A.83,5,7.91,9.A3,5,7.C0,2,4,6,8,A.E1,3,5,7,9;101,4,6,9.30,3,5,7,A.54,6.60,A.83,7.A4,6.C0,3,5,7,A.E1,3,5,7,9;233,7.45.60,A.83,7.B5.C3,7.E3,7;3D3,7:VR3SJQYQ63M9VX736YUV80DKORRJXIIRG8QS7UKE0Q9YIFU59KJIUJ5KEVCYOE5ODG9OF5LELF0FC3M0", "hasFlower": 0, "hasSeason": 0}, {"id": "68", "q": "011,3,5,7,9.30,2,4,6,8,A.50,2,4,6,8,A.70,2,4,6,8,A.90,2,4,6,8,A.B0,2,4,6,8,A.D0,2,4,6,8,A;113,7.21,9.41,4,6,9.64,6.70,A.83,7.A3,7.B1,9.D1,4,6,9;221,9.44,6.83,7.D1,9;321,9.45.D1,9:0NAE7VYI7YG0ZAY9RSVJHR6XDYI37O0O33H9XO8DVJ6ZJ0JI6ZAED6H9Z8AX3VO7DSPHN9FPXIGF", "hasFlower": 0, "hasSeason": 0}, {"id": "69", "q": "000,2,4,6,8,A,C,E.20,2,5,7,9,C,E.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.80,2,4,6,8,A,C,E.A0,2,4,6,8,A,C,E.C0,2,4,6,8,A,C,E.E1,3,5,7,9,B,D;136,8.43,B.71,5,9,D.A3,B.C6,8:966ZU16H1G0KFZEFTS7UTB90GLLZBRHK79PKL7LBEETSGPNGNN0F71SFSKTE1UBNZP0UR96RRP", "hasFlower": 0, "hasSeason": 0}, {"id": "70", "q": "000,3,5,7,9,C.21,4,6,8,B.42,4,6,8,A.50,C.62,4,8,A.76.81,3,9,B.A0,4,6,8,C.B2,<PERSON><PERSON>C5,7.D1,B<PERSON>E3,5,7,9;100,5,7,C.21,4,6,8,B.42,4,6,8,A.50,C.64,8.72,A.A0,5,7,C.B2,A<PERSON>C5,7.D1,B.E3,9:2HES9L2CJLZFG3TTYWZLKLZMK5ZKHR88GDU5RDJMFSO0TW65E21TE1NGVCEY6375KNUOG297V0", "hasFlower": 0, "hasSeason": 0}, {"id": "71", "q": "001,3,5,7,9.20,2,4,6,8,A.40,2,4,6,8,A.61,4,6,9.80,2,4,6,8,A.A0,2,4,6,8,A.C2,4,6,8.D0,A<PERSON>E2,4,6,8;101,5,9.21,9.33,7.64,6.82,8.90,4,6,A.B2,4,6,8.D1,9.E4,6;265.95.B3,7;3B3,7:3B4CVZ4Q7GAG1QCZSV27DD1VD4PPK7V7SHIG1G2IH8ZOAKOA821AQDIZ4HQ82SBOH8JSIJO3", "hasFlower": 0, "hasSeason": 0}, {"id": "72", "q": "000,2,5,9,C,E.17.20,3,5,9,B,E.42,4,6,8,A,C.50,E.62,4,6,8,A,C.70,E.82,4,7,A,C.90,E.A2,4,6,8,A,C.C2,4,6,8,A,C.D0,E.E2,4,6,8,A,C;115,7,9.43,7,B.62,7,C.74,A.82,C.97.B3,7,B.D4,A<PERSON>E7;247.62,C.B7;362,C:OFGPXSU02I0MMI4BCT68H6ILVBHBHCF4X40FFISUUL4622LXOBOSGP80SLP6QM1XUCOTV181VV12MCHQ8P", "hasFlower": 0, "hasSeason": 0}, {"id": "73", "q": "000,4,8,C.16.20,2,A,C.34,8.40,2,6,A,C<PERSON>54,8.60,2,A,C<PERSON>74,8.81,B.93,9.A5,7.B0,2,A,C.C4,8.D0,2,6,A,C.E4,8;100,C.21,B.33,9.40,6,C.64,8.A5,7.B0,2,A,C.D1,3,5,7,9,B;200,C.B2,A:PR8XHUHMM3U4HID87T43D80IPK4WD7NWIUWPZPXN3KTTXWR68HT3NR4DUKIK6Z07M7RNMX", "hasFlower": 0, "hasSeason": 0}, {"id": "74", "q": "000,3,5,7,9,B,E.20,3,7,B,E<PERSON>35,9.41,3,7,B,D.55,9.60,2,7,C,E.74,A.81,6,8,D.93,B.A0,5,9,E.B3,7,B.C1,5,9,D.D3,7,B.E0,5,9,E;100,5,9,E.20,7,E.42,7,C.61,7,D.74,A.86,8.B7.D6,8:OHJLEME2BRVM8S4I1PFMMNXFN5HKKI8VHWTF9HVR6NWJTB4012NP0UU7Z57ZFG6BLOBVGXS9", "hasFlower": 0, "hasSeason": 0}, {"id": "75", "q": "000,3,5,7,9,C.21,3,5,7,9,B.40,3,9,C.55,7.60,2,A,C<PERSON>74,8.80,C.92,4,6,8,A.B0,3,9,C<PERSON>C5,7.D1,3,9,B<PERSON>E5,7;104,8.16.23,9.43,9.94,8.B3,9.C5,7.D3,9.E5,7;223,9.43,9.B3,9.C5,7.D3,9.E5,7;3C4,6,8.E6:25R9SCZ2AV8N8SUSZRJK8JR58RNMJ5U9LNMWUKXAWC5G04LS6ZXNAKX4V2V9266GV0TZKUJAX69T", "hasFlower": 0, "hasSeason": 0}, {"id": "76", "q": "000,4,6,8,C.20,2,4,6,8,A,C.40,2,5,7,A,C.60,2,4,6,8,A,C.81,3,6,9,B.A0,2,4,8,A,C.B6.C0,3,9,C;100,4,8,C.22,5,7,A.30,C.42,6,A.50,C.63,6,9.81,3,6,9,B.A0,2,A,C.C3,9:O0X8MIRDDGO0USZLK98O0BRSUABWALQZAZLARHWBUGO5G5MKLQGXM95BRC5DMKUIZ0CKDH", "hasFlower": 0, "hasSeason": 0}, {"id": "77", "q": "002,4,6,8,A.10,C.22,4,8,A.30,C<PERSON>43,6,9.51,B.64,6,8.71,B.83,5,7,9.90,C.A3,5,7,9.B0,C.C2,4,8,A<PERSON>D0,C<PERSON>E2,4,8,A;105,7.11,3,9,B.33,9.46.51,B.65,7.93,5,7,9.A0,C.B3,9.D2,4,8,A:RE9GKV11OZGKWJSSZ3QMGMRZ8AHK9QAGD3VOCQHP4KJ8O9C9DEDC4SEAW18ZES8OQD1CPA", "hasFlower": 0, "hasSeason": 0}, {"id": "78", "q": "000,2,5,8,A.20,2,5,8,A.40,2,4,6,8,A.60,2,4,6,8,A.80,2,4,6,8,A.A0,2,8,A.B4,6.C0,2,8,A.D5.E0,2,8,A;101,9.22,5,8.30,A.42,4,6,8.61,3,5,7,9.84,6.91,9.B0,2,4,6,8,A.D2,5,8:KWHQQA83G553KC424WT45NA24HY1KTKGAMHCW1Q00T8OC28JNG2JWL5N1G1OMCUH8LTNYQUA", "hasFlower": 0, "hasSeason": 0}, {"id": "79", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,2,4,6,8,A,C.60,2,4,8,A,C.76.81,3,9,B.95,7.A0,2,A,C.B4,8.C1,6,B.D3,9.E1,5,7,B.G0,2,4,6,8,A,C;104,6,8.21,4,8,B.36.44,8.60,C.A0,C.E1,5,7,B:8CQ5VRTTE3XD4RRFM7ZLHRVUQQ66XEHMHVEQE337ULS36FLVUXHY5C54CPYLD0UPZ4X4SC0586", "hasFlower": 0, "hasSeason": 0}, {"id": "80", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,2,5,7,A,C.60,3,5,7,9,C.80,3,5,7,9,C.A1,6,B.B3,9.C0,5,7,C.D2,A.E0,4,6,8,C.F2,A.G0,4,6,8,C;104,6,8.10,2,A,C.26.32,A.40,C.C0,C.E2,4,8,A.F0,6,C;226.E3,9.F6:D8A9SXN5NR75GYJ7WRMXIR6XWHX8MPCRI8AK7SOVZFYBQFB8Q3AKVJ229BJP6O36Z7PGDHANP5BNC56J", "hasFlower": 0, "hasSeason": 0}, {"id": "81", "q": "000,2,4,6,8,A,C,E.20,2,5,7,9,C,E.40,2,4,7,A,C,E.60,2,4,6,8,A,C,E.80,5,7,9,E.92,C.A0,5,9,E.B2,7,C.C0,4,A,E.D2,6,8,C.E4,A;101,5,7,9,D.30,2,C,E.54,7,A.61,D.75,9.91,D.A5,9.B0,E<PERSON>C2,C.D4,A:W1YJH5RJ46L2AQNRWY9TDXTGWXUYJEE2M5ML1J59EWGX15CE7M7MIH6AQ4R6I1GYN7UOCXR06OFDG0F7", "hasFlower": 0, "hasSeason": 0}, {"id": "82", "q": "000,2,4,6,8,A,C.20,2,4,8,A,C.36.40,3,9,C.56.60,2,4,8,A,C.76.81,4,8,B.96.A0,2,4,8,A,C.C1,6,B;101,3,9,B.20,3,9,C.40,3,6,9,C.61,B.81,4,8,B.96.A1,B;201,3,9,B.20,C.46.84,8.91,6,B:E9WPKN0DASWEDK27778IPW55HLHVH1Z7J1KKXR5VQR0P8GAUX95IWUVNQVGZJHJCA2SPEECLJA", "hasFlower": 0, "hasSeason": 0}, {"id": "83", "q": "002,4,6,8,A.10,C.22,4,8,A.30,C.43,5,7,9.50,C.62,4,6,8,A.80,2,4,6,8,A,C.A0,2,5,7,A,C.C0,2,4,6,8,A,C;102,4,6,8,A.20,3,9,C.43,5,7,9.62,4,6,8,A.81,3,5,7,9,B.A0,5,7,C.C2,4,8,A:GBJIL79JXJ1UE7IA78CMZNX40LJ6TTGU3IHTARHMZ6YQ963ISY5S3PTQYCBY0PL3L71E6N5KKR48", "hasFlower": 0, "hasSeason": 0}, {"id": "84", "q": "000,3,9,C.15,7.21,3,9,B.40,2,4,6,8,A,C.60,3,6,9,C.80,3,5,7,9,C.A0,3,6,9,C.C0,2,4,8,A,C.E3,5,7,9;103,9.40,5,7,C.53,9.60,C.76.84,8.90,C.A3,6,9.B0,C;240,6,C.60,C.84,8.90,C.A6.B0,C;340,6,C.B0,C:SO3LDYUUIHDZXEX30NOR32IFMC86VRNKC1W1KMQD31H5T9E4WVJR4QM06S6U62Z59DXLMJXT0F1U0YR8", "hasFlower": 0, "hasSeason": 0}, {"id": "85", "q": "000,2,4,6,8,A,C,E.20,2,4,7,A,C,E.40,2,5,7,9,C,E.60,3,5,9,B,E.77.80,2,4,A,C,E.96,8.A1,3,B,D.B5,9.C0,2,7,C,E.D5,9.E0,2,7,C,E.F4,A.G0,2,6,8,C,E;101,6,8,D.14,A.30,7,E.60,E.87.A2,C.E0,7,E.G0,E:4LADQ9VIZEATZMCQXPWWEVY9CHK24P90S04S4YXSDIMLDZ2YEMDYQIMJCSQ0WXCAWPKAJK0PIJE9ZKTJXH", "hasFlower": 0, "hasSeason": 0}, {"id": "86", "q": "001,3,5,7,9,B.21,3,5,7,9,B.41,3,5,7,9,B.61,3,5,7,9,B.80,3,5,7,9,C.A0,2,4,8,A,C.B6.C0,3,9,C.D5,7.E1,3,9,B;105,7.11,B.23,9.41,6,B.54,8.62,6,A.74,8.A2,4,8,A.B6.C0,C.D3,9.E1,B;223,9.A3,9.D3,9:216PYG4LQPNAFTI94ABSGQ79TUY7U5L64LPHSYCAN5Y2J84MRLQPMVC5DRV0DB1J1G1H5GXF0X8NANIQ", "hasFlower": 0, "hasSeason": 0}, {"id": "87", "q": "000,3,5,7,9,C.20,2,5,7,A,C.40,3,9,C.55,7.60,3,9,C.75,7.82,A.90,4,8,C<PERSON>A2,A.B0,5,7,C.C2,A;104,6,8.10,C.25,7.40,C.55,7.63,9.75,7.82,A.90,<PERSON>.A2,A.B0,5,7,C;206.40,C.64,8.90,C:ROE6P9DJXM7PS3C4MLG4OR867EFMY0X83QC3N2IPS1GOX3DL0I7X1FPLQSEOLYN2E7SMJ9", "hasFlower": 0, "hasSeason": 0}, {"id": "88", "q": "000,4,8,C.16.21,3,9,B.35,7.42,A.55,7.60,2,A,C<PERSON>74,6,8.80,2,A,C.94,6,8.A1,B.B3,5,7,9.C0,C.D3,5,7,9.E1,B;100,C.16.21,B.35,7.52,5,7,A.60,C.72,4,8,A.91,4,6,8,B.B3,9.C5,7.E1,B;256.72,4,8,A:UGFGEINMATMQ6LQYCH6HFWYRR35HRVM20LW9VXT23UQ33JLAYW4JH84QMSXS5RNFW98IF1L1CEY0", "hasFlower": 0, "hasSeason": 0}, {"id": "89", "q": "000,3,5,7,9,B,E.20,2,5,7,9,C,E.40,2,4,7,A,C,E.60,2,5,7,9,C,E.80,2,5,7,9,C,E.A0,3,5,9,B,E.B7.C0,2,4,A,C,E.D7.E3,5,9,B;105,9.10,7,E.22,C.40,2,7,C,E.66,8.A0,E.B3,7,B.E3,5,9,B;222,C.40,2,C,E.E5,9:XFHOUI9IOX7XEE3DBPDQQ2J7B6922MW45T7P54F3TUMUD7WLJDUJPJPMZT24FLET66CEX6H1IZCIM941QFQ9", "hasFlower": 0, "hasSeason": 0}, {"id": "90", "q": "000,2,4,6,8,A.21,4,6,9.40,2,4,6,8,A.63,5,7.70,A.93,5,7.B0,2,4,6,8,A.D1,4,6,9;102,8.21,4,6,9.40,2,5,8,A.63,5,7.70,A.93,5,7.B0,2,5,8,A.D1,4,6,9;202,8.31,**********.B2,5,8;331,9.55.A5:9HYBSQCGU1YT9EN4GVJ6VZQXX2O3P3EXBYJSQ0TDOHMMJ8IRDC33E246EUYZXB7OPQO18JN0I7BR", "hasFlower": 0, "hasSeason": 0}, {"id": "91", "q": "000,2,5,7,A,C.20,2,5,7,A,C.40,2,4,8,A,C.60,2,5,7,A,C.80,2,5,7,A,C.A0,2,A,C.B5,7.C0,2,A,<PERSON>.D4,8.E1,B.F3,5,7,9.G0,C;100,6,C.20,5,7,C.32,A.52,A.60,5,7,C.72,A.80,5,7,C.A1,B.B5,7.C2,A<PERSON>F5,7.G0,C;200,6,C.60,6,C.85,7.B6:JEESA0BEUWYNKLA1JV35V0K1YKB1LW65O4L766WSNN4G7Z7YANZ3U4BJYGSCLA4EWKZC5G0G6V37VCOBCJOOZ1S530", "hasFlower": 0, "hasSeason": 0}, {"id": "92", "q": "001,3,6,8,A,D,F.20,2,4,6,8,A,C,E,G.40,2,4,6,8,A,C,E,G.60,3,5,7,9,B,D,G.80,2,4,6,8,A,C,E,G.A0,3,5,7,9,B,D,G.C1,4,6,8,A,C,F.E0,2,4,7,9,C,E,G.G1,3,5,7,9,B,D,F;111,F.38.57,9.80,8,G.C7,9.E8.G1,F:GK7I01P637VORQ2S12L0FFWRHDTHG2PZQ91814EVWGOZ3BO2MESBK4OT4UA9ESVIFDVE8FM46L33AZSMGZIUIM", "hasFlower": 0, "hasSeason": 0}, {"id": "93", "q": "000,3,5,7,9,B,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.61,3,5,7,9,B,D.80,2,4,6,8,A,C,E.A0,2,4,6,8,A,C,E.C0,2,4,6,8,A,C,E.E0,2,4,7,A,C,E.G0,2,5,9,C,E;105,9.27.56,8.61,3,B,D.80,E.A1,4,A,D.B6,8:FDXB8UA8UHNSIGHEF23A8G5EF30BFN10S35ZZ0USRQZRI2D6X0IBTQBGUNSXTE13Z1WAAGQ767QXWIWWE18N", "hasFlower": 0, "hasSeason": 0}, {"id": "94", "q": "000,3,5,7,9,C.20,2,4,6,8,A,C.40,2,4,6,8,A,C.61,5,7,B.73,9.86.A0,2,4,8,A,C.B6.C0,2,A,C.D4,6,8.F0,2,4,6,8,A,C;103,6,9.10,C.23,9.30,6,C.44,8.65,7.B0,6,C.D4,6,8.F3,9;206.44,8.D4,8;306.44,8.D4,8:K01MDG2A1M8IUWKIVVAYY4KMLST89BKBYW66JGOGDXSYU3F9W2FWH1I3HZ4XNZFXXIJNFMTAU1OLG0UA", "hasFlower": 0, "hasSeason": 0}, {"id": "95", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.45,9.50,7,E.62,4,A,C.70,7,E.82,4,A,C.96,8.A0,4,A,E.C0,2,4,A,C,E.D6,8.E1,3,B,D;104,A.17.20,3,B,E.45,9.57.62,C.70,E.82,C.A4,A.B0,E.D3,7,B;220,3,B,E.57.D3,B:TPGP4PQQ0AYUUJUABZJZYBI9FP6VNRRO4IUQWHYIVA0CG9VEBETYMS9OTCTOBEHIQRV9MS6CNRCAOWEF", "hasFlower": 0, "hasSeason": 0}, {"id": "96", "q": "000,2,4,6,8,A,C,E.20,3,5,7,9,B,E.42,4,6,8,A,C.50,E.63,5,9,B.71,7,D.83,5,9,B.91,D.A3,7,B.B5,9.C2,C.D0,5,7,9,E.E3,B;103,6,8,B.23,6,8,B.42,5,7,9,C.50,E.71,3,6,8,B,D.92,C.A7.B5,9.D6,8.E3,B:L5HK1DH4IE22AH9DWLD2PGWLJ94WGYKJI7PAKFIKCCJZJ7CGAA5DPSE2EFZL171975SEGHYC1W9SSI5P", "hasFlower": 0, "hasSeason": 0}, {"id": "97", "q": "002,6,8,C.14,A.20,2,7,C,E<PERSON>35,9.40,<PERSON><PERSON>52,4,6,8,A,<PERSON><PERSON>60,<PERSON><PERSON>72,4,7,A,<PERSON><PERSON>80,E<PERSON>92,4,6,8,A,<PERSON>.A0,E<PERSON>B6,8.C0,2,C,<PERSON><PERSON>D4,7,A<PERSON>E1,D;114,A.27.52,5,9,C<PERSON>60,E<PERSON>73,B.80,E<PERSON>95,9.B7.C2,C.D4,7,A;273,B.D4,A:PRD6GSPQ7W6CHMUBXP3Z0RZDSM1QNSXRJTNACRHGCCTZ3MZMB7U07Q2ILL0U72US0WOQOJ1PAI", "hasFlower": 0, "hasSeason": 0}, {"id": "98", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.80,2,4,6,8,A,C,E.A0,2,4,6,8,A,C,E.C0,2,4,6,8,A,C,E.E0,2,4,6,8,A,C,E.G0,2,4,6,8,A,C,E;105,9.27.47.61,D.80,E.A2,C.D7.F7:HKHFV1XRQRURC10EHHAQ9RMXPYC21NA4W273M0KD2FC1E7OM7W3Q2YD0UYDCMKUY75IVUO6D4I56KFFN09QP", "hasFlower": 0, "hasSeason": 0}, {"id": "99", "q": "001,5,7,B.20,3,5,7,9,C.40,3,6,9,C.60,2,4,6,8,A,C.80,2,5,7,A,C.A0,3,5,7,9,C.C0,2,4,6,8,A,C.E0,2,4,8,A,C.G0,2,4,8,A,C;115,7.20,C.33,9.61,6,B.80,2,6,A,C.A3,9.B6.C4,8.E3,9.F0,<PERSON>;215,7.33,9.82,A.A3,9.E3,9:PYZZA8V7XQXONEAV4WZMPGRI2SPMINCSYNIZYEWE6ICOH89QQXR6HOW2DS4V9HC4R4HYSOW7MANQDVCMGAPXRE", "hasFlower": 0, "hasSeason": 0}, {"id": "100", "q": "000,3,5,7,9,B,E.20,2,5,9,C,E.41,7,D.53,5,9,B.70,2,4,6,8,A,C,E.94,6,8,A.A0,2,C,E.B7.C0,2,4,A,C,E.D6,8.E0,2,4,A,C,E;100,4,A,E.22,C.47.54,A.70,2,6,8,C,E.B0,2,7,C,E.D2,7,C.E0,E;200,E.47.54,A.B7.E0,E:2O68PKS21YAXGL34NCM833IJZ0I2VLHVZ0IEMYBIHPDOOVWHGEAB9A6XTTSTCK1QX492JCCAOGXV3NQDTHWG", "hasFlower": 0, "hasSeason": 0}, {"id": "101", "q": "000,2,5,7,A,C.20,2,4,6,8,A,C.40,2,4,6,8,A,C.60,3,5,7,9,C.84,8.90,C.A2,4,6,8,A.B0,C.C2,4,6,8,A.D0,C<PERSON>E3,5,7,9;101,6,B.22,4,6,8,A.43,6,9.60,C.90,C.A3,5,7,9.C3,9.D6;223,9.A5,7;323,9:45LO2H34KSUS6PH6I3Z19XVV6SZIOZ81RJ95R2OK28IUL9JR553GPLVGHSW4LTVORH2X9WZ463TI", "hasFlower": 0, "hasSeason": 0}, {"id": "102", "q": "002,5,7,A.20,5,7,C.33,9.40,5,7,C<PERSON>53,9.60,5,7,C<PERSON>73,9.85,7.90,3,9,C.A6.B0,3,9,C.C6.D0,C.E2,4,8,A;102,5,7,A.20,6,C.43,5,7,9.60,4,8,C.83,5,7,9.90,C.B3,9.C0,C.E2,4,8,A;202,A.36.44,8.E2,A;302,A;402,A:SE4K26ZYJGXF528SUML2SXHMYGYBKJTPORNEOT7SVJK6LCQP2F8PA7RPYAZQT4CBDKAZ7TUN5VZDHA7J", "hasFlower": 0, "hasSeason": 0}, {"id": "103", "q": "000,3,5,7,9,B,E.20,3,5,7,9,B,E.40,2,4,A,C,E.60,2,4,6,8,A,C,E.81,3,7,B,D.A1,3,5,9,B,D.C0,2,4,6,8,A,C,E.E0,2,4,6,8,A,C,E;103,5,9,B.42,4,A,C.50,E.66,8.72,C.A1,4,A,D.C5,7,9.D2,<PERSON>;205,9.43,B.66,8.A4,A:O4668SRRSSJC486WJ854T7O2FOW7CD7JGXH23D37OTF4HQ5D2CWTXD2RMR03QFNCWZJ05GZXFQTNS3X5QGMG86", "hasFlower": 0, "hasSeason": 0}, {"id": "104", "q": "000,2,5,7,A,C<PERSON>21,6,B.33,9.40,5,7,C<PERSON>62,6,A.80,2,5,7,A,C.A1,5,7,B.B3,9.C0,C<PERSON>D4,6,8.E1,B.F6.G0,3,9,C;100,2,5,7,A,C.21,B.36.40,C.56.76.81,B.95,7.A1,B.C0,C.D5,7.E1,B.G3,9;201,B.21,B.56.86.D5,7.E1,B;356.D5,7.E1,B:EPYZP8XKAYZQCX9ZOOVUUF38UW1WQKHDUCORVHZQ3KD1Q9VA3X9D8HAF1RP8EOPCEFC3E9VRYWH1YRWDKXAF", "hasFlower": 0, "hasSeason": 0}, {"id": "105", "q": "000,4,7,A,E.12,C<PERSON>24,7,A.30,2,C,E.44,6,8,A.50,2,C,<PERSON><PERSON>65,9.70,2,7,C,E<PERSON>84,A.91,6,8,D.A3,<PERSON><PERSON>B0,5,7,9,E<PERSON>C2,<PERSON><PERSON>D0,5,9,E<PERSON>E2,7,<PERSON>.F0,4,A,<PERSON><PERSON>G2,7,<PERSON><PERSON>H4,A.I1,6,8,D;104,7,A.23,B.30,E.44,6,8,A.61,D.81,7,D.B6,8.C1,D.F0,4,A,E.I6,8:YENP26MZQ67JMXMEE9BTCK296D7OYQBUU17FZOP9N3IVK0TQ1TD2UO3X11ZJNKFV3VPM20EV63L00L9KNP7UTIOCQZ", "hasFlower": 0, "hasSeason": 0}, {"id": "106", "q": "003,9.10,6,C.22,A.30,4,6,8,C.50,3,5,7,9,C.70,2,5,7,A,C.91,3,5,7,9,B.B0,2,4,6,8,A,C.D0,3,5,7,9,C.F2,5,7,A.G0,C;130,4,8,C.50,C.65,7.71,B.85,7.A1,3,9,B.B5,7.C0,C.D3,5,7,9.F5,7;2D5,7.F5,7;3D6:126MF2H6CE33L6IMEBZKA7VYOPHB55T24ROUNZVIR6PJI0YYH2ITH3FB73NKSGBANGLJUN1ODDO04SCY", "hasFlower": 0, "hasSeason": 0}, {"id": "107", "q": "000,3,6,8,B,E.20,2,4,A,C,E.36,8.40,2,4,A,C,E.57.60,2,4,A,C,E.76,8.80,2,4,A,C,E.96,8.A1,3,B,D.B5,7,9.C1,3,B,D.E0,2,6,8,C,E;100,3,6,8,B,E.24,A.36,8.57.A7.E0,2,6,8,C,E;236,8.57.A7.E0,E;336,8.57.A7:2H8SVA8SD79DH97JDE6OLMA264K92KQ46RGVX8E7L2NILQLGTJI5C8NGMQTBDRRGRCOQXB97O4ZO64ZA5A", "hasFlower": 0, "hasSeason": 0}, {"id": "108", "q": "000,2,5,7,A,C.21,3,5,7,9,B.40,2,4,6,8,A,C.60,3,5,7,9,C.82,4,8,A.A1,5,7,B.B3,9.C0,5,7,C<PERSON>D2,A<PERSON>E4,6,8.F1,B.G3,9;100,6,C.12,A.25,7.32,A.53,9.66.74,8.A6.B3,9.E5,7.F1,B;200,C.12,A.26.E6.F1,B;312,A.26:21E487N97D38WF17T7GL9IE90ZI2YD3PBJ08DLE836PSNYSH6UUISS4TWHIN2ETTJDZZ56FN265ZG39B", "hasFlower": 0, "hasSeason": 0}, {"id": "109", "q": "000,2,4,6,8,A,C,E.20,2,4,7,A,C,E.41,3,5,7,9,B,D.60,2,4,6,8,A,C,E.80,2,4,7,A,C,E.A0,2,5,7,9,C,E.C1,3,5,7,9,B,D.E0,2,4,6,8,A,C,E.G0,2,4,7,A,C,E;112,C.46,8.53,B.83,B.C3,B.D6,8.G2,C:4S3UR0SC97FICCM3D0M81F1PSO9SUPAG5GJ84O087ODC5BI0A2HXHRPJUU7JJKIRPMA3K8BXO2M73AIR", "hasFlower": 0, "hasSeason": 0}, {"id": "110", "q": "001,3,5,7,9,B.20,4,6,8,C.32,A<PERSON>44,6,8.50,2,A,C<PERSON>64,8.70,6,C.84,8.90,C.A3,5,7,9.B0,C.C2,4,6,8,A.E0,2,4,6,8,A,C.G0,2,6,A,C;101,B.14,8.26.32,A.50,4,8,C.74,8.A4,8.C4,8.E1,B.F6.G0,C;274,8.A4,8.G0,C:PWE40E47YL1UIPIOCPU467RRXROVUVL0W51Z1Z6X7WZL41XZ5I5TYOMLM0UXIERYOTY0M6TCWP5T7EM6", "hasFlower": 0, "hasSeason": 0}, {"id": "111", "q": "000,2,4,6,8,A.24,6.32,8.40,4,6,A.60,4,6,A.81,3,5,7,9.A1,3,5,7,9.C2,4,6,8.E2,4,6,8;104,6.24,6.40,4,6,A.65.83,5,7.A1,3,5,7,9.D2,4,6,8;204,6.24,6.44,6.A2,4,6,8.D4,6;315.34,6.A3,7;434,6:K61IX4JYJ9AK7TXKPXVOQ54C5ACC6UR16O9LTU1IWW95QZKYVQTLP9IIMVZ7XZQ65MTCZN1RNV77", "hasFlower": 0, "hasSeason": 0}, {"id": "112", "q": "000,2,4,6,8,A,C.23,5,7,9.30,C.45,7.50,3,9,C.65,7.71,3,9,B.85,7.91,3,9,B.A5,7.B0,C.C2,4,8,A.D6.E0,3,9,C;100,3,9,C.15,7.23,9.36.40,C.53,5,7,9.72,5,7,A.92,A.A5,7.B0,C.C2,4,8,A<PERSON>E0,C;213,6,9.54,8.C2,4,8,A:2MX0CZMCDP73B4FVVAPN1FGWU10J5CP2M5FJV3BO401ONRVRXG35GJDP4Z0JORGM5471ACBXX7R7FUUBWU3O", "hasFlower": 0, "hasSeason": 0}, {"id": "113", "q": "000,2,5,9,C,E.17.21,3,5,9,B,D.37.40,3,5,9,B,E.57.63,B.70,5,7,9,E.82,C.90,4,7,A,E.B3,6,8,B.C0,E<PERSON>D3,5,9,B.E0,7,E<PERSON>F2,4,A,C.G0,7,E;115,7,9.33,6,8,B.57.75,9.81,D.B6,8.D3,B.E0,E.F7.G0,E;2D3,B.F0,E:6QYCBHAFF9IC95SYQCLHM836HA737XECZ7ME6KIZN6YJM88TMXPNOL9XNKTP9ADYIOTDBSHTI7X5JNA8", "hasFlower": 0, "hasSeason": 0}, {"id": "114", "q": "000,2,4,6,8,A,C.20,3,5,7,9,C.40,2,4,6,8,A,C.61,3,5,7,9,B.80,2,4,6,8,A,C.A1,3,5,7,9,B.C0,2,4,6,8,A,C.E1,3,5,7,9,B;103,9.15,7.30,C.46.52,A.71,3,5,7,9,B.A2,A.B6.C0,C.D2,A<PERSON>E5,7;273,9.D2,A:LD0N3HNW7R8KIIPMNVKQ7NGWMP7BDILIL3G7BZEQWHFP4FGZYWFRHZYD0K868H36B3K8EGMLMDVZFBP4", "hasFlower": 0, "hasSeason": 0}, {"id": "115", "q": "000,2,4,6,8,A,C,E.21,3,5,7,9,B,D.40,2,4,7,A,C,E.61,3,5,7,9,B,D.80,2,5,7,9,C,E.A0,2,4,7,A,C,E.C0,2,6,8,C,E.D4,A.E0,6,8,E.F2,4,A,C.G6,8.H1,3,B,D.I5,7,9;112,C.25,9.31,3,7,B,D.65,9.80,E.92,C.B0,E.D5,9.E0,E.G2,7,C:H1USMQC38PDKW2PIZNQXKI5S8ZBDDWCMXIJ4PIU3KV1VKAJNTXCB11QN5PB5X53M2TDAHNUVA3H8JAUZ28V2MQBZ4HJC", "hasFlower": 0, "hasSeason": 0}, {"id": "116", "q": "000,2,4,A,C,E.16,8.21,3,B,D.36,8.40,2,4,A,C,E.57.61,3,B,D.75,9.81,D.93,6,8,B.A0,E.B2,4,6,8,A,C.D1,3,5,7,9,B,D;101,3,B,D.17.21,D.33,7,B.41,D.53,7,B.61,D.81,D.96,8.A3,B.B5,9.C3,7,B.D1,5,9,D:BM4AP9VJ5PCQJYTGTCPFM7NT74BQ8QPDN4NF6H8VTE08J7B6E8FEYG46OGH5NQFEBGOD0AJ950570CC6", "hasFlower": 0, "hasSeason": 0}, {"id": "117", "q": "000,4,6,8,C.12,A.24,6,8.30,2,A,C.44,8.50,2,A,C.65,7.70,2,A,C.84,8.90,2,A,C.A4,8.B0,2,A,C.C4,6,8.D2,A.E0,4,6,8,C;104,6,8.12,A.24,8.40,2,4,8,A,C.72,A.84,8.A1,4,8,B.C3,9.E5,7;214,8.42,4,8,A.84,8.A1,4,8,B.E6;343,9.94,8;443,9:Z4JVUZ2YYDEFHXOHW04FSQ9RU4AYG07UMT6GF11RHB2IUBJQ7HJVOP056552VMB5V9PXAYL2DQMLWJAQTSWB0EFIMWA4", "hasFlower": 0, "hasSeason": 0}, {"id": "118", "q": "001,3,5,7,9,B.20,2,4,8,A,C.40,2,A,C.55,7.60,2,A,C.75,7.80,2,A,C.94,6,8.A1,B.C2,5,7,A.D0,C.E2,5,7,A;102,4,6,8,A.21,4,8,B.40,C.52,5,7,A.60,C.76.80,C.94,8.A1,B.C2,5,7,A.E5,7;204,8.40,C.55,7.94,8.C6;340,C;440,C;540,C:A5RJR8SWAPKPHSXEJSLT9HKXIBBL7P9XEW25DT9KLST7KB8GHALJD27D2RGIW8HWEDPX520BI97T5E80GRJG33IA", "hasFlower": 0, "hasSeason": 0}, {"id": "119", "q": "003,5,7,9.10,C.23,6,9.30,C.43,5,7,9.51,B.63,5,7,9.70,C<PERSON>82,A.90,<PERSON><PERSON>A2,4,8,A.C1,3,9,B.D5,7.E0,C<PERSON>F3,6,9.G0,C;104,8.20,C.33,9.51,3,9,B.65,7.70,C.90,C.B3,9.C1,B.F0,C;220,C.43,9.51,B.70,C.B3,9.C1,B.F0,C;343,9.51,B.B3,9.C1,B:WOOJGWEYW33MX167KJ9IOG1Z2RM2TBAEKV62R06XAH10GVH0A5XI5ZGWBVTO09A7TUXVHD69MHT2YD3R3M9RUB1B", "hasFlower": 0, "hasSeason": 0}, {"id": "120", "q": "000,3,5,7,9,C.20,4,6,8,C.32,A.40,4,6,8,C.52,A.64,8.76.80,2,A,C.95,7.B2,4,8,A.C6.D0,2,4,8,A,C.E6.F0,4,8,C.G6;100,4,6,8,C.25,7.30,3,9,C.53,9.76.81,B.95,7.B2,A<PERSON>C4,6,8.E0,4,6,8,C.G6;215,7.43,9.86.D5,7;343,9:R7YSBHZG97R42O2C5P3DZ4LVSVYI33V5A7RNBIL88I9K6GM7JOHMLTQKZNCL5M3QQCMJ5HPHTJDIARQJ6CZV", "hasFlower": 0, "hasSeason": 0}, {"id": "121", "q": "000,2,4,6,8,A,C,E,G.20,2,4,6,8,A,C,E,G.40,2,4,6,8,A,C,E,G.60,2,4,6,8,A,C,E,G.80,2,4,6,8,A,C,E,G.A0,2,4,6,8,A,C,E,G.C1,3,5,7,9,B,D,F.E0,2,5,7,9,B,E,G.G0,2,4,6,8,A,C,E,G.I0,2,4,6,8,A,C,E,G:H970RNC6BCHR3ISXAPG7XB7LHFE341OLPBZ3CZEROA1974ZFNXLWC06EZEIIONWDW1D4QBRGIOA43HS1WSSXLQNA", "hasFlower": 0, "hasSeason": 0}, {"id": "122", "q": "001,3,5,7,9,B.21,4,8,B.36.40,2,A,C.54,6,8.60,2,A,C.75,7.81,3,9,B.95,7.A0,C.B4,8.C0,2,A,C;102,4,8,A.31,6,B.50,2,5,7,A,C.71,B.83,5,7,9.C2,A;202,A.31,6,B.55,7.85,7.C2,A;336.55,7.85,7;436:R9LNPX9YXM1MBK25ALEITCVJ6C1SPJJFMNBF01WNDG2EHYRAXI6M10S6KSTDCC5XGBHHNWVJ6BSH", "hasFlower": 0, "hasSeason": 0}, {"id": "123", "q": "001,3,5,7,9,B.21,3,6,9,B.43,5,7,9.51,B.63,9.75,7.80,C.94,6,8.A2,A.C1,5,7,B.D3,9.E1,B<PERSON>F3,6,9.G0,C;104,6,8.11,B.23,6,9.44,6,8.52,A.76.80,C.96.C6.D1,B.E3,9.F6.G0,C;205,7.11,B.36.80,6,C.E3,9;336.E3,9;436:AXJS1DAV38XNCEWH4KZLWZ2DML08IO0MYV9N0OIS38XLJ1SGFJIJI6OAKOYL2946A9CCH0GKSX8K9CEF", "hasFlower": 0, "hasSeason": 0}, {"id": "124", "q": "000,2,5,7,A,C.20,3,5,7,9,C<PERSON>41,3,9,B.55,7.60,3,9,C.75,7.80,2,A,C.95,7.A1,3,9,B.B5,7.C0,C<PERSON>D3,9.E1,5,7,B.F3,9.G0,6,C;101,5,7,B.23,6,9.42,A.56.64,8.76.80,C.A2,6,A.C0,C.E1,4,8,B.F6;205,7.23,9.65,7.C0,C.E1,B:WS56R2VXR1U02TXF6Z12USRD0261R8Z88MFULY0YWEZX5SWFIVVMXEM0SI6YI5EVUMLTLWF1DIETTZDD85YL", "hasFlower": 0, "hasSeason": 0}, {"id": "125", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.80,2,4,6,8,A,C,E.A0,2,4,6,8,A,C,E.C0,2,4,6,8,A,C,E.E0,2,4,6,8,A,C,E.G0,2,4,6,8,A,C,E.I0,2,4,6,8,A,C,E.K0,2,4,6,8,A,C,E:P473AR4GAJHMXV69PBDDGW0KZIZ087JQJC1EBGO5O0LOVUIC4D9G908X65QJLYWDEEY4KHAX1MUCRO8HEC9A8HX3", "hasFlower": 0, "hasSeason": 0}, {"id": "126", "q": "002,4,8,A.10,6,C<PERSON>22,A.30,4,8,C<PERSON>42,6,A<PERSON>50,C<PERSON>65,7.71,3,9,B.85,7.90,<PERSON><PERSON>A2,4,6,8,A<PERSON>B0,C<PERSON>C3,5,7,9.D0,C<PERSON>E3,5,7,9.F0,C<PERSON>G3,6,9;104,8.20,C.32,A.50,6,C.72,A.85,7.A0,2,A,C.B5,7.C0,3,9,C.E3,6,9.F0,C;232,A.85,7.C3,9:R0HOWJFRSAFHUHPHXJK62MSJALM0EUETA67XP05WK5FKELROUM115D5AFTKPXBDSX6Y2YEODMBP7RJUOD6S0", "hasFlower": 0, "hasSeason": 0}, {"id": "127", "q": "001,3,6,9,B.20,2,4,8,A,C.36.42,4,8,A.50,6,C.62,A<PERSON>70,5,7,C.82,A.90,4,6,8,C<PERSON>A2,A.B0,5,7,C<PERSON>C2,A<PERSON>D4,6,8.E0,2,A,C.F4,8;101,6,B.13,9.32,4,6,8,A.71,B.92,4,8,A.B5,7.D2,4,6,8,A;206.34,6,8.94,8.C6;336.94,8:DAZXPUZLA9DAY2UR312N0ZCFCRF1DR0WN9XA4D4210N3LPPYY31V30Q4FPXL9UMCUVFX2QRCZVNYLV49WM", "hasFlower": 0, "hasSeason": 0}, {"id": "128", "q": "000,2,4,6,8,A,C.20,3,5,7,9,C.41,3,5,7,9,B.60,3,9,C.75,7.80,2,A,C.94,8.B1,3,5,7,9,B.D0,3,5,7,9,C;100,2,4,8,A,C.24,8.42,4,8,A.63,9.70,5,7,C.94,8.B2,4,8,A.D4,8;200,3,9,C.44,8.76.94,8.B4,8;300,C.44,8.A4,8:H6E2XDWGI1Z61PUVLV0D8D4UFCZP1I4W0D66I55HAF81842E5PUEC0GHH5ICCZFAXE84ALYZ2LUPFVLYXVX02A", "hasFlower": 0, "hasSeason": 0}, {"id": "129", "q": "002,4,6,8,A,C.10,E.22,4,7,A,C.30,E.42,4,7,A,C<PERSON>50,<PERSON><PERSON>66,8.72,4,A,C.90,6,8,E.A2,4,A,C.B7.C0,2,5,9,C,E.D7.E0,2,4,A,C,E;104,A.10,2,C,E.31,7,D.50,E.66,8.72,4,A,C.90,6,8,E.A3,B.C1,7,D.E0,2,C,<PERSON>;231,D.67.72,C.90,7,E.C1,D:HR8BSNRT1NVQY7TSBE3YTXKQ5SL43KTIEPZUQ3ZUAELNJHLIOOCPI1KL87ORCAKVX5C1HPN4148PJ38YYHR4QIOECS", "hasFlower": 0, "hasSeason": 0}, {"id": "130", "q": "000,2,4,8,A,C.20,2,5,7,A,C.41,3,5,7,9,B.60,2,5,7,A,C.81,4,6,8,B.A0,2,5,7,A,C.C0,2,4,6,8,A,C.E1,3,6,9,B.G0,3,9,C;100,C.21,5,7,B.71,5,7,B.95,7.A1,B.C4,8.E2,A.G0,C;221,B.71,6,B.95,7.A1,B.E2,A.G0,C:93WS95SRAI8M69L28A8SAWJNLU5RL5ML1RQUN6CUQIVWMIVDCQRQ6E1SJMNU2N36DAJ2J23I1WEV3C9V518C", "hasFlower": 0, "hasSeason": 0}, {"id": "131", "q": "000,2,4,6,8,A.20,2,4,6,8,A.40,5,A<PERSON>53,7.61,9.73,7.81,9.93,7.A0,5,A<PERSON>C2,8.D0,4,6,A;101,4,6,9.20,3,5,7,A.40,A.61,3,7,9.81,9.93,7.A0,5,A<PERSON>C2,8.D0,4,6,A;204,6.20,5,A.40,A.61,9.81,9.C2,8.D0,5,A;304,6.30,A.61,9:D7MK76ZF4E6UHGHER5XDSAMJTPBEURLG4K8PSZABVJXX5KVZJDSIPJKOAX5ALSL1H5TFWW6E6FZ1ODFL8IPH", "hasFlower": 0, "hasSeason": 0}, {"id": "132", "q": "001,3,5,7,9,B.20,3,5,7,9,C.40,3,6,9,C.60,3,6,9,C.80,2,5,7,A,C.A1,3,5,7,9,B.C1,3,5,7,9,B.E2,4,8,A;105,7.13,9.20,5,7,C.33,9.53,6,9.82,5,7,A.A3,6,9.C1,3,6,9,B.E2,4,8,A;205,7.24,6,8.43,9.85,7.B3,6,9.C1,B.E2,A;316.B6.C1,B;416.C1,B:LRNMPYKFRMDI9L4W5UUPYBDJJ2H4AAIYM2W8JCU8OQECPDBQWOBRBHXFJYIOL5N56R5VKALU4WF9M6IQVDC2FAXE2VO4QCPV", "hasFlower": 0, "hasSeason": 0}, {"id": "133", "q": "000,2,4,6,8,A,C,E,G.20,3,5,7,9,B,D,G.40,2,4,6,8,A,C,E,G.60,2,4,6,8,A,C,E,G.80,2,4,6,8,A,C,E,G.A0,2,4,6,8,A,C,E,G.C0,2,4,6,8,A,C,E,G.E0,2,4,6,8,A,C,E,G.G0,2,4,6,8,A,C,E,G:4VN6DOR8DBXAU0ZV155AB8F0MXZIBR3V3E50I6VAFMM3FMFD3OJEA0Y8UUX8DJUH4N1RYKR6K5BHX6PP", "hasFlower": 0, "hasSeason": 0}, {"id": "134", "q": "002,4,6,8,A,C.23,5,7,9,B.42,4,6,8,A,C<PERSON>50,E.63,6,8,B.70,E<PERSON>82,5,9,C.90,7,E<PERSON>A4,A<PERSON>B1,6,8,<PERSON><PERSON>C3,B<PERSON>D0,6,8,<PERSON><PERSON>E4,<PERSON><PERSON>F2,6,8,C.H3,5,7,9,B.I1,D;103,5,9,B.33,6,8,B.53,7,B.70,E.82,C.97.B7.D6,8.G6,8.I1,D;203,B.47.70,E.82,C.C7;347.70,E;447:05V8WFJTZ14LW08Y9B5M9VP0YJJM47LW17II9H3BDIDLL4KKM581ZKB301YH357PYTMB4T8FPHDZFIJ3DKHF7WVTZP9V", "hasFlower": 0, "hasSeason": 0}, {"id": "135", "q": "000,2,8,A.14,6.20,2,8,A.34,6.50,2,4,6,8,A.70,2,4,6,8,A.90,3,5,7,A.B4,6.C0,A.D2,5,8.E0,A;100,2,8,A.14,6.34,6.52,5,8.60,A.75.90,3,5,7,A.C0,5,A.E0,A;201,9.24,6.55.94,6.C0,A;324,6.55;425.55:EX4LW86CK67EB8NW92ET4RPVC38KLFR0U0P72R7N19X29CR34WWXB6L0S9218L7X4PUCVF6S0TEP", "hasFlower": 0, "hasSeason": 0}, {"id": "136", "q": "002,4,6,8,A.21,3,6,9,B.41,4,8,B.56.60,2,4,8,A,C.76.80,2,4,8,A,C.A1,4,6,8,B.C0,3,6,9,C.E0,2,4,8,A,C.F6.G1,3,9,B;102,4,6,8,A.22,6,A.55,7.62,A.74,8.81,B.A4,8.C0,C.D3,9.F2,6,A;222,6,A.F2,6,A;322,A.F2,A;422,A.F2,A:39623S8760AOY1GYVVFSAGGGTPVVZZ6SH3I119CZ4ZTYA88CO9OCIPHT3481XXA0CYIX4T04F7FOHS902FX6IPHP", "hasFlower": 0, "hasSeason": 0}, {"id": "137", "q": "000,2,4,6,8,A,C.20,3,9,C.36.41,<PERSON><PERSON>53,9.60,5,7,C.80,3,5,7,9,C.A1,4,8,B.B6.C0,2,A,C.E0,3,6,9,C.G1,3,5,7,9,B;103,5,7,9.20,3,9,C.41,B.53,9.75,7.80,C.A1,4,8,B.C0,2,A,C.E3,9.F6.G3,9;204,8.80,C.C1,B.F6;304,8;404,8:M478PQZ8W69Y958DS3E7Y85P91MQGKM15M6X6W41DEOAAKDQ3GQZ4S7W3DOZ4YVC9OPVXAV7ZO6GP531VWCYAG", "hasFlower": 0, "hasSeason": 0}, {"id": "138", "q": "003,5,9,B.10,7,E<PERSON>23,5,9,B.31,7,D<PERSON>44,A.50,2,C,E<PERSON>65,7,9.70,2,C,E.84,6,8,A.90,2,C,<PERSON><PERSON>A4,<PERSON><PERSON>B0,7,E<PERSON>C2,5,9,C.D7.E0,4,A,E;104,A.10,6,8,E.31,4,7,A,D.50,2,C,E.67.70,2,5,9,C,E.87.90,4,A,<PERSON><PERSON>B0,<PERSON><PERSON>C6,8.E0,4,A,E;231,D.50,E.72,C.A0,E:0L2J2DDYMXIQ3XG6O8K5D0Q7G7ZHHF34YCN58Z5PYBURJYLU5Q4X89V32PQBZ2GUNFOB8XPI9RBC3KHZ0WVPHDG0U6WM", "hasFlower": 0, "hasSeason": 0}, {"id": "139", "q": "000,2,4,A,C,E.16,8.20,E.32,4,6,8,A,C.50,2,4,6,8,A,C,E.70,3,5,7,9,B,E.90,2,4,7,A,C,E.B1,3,5,9,B,D.D0,5,9,E.E2,7,C;100,4,A,E.17.20,E.34,7,A.51,5,7,9,D.70,3,B,E.92,7,C.B4,A.D0,5,9,E.E7:BXJSJDSTNBOARD63VD0CNQV60TA3XL0NGQCMPRPXGRPQGSGTCPVSM6O3RATMQDLJOJBXALBM630NVCLO", "hasFlower": 0, "hasSeason": 0}, {"id": "140", "q": "001,3,9,B.16.20,2,A,C<PERSON>34,6,8.40,2,A,C<PERSON>54,8.60,2,6,A,C.82,5,7,A.90,C.A4,8.B0,2,6,A,C.C4,8.D0,2,A,C.E4,6,8;101,3,9,B.22,A.34,6,8.40,C.53,9.60,C.72,A.85,7.90,C.B0,5,7,C.C3,9.E4,8;250,3,9,C.85,7.90,C.C3,9;390,C:AVZTTAHWX6MASUEB5EUWLVFSW5EBC1MV0FCI51SMI06FX61Z0C5F6U1O0BHNXCTZAXNIS4VOTZOIBWHU4MLOEH", "hasFlower": 0, "hasSeason": 0}, {"id": "141", "q": "000,2,6,A,C.20,2,4,8,A,C.36.41,3,9,B.62,4,6,8,A.70,C<PERSON>82,4,8,A.90,C<PERSON>A2,4,6,8,A.C0,3,9,C.D5,7.E0,2,A,C.F4,8.G0,2,6,A,C;100,2,6,A,C.20,3,9,C.43,9.62,4,8,A.81,3,9,B.A4,6,8.C3,9.D6.E2,A.F0,C:1XU1RPNPN2DONRE5QG29XW81AOYEDIXU3FRF522A1H9PODVE8DR7P9KUHGQXO7I3QY5EIV5QNUKIWKK9", "hasFlower": 0, "hasSeason": 0}, {"id": "142", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.80,2,4,6,8,A,C,E.A0,2,4,6,8,A,C,E.C0,2,4,6,8,A,C,E.E0,2,4,6,8,A,C,E.G0,2,4,6,8,A,C,E.I0,2,4,6,8,A,C,E.K0,2,4,6,8,A,C,E:RRJX3RY9GFNC4ZFGJL2JOR4G7I23INFB37P4N0Y9XLGFCOY07L94ZYMBVC2V0OCIVL3ZMNV2PXD7IMDDMXZOJ90D", "hasFlower": 0, "hasSeason": 0}, {"id": "143", "q": "000,2,4,6,8,A,C,E.20,2,5,7,9,C,E.40,3,6,8,B,E.60,2,5,7,9,C,E.80,3,5,7,9,B,E.A0,2,4,6,8,A,C,E.C0,3,5,7,9,B,E.E1,3,5,7,9,B,D.G0,3,5,7,9,B,E;107.12,C.66,8.B3,6,8,B.D4,<PERSON><PERSON>E1,D.F4,A.G7:OK47F6X6Q9L5GXEWVFJKEJVXB4G6AG9SJQO4QLSA5AJW0P5VFO97KBQ7BEW0LFVOWE70XG49PABL506K", "hasFlower": 0, "hasSeason": 0}, {"id": "144", "q": "002,6,A.14,8.20,2,6,A,C.34,8.40,C<PERSON>53,5,7,9.60,C<PERSON>73,9.80,6,C.92,A.A0,4,8,C<PERSON>B2,<PERSON><PERSON>C4,8.D0,<PERSON><PERSON>E3,6,9.F0,C<PERSON>G2,4,8,A;106.13,9.26.30,4,8,C.53,5,7,9.60,C.80,6,C.92,A<PERSON>B2,4,8,A.D0,<PERSON><PERSON>E3,6,9.G3,9;226.56.70,C.92,A.D0,C:LH2NN3GU09F3H4PDH5437QGUDI7FPLKJ1QDLP4B9I1GXB1Q97B35EF7N40G52EP12U9Q05F2JUX0BHILKDIN", "hasFlower": 0, "hasSeason": 0}, {"id": "145", "q": "000,2,6,8,C,E.14,A.21,D.33,5,9,B.40,7,E<PERSON>55,9.62,7,C<PERSON>70,4,A,E<PERSON>82,7,C.94,A.A6,8.B0,2,4,A,C,E.D1,4,A,D.E7;100,2,7,C,E.14,A.33,5,9,B.47.62,7,C.74,A.82,7,C.94,A.A7.B3,B.D4,A.E7;201,D.57.94,A.A7;357.A7:A8EWL4GSCDO7RRTI4QQCB0CYKRPJ1SBJFSGGCKI4VOAA4LAR2WWYSF1VPEVTE6V0700GI8WYE7Y276DI", "hasFlower": 0, "hasSeason": 0}, {"id": "146", "q": "000,2,5,7,A,C.20,2,6,A,C.40,2,5,7,A,C.60,4,6,8,C.81,3,6,9,B.A0,3,5,7,9,C.C0,3,9,C.D5,7.E0,C.F2,6,A;100,2,5,7,A,C.20,2,A,C.40,5,7,C.66.82,A.A0,C.C0,C.D5,7.E0,C.F2,A;201,B.20,2,A,C.40,C.56.A0,C.C0,C.F2,A;320,C.A0,C;4A0,C:40G31EUG36RY1NT8Q4AYZ65EE8LSQN7VUXAJPG0NREXV1T5MYA0ZGU17M6MUX6L0VZ7VRX8L5TMJF8LFAPQ7QTZRYN5S", "hasFlower": 0, "hasSeason": 0}, {"id": "147", "q": "000,2,4,8,A,C.20,2,4,8,A,C.41,3,5,7,9,B.60,2,4,8,A,C.80,2,5,7,A,C.A1,3,5,7,9,B.C0,3,5,7,9,C.E1,3,5,7,9,B;100,2,A,C.14,8.44,8.60,3,9,C.95,7.B4,8.C0,C.D3,9.E1,5,7,B;201,B.14,8.60,3,9,C.95,7.C0,C.E1,5,7,B:WEDG2T3Q06C7KGX7EKLP8H2728GBX7AJ6DVUDUDUARVYX0BFRWG90NKBJ0UTXR3C2PFJW9J8WKS8R43B4SL3QNYH", "hasFlower": 0, "hasSeason": 0}, {"id": "148", "q": "000,3,5,7,9,C.21,3,5,7,9,B.42,4,6,8,A.60,2,4,8,A,C.76.81,3,9,B.A1,3,5,7,9,B.C1,3,5,7,9,B.E1,3,5,7,9,B;113,9.21,5,7,B.45,7.53,9.61,B.91,B.A3,9.B5,7.C1,B.D5,7.E3,9;221,B.45,7.61,B.91,B.B5,7;321,B.61,B.91,B:C5CZHWCS6U84Y33YJSNVJZWKM58EBKMTM5HO7NBOPJ74J8XWKTWYZZC3TY46XX4PU22TE58OBHBIIM3HVVVXOK", "hasFlower": 0, "hasSeason": 0}, {"id": "149", "q": "000,3,7,B,E.15,9.20,2,C,E<PERSON>34,6,8,A<PERSON>41,D<PERSON>53,6,8,B.70,2,4,6,8,A,C,E.92,5,9,C.A7.B1,3,B,D.C5,7,9.D0,2,C,E.E4,6,8,A;100,7,E.15,9.20,E.34,6,8,A.56,8.63,B.71,5,9,D.92,5,9,C.A7.B3,B.C1,6,8,D.E5,7,9:B7KY7NAZ1565CB3G1JJCFO5GB5FJXEJ8R8C98MBM27NPE7ZQZ3W63HTXZU4AQCUR84A1A93Y2GHWKTG1OP", "hasFlower": 0, "hasSeason": 0}, {"id": "150", "q": "000,4,6,8,A,E.20,3,B,E.42,4,7,A,C.60,2,6,8,C,E.74,<PERSON><PERSON>80,6,8,E.92,C.A4,7,A.B2,C.C0,4,A,E.E0,3,5,9,B,E;100,4,6,8,A,E.20,E<PERSON>43,B.66,8.70,E.86,8.A3,B.C0,4,A,E.E5,9;204,A.10,E.66,8.C0,E;367.C0,E;467:9FIW7JDMOOUASL4S4W7EA2W4NL20IL5FEVIR5WHBNLVGMV1XHRK8X0G1PFK8I24T3FA2ETBEJ39PVADU", "hasFlower": 0, "hasSeason": 0}, {"id": "151", "q": "000,2,4,6,8,A,C,E.20,2,5,7,9,C,E.40,2,4,7,A,C,E.61,4,A,D.77.80,2,4,A,C,E.A1,3,5,7,9,B,D.C0,3,6,8,B,E.E1,3,5,7,9,B,D;105,9.32,C.54,A.61,D.74,A.81,D.A4,7,A.C3,B.E5,9;232,C.54,A.A4,A.C3,B:LQ1U5K7LRYB5FQVZXCJRJBP09F3PNMMEJ0A7U38ZBA2JK9EDQVO8UTYO0PKCMM2XIPHKNIDFB1UQFH0T", "hasFlower": 0, "hasSeason": 0}, {"id": "152", "q": "000,4,8,C.12,6,A.20,C.35,7.40,2,A,C<PERSON>54,8.61,6,B.80,2,5,7,A,C.A1,5,7,B.B3,9.C0,C.D2,5,7,<PERSON><PERSON>E0,<PERSON><PERSON>F2,6,A;111,6,B.35,7.41,B.61,6,B.80,2,5,7,A,C.A1,5,7,B.D1,6,B.F2,A;216.35,7.66.71,B.85,7.91,B.A6.D6.F2,A;336.66.81,6,B.A6.D6;466.D6;566:HBJOZ0SMSQ3OFA6RAOJECSAF010QL7KSINGWBY76RU9KNPENP3PHJG33FHEZWQOJP9CFK2MAL22LURNH2KRQME0L1YIM", "hasFlower": 0, "hasSeason": 0}, {"id": "153", "q": "001,4,6,8,A,D.20,4,6,8,A,E.32,C<PERSON>45,9.51,3,B,D.67.70,2,5,9,C,E.87.90,2,5,9,C,E.B0,2,4,7,A,C,E.D1,3,5,9,B,D.E7.F0,4,A,E.G2,6,8,C;104,7,A.20,5,9,E.53,B.61,7,D.75,9.82,C.A0,2,C,E.B7.C3,B.D1,D.E7.F0,E:WH19XLKXZNM98DQHO61KZYJKCDRL4XI90AEMEJG4ORYM4ALJJCOR8A4ZSXZRNO1WNSMEKHIQ10LH98EA86NG", "hasFlower": 0, "hasSeason": 0}, {"id": "154", "q": "000,2,5,7,A,C.20,2,6,A,C.34,8.40,2,6,A,C.62,6,A.70,C<PERSON>83,5,7,9.90,C.A3,5,7,9.C0,3,5,7,9,C.E1,3,9,B.F6.G0,2,A,C;100,C.22,A.30,4,6,8,C.52,6,A.70,C.84,8.90,C.A3,9.B5,7.E1,B;230,C.A3,9.E1,B;330,C:3CVZFEDH5QOHU5GGIC4FBMG03FCAWNVN9Z6WVNB09WVAQJE832YJM68X6OWDC2G4ZI3YN5X46Z5PFU4P", "hasFlower": 0, "hasSeason": 0}, {"id": "155", "q": "001,3,5,7,9,B.20,2,6,A,C.41,3,9,B.60,3,9,C.76.80,2,A,C.96.A1,3,9,B.C1,3,9,B.E1,5,7,B.F3,9.G5,7;101,3,5,7,9,B.20,6,C.32,A.60,3,9,C.76.82,A.A3,9.C1,B.E1,B.F4,6,8;202,4,6,8,A.82,A.D1,B.F4,8;302,6,A.82,A:PZ1XZEEPVF4AKBGTOMA9L37EWUSNASO9YD3TG3AD5BU23M9I5NS1J4IF9L7JCVHHOVDWY1DS1OVCLEKNNXL2", "hasFlower": 0, "hasSeason": 0}, {"id": "156", "q": "001,5,7,B.13,9.20,6,C.32,A.40,<PERSON><PERSON>52,5,7,A.74,8.93,9.A0,5,7,C<PERSON>B2,<PERSON><PERSON>C0,5,7,C<PERSON>D3,9.E0,5,7,C;101,5,7,B.13,9.20,6,C.32,A.40,C.52,5,7,A.93,9.A5,7.B0,2,A,C.C6.D0,3,9,C;230,C.52,5,7,A.A5,7.C0,6,C;352,A;452,A;552,A:EBY7Y7L8ZMRUZSNCUCMQSOMXMR9HH8QR6NB92ED68EU2ULO6LQZSQDXCHL9R2NX6OXNYBOSZYDCHB7D9E872", "hasFlower": 0, "hasSeason": 0}, {"id": "157", "q": "000,4,6,8,A,E.12,C.20,4,6,8,A,E.41,3,B,D.55,7,9.60,2,C,E.75,9.80,2,7,C,E.94,A.A0,2,6,8,C,<PERSON><PERSON>B4,<PERSON><PERSON>C0,6,8,E<PERSON>D2,4,A,<PERSON><PERSON>E0,7,<PERSON><PERSON>F3,<PERSON><PERSON>G0,7,E.H2,4,A,C.I0,6,8,E;105,9.12,C.20,5,9,E.60,E<PERSON>A5,9.C4,6,8,A.D0,<PERSON>.H0,E:015OWOAHJH9T3EYH4SEWFPY53S9CGAWMT4QPZQ6C4M0TOTBNQFYCGCG8HRPBM6P7UR45Q83W71RM3YUOZGJNR5", "hasFlower": 0, "hasSeason": 0}, {"id": "158", "q": "002,4,6,8,A.10,C.24,6,8.31,B.43,5,7,9.50,C.62,4,6,8,A.70,C.82,4,6,8,A.90,C.A3,5,7,9.B0,C.C2,6,A.D4,8.E0,2,6,A,C;103,5,7,9.10,C.26.53,9.61,5,7,B.84,8.A3,9.D6.E0,C;203,5,7,9.26.53,9.A3,9.D6;316.53,9.A3,9;416:V28QLCN1KFZ38CSS8B73AVHSCYZHNFF1AMZCMRU04DWL447DUTW3ZJ8LKBG2QYLBFNBQM0TNGQJS3HWHW4MR", "hasFlower": 0, "hasSeason": 0}, {"id": "159", "q": "000,2,5,7,A,C.20,3,5,7,9,C.41,3,5,7,9,B.60,3,5,7,9,C.80,2,6,A,C.A1,3,9,B.B5,7.C0,2,A,C.D4,8.E0,2,6,A,C.F4,8.G1,6,B;100,5,7,C.20,6,C.43,9.55,7.63,9.80,C.A2,A.B5,7.C0,2,A,C.E0,6,C.G6;200,C.16.55,7.A2,A.C1,B.E0,C.F6;300,C:U696SS91Y3748DU147WXFYOGWXO3R3QHFW7GCUSD1XCQ4ZFGRMRQ6QHZ8ZHUH4JSKWF6KDY8MM9CKX1ZMKY3R9CDJG87", "hasFlower": 0, "hasSeason": 0}, {"id": "160", "q": "000,2,7,C,E.14,A.20,2,6,8,C,E.34,A<PERSON>40,6,8,E<PERSON>52,4,A,C<PERSON>60,6,8,E.80,3,5,7,9,B,E.A1,3,7,B,D.B5,9.C0,7,E.D2,4,A,<PERSON><PERSON>E0,7,E.F3,5,9,B.G0,7,E.H2,4,A,C.I0,6,8,E;113,B.20,E.46,8.86,8.B6,8.D2,4,A,C.F6,8.H0,E;220,E.46,8.F6,8.H0,E:XF3OZEG8O6EDUJOPMDGG3WB5FUER6U9HDWREP2G389MJCJ8MZW2ML6HFL2X8IFJIZ3CIHXIXB5DR655ZCPUOW2BHBRPC", "hasFlower": 0, "hasSeason": 0}, {"id": "161", "q": "001,3,5,8,B,D,F.20,2,5,8,B,E,G.43,8,D.50,5,B,G.62,7,9,E.74,C.81,6,8,A,F.93,D.A6,A.B0,2,8,E,<PERSON>.C4,C.D8.E0,3,6,A,D,G.F8.G0,3,5,B,D,G;101,4,C,F.18.20,2,E,G.68.74,C.86,A.93,D.A6,A.B1,8,F.E7,9.F0,3,D,G;218.20,2,E,G.93,D.E8.F0,3,D,G:9DIT0XB5UMTHQAX3UQ3CDLJF0OC0IYS8M9F5GJJSHAT9VYWLPV3LDFPGSP71GEWOBD7VGLJ3HMP8MU8V0UEHEE1B28FS29TB", "hasFlower": 0, "hasSeason": 0}, {"id": "162", "q": "000,3,6,8,B,E.20,2,4,6,8,A,C,E.42,4,6,8,A,C.50,E.62,4,6,8,A,C.81,D.93,5,7,9,B.A0,E.B2,4,6,8,A,C.C0,E.D2,4,6,8,A,C.E0,E;103,7,B.10,E.23,5,9,B.37.45,9.50,E.63,B.93,B.A0,5,9,E<PERSON>C0,5,7,9,E.D3,B.E0,E;210,E.24,A.36,8.C6,8.E0,E:3VGTKAZWUEQX9TQTXIJLGDK3WIY4GUZY5XO3RZSUQVER4RD7SLVWLJQS9RD5XL37ZTOYUKEAO7S9YDAEIGAO744KJV9JIW", "hasFlower": 0, "hasSeason": 0}, {"id": "163", "q": "001,3,6,9,B.20,2,5,7,A,C.40,3,5,7,9,C.60,2,4,8,A,C.80,2,A,C.94,8.A1,6,B.B3,9.C0,5,7,C<PERSON>D2,<PERSON><PERSON>E4,6,8;101,3,9,B.25,7.30,C.50,4,8,C.70,2,A,C.91,B.C0,5,7,C.E4,8;201,B.25,7.30,C.50,C.70,2,A,C.91,B.C0,6,C.E4,8:NRQ43B06H1HCQR0FTAAAB3809LAQULGU96HBXDP1GB4RJXHQ4Z0T88N1YUC5XFPDZ58RYJ1XP5YYZ36463Z5PU", "hasFlower": 0, "hasSeason": 0}, {"id": "164", "q": "000,4,6,8,C.12,A.20,4,6,8,C.40,2,4,6,8,A,C.61,5,7,B.73,9.80,5,7,C.92,A.A0,5,7,C.C0,2,5,7,A,C.E1,3,9,B.F5,7.G2,A;111,5,7,B.34,6,8.40,2,A,C.55,7.61,B.75,7.A5,7.B0,C.C2,5,7,A.E2,<PERSON><PERSON>G2,A;225,7.40,2,A,C.61,5,7,B.B5,7.C2,A.E2,A:BCW8HHE988IZLRKCF56XR49LF4CPP5FWY5L52E0JJVZ3L3Y6HIGPRI9JCHB03WEZ6YFTET23KWIYQQXV206R89J0XGX2PZ", "hasFlower": 0, "hasSeason": 0}, {"id": "165", "q": "000,3,5,7,9,B,E.20,2,4,A,C,E.36,8.41,3,B,D.56,8.61,3,B,D.75,7,9.80,2,C,E.94,6,8,A.A0,2,C,E.B4,6,8,<PERSON>.C0,E<PERSON>D2,6,8,<PERSON>.E0,E<PERSON>F2,4,A,C.G0,6,8,E;104,6,8,A.33,B.46,8.67.75,9.81,D.A4,A.B7.D2,6,8,C.G6,8;233,B.57.81,D.C7.D2,C;3D2,C:O62M8DK3C3F2QX63JXV3M86Y9VKKBK9BX78C9EOETO5FYT4ECC5BYM64EXMQG4BOTFGDA822Q4TA7YJQV59G7A7V5FAG", "hasFlower": 0, "hasSeason": 0}, {"id": "166", "q": "000,2,4,6,8,A,C,E,G,I.20,2,4,6,8,A,C,E,G,I.40,2,4,6,8,A,C,E,G,I.60,2,4,6,8,A,C,E,G,I.80,2,4,6,8,A,C,E,G,I.A0,2,4,6,8,A,C,E,G,I.C0,2,4,6,8,A,C,E,G,I.E0,2,4,6,8,A,C,E,G,I.G0,2,4,6,8,A,C,E,G,I.I0,2,4,6,8,A,C,E,G,I:TVUN0R3WYO79MYAXFCT59Q6FDODLDSS3HIXNU6CFTN0ZIC7SY3OVIMUIQMVA07F95ZRW6Q56QWHVOM7LSRHCWA30A5NTH9XYDRUX", "hasFlower": 0, "hasSeason": 0}, {"id": "167", "q": "000,2,4,6,8,A,C,E.21,4,7,A,D.40,3,5,7,9,B,E.60,2,4,7,A,C,E.82,4,6,8,A,C.A0,2,4,A,C,E.B7.C0,4,A,E.D6,8.E1,3,B,D.F5,7,9.G0,2,C,E;100,4,6,8,A,E.21,7,D.34,A.47.53,B.67.74,A.86,8.93,B.A0,E<PERSON>B4,7,A.D7.E2,C.F7.G0,E;205,9.17.47.D7;305,9:AJHF4WXKUHTOKF4RJFTZQSO2NCJ75LU3I7GCNRPBIIXQ9WRJ3L5S2AXTG03V3L5EPCIAAR5TYKLSB0Q4YGSQVK94GCFBBEZX", "hasFlower": 0, "hasSeason": 0}, {"id": "168", "q": "000,2,4,6,8,A,C.20,4,6,8,C.32,A.40,4,8,C<PERSON>52,A<PERSON>65,7.70,2,A,C.85,7.90,2,A,C.A4,8.B1,6,B.C3,9.D0,5,7,C<PERSON>E2,<PERSON><PERSON>F0,4,6,8,C.G2,A;100,2,4,8,A,C.16.31,3,9,B.62,6,A.70,C.82,6,A.90,C.B6.D5,7.E1,B.G2,A;262,A.76.82,A.C6;372,A:ZVVZEM53UMWT7B5BFR1ONTQJWWCDVQ305OCR1ENNYM5TILZCZ7L0FBUM8SJDCJ443TY3LU868WFNBISQF6UQ8VLJ", "hasFlower": 0, "hasSeason": 0}, {"id": "169", "q": "000,2,4,6,8,A,C.21,4,6,8,B.40,2,4,8,A,C.62,5,7,A.70,C.83,5,7,9.90,C.A2,6,A.C0,2,4,8,A,C.E1,4,6,8,B.G0,3,5,7,9,C;102,5,7,A.24,8.40,C.65,7.70,C.83,9.A2,A.C0,4,8,C.E4,8.G0,6,C;206.40,C.65,7.83,9;340,C:ATI5J4XPX5GFDIFUTF6QSDOB68KGPAPK82FO4APV5U94U92QOYG4YSOB65THJT3BDHZ9H3GH388UBZV39D6A", "hasFlower": 0, "hasSeason": 0}, {"id": "170", "q": "001,4,6,8,A,C,F.20,4,6,8,A,C,G.40,3,5,7,9,B,D,G.60,2,5,8,B,E,G.80,2,5,7,9,B,E,G.A0,3,5,7,9,B,D,G.C0,7,9,G.D3,5,B,D.E0,7,9,G.F4,C.G7,9;106,A.24,7,9,C.30,G.48.71,F.85,B.A3,<PERSON><PERSON>C7,9.E0,7,9,G.F4,C.G7,9:C1V9M4JYVTI43H51WVHFLFXITU5E494LAGVRHEL1GD0TXECFW0REIN3FN3YXJ3JRMTXAMZRHUJA00BBLZAID1M", "hasFlower": 0, "hasSeason": 0}, {"id": "171", "q": "000,2,4,6,8,A,C,E,G,I.20,2,4,6,8,A,C,E,G,I.40,2,4,6,8,A,C,E,G,I.60,2,4,6,8,A,C,E,G,I.80,2,4,6,8,A,C,E,G,I.A0,2,4,6,8,A,C,E,G,I.C0,2,4,6,8,A,C,E,G,I.E0,2,4,6,8,A,C,E,G,I.G0,2,4,6,8,A,C,E,G,I.I0,2,4,6,8,A,C,E,G,I:JQIZBIXLMQS1CG1ID8QU2J6FG6OMFUAZMHCWX2W0ZWHHO82O3LAHQ2FPGDSG8D89903BVDB3PVPFUCBCA1VPU6WI19AO0V9M30Z6", "hasFlower": 0, "hasSeason": 0}, {"id": "172", "q": "002,4,6,8,A,C.10,E.22,4,6,8,A,C.41,3,5,7,9,B,D.60,2,4,6,8,A,C,E.80,2,4,6,8,A,C,E.A0,3,5,7,9,B,E.C7.D1,3,5,9,B,D.F1,3,6,8,B,D;102,5,9,C.25,9.41,3,7,B,D.72,5,9,C.94,A.D1,3,B,D;215,9.41,D.D1,D:3X9ZNCKCK7K2A55T6YON03MAUNFSLMLU2QNW9M0WJKQT76CGEYY2JFMLFEVY33WAOXZZX0XSAF0CZU2LVUWG", "hasFlower": 0, "hasSeason": 0}, {"id": "173", "q": "000,3,5,9,B,E.17.20,2,4,A,C,E.36,8.40,3,B,E.55,7,9.60,2,C,E.75,9.80,3,B,E.95,7,9.A0,2,C,E.B4,6,8,A.C0,E.D2,5,7,9,C.F0,3,5,7,9,B,E.H0,2,4,6,8,A,C,E;105,9.17.24,A.37.60,E.75,9.B4,6,8,A.C0,E.G7.H4,A:HKHQ2QP0C0W6WV38WCS16NVGO3NJTFCLGIUSM3S2CS9Y0UP6TGD350DKDPH19ZJG1PY5JZH1DML86JIFOQWQ", "hasFlower": 0, "hasSeason": 0}, {"id": "174", "q": "000,2,6,A,C.14,8.20,C.32,4,8,A.40,6,C<PERSON>52,A.64,6,8.71,B.83,5,7,9.90,C.A4,6,8.C1,5,7,B.D3,9.E1,<PERSON><PERSON>F4,8.G1,B;102,6,A.20,C.32,A.46.65,7.71,B.83,6,9.90,C.A5,7.C5,7.D2,A.F1,B;232,A.46.76.83,9.96.C5,7;332,A:UTQBGEGBR3H27VCB3YACMCJJTUDYHPMRAZKDQ68GVAZ9BOG3SZ0K8326Y8VSAOH6E6C0H9YV070PJZJ8", "hasFlower": 0, "hasSeason": 0}, {"id": "175", "q": "000,2,4,6,8,A,C,E,G.20,2,4,7,9,C,E,G.40,2,4,6,8,A,C,E,G.60,2,4,6,8,A,C,E,G.80,2,4,6,8,A,C,E,G.A1,3,5,7,9,B,D,F.C0,2,4,6,A,C,E,G.D8.E0,2,4,6,A,C,E,G.F8.G0,2,4,6,A,C,E,G:SB8YWO1D8ZB0ZKSKLO8KW3YVIKI8WW053H3IVLGG1HAU10QYH3QAUSQA5Z0LD5ZGGIOD1YQHD5OLAS", "hasFlower": 0, "hasSeason": 0}, {"id": "176", "q": "000,2,6,A,C.21,3,6,9,B.42,4,8,A.50,C.62,4,8,A.70,C.82,4,6,8,A.A0,2,4,8,A,C.C0,2,4,8,A,C.E3,6,9.F1,B<PERSON>G5,7;111,B.42,4,8,A.50,C<PERSON>62,A.70,C.83,5,7,9.A0,2,A,C.C0,C.D3,9;211,B.50,C.62,A.70,C.A0,2,A,C.C0,C;350,C.70,C.A0,C.C0,C:A6EG13YMWB2CSROJXTOHHW3S6ZEQ51SVPJXEIN1V6QQZL7B52G2PI6GEIYIA3PG5AACPMKNZC30MCRUQTLU7501Z2MKS", "hasFlower": 0, "hasSeason": 0}, {"id": "177", "q": "000,2,5,7,9,C,E.21,3,5,7,9,B,D.41,4,6,8,A,D.60,3,5,7,9,B,E.80,2,6,8,C,E.A0,2,4,6,8,A,C,E.C0,3,5,7,9,B,E.E0,2,4,6,8,A,C,E.G2,4,6,8,A,C;105,9.11,D.27.41,D.57.70,E.82,6,8,C.A0,3,B,E.C3,7,B.E3,B.F7.G2,C:RGYG2Q4W6SS6T6ZBW4TADYHFFMAD240Y8ROOWGQ4IM8RSHZ0FIZFIOAEEWJQIBPMRZ7JP7DQ7SN0575AN0OG6YMD", "hasFlower": 0, "hasSeason": 0}, {"id": "178", "q": "001,3,5,9,B,D.20,3,5,7,9,B,E.42,4,6,8,A,C.50,E.63,5,7,9,B.70,E.82,4,6,8,A,C.A0,3,6,8,B,E.C0,2,4,A,C,E.D6,8.E0,3,B,E;102,5,9,C.20,6,8,E.33,B.47.54,A.73,5,9,B.93,B.C0,3,B,E.D6,8.E0,3,B,E;226,8.47.93,B.D6,8;326,8.93,B.D6,8:SHAXJT04JOHK6EZDK8816SO5S3KQHKNWANQF6YI0ZC5YR8Z4H5ZRCEY6FXO1A51CDYR3A8CFRDODT4NMEE1WSTFM4ITWNW", "hasFlower": 0, "hasSeason": 0}, {"id": "179", "q": "002,4,8,A.16.23,9.30,C.42,4,6,8,A.60,3,5,7,9,C.80,3,5,7,9,C.A0,2,6,A,<PERSON><PERSON>B4,8.C1,B<PERSON>D3,6,9.E0,<PERSON><PERSON>F3,9.G5,7;103,9.16.42,4,6,8,A.60,3,6,9,C.80,5,7,C.B4,8.C1,B.D3,6,9.G6;203,9.43,6,9.63,6,9.80,5,7,C.B4,8.D3,6,9;303,9.53,6,9.76:0UR8PF5P3N9DU41DG37Y46V7C5D3Q9UYBTUZRJ5RN65Z0LV9RGN6GGTB7INJILJ0DT19132IJ8P2Q6TBVFV0ZC7P1BIZ", "hasFlower": 0, "hasSeason": 0}, {"id": "180", "q": "000,2,4,7,A,C,E.21,3,6,8,B,D.40,3,5,7,9,B,E.61,3,5,7,9,B,D.80,2,6,8,C,E.94,A.A0,2,6,8,C,E.B4,A.C0,2,6,8,C,E.E1,4,6,8,A,D;101,3,B,D.17.23,B.46,8.62,5,9,C.81,D.95,9.A0,E.B6,8.C0,2,C,E.D7.E4,A;202,C.17.46,8.A0,E.B6,8;317.47.B7;417:6J7S7KYWKCDIOIHKA87371AHCWH090D8WL3DPH9U0Y1S2OIOFJJL9PP90YU8P6K1UFC6SUMO2S2MLCDMAFY36L1M8WAIJF32", "hasFlower": 0, "hasSeason": 0}, {"id": "181", "q": "001,3,6,8,B,D.20,2,6,8,C,E.40,2,4,A,C,E.56,8.60,2,C,E.74,6,8,A.90,2,5,9,C,E.A7.B0,2,4,A,C,E.C6,8.D0,2,C,E.E4,6,8,A;103,6,8,B.20,6,8,E.42,C.50,E.74,6,8,A.90,5,9,E.B2,C.D0,6,8,E;206,8.26,8.90,E.D6,8;307.26,8.90,E.D6,8;426,8.D6,8:YWS0DX9GYS1BMDV0YTIETQ41EQIPPMBESX29PXPR027OM2DCBW9ZLLYBL3STRCE74O1GJLGIVZM1UDJJX2I30RVJ97RUG7TV", "hasFlower": 0, "hasSeason": 0}, {"id": "182", "q": "000,2,4,6,8,A,C,E,G.20,2,4,6,8,A,C,E,G.40,2,4,6,8,A,C,E,G.60,2,4,6,8,A,C,E,G.80,2,4,6,8,A,C,E,G.A0,2,4,6,8,A,C,E,G.C1,3,5,7,9,B,D,F.E0,2,4,6,8,A,C,E,G.G0,2,4,6,8,A,C,E,G.I0,2,4,6,8,A,C,E,G.K0,2,4,6,8,A,C,E,G:A4TQQKYI6K109JODHJO4K6LIC3B0DXFYWWWEO6GTHB6LDCGTWZ3ZA1FX0Y1QHXKE93BO04ICLCL3HY4GBFDEIZJAQGTXJEFA1Z", "hasFlower": 0, "hasSeason": 0}, {"id": "183", "q": "001,3,6,8,B,D.20,2,4,6,8,A,C,E.41,3,5,7,9,B,D.63,5,7,9,B.83,5,7,9,B.A4,6,8,A.C0,2,5,7,9,C,E.E2,4,6,8,A,C.F0,E.G4,6,8,A;101,7,D.13,B.26,8.31,4,A,D.46,8.64,6,8,A.83,7,B.A4,6,8,A.C0,E.D5,9.E2,7,C.F4,A.G7;213,B.27.35,9.47.66,8.C0,E.E2,7,C;336,8.67:9N6CZ8QFYBSY5TMGIRE5BH0Y3JNK7RFJXM9TP3PEILCJ3G1G5NT02LG2ARR2I40KAZH1HA260N8XFQBC3TYI6A6SJ714KCF49951H4KB", "hasFlower": 0, "hasSeason": 0}, {"id": "184", "q": "003,8,D.15,B.21,7,9,F.34,C.40,8,G.53,6,A,D.61,8,F.73,5,B,D.80,7,9,G.92,E.A0,4,6,8,A,C,G.B2,E.C0,4,6,8,A,C,G.E0,2,4,6,A,C,E,G.G2,4,6,A,C,E;103,D.15,8,B.40,8,G.61,F.73,8,D.92,7,9,E.B0,7,9,G.C5,B.D0,G.E2,5,B,E.G2,5,B,E;240,G.B7,9.D0,G:FC0PP84I2E4M3WBMLW2HMZ27VV8ALE1H9YK8XC23CPQ5FS167FSQWXY7YCWOBK5K70X3ZPE3OE1XASSBBFQ109KJ6MJ0LHYI8QLH", "hasFlower": 0, "hasSeason": 0}, {"id": "185", "q": "000,4,6,8,A,E.12,C.25,9.30,2,7,C,E.44,A.50,2,6,8,C,<PERSON>.64,A<PERSON>71,6,8,D<PERSON>83,B.90,5,7,9,<PERSON><PERSON>A2,C.B0,4,6,8,A,<PERSON><PERSON>C2,C<PERSON>D4,6,8,A.E0,2,C,E.F5,9.G0,2,C,E;104,6,8,A.22,C.40,2,4,A,C,E.56,8.86,8.90,<PERSON>.A2,C.B6,8.C3,B.D6,8.E2,C.F0,E:M5RW57LZIILUQJT9VTPNLMMNZJPJ0OTUS9D9PVR8ENIE115OWWDIDP0VQUFFTF1AZQQOVNAOZ5MLDRUE7F1ESRW8J9", "hasFlower": 0, "hasSeason": 0}, {"id": "186", "q": "000,3,5,7,9,B,D,G.21,3,5,B,D,F.38.40,2,4,C,<PERSON>,G.56,8,A.60,2,4,C,E,G.76,8,A.80,2,4,C,E,G.97,9.A0,2,4,C,E,G.B7,9.C0,2,4,C,E,G.D7,9.E0,2,4,C,E,G.F6,A.G0,2,4,C,E,G;103,D.22,E.38.42,E.72,5,8,B,E.91,3,D,F.A8.C1,F.D7,9.E1,F.G2,E:WV4FY13BH10C4UJG2RPS93YO76VCPFBMM6EYWOF7YEOJME62VB59PHSWGC31UEGV74GRRFBR0W395476SH1MJ5C2SOJHP592", "hasFlower": 0, "hasSeason": 0}, {"id": "187", "q": "000,3,6,8,B,E.21,3,B,D.37.40,2,4,A,C,E.57.61,4,A,<PERSON><PERSON>76,8.83,B.A0,2,5,7,9,C,E.C4,7,A.D0,2,C,E.E4,7,<PERSON><PERSON>F1,D.G5,7,9;100,3,6,8,B,E.21,3,B,D.40,2,C,E.54,7,A.83,B.A0,2,C,E.C4,7,A.D0,2,C,E.F1,7,D;203,6,8,B.23,B.41,D.54,A.83,B.A0,E<PERSON>C4,A.D1,D.F7:K6EGAQUYQX5UILLNCDS0Y27IBQARHYH25ILKK89VBXO9O5ELRXMMNS7FMTN2GINBUEWEY8SV527MWACFW67RDW8KC0BSUCXRQAT8", "hasFlower": 0, "hasSeason": 0}, {"id": "188", "q": "000,2,4,6,8,A,C.20,3,5,7,9,C.41,3,5,7,9,B.60,2,6,A,C.74,8.80,2,6,A,C.A0,2,4,6,8,A,C.C3,5,7,9.D1,B<PERSON>E4,6,8.F0,C.G4,6,8;100,2,4,6,8,A,C.23,9.36.41,4,8,B.56.61,B.73,6,9.81,B.A0,3,6,9,C.C3,6,9.D1,B.E4,6,8.G4,6,8:CEVCI4H8GI6OTHPGRXHI3WU1JJMZZ9E2GS9PEOB2C1R9JB8VO2ITJ2WXHWGX3TOP48MWV3VC9S3UZE164UZ1UP4X8T", "hasFlower": 0, "hasSeason": 0}, {"id": "189", "q": "000,4,8,C.12,6,A.36.40,2,A,C<PERSON>55,7.61,3,9,B.75,7.81,B.94,6,8.B1,3,5,7,9,B.D0,2,6,A,C.F1,3,5,7,9,B;140,2,A,C.55,7.62,A.76.81,B.94,8.A6.B2,4,8,A.D0,2,A,C.F1,3,5,7,9,B;240,2,A,C.56.76.A4,6,8.D0,2,A,C.F2,5,7,A;341,B.A5,7.D1,B.F5,7;4A5,7.F6:6XH78M21NIC5C0SIU7SQ63RQLMF14RE9EPPPFVPOKFLOKVBIC4XQMSQV35GGDRB0J62DC85BJK4HUE5IS01HB91HVKMFN04ER6", "hasFlower": 0, "hasSeason": 0}, {"id": "190", "q": "000,2,4,7,A,C,E.22,4,6,8,A,C.42,4,A,C<PERSON>56,8.62,C.70,5,7,9,E<PERSON>82,C.94,6,8,A.A1,D.B3,6,8,B.C0,E.D2,6,8,C.F2,4,A,C.G6,8.H2,4,A,C.I0,6,8,E;101,7,D.13,B.26,8.43,B.57.71,6,8,D.94,A.B3,6,8,B.F3,B.G5,9.H7;201,D.17.94,A.B7;317.94,A:UYGAYS55AZZW726RHTI0R5RFG2HJDYPDF2AU53BH7TUTGPBND06HNK3444PF0IYG0KDKNT2JWJZPN4WKAFRSBW3JU3BZ", "hasFlower": 0, "hasSeason": 0}, {"id": "191", "q": "000,2,5,7,9,C,E.21,5,7,9,D.41,3,6,8,B,D.60,4,6,8,A,<PERSON><PERSON>72,C.84,7,A.90,E.A3,5,9,B.B1,D<PERSON>C6,8.D1,D<PERSON>E3,5,7,9,B;115,7,9.41,3,6,8,B,D.60,4,A,E.77.90,4,A,E.B1,D.D7.E4,A;215,7,9.43,6,8,B.64,A.77.94,A;315,7,9.47.77;416,8:GAX582HZAJX7PRKYIDE05OIAAYYS6ROPMIKP8TTUNJKWJJ83U3USENGT2GVW5D75YNZP83DKG4ZM6UD0NHVZI43T", "hasFlower": 0, "hasSeason": 0}, {"id": "192", "q": "000,2,6,8,C,E.14,A.20,2,6,8,C,E.34,A<PERSON>46,8.50,2,4,A,<PERSON>,E.66,8.70,2,C,E.84,6,8,A.90,E<PERSON>A2,5,9,C<PERSON>B0,7,E.C2,4,A,<PERSON><PERSON>D6,8.E1,4,A,<PERSON>.F6,8.G3,B;107.10,3,B,E.34,6,8,A.51,6,8,D.70,7,E.90,E<PERSON>A5,9.B0,E<PERSON>C2,7,C.E4,6,8,A.G3,B;234,6,8,A.77.80,E.C2,C.D7.E4,A:GS9PBA2QIWKDL1R6B3B26K55WFFQHGLL75GN9RDM389B18KG1ON4K4DIWXP7R9H7ITP4X7LSN1FXFTMPWDHT6XATNRI65SM4MHSO", "hasFlower": 0, "hasSeason": 0}, {"id": "193", "q": "000,2,4,6,8,A,C,E.20,2,5,7,9,C,E.41,4,6,8,A,D.60,3,6,8,B,E.80,2,5,7,9,C,E.A0,3,5,9,B,E.B7.C1,5,9,D.D3,<PERSON><PERSON>E0,5,9,E.G2,4,7,A,<PERSON><PERSON>H0,E.I5,7,9;101,3,7,B,D.20,E.44,A.67.80,2,7,C,E.A3,B.B7.C5,9.E0,E<PERSON>H0,E<PERSON>I6,8;202,C.44,A.80,2,C,E.E0,E.I7:2QBBLKDXLT98DQXFO6RKKGLML0GNHDU349PC0XH42OUNIU6Q3TIRUTOGFMC4THJ8BYN8MON1PRKIQBX56Y56CHCRD4J8IMF1FG", "hasFlower": 0, "hasSeason": 0}, {"id": "194", "q": "001,3,5,7,9,B,D.20,2,4,6,8,A,C,E.41,3,5,9,B,D.57.60,2,5,9,C,E.77.81,3,5,9,B,D.97.A0,2,5,9,C,E.B7.C0,2,5,9,C,E.D7.E3,5,9,B.F1,D.G3,5,7,9,B.H0,E.I2,4,6,8,A,C;160,E.81,3,B,D.95,7,9.A0,2,C,E.C0,E;282,C.95,9.B0,E:K9K4AEZ9O7GJXLDC4CZG7BB0ZFKNA50X6JK45DYJOYC9093DED5ELFWOY0LYNICHV3ASESZ33WFF64H5OIVJ6IAI6L", "hasFlower": 0, "hasSeason": 0}, {"id": "195", "q": "000,3,5,7,9,C.20,3,5,7,9,C.40,2,4,6,8,A,C.60,3,5,7,9,C.80,2,4,6,8,A,C.A0,2,4,6,8,A,C.C0,3,5,7,9,C.E0,3,5,7,9,C.G0,2,4,8,A,C;100,3,9,C.16.20,4,8,C.45,7.53,9.60,C.73,9.81,B.94,8.B0,C.C3,9.D5,7.E0,C.F4,8:C57LDPS0Y1L5TP1DAGA5BVGIIVNTAN7RWSCT70N6GD4WY090VP41LOAB7LSB5ORPC6RRWVIT61ID9WSO6NBYYGOC", "hasFlower": 0, "hasSeason": 0}, {"id": "196", "q": "001,3,7,B,D.23,5,7,9,B.41,3,5,7,9,B,D.60,2,5,7,9,C,E.80,4,6,8,A,E.A0,3,7,B,E.B5,9.C0,2,7,C,E<PERSON>D5,9.E3,B<PERSON>F5,7,9.G1,3,B,D;107.23,5,7,9,B.45,7,9.66,8.70,E.90,E<PERSON>B6,8.C0,<PERSON><PERSON>D5,9.F3,5,7,9,B.G1,D;246,8.66,8.B6,8;356,8.B6,8;457:896B0BS5N3D2ZTHSJW06LAJG7WJ5SYA1LZH88FC906FYYTAG9VDFH3JDPXH5VP6ANPDVBG0LP9T5YBCF2V78X1ZLSTZG", "hasFlower": 0, "hasSeason": 0}, {"id": "197", "q": "000,3,6,A,D,G.18.20,2,4,6,A,C,E,G.38.41,3,5,B,D,F.61,4,6,8,A,C,F.80,2,5,7,9,B,E,G.A0,3,5,7,9,B,D,G.C1,3,5,B,D,F.D7,9.E0,2,4,C,E,G.F6,8,A.G0,2,E,G;100,3,D,G.17,9.22,E.43,5,B,D.65,B.80,6,A,G.A6,A.C4,C.E2,E.G0,G:2G7KXX9QQLXOVGUQ8FHJ7YO6OGJ1PCQKRPPL1MTJF6917DJHHHREY1DVKA5F2OUV8AFVXAETA2CM2PT5T3UCD73CUGDK", "hasFlower": 0, "hasSeason": 0}, {"id": "198", "q": "001,5,7,B.13,9.20,6,C.32,4,8,A.51,4,8,B.66.71,B.84,6,8.90,2,A,C.A5,7.B0,2,A,C.C4,8.D2,A<PERSON>E0,4,6,8,C.G0,2,4,8,A,C;101,5,7,B.20,C.44,8.66.71,B.84,6,8.92,A.B0,2,A,C.C4,8.E0,5,7,C.G0,C;201,5,7,B.66.71,B.84,8.E0,6,C;301,B.71,B:3W4F69PLFR4U2JVFKKWEVI0OR2G07Y34B9GS2LO4K5MEJO5GY07BZ29SZJY3VIKWEUG96JRYOVB55RE073WZFPB7MZLL", "hasFlower": 0, "hasSeason": 0}, {"id": "199", "q": "000,2,5,7,A,C.20,4,8,C.32,6,A.40,4,8,C.52,6,A.70,2,4,6,8,A,C.91,3,5,7,9,B.B0,2,4,8,A,C.C6.D0,4,8,C.E2,6,A;100,2,5,7,A,C.20,4,8,C.36.43,9.66.72,A.86.91,4,8,B.B3,9.C5,7.D0,C.E6;200,5,7,C.36.C5,7;305,7:5IQCNJCRREE4WK5FGW233TRM17P0JGUPYU7WXPFQX2XEZI074546K7P6E9YTR91CJH48SJSDHWCNHMD58ZXH", "hasFlower": 0, "hasSeason": 0}, {"id": "200", "q": "002,4,6,8,A,C.10,E.22,4,6,8,A,C.30,E.42,4,6,8,A,C.50,E.64,6,8,A.70,2,C,E.84,6,8,A.90,E<PERSON>A2,4,6,8,A,C.B0,E<PERSON>C4,6,8,A.D2,C.E0,4,7,A,E;102,6,8,C.21,6,8,D.41,3,5,7,9,B,D.60,4,7,A,E.84,A.90,7,E.A4,A.B6,8.D2,C:URFK242B5M6F8VLXH2GU2NID80MBJ70K4CDHTI87CP4RXZIJV7O5FF5INZX3U3HGOUVVT5ZJDDP4L8XZ7Q6GJHQG", "hasFlower": 0, "hasSeason": 0}]