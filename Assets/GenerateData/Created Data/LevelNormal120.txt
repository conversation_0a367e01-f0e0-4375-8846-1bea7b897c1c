[{"id": "1", "q": "000,3,5,7,A.21,3,5,7,9.40,2,4,6,8,A.61,3,5,7,9.80,3,7,A.95.A0,2,8,A.B4,6.C0,A;103,5,7.22,4,6,8.40,2,4,6,8,A.61,3,5,7,9.80,A.A1,9.B4,6:APLWRUJX5LK9772I0XMK53BIVBWKVOMQ9STKYPSO53WRYAXX0UQ5JW3T32", "hasFlower": 0, "hasSeason": 0}, {"id": "2", "q": "000,2,4,6,8,A.20,2,8,A.40,4,6,A.60,2,4,6,8,A.80,4,6,A.A0,3,7,A.C1,3,7,9;102,4,6,8.21,9.44,6.50,A.64,6.70,A<PERSON>A0,3,7,A.C1,3,7,9:AEMGXXQP3HK3PCUMHEGVVPAQ3VHAKH5KTXLCMEGUXE3UKATL5UMPVG", "hasFlower": 0, "hasSeason": 0}, {"id": "3", "q": "000,2,4,8,A,C.21,3,6,9,B.42,4,8,A.50,6,C.62,A.75,7.80,2,A,C.94,8.A0,C.B2,4,6,8,A;100,2,4,8,A,C.23,6,9.42,A.56.62,A.75,7.81,B.A0,C<PERSON>B3,6,9:4NCCK943BHLIX3DULUKDHN3LJEDVB43WJ9XJHP4CBRIXEXIWBPLCVJRIDH", "hasFlower": 0, "hasSeason": 0}, {"id": "4", "q": "000,3,5,7,A.20,2,8,A.34,6.40,A.52,4,6,8.60,A.72,5,8.80,A.93,5,7.A0,A<PERSON>B2,8.C4,6;103,5,7.20,2,8,A.34,6.40,A.60,2,8,A.90,4,6,A.B2,8.C4,6:46SEAMEP5QK54TS6P6M1A70T6QI2S4FV8FI0XS2RR4UV22A81G7XAUKG", "hasFlower": 0, "hasSeason": 0}, {"id": "5", "q": "001,3,5,7.20,2,6,8.34.40,2,6,8.60,2,4,6,8.80,3,5,8.A0,3,5,8.C1,7;102,4,6.21,7.40,8.52,6.64.83,5.90,8.C1,7;221,7.40,8.90,8:NWJJ7C7FJ4NRR49WH4IC7J9FCUS2HI73WU4UNUR3W2PS9N9CRP", "hasFlower": 0, "hasSeason": 0}, {"id": "6", "q": "001,3,5,7,9.20,3,5,7,A.40,2,4,6,8,A.60,4,6,A.72,8.80,5,A.A0,2,8,A.B4,6.C1,9.D3,5,7.E0,A;103,7.23,5,7.41,5,9.64,6.A1,9.B5.C1,9.D3,5,7:SJT6DO63DUVJ1H6HQ7TQ84O4U8FM41JJSHHP4MQ6VD7FQ3ODO11P8FF8", "hasFlower": 0, "hasSeason": 0}, {"id": "7", "q": "000,2,4,6,8,A.20,2,4,6,8,A.40,3,5,7,A.60,2,4,6,8,A.81,5,9.93,7.A0,5,A<PERSON>B3,7.C0,5,A<PERSON>D2,8.E0,4,6,A;114,6.33,7.40,5,A.61,9.B0,5,A.E4,6:UDHR48A1V2KAADH9LS6Y3JFPLQ9FU3KV12VH68XA9VQHPJ9K2KS4Y2XR", "hasFlower": 0, "hasSeason": 0}, {"id": "8", "q": "000,5,A.12,8.20,4,6,A.41,3,5,7,9.60,2,4,6,8,A.80,2,5,8,A.A0,3,5,7,A.C0,2,4,6,8,A;100,A.12,8.34,6.42,8.55.62,8.81,5,9.A0,3,7,A.C2,8:C5N6UE5PMLE1V81P5CM6863P2MV21V5XN3XZMZUCL32U62UEZV31ECZP", "hasFlower": 0, "hasSeason": 0}, {"id": "9", "q": "002,4,6,8,A.10,C.23,5,7,9.30,C.42,5,7,A.50,C.62,4,8,A.80,2,4,8,A,C.A0,2,4,6,8,A,C.C0,2,4,8,A,C;115,7.35,7.51,B.63,9.81,4,8,B.A1,5,7,B:TEFSF8ISEDMR5BFUDCI2803TFYUU3W6OY2C6ORU005W9TBET09GMQQGJEJ", "hasFlower": 0, "hasSeason": 0}, {"id": "10", "q": "000,3,5,7,9,C.22,4,8,A.30,C.43,5,7,9.50,C.62,5,7,A.80,3,5,7,9,C.A0,4,8,C.B2,A.C4,6,8;100,3,6,9,C.22,4,8,A.30,C.43,6,9.62,A.84,6,8.A0,4,8,C.B2,A.C4,8:5FORYRJS2EACGPPEXAYKZEISV6NEWAHIUU0Z214MO5CM46TD2102AGWDFNXVKJTH", "hasFlower": 0, "hasSeason": 0}, {"id": "11", "q": "001,3,5,7,9,B.20,5,7,C.32,A.45,7.50,C.62,5,7,A.70,C<PERSON>82,4,6,8,A.90,C.A4,6,8.B1,B<PERSON>C3,5,7,9.D0,C<PERSON>E4,6,8;102,5,7,A.26.45,7.50,C.62,6,A.70,C.83,6,9.A6.B1,B.C3,9.D6:7GEDS0MCTWR9AVLT1SCYJZO0EMBS1MWQO2VE5Y678SFI28GFJQRZBMI5AQXQD9LE6X", "hasFlower": 0, "hasSeason": 0}, {"id": "12", "q": "000,2,5,8,A.20,2,4,6,8,A.42,8.54,6.60,A.75.81,9.94,6.B2,8.C0,4,6,A.D2,8.E5;101,5,9.20,3,5,7,A.54,6.60,A.75.81,9.94,6.C2,4,6,8.E5;215.23,7.54,6.60,A.C3,5,7.E5:MVEG721VZENEOQNT9YWU5WYJOBS6JWSH9D0QAA57SMMQ10MUD5QGBFTFE5H2W6ZS", "hasFlower": 0, "hasSeason": 0}, {"id": "13", "q": "000,3,5,7,A.20,5,A.32,8.40,4,6,A.52,8.64,6.70,A.83,7.A0,2,4,6,8,A.C0,2,5,8,A.E0,5,A;100,4,6,A.20,A.32,5,8.65.A0,A<PERSON>C1,5,9.E0,A;204,6.32,8.A0,A.C1,9;305.32,8;432,8:R7YOSXCYZ2XTZOMBHRHZCFM4BMI2TYGSIORBYJPSOPR76G7UB664JUCFS67ZCWW22M", "hasFlower": 0, "hasSeason": 0}, {"id": "14", "q": "001,3,6,9,B.20,2,4,8,A,C.40,2,4,6,8,A,C.61,3,6,9,B.80,2,4,6,8,A,C.A0,2,4,8,A,C.C3,9;101,3,9,B.21,3,9,B.44,6,8.66.80,C.94,8.A2,A;201,3,9,B.80,C;302,A:3S08QNG8S3538RZVNC0VP6141EQZS1PF8SPP3F9915RU0UNC7R7RAAG60BBNGGE4", "hasFlower": 0, "hasSeason": 0}, {"id": "15", "q": "001,3,5,7,9,B.21,5,7,B.33,9.40,5,7,C.52,A.64,6,8.70,C.83,6,9.A1,3,5,7,9,B.C0,2,5,7,A,C.E0,2,6,A,C;102,A.16.45,7.52,A.70,C.83,9.A2,6,A.C0,C.E1,6,B;216.45,7.52,A.70,C.A2,A.E6:DTCYV22PKHT3D2I4HKWQWIV3O3Y14IPCO14QSDOPH5354BVK1NOIHWPDK2YBSNNB5NBV15WY", "hasFlower": 0, "hasSeason": 0}, {"id": "16", "q": "000,2,5,8,A.20,2,4,6,8,A.40,2,4,6,8,A.61,3,5,7,9.80,2,4,6,8,A.A0,3,5,7,A.C0,2,5,8,A.E0,3,5,7,A;101,5,9.20,5,A.40,A.53,7.73,7.A0,3,7,A.C5.D0,A<PERSON>E4,6:HRS48UWT0K38UARFI6MQFF6HFC1MA01AY5LV1Q1CWYXV2SVL4ETI3XK588VPEP2A", "hasFlower": 0, "hasSeason": 0}, {"id": "17", "q": "000,2,5,7,A,C.20,3,9,C.35,7.40,3,9,C.55,7.60,2,A,C.74,8.80,C.92,4,8,A.B0,3,5,7,9,C.D0,3,9,C.E5,7;100,5,7,C.20,C.33,5,7,9.40,C.61,B.74,8.93,9.B0,3,9,C.D0,C:WPKHZ9OZZV91QJ1FH2LJM47SXVA0SPOJM24QS0749MHMPWS2Q2K94ZFPFH0AL0JXFQ", "hasFlower": 0, "hasSeason": 0}, {"id": "18", "q": "000,3,7,A.21,3,7,9.41,9.53,5,7.60,A<PERSON>72,5,8.80,A.92,8.A0,4,6,A<PERSON>C0,<PERSON><PERSON>D3,7.E0,A;100,3,7,A.21,3,7,9.41,9.54,6.71,5,9.91,9.A4,6.C0,A<PERSON>D3,7;200,3,7,A.22,8.75.C0,A;322,8.C0,A;4C0,A:237Y7O6ITU0RPKI7PT917K0389OY3U0WRWWO28P1O31U8K4R26YR99410IY4TTIKW84UP2", "hasFlower": 0, "hasSeason": 0}, {"id": "19", "q": "000,2,4,6,8,A,C.20,3,5,7,9,C.40,3,5,7,9,C.64,6,8.70,2,A,C.85,7.A0,3,5,7,9,C.C0,2,A,C.D4,6,8.E0,C;101,4,6,8,B.26.33,9.40,C.55,7.70,2,A,<PERSON><PERSON>95,7.A3,9.B0,C.D6:JS9XK45PZDOJX6SD19G42YXWUTSH4Y2ZXGATWK63UHN4WHAHKSV8PVKW8Z1EO53ENZ", "hasFlower": 0, "hasSeason": 0}, {"id": "20", "q": "001,3,5,7,9.20,2,5,8,A.40,4,6,A.60,2,5,8,A.81,3,7,9.95.A0,3,7,A.B5.C1,3,7,9.D5.E0,3,7,A;102,4,6,8.21,9.35.40,A.55.60,2,8,A.81,9.93,7.A0,5,A.B3,7.C1,5,9.E0,3,7,A:15RG7L1C5KM59KCQENM5CVNGFVTKE8NX71XKE8FMNVZLVCRM9RSZLQSZSEZ1LQ9SQT9R", "hasFlower": 0, "hasSeason": 0}, {"id": "21", "q": "000,2,4,6,8,A,C.20,3,5,7,9,C.40,2,6,A,C.54,8.60,6,C.73,9.80,5,7,C.92,A.A0,4,6,8,C.C0,2,5,7,A,C;100,4,8,C.20,3,6,9,C.56.86.A5,7.B0,C;220,6,C.B0,C;320,C.B0,C:90100JLKLJJ12XO33XL9K3Z97I878KKZIG18LOZOZ2V0CVG9PCGCG6O3J7768CP1VV", "hasFlower": 0, "hasSeason": 0}, {"id": "22", "q": "000,2,4,6,8,A.20,2,5,8,A.40,3,5,7,A.60,3,5,7,A.80,2,8,A.94,6.A0,A<PERSON>B3,5,7.C1,9.D3,5,7.E1,9;100,3,7,A.43,5,7.60,3,7,A.90,A<PERSON>A4,6.C1,4,6,9:Y4U5SWU9OWEKB6LFUCTL19I38OMX3S7EDQ85C76MXAI4CKYC1TZUAQBFDZ", "hasFlower": 0, "hasSeason": 0}, {"id": "23", "q": "000,2,4,6,8,A.31,9.44,6.50,A.63,5,7.70,A.83,5,7.A1,9.C1,3,7,9;102,8.31,9.44,6.50,A.63,7.83,5,7.A1,9.C1,3,7,9;231,9.84,6.A1,9.C3,7;331,9.85.A1,9;431,9.A1,9:KNHCJUI8YNO12YQ7EMM0E48SOWSH01J8QRG542KLWW4HCUGLR5IBA87BHL4ALW", "hasFlower": 0, "hasSeason": 0}, {"id": "24", "q": "001,3,5,7,9.20,2,5,8,A.40,2,4,6,8,A.60,2,4,6,8,A.80,2,4,6,8,A.A0,2,5,8,A.C0,3,5,7,A;103,7.30,5,A.43,7.50,5,A.62,8.70,A.83,5,7.A0,5,A.C0,4,6,A:WKJ7FJ2M2U7N2GKFLJL0EAT3CGTUZEQQ4URLJL0TNS21CVWS41MU7VCA7T3RCZ", "hasFlower": 0, "hasSeason": 0}, {"id": "25", "q": "000,2,4,6,8,A.21,3,5,7,9.40,3,7,A.60,2,5,8,A.80,3,7,A.95.A0,3,7,A.C0,2,4,6,8,A.E0,3,7,A;101,5,9.24,6.40,3,7,A.62,8.B0,3,7,A.C5;201,5,9.24,6;301,9;401,9:U4IX0DMSNO270YMASYS846L6G19PNXMTIGPTEEUMQ1CZY2B7D9ZSKOQKV8VYACLB", "hasFlower": 0, "hasSeason": 0}, {"id": "26", "q": "000,2,4,6,8,A,C.20,2,4,8,A,C.41,3,5,7,9,B.60,2,5,7,A,C.80,2,4,6,8,A,C.A1,5,7,B.B3,9.C0,5,7,C;104,8.23,9.43,6,9.51,B.80,3,9,C.96.B3,5,7,9:5VG3I7UJ2IAUV7C0RCQLMNG587VH2V8MJDRFN0FSBBLHK3FTNN1KAFQT17SD", "hasFlower": 0, "hasSeason": 0}, {"id": "27", "q": "000,2,4,8,A,C.16.20,2,A,C.34,8.42,A.50,6,C.63,9.70,6,C.82,A.94,8.A0,2,A,C.C1,4,6,8,B;100,4,8,C.16.32,4,8,A.50,6,C.94,8.A2,A.C1,5,7,B;204,8.33,9.50,C.94,8:JB93CXP25F7CIU5FPG2LDABXOC7LAXEL6I7BXSA2JLAE8U48DB4G3O67IC33S29I", "hasFlower": 0, "hasSeason": 0}, {"id": "28", "q": "000,2,4,6,8,A,C.21,3,9,B.35,7.40,3,9,C.55,7.60,C.74,6,8.90,3,5,7,9,C.B1,3,9,B;100,2,5,7,A,C.23,9.35,7.56.60,C.74,6,8.94,6,8.B3,9;206.36.60,C.94,6,8:AANCMA5R9ZCZCMN6LRM69QQI5ZVLZEVMEKRKN6KCEK995IRQV6QEIILL5AV2N2", "hasFlower": 0, "hasSeason": 0}, {"id": "29", "q": "000,3,6,9,C.21,3,5,7,9,B.40,3,5,7,9,C.60,2,4,6,8,A,C.80,3,5,7,9,C.A1,4,6,8,B.C0,2,4,6,8,A,C.E0,2,4,8,A,C;100,C.22,A.40,4,8,C.B4,8.C2,A.D0,C.E2,A:KFR2R41PHF1BI0TIEMSPD2JUX9UL3GISXQ7KUV3TJSVSJHBL5GUK547QJ9MIDE0K", "hasFlower": 0, "hasSeason": 0}, {"id": "30", "q": "000,2,4,6,8,A,C.20,2,5,7,A,C.40,3,6,9,C.60,4,8,C.72,6,A.80,4,8,C.A1,4,8,B.B6.C1,4,8,B.D6.E0,2,4,8,A,C;104,6,8.21,6,B.50,C.70,3,9,C.A1,B.B5,7.C1,B.D6.E3,9:QPAQHUWMHM72AJY8TOYA28FMHYWU7LVVUPK87RLPMQFYRTHO7KTO28FLLPFOAQ2TJU", "hasFlower": 0, "hasSeason": 0}, {"id": "31", "q": "000,3,9,C.15,7.20,2,A,C.35,7.50,3,5,7,9,C.73,6,9.80,C.94,6,8.A1,B.B3,5,7,9.C0,C;110,C.22,A.45,7.50,C.80,C.95,7.B3,9.C0,C;210,C.45,7.50,C.80,C.95,7.C0,C;350,C.80,C:C2YX0MO7DUQZ25RWDU8BCNOLBS5LZMS0M8NMJ9VIVPPXHWKR739IA6HQ6K344YNJAN", "hasFlower": 0, "hasSeason": 0}, {"id": "32", "q": "000,3,6,9,C.21,3,6,9,B.40,2,4,6,8,A,C.60,3,5,7,9,C.80,2,4,8,A,C.96.A0,2,4,8,A,C.B6.C0,2,4,8,A,C;106.22,A.36.41,4,8,B.60,3,9,C.80,C.94,8.A6.B2,A.C0,C:R03H2FB3MUKHM3HNM9M9RP2R6BLPU3077UKKXNYKFFFPL697Y9S6L6RLBXHPBSU7", "hasFlower": 0, "hasSeason": 0}, {"id": "33", "q": "000,3,5,7,A.21,5,9.41,3,5,7,9.61,4,6,9.80,4,6,A.92,8.A0,4,6,A.C0,4,6,A.D2,8.E0,5,A;105.21,9.45.61,4,6,9.91,3,7,9.B5.C0,A<PERSON>E0,5,A;205.61,9.91,9.C0,A.E5:LEUDSLKYK4LEB1QWLA1TP1JT6U8EQ686J77617CG07AA0F4FDSWAVB55YGCPVE", "hasFlower": 0, "hasSeason": 0}, {"id": "34", "q": "001,3,5,7,9.20,2,4,6,8,A.40,3,5,7,A.60,2,5,8,A.80,3,7,A.95.A1,3,7,9.B5.C0,2,8,A.D4,6.E0,2,8,A;103,5,7.20,3,5,7,A.40,5,A.61,9.83,7.B2,5,8.D0,3,5,7,A:VX7RRIPDW76H3SMM2T258CUJ0W0BHKU1S6ACA3M1XRREYGK8IB4J5GPLVETMYDL4", "hasFlower": 0, "hasSeason": 0}, {"id": "35", "q": "000,2,4,6,8,A.20,2,4,6,8,A.43,5,7.50,A.62,5,8.70,A.82,4,6,8.90,A.A3,5,7.C0,3,5,7,A.E0,3,7,A;122,4,6,8.43,7.60,2,8,A.75.80,3,7,A.A4,6;243,7.60,A.75.A4,6:II8T53XK5PF3QP9O4CM7GJ40GKDS68QILFHY3S9MVH74TXVD461AAOUCL1IJY3U0", "hasFlower": 0, "hasSeason": 0}, {"id": "36", "q": "000,4,6,A.12,8.20,4,6,A.32,8.40,4,6,A.52,8.60,4,6,A<PERSON>72,8.84,6.90,A.A2,4,6,8.B0,A.C2,4,6,8.D0,A<PERSON>E2,4,6,8;104,6.21,4,6,9.42,8.50,4,6,A.A0,4,6,A.B2,8.C4,6.D1,9.E4,6:EM49PP2WXTMUZUK9XQ9077E024BUGK0XPTPG04G2BUMZ4BK77TWBM2E9QTREXRZKRRZG", "hasFlower": 0, "hasSeason": 0}, {"id": "37", "q": "000,2,5,7,A,C.20,3,5,7,9,C.41,3,5,7,9,B.60,2,6,A,C.81,4,8,B.A1,4,6,8,B.C0,3,5,7,9,C;100,6,C.20,3,9,C.36.43,9.51,6,B.71,B.A1,4,6,8,B.C0,3,5,7,9,C:ZCV6I1YEVGNP6FPZSIYCWPF4V6RRFBEWN0W4D0C1BPR76VCYWR7Y7DTSFJ0J70TG", "hasFlower": 0, "hasSeason": 0}, {"id": "38", "q": "000,2,5,8,A.20,3,5,7,A.40,3,5,7,A.60,2,4,6,8,A.80,2,4,6,8,A.A0,2,5,8,A.C0,2,5,8,A.E0,2,4,6,8,A;123,7.30,A.44,6.60,3,5,7,A.80,2,5,8,A.A5.B0,A.C2,8.E1,4,6,9:X93ZZR6SFVM4AZ6HDG7W1S83EV4E41ENRS2FDW9E3GFAHDXFHMARRHKZ7ND4KA2GS8G3", "hasFlower": 0, "hasSeason": 0}, {"id": "39", "q": "000,2,4,6,8,A,C.21,3,5,7,9,B.40,2,4,8,A,C.56.61,4,8,B.76.80,3,9,C.A0,2,4,6,8,A,C.C0,2,4,8,A,C.D6.E2,4,8,A;100,4,8,C.22,4,8,A.54,8.66.80,C.A0,2,4,8,A,C.C1,3,9,B.D5,7:XXO95V8TRBXQOLYG576QFPP6QBVY6POOWG2IQRKH6PVLYH8BX305K0IWY39B079092RRF5VT", "hasFlower": 0, "hasSeason": 0}, {"id": "40", "q": "000,2,8,A.15.20,2,8,A.34,6.40,2,8,A<PERSON>54,6.60,A.72,5,8.80,A.93,7.A0,5,A.B2,8.C0,4,6,A.D2,8.E0,5,A;101,9.15.20,A.32,5,8.40,A.70,A.93,7.B0,A<PERSON>C2,5,8.D0,A<PERSON>E5;232,5,8.70,A.93,7.C2,5,8:UWLL2WK2K1BNMGCR3GUZ1J2VYFZLHFOYOFJUMY1V7WL3M1OVJ6GGCO7YWRHJNNV67BUF72NM", "hasFlower": 0, "hasSeason": 0}, {"id": "41", "q": "000,2,5,8,A.20,2,5,8,A.40,2,4,6,8,A.60,3,5,7,A.81,3,5,7,9.A0,3,7,A.B5.C1,3,7,9;100,A.20,2,8,A.35.40,3,7,A.64,6.83,7.A0,A<PERSON>B4,6.C1,9;210,A.35.43,7.83,7;335.83,7:ENLL4863PXMZJ62WTTW7Z5R2A4D39PBNUK8XK07Z5OZA6IRB9UE0JBHGBHM2DIO2G6", "hasFlower": 0, "hasSeason": 0}, {"id": "42", "q": "000,3,6,9,C.20,2,5,7,A,C.40,2,4,8,A,C.60,2,4,6,8,A,C.80,2,4,8,A,C.A0,2,6,A,C.C0,4,6,8,C;132,A.50,2,4,8,A,C.73,9.80,C.A2,A.C4,8;242,A.50,4,8,C.73,9.80,C.C4,8:YHK72TDIHYEXUN077PU74HOEPI5SJT5GVSOBXZ6N0ZLF6YZUJ2KFL4PDCZHVMPCUGMYB", "hasFlower": 0, "hasSeason": 0}, {"id": "43", "q": "000,3,6,9,C.20,2,5,7,A,C<PERSON>41,5,7,B<PERSON>53,9.60,5,7,C<PERSON>72,<PERSON><PERSON>80,5,7,C.93,9.A1,5,7,B.C0,2,4,6,8,A,C.E2,4,6,8,A;103,9.10,6,C.22,A.35,7.53,9.65,7.85,7.A1,B.B5,7.C1,B.D5,7:CCR0VEECXVK35K0X9AXG80IOUGICK9DAVERU5IA0GRDDUOAFGK33FF58V3ERDUF95IX9", "hasFlower": 0, "hasSeason": 0}, {"id": "44", "q": "001,4,6,8,B.20,4,6,8,C.32,A.40,4,6,8,C.52,A.60,4,6,8,C.72,A.80,4,6,8,C.92,A.A0,5,7,C<PERSON>B3,9.C0,5,7,C<PERSON>E0,3,5,7,9,C;105,7.32,A.46.50,C.65,7.73,9.85,7.A0,6,C.E4,6,8:9JSY71NWFXM8UME44CASIVHLZ5EQ33450TCZ0TT759PQWJGIGR2U5XHTFAVR4LP82Y1N", "hasFlower": 0, "hasSeason": 0}, {"id": "45", "q": "001,3,6,9,B.20,2,4,8,A,C.43,9.56.60,2,A,C.76.84,8.A0,5,7,C.B2,A.C0,4,8,C;102,6,A.20,2,4,8,A,C.56.60,2,A,C.76.A0,5,7,C.B2,A.C0,4,8,C;223,9.56.60,C.76.A0,5,7,C:X35WFJ73X8DW03YXVJCDIB8HBYH9RHLC6N7F06ULR4J6NB4EEDPUH3JPVLX9B5LD6I", "hasFlower": 0, "hasSeason": 0}, {"id": "46", "q": "000,3,7,A.15.20,2,8,A.34,6.40,2,8,A<PERSON>54,6.60,2,8,A.74,6.80,2,8,A.94,6.A0,A.B2,8.C0,4,6,A.D2,8.E0,5,A;100,A.20,A.33,7.52,4,6,8.72,4,6,8.94,6.B2,8.C4,6.D0,A:PMYMK6YD7HMCWPYPE484OW7Q7C9N8NNW97DHONH4MECKKW4Q6HOCOB6KP6DD8B8Y", "hasFlower": 0, "hasSeason": 0}, {"id": "47", "q": "000,2,5,7,A,C.21,5,7,B.40,2,5,7,A,C.60,2,4,6,8,A,C.80,3,5,7,9,C.A0,2,5,7,A,C.C4,8.D1,6,B.E3,9;102,5,7,A.26.42,A.60,2,4,8,A,C.85,7.90,C.D6;202,A.64,8.85,7;364,8:Y3M2DWO3Z1IOLLCCJOS922AEIE589G9ZEO6C269AGCXJ3W5MJUK43DJ4EYZX8S4Z41UK", "hasFlower": 0, "hasSeason": 0}, {"id": "48", "q": "000,2,4,8,A,C.16.20,3,9,C.36.41,4,8,B.56.60,2,A,<PERSON>.75,7.81,3,9,B.95,7.A0,2,A,C.B4,6,8.C0,2,A,C.D4,8.E0,2,6,A,C;101,B.20,3,9,C.56.83,9.A2,5,7,A.C0,C.D3,9.E0,C;201,B.56.A2,6,A:2KVRUUA85Y7X9SCVYGIC2R2CG9XCWS8AU2ZWHIWHZYYVIK3ZTKUIHK95THTSTW89VS37Z338", "hasFlower": 0, "hasSeason": 0}, {"id": "49", "q": "000,3,5,7,9,C.20,3,5,7,9,C.40,3,9,C.60,2,5,7,A,C.81,3,9,B.A2,4,6,8,A.B0,C.C2,4,6,8,A;115,7.20,C.43,9.61,5,7,B.82,A<PERSON>B0,C<PERSON>C5,7;215,7.20,C.43,9.61,B.82,A.B0,C.C5,7:42HTHGFAB6FG8B8XLGTOLR7T9FK0TES3PIXFBEJSX4HXHBN0IM9ROGV7V6N3AZKJZM2P", "hasFlower": 0, "hasSeason": 0}, {"id": "50", "q": "000,2,4,6,8,A.21,4,6,9.40,2,5,8,A.60,3,5,7,A.80,2,5,8,A.A1,4,6,9.C0,2,4,6,8,A.E2,4,6,8;102,5,8.21,4,6,9.40,2,8,A.65.70,A.82,5,8.A1,4,6,9.C0,3,7,A.E2,4,6,8:W7N32K4R427NHD02BHNWW8K0E18Q73C5183DC51WHQ6Q6DNKRBEQ43712HD84X0K66X0", "hasFlower": 0, "hasSeason": 0}, {"id": "51", "q": "000,2,4,6,8,A,C.21,3,5,7,9,B.40,2,4,8,A,C.56.60,2,A,C.75,7.80,2,A,C.94,6,8.A1,B.B3,5,7,9.C0,C.D2,4,8,A<PERSON>E0,6,C<PERSON>F3,9.G0,5,7,C;103,9.16.23,9.31,B.43,9.76.95,7.B5,7.D3,9.E6.F0,3,9,C.G6:920FIJ29NGM3TBZZZNJ9GUYT6YFBGGZ0956PJ530MTM5UBIVYR36B6LLTKV5PSMJ0OKRSQLYQLO3", "hasFlower": 0, "hasSeason": 0}, {"id": "52", "q": "000,2,5,8,A.20,2,4,6,8,A.41,3,5,7,9.60,2,8,A.74,6.80,2,8,A.A2,5,8.C0,2,4,6,8,A.E0,2,4,6,8,A;101,5,9.20,2,4,6,8,A.41,3,7,9.70,3,5,7,A.A2,8.C5.D0,2,8,A;220,4,6,A.42,8.70,A.D0,A:JFJXB6FHYFPYPC3KUK11MEP6Y63AX1UBK881UDOHALEMTFUA9AXTWCP9LCGOGMCMWHZ86KZD8XYH", "hasFlower": 0, "hasSeason": 0}, {"id": "53", "q": "000,2,5,7,A,C.20,2,4,8,A,C.36.42,4,8,A.60,2,4,8,A,C.80,2,5,7,A,C.A1,3,5,7,9,B.C0,2,4,6,8,A,C.E0,2,4,8,A,C;100,5,7,C.22,A.35,7.63,9.71,B.85,7.B5,7.C0,2,A,C.E0,4,8,C:MAPJMKPAEHXI1IZC1SHC514J9LFE4GP6XE9ZMCNILEAJ614SKIC5LFPGXHSHLXMGJSAG774N", "hasFlower": 0, "hasSeason": 0}, {"id": "54", "q": "000,3,5,7,9,B,E.20,2,5,7,9,C,E.40,3,7,B,E.55,9.60,2,7,C,E.80,2,6,8,C,E.A0,2,4,6,8,A,C,E.C2,6,8,C.D0,E.E2,4,6,8,A,C;105,9.22,C.30,E.47.50,E.70,E.91,D.B7.C2,C.E2,C:YLPCEOS55VD85E0S0JOGWZB8GHT2HPJ2VUUHW5L2VV230LYTPO8H0437CEZOBYPYED87L4", "hasFlower": 0, "hasSeason": 0}, {"id": "55", "q": "000,2,4,6,8,A.20,2,4,6,8,A.40,3,5,7,A.61,3,7,9.75.81,3,7,9.A0,2,4,6,8,A.C2,4,6,8.D0,A.E2,5,8;102,8.15.20,A.63,7.83,7.A2,5,8.D0,A.E5;202,8.15.20,A.63,7.83,7.D0,A.E5:06AR106KG3AGT0SAZDYT2T1GGKR93260L3Z9TLRYRDKPSYKLZ9PYAPLD292D3S61Z1SP", "hasFlower": 0, "hasSeason": 0}, {"id": "56", "q": "000,3,5,7,9,C.21,3,5,7,9,B.42,5,7,A.50,C.62,4,6,8,A.70,C.82,4,6,8,A.A0,2,4,6,8,A,C.C1,4,6,8,B;104,8.21,3,5,7,9,B.45,7.51,B.63,6,9.71,B.83,5,7,9.A1,3,6,9,B.C1,4,8,B:EYJCNX9BCK5NY2XB321JVU8X1P3BM3K9BYMG9UPNK8VU5ZZE89P2JX8ZN1V3P2V1UEGKJZEY", "hasFlower": 0, "hasSeason": 0}, {"id": "57", "q": "001,3,6,9,B.21,3,5,7,9,B.40,4,6,8,C.52,A.60,4,8,C.72,A.80,5,7,C.92,A.A4,6,8.B0,C<PERSON>C2,4,6,8,A.E1,3,9,B;111,B.34,8.40,6,C.54,8.61,B.80,2,5,7,A,C.A4,6,8.B0,C.C2,4,8,A.E1,B:TH8VO3M4U6SS12GOGOPHPZVZ65514O8CG5T2KCKZBB87533S8SCPZBDEVPDB43VG7MD4DUCE", "hasFlower": 0, "hasSeason": 0}, {"id": "58", "q": "000,2,4,6,8,A.20,3,5,7,A.43,5,7.50,A.62,8.74,6.81,9.A0,2,4,6,8,A.C0,4,6,A.E0,2,5,8,A;102,4,6,8.23,7.35.43,7.50,A.81,9.A0,A.B4,6.C0,A<PERSON>E5;213,7.50,A.81,9.A0,A.B5;313,7.50,A.A0,A;450,A.A0,A:Y5LMJM90QKJAM2IGBBCUPX37KDQ70NVFWNDK9CF5SDDSE9TPT293A3UGL2WAXABEM3H8YH2I8VKB", "hasFlower": 0, "hasSeason": 0}, {"id": "59", "q": "000,3,5,7,9,C.21,3,5,7,9,B.40,3,5,7,9,C.60,3,5,7,9,C.80,3,6,9,C.A0,3,5,7,9,C.C0,2,5,7,A,C.E0,2,4,6,8,A,C.G1,3,6,9,B;104,8.26.40,5,7,C.63,6,9.86.A4,8.B6.D0,5,7,C.E2,A.F6.G1,3,9,B:GXUNDLGYUIYJF1CGR1PL16UJBNF962JRZ2Z1JCEU0PCIVVE0VPR9N56BZE5Y7E97X9NXRVZDC6YPXG", "hasFlower": 0, "hasSeason": 0}, {"id": "60", "q": "000,3,6,9,C.21,3,9,B.35,7.40,2,A,<PERSON><PERSON>54,6,8.60,C<PERSON>72,4,6,8,A.80,C.93,5,7,9.B0,4,6,8,C<PERSON>C2,<PERSON><PERSON>D0,6,C.E2,4,8,A.G1,3,9,B;121,B.35,7.40,C.55,7.83,6,9.B4,8.C2,A<PERSON>D0,C.E2,A;235,7.C2,A.E2,A:BB5FJ7PV7YID93D923Y6VF2Z6F2A8BX4CPD1AC7PH4HP1HJFJ1INZ16B6YX97N44H2DN5NJY98", "hasFlower": 0, "hasSeason": 0}, {"id": "61", "q": "000,2,4,6,8,A,C.20,2,4,8,A,C.40,3,5,7,9,C.60,2,5,7,A,C.81,3,6,9,B.A0,4,6,8,C.B2,A.C0,4,8,C.E1,4,8,B.G0,3,5,7,9,C;101,5,7,B.23,9.43,5,7,9.60,C.72,A.86.A4,8.B0,C.E1,B.G4,8:Y0KQ2Q4QIUG0RG842MLXO0MUYA4O3SY3RSLHWMZG6JH0H6XVLH7G8WOWO6KIM6VKYL4JZAKQW7", "hasFlower": 0, "hasSeason": 0}, {"id": "62", "q": "000,2,4,6,8,A,C.20,3,9,C.36.41,B<PERSON>54,6,8.61,B.73,6,9.80,C.92,6,A.A0,4,8,C.B2,6,A.C4,8.D0,C.E2,4,8,A;105,7.36.51,4,6,8,B.73,9.96.A1,3,9,B.B5,7;205,7.46.54,8.73,9.A2,A.B5,7:LM8D9S7ZJQ3MGKV4KMCAH3XP9DA4AP8LZOS6YWMYUGQ0WYW7YUA7O9KFVGG69XHC0FKW7J", "hasFlower": 0, "hasSeason": 0}, {"id": "63", "q": "000,4,6,A.12,8.24,6.30,A.42,4,6,8.50,A.63,5,7.70,A.82,4,6,8.90,A.A2,4,6,8.C0,3,5,7,A.E0,2,4,6,8,A;100,4,6,A.34,6.40,2,8,A.54,6.60,A<PERSON>73,5,7.80,A.92,8.A4,6.C3,7.E1,3,5,7,9:55418890L1LR9TYLQGSRU0QS9Y288USQTP5UUFSFGGR4F1QTP4FY02YPLPO2T92G1R4OOO50", "hasFlower": 0, "hasSeason": 0}, {"id": "64", "q": "000,3,9,C.15,7.20,C.32,4,6,8,A.40,C<PERSON>52,4,6,8,A.60,<PERSON><PERSON>72,5,7,A.80,C.93,9.A1,6,B.B3,9.C0,6,C.D4,8.E1,B;100,C.26.30,2,A,C.44,8.50,2,A,C.65,7.70,2,A,C.93,9.A1,B.C0,6,C.E1,B:R3C4S0YQC53B6S716QIG2T9LBTPX12M06WY2PIMYGT79GNQTERIILNVGX6D004VDQW2Y5E", "hasFlower": 0, "hasSeason": 0}, {"id": "65", "q": "002,4,6,8.10,A.23,7.31,9.43,5,7.50,A.63,5,7.70,A.82,5,8.90,A.A2,4,6,8.B0,A<PERSON>D0,3,7,A.E5;102,8.32,8.50,3,7,A.65.82,8.95.A0,2,8,A.C0,A;202,8.50,A.65.95.A0,2,8,A.C0,A;302,8.50,A.A0,A;402,8:YAK06XJJE0U249ZRH45U9BOY9CT41A1ZLXT098Q4KCLOJWBEAH0C53J3NC8VORVNOQKAUK62WU", "hasFlower": 0, "hasSeason": 0}, {"id": "66", "q": "000,2,4,6,8,A,C.20,2,4,8,A,C.40,2,4,6,8,A,C.60,3,6,9,C.80,2,5,7,A,C.A0,2,4,8,A,C.B6.C0,3,9,C.D5,7.E1,3,9,B.F5,7.G0,2,A,C;103,5,7,9.34,8.82,A.A3,9.D3,5,7,9.F5,7.G2,A:XAYF5LU5O704ZG068N2LL9CDG820FAMJB713YO5DL4I15SCFYFT0C6XJZUZCZTI8NS98BY3M", "hasFlower": 0, "hasSeason": 0}, {"id": "67", "q": "000,3,5,7,9,C.20,2,A,C.34,6,8.51,6,B.63,9.70,6,C.83,9.95,7.A1,B.B6.C2,4,8,A<PERSON>D0,C.E2,5,7,A;103,6,9.10,C.22,A.34,6,8.56.63,9.70,C.83,9.95,7.C3,9.E2,5,7,A;206.22,A.46.63,9.70,C.83,9.95,7.E2,5,7,A;306.63,9.83,9:Q0YY5NCGG0X09QB1B1NGE3887I1U5UW9SKW71A2C9CR2EKR8YQ0X75UCASR8B95E3I7UB2GQWRKXY2LLXWEK", "hasFlower": 0, "hasSeason": 0}, {"id": "68", "q": "000,2,4,6,8,A,C.20,2,4,8,A,C.36.40,2,4,8,A,C.56.60,4,8,C.72,6,A.80,4,8,C.96.A1,4,8,B.C0,2,4,6,8,A,C.E0,2,4,8,A,C;101,3,5,7,9,B.20,4,8,C.32,A.40,5,7,C.64,6,8.70,C.94,6,8.C2,5,7,A.D0,C.E2,A:3JHLW1T9C4M4DF7ACDED1KLTQWDJM3EKFUAOOLKUE4UJPSAFVUVLMMQS4VWTJ1HCO93FPTAS37WVCSOK1E", "hasFlower": 0, "hasSeason": 0}, {"id": "69", "q": "000,2,5,7,A,C.20,2,5,7,A,C.41,3,5,7,9,B.60,4,6,8,C.81,4,8,B.96.A1,4,8,B.B6.C0,2,A,C.D5,7.E1,3,9,B;102,5,7,A.20,2,5,7,A,C.45,7.60,C.81,B.A1,B.C6.D1,B.E3,9;212,A.45,7.60,C.81,B.A1,B;360,C.91,B:E68JOE7YSDCVDKS7OAIFWY2N4Z33ECO6Q7ZBOFTMFB7VYVKK2TYK8MAUNEAL525QURAR5IVFL5B2B4WJ", "hasFlower": 0, "hasSeason": 0}, {"id": "70", "q": "001,3,5,7,9,B.20,2,4,6,8,A,C.41,3,5,7,9,B.60,3,5,7,9,C.80,4,6,8,C.A0,4,6,8,C.C0,2,6,A,C.E0,3,5,7,9,C.G0,2,6,A,C;101,4,8,B.16.33,6,9.63,9.84,6,8.A4,8.C2,A.E3,6,9.G6;201,B.36.84,8.A4,8.E6:LWDVZPQG7IDVC7Q115F7L73GS6V5ZCQ36FDCHNWPBP6M8UMZD1HS0M1H56089MH00CPUZ9I5VWWBUNUQ", "hasFlower": 0, "hasSeason": 0}, {"id": "71", "q": "000,2,4,8,A,C.16.22,4,8,A.30,C.42,5,7,A.50,C<PERSON>65,7.73,9.81,6,B.94,8.A6.B1,B<PERSON>C3,5,7,9.D1,B<PERSON>E3,5,7,9.G1,3,5,7,9,B;100,3,9,C.15,7.30,2,A,C.50,5,7,C.81,6,B.C1,3,5,7,9,B.E5,7.G1,5,7,B:LG7QPVIEGYX6MFT4WVS26Q5E69AI5T0X2B3SE6RRXGB098A2KKWUA0E4U32M0AONVF78LOYNPXVG", "hasFlower": 0, "hasSeason": 0}, {"id": "72", "q": "000,2,4,8,A,C.16.20,2,4,8,A,C.36.40,2,4,8,A,C.56.61,3,9,B.76.82,4,8,A.96.A2,A.B0,4,6,8,C.D0,2,4,6,8,A,C;100,2,A,C.24,8.30,C.45,7.51,B.73,6,9.92,A.B0,5,7,C.D4,8;202,A.24,8.73,9.D4,8:YXTVZYOYHD96HKQV80TBOLFV1SI6X8S9Q1K4CFF3CFC3V1Z8I0XGALOGCZYD19ZSXES8EB94OA66", "hasFlower": 0, "hasSeason": 0}, {"id": "73", "q": "000,2,4,6,8,A,C.20,2,5,7,A,C.40,2,4,6,8,A,C.60,2,5,7,A,C.80,2,5,7,A,C.A0,3,5,7,9,C.C0,3,5,7,9,C.E1,3,5,7,9,B;100,2,5,7,A,C.30,C.44,8.56.71,6,B.95,7.B4,8.C0,C.E5,7:VVJN61K9F6QB566ZZNTV8XV21B5DJ581UZIRNUIXFXUN2LFL9KJ1TXRBFQ2DJ5D2UDZWBRRW", "hasFlower": 0, "hasSeason": 0}, {"id": "74", "q": "002,4,6,8,A,C.22,4,A,C.30,6,8,E<PERSON>43,B.50,6,8,E<PERSON>62,4,A,C.70,6,8,E<PERSON>84,<PERSON><PERSON>91,6,8,D.A3,<PERSON><PERSON>B0,5,9,E<PERSON>C2,7,<PERSON><PERSON>D4,<PERSON><PERSON>E1,7,D;106,8.22,4,A,C.30,<PERSON><PERSON>B0,E<PERSON>C2,C.D4,7,A.E1,D;206,8.23,B.B0,E.C2,C.D7:AQCZOL0AK85AYF9X95QVHY54O49LO8BZHB1XVVH0AL7FU51LUJUFHGC40ZK9UJ04ZGGKO7FKVG", "hasFlower": 0, "hasSeason": 0}, {"id": "75", "q": "000,2,4,6,8,A,C.20,3,5,7,9,C.40,2,4,6,8,A,C.61,3,5,7,9,B.81,3,5,7,9,B.A1,3,5,7,9,B.C1,3,9,B.D5,7.E0,2,A,C;102,5,7,A.10,C.23,6,9.30,C.44,8.56.62,A.74,8.82,A.A3,6,9.C1,B.D6.E0,C:JR4Q8DADCQ7A3JE7LDEA6IFTRCLEFGC2I244R5TQ7I3R36TFL6LGT5CJ322GGI7886Q5DEF48AJ5", "hasFlower": 0, "hasSeason": 0}, {"id": "76", "q": "000,2,5,7,A,C.20,2,4,6,8,A,C.40,2,4,6,8,A,C.60,2,4,8,A,C.76.82,4,8,A.90,C.A2,5,7,A.B0,C.C2,5,7,A.D0,C<PERSON>E2,4,6,8,A;101,B.15,7.20,2,A,C.34,6,8.41,B.54,8.62,A.75,7.92,A.B1,B.C6.D0,2,A,C.E5,7:DG1GEVFCTE47QYH197GGQODE8NVNN7QB3Z4CXAZHT9A0Z2NY70B23WIW9TTCYV0V2QF4X2IC0O94E8ZY", "hasFlower": 0, "hasSeason": 0}, {"id": "77", "q": "000,2,5,7,A,C.21,4,6,8,B.40,2,5,7,A,C.60,2,4,6,8,A,C.80,2,4,6,8,A,C.A0,2,4,6,8,A,C.C0,3,6,9,C.E0,3,5,7,9,C.G2,4,6,8,A;101,6,B.31,B.45,7.52,A.60,4,8,C.B0,C.D0,6,C.E3,9.G5,7;246.52,A.C0,C.D6.G5,7:EIVLCENTX9FGN3BQH99V1FNZAC1UZB2K3SXPML0MPAV23QXIT6YS0RXNG8CHYR10G6K01685956W3GWCYVYU", "hasFlower": 0, "hasSeason": 0}, {"id": "78", "q": "000,2,4,6,8,A,C,E.20,2,4,A,C,E.37.40,3,B,E.55,7,9.60,3,B,E.75,7,9.80,3,B,E.95,7,9.A0,E.B3,6,8,B.C0,E.D2,4,A,C.E0,E;100,2,5,7,9,C,E.20,E.37.40,E.54,7,A.60,E.73,6,8,B.80,E.95,9.A7.B0,E.D0,E:1L7X8WRJPHS9M70CY1CSG6Z4DHJZ7QFDPOKMXBPFUQDJDOQ7MM4WK2T9HBJ45O5HOL2T6RQU8Y022P4G", "hasFlower": 0, "hasSeason": 0}, {"id": "79", "q": "000,2,4,6,8,A,C.20,3,5,7,9,C.40,2,5,7,A,C.60,2,4,8,A,C.81,4,8,B.A2,4,6,8,A.B0,C.C2,5,7,A.D0,C.E2,4,8,A;101,4,6,8,B.23,5,7,9.45,7.60,3,9,C.81,4,8,B.A6.C5,7.D1,B.E3,9;204,6,8.35,7.60,C.84,8.B6.D1,B.E3,9:OBXKRIRQKWEO52CZBV8RVT29569OW82T8XIZFC4SPS3U7CVRBZ9VSFC4FUG3DQPQ697QBG8GZ7DO2FSEGX7AXA", "hasFlower": 0, "hasSeason": 0}, {"id": "80", "q": "001,5,7,9,D.13,B.20,6,8,E.32,4,A,C.40,6,8,E<PERSON>52,4,A,C<PERSON>60,6,8,E<PERSON>73,B.80,5,9,E.92,C.A0,4,6,8,A,E.B2,C.C0,4,6,8,A,E.D2,C.E0,4,6,8,A,E.F2,C.G0,5,7,9,E;117.32,5,9,C.55,9.60,<PERSON><PERSON>A2,C<PERSON>B0,<PERSON><PERSON>C5,9.E2,5,9,C.G7:8UQUGSBG19U2WRJLCWEHWOLTSWH29J4DPMDN1EIED7MMOFFSRC5PD5M5QCH7UBIOQ5TLRCNRLEIS2O8Q2H4I", "hasFlower": 0, "hasSeason": 0}, {"id": "81", "q": "000,3,5,7,9,C.23,6,9.30,C<PERSON>42,6,A<PERSON>50,4,8,C<PERSON>62,A<PERSON>75,7.83,9.90,5,7,C.B2,6,<PERSON><PERSON>C0,C.D3,6,9.E0,C;100,4,8,C.16.23,9.30,C.46.51,3,9,B.75,7.90,5,7,C.B6.C0,C.D3,9.E0,C;204,8.30,C.52,A.90,6,C.C0,C;330,C.90,C.C0,C;490,C:RBEW4G1HPERSQOFSLJU8KAQWVKRA70F9OXE7H83DT7ITQQ2ZXPW34VIWX2ZTUUZPXRGLZ2TB9DJ2PE0G71UG", "hasFlower": 0, "hasSeason": 0}, {"id": "82", "q": "000,2,5,7,9,C,E.20,2,4,7,A,C,E.40,2,4,6,8,A,C,E.60,3,6,8,B,E.80,2,4,7,A,C,E.A2,4,6,8,A,C.B0,E.C2,4,6,8,A,C.D0,E.E2,4,A,C;100,2,5,9,C,E.17.31,D.43,6,8,B.67.70,E.92,4,7,A,C.B3,6,8,B.C1,D.E3,B:FGRRO71D4E20J03F3BU8O86EM7RNDEPU2FOFSJP3ZSM4R1G2Z5UD1O1V6PGP73NV8UGM70EBM0J2N8SJDN5S", "hasFlower": 0, "hasSeason": 0}, {"id": "83", "q": "000,2,6,8,C,E.14,A.20,2,6,8,C,E.40,2,4,6,8,A,C,E.60,2,4,7,A,C,E.81,3,6,8,B,D.A1,3,5,7,9,B,D.C0,3,5,7,9,B,E.E0,2,5,7,9,C,E.G0,4,6,8,A,E;106,8.10,3,B,E.32,7,C.53,7,B.60,E.81,7,D.B3,7,B.D6,8.E2,C.G0,4,A,E:52IWB5C8JMBQR899GUFPQUDACNJQSA22SFUBPUS9LWCDJMGACRVM5SGMVQ77FLJID8A7I2W5NRWFR8GD7PNNVVBP9I", "hasFlower": 0, "hasSeason": 0}, {"id": "84", "q": "000,2,4,A,C,E.20,2,5,9,C,E.37.40,4,A,E.52,6,8,C.60,E<PERSON>72,5,7,9,C.90,3,6,8,B,E.B0,4,6,8,A,E.C2,C.D0,5,9,E.E3,B;100,E.21,5,9,D.37.50,2,7,C,E.72,C.90,E.A7.C0,2,5,9,C,E.E3,B;200,E.37.51,D.72,C.C2,C.E3,B:V64WJ19WO4JGY9Y1V4I7WJES6OGX1S07VG6B0LES1XOL7OYIMLJBLMF9QMQXBEF0BVF4Q6YEGQW7XF09MIIS", "hasFlower": 0, "hasSeason": 0}, {"id": "85", "q": "000,4,7,A,E.12,C.20,4,6,8,A,E.40,2,4,6,8,A,C,E.61,3,6,8,B,D.82,4,6,8,A,C.A1,3,5,7,9,B,D.C0,3,5,7,9,B,E.E0,2,4,A,C,E;100,E.13,7,B.30,5,9,E.51,6,8,D.77.A1,5,7,9,D.C0,5,9,E.D3,B.E0,E:7QYH7W7RAQI6RFNN2Y6UHSUA2HW4N8PZ1FUYY206WZUZIIPNSAEW6Z8P18HE00102DQ7GIA4PGEDEQ81", "hasFlower": 0, "hasSeason": 0}, {"id": "86", "q": "000,2,5,7,A,C.20,3,6,9,C.40,2,4,6,8,A,C.60,3,5,7,9,C.80,2,4,6,8,A,C.A0,2,4,8,A,C.C1,3,9,B.D5,7.E0,3,9,C.F6.G0,2,4,8,A,C;101,B.16.23,9.36.40,C.53,5,7,9.60,C.74,8.80,C.A0,4,8,C.C1,3,9,B.D5,7.F3,9:GJ5XYCKAK8HFN0V9SH8RGJT3J3PSGY9NX9ETU0UYU5PNJRHPCCC8FRPR90VXX3TSNVEKEVYHKASUAG308TEA", "hasFlower": 0, "hasSeason": 0}, {"id": "87", "q": "000,3,5,7,9,C.20,3,5,7,9,C.41,3,5,7,9,B.60,2,4,8,A,C.80,4,6,8,C.A0,3,5,7,9,C.C0,3,5,7,9,C.E1,3,6,9,B.G0,3,6,9,C;115,7.20,C.33,9.51,B.63,9.85,7.A3,9.E3,9.G6;215,7.20,C.63,9;320,C:N56C4MG57P0FLC59VHLTVWWPRUR6V07MYJ4439HFVGZPU8OE09QH5OY9R2JNZ6T42E06RZP3QHZ8", "hasFlower": 0, "hasSeason": 0}, {"id": "88", "q": "000,3,9,C.16.21,3,9,B.35,7.42,A.50,4,8,C.66.70,2,4,8,A,C.86.91,3,9,B.B1,5,7,B.C3,9.D5,7.E0,2,A,C.F4,6,8.G2,A;100,3,9,C.21,6,B.42,A.50,C.64,6,8.71,B.83,6,9.A1,B.B6.E0,2,5,7,A,C:IX7WOTZ2D1D2SZACCSQK8YVOTO6BJBVJ41SGTN3YAXIL3SEMK2EF2RNMX6RLGAN84FOXWQNAT7", "hasFlower": 0, "hasSeason": 0}, {"id": "89", "q": "000,2,4,7,A,C,E.21,4,6,8,A,D.40,2,4,A,C,E.57.60,4,A,<PERSON><PERSON>76,8.80,3,B,E.95,9.A0,7,E.B2,4,A,C.C0,6,8,E.D2,4,A,C.E0,6,8,E;101,7,D.14,A.21,7,D.34,A.40,E.60,7,E.80,3,B,E.A0,E.B3,7,B.D1,3,5,7,9,B,D:ZUQQA58K7GM7ZYGVMD180U1C5MA1RC0CDWKZ4J5D33R0KA74JYUWKVZR0U33D1W8JY5VXJQWAXYRM8QCV7", "hasFlower": 0, "hasSeason": 0}, {"id": "90", "q": "000,2,5,7,A,C.21,4,6,8,B.40,2,4,8,A,C.61,3,5,7,9,B.80,2,4,6,8,A,C.A0,2,4,8,A,C.C0,2,4,6,8,A,C.E0,2,4,8,A,C;100,5,7,C.31,B.43,9.63,5,7,9.71,B.85,7.90,3,9,C.B2,4,8,A.C6.D2,A.E0,C:QLMUYHSXRDS4TP1TQBZYBA6XUM2216EDYOE1QUMR4W6QBOU9NKLKRX9H9J3AW69Z0NP3R0MBXJ33Y1", "hasFlower": 0, "hasSeason": 0}, {"id": "91", "q": "000,2,4,6,8,A,C.20,2,4,8,A,C.40,2,4,8,A,C.56.61,B.75,7.80,2,A,C.96.A0,2,A,C.B4,8.C0,2,A,C.D4,8.E0,2,6,A,C;100,4,6,8,C.20,3,9,C.41,B.66.71,B.86.92,A.A0,C.C0,2,4,8,A,C.E6;200,4,8,C.20,C.66.E6:1NC8JZ210RJG2JA6WHS0ARWGDH10YPMDAYSTPCT2SCCM6WTZDZ0NSD8GHY128MRPJTYH6A8NNMGWR6ZP", "hasFlower": 0, "hasSeason": 0}, {"id": "92", "q": "000,2,4,6,8,A,C,E.21,4,A,D.36,8.40,3,B,E.56,8.60,2,4,A,C,E.76,8.80,3,B,E.95,7,9.A0,2,C,E.B4,7,A.C0,<PERSON><PERSON>D4,<PERSON><PERSON>E0,2,6,8,C,E;101,4,7,A,D.21,4,A,D.37.43,B.57.63,5,9,B.70,E.83,B.95,9.A7.C4,A.E1,D:VN9EYMO6K5OIDNM0BW2SPBB1XRPQVHNUTI39YWC3I2BUIDXHAK0M9UJEMUTA4Q2A1JDARF45DS69FNC2", "hasFlower": 0, "hasSeason": 0}, {"id": "93", "q": "001,3,5,7,9,B.21,4,8,B.36.40,2,A,<PERSON><PERSON>54,6,8.61,B.73,5,7,9.80,C.92,5,7,A.A0,C<PERSON>B4,6,8.C0,C.D2,5,7,A.F1,4,8,B.G6;102,5,7,A.21,B.42,A<PERSON>54,6,8.75,7.80,C.95,7.A0,C.B4,8.C6.D2,A.F1,<PERSON>;205,7.42,A.56.75,7.80,C.95,7.B4,8.C6.D2,A:CCAYDU9LP2360TD6SK905WGO5KOHMUK6CDYUEWTZ8TSM0WOK06MLGJL9XDO2H8JW58PXTYBQ7M58ZBQA7C9YELU3", "hasFlower": 0, "hasSeason": 0}, {"id": "94", "q": "000,2,6,8,C,E.14,A.21,6,8,D<PERSON>33,B.40,6,8,E<PERSON>53,B.60,5,9,E.80,2,5,7,9,C,E.A0,4,6,8,A,E.B2,C.C0,4,7,A,E.E0,2,4,6,8,A,C,E.G0,3,5,9,B,E;106,8.11,D.26,8.40,E.60,E.75,9.80,E.95,9.A7.B0,3,B,E.C7.D0,E.E4,6,8,A.F0,E.G3,B:6AI6FA0WMGUIXSZHMH7P0ASCJ5SD07QXD6IICUAUFGR0S6JRU1PF1Y1Z1WZPDHGW5C7JCXDHY3Q53JQ7W5X3PQFZ3G", "hasFlower": 0, "hasSeason": 0}, {"id": "95", "q": "000,2,5,7,9,C,E.20,3,5,7,9,B,E.40,2,5,7,9,C,E.60,3,5,9,B,E.77.80,2,4,A,C,E.96,8.A0,2,C,E.B5,7,9.C0,2,C,E.D4,7,A.E0,2,<PERSON>,<PERSON>;101,5,9,D.17.40,5,9,E.63,5,9,B.77.82,C.A0,6,8,E.C1,D.D7;245,9.64,A.82,C.A0,E.C1,D.D7:W2C65R9TTEZZNRMMRZXTRVDWG7V6KX9G2YDM726WKFDU9KV51DXYY157XG1NCWENF6MU5NKGEZCCETUV1FF97Y2U", "hasFlower": 0, "hasSeason": 0}, {"id": "96", "q": "000,2,4,6,8,A,C,E.20,2,C,E.35,7,9.40,3,B,E.55,7,9.61,3,B,D.75,7,9.80,2,C,E.94,6,8,A.A0,E.B3,6,8,B.C0,<PERSON><PERSON>D2,C<PERSON>E0,4,7,A,E;100,3,5,9,B,E.36,8.53,6,8,B.61,D.75,9.A0,6,8,E.E0,<PERSON>;205,9.46,8.53,B.61,D.A0,6,8,E.E0,E:CTSQYGYXCLMTLDJUZJV3OCI8AGMQXQAMBBYYUVO67S73I8AZOOSVTAI7PDDHBUMZ7J8F1CG1UIS8B6VHTPDZFJGQ", "hasFlower": 0, "hasSeason": 0}, {"id": "97", "q": "000,2,4,7,A,C,E.21,4,6,8,A,D.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.84,A.A0,2,5,7,9,C,E.C1,3,5,7,9,B,D.E2,5,9,C.G0,3,5,9,B,E;100,7,E.21,5,7,9,D.41,4,A,D.56,8.63,B.84,A.A0,5,9,E.B7.C2,C.E2,C.F5,9.G0,3,B,E:CDBSH3ZZ81LTJW4U9GLTH55SSS75T4QQI4JKLX98ZDI741I8X8KTUWWQQD3PJH5KB7P1K71IBUXWDPPLHZBGCJXU", "hasFlower": 0, "hasSeason": 0}, {"id": "98", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.42,4,6,8,A.50,C.63,5,7,9.70,C.82,5,7,A.90,C<PERSON>A2,5,7,A.B0,C.C2,4,6,8,A.E1,3,5,7,9,B.G0,2,4,6,8,A,C;102,4,6,8,A.22,4,8,A.44,8.A1,B.C5,7.E4,8.F2,A<PERSON>G4,6,8;206.12,A.24,8.E4,8.F2,A:X26E9I4ZZWLTT1N9HLFB4BMH01BUINNUBQI5U450MN646HESU6Q20SC1MLTXTCRZIWECSEWRFL20F2X1SFXCWHMZ", "hasFlower": 0, "hasSeason": 0}, {"id": "99", "q": "000,2,4,6,8,A,C,E<PERSON>23,7,B.31,5,9,D.43,7,B.51,5,9,D.67.70,3,5,9,B,E.87.90,2,4,A,C,E.A6,8.B1,4,A,<PERSON>.C6,8.D0,3,B,E<PERSON>E5,7,9.F3,B.G0,5,7,9,E;101,D.17.35,7,9.41,3,B,D.56,8.70,5,9,E.A5,9.B1,D<PERSON>C6,8.D3,B.E5,7,9.G0,7,E:E0R9I4EX7WY3YGV6N9EQ8XCYU8VWXGPP36DP0BCIIERPD036NW998QRQW580US74U3YGNNR74D5Q4SI6XBJUGJ7D", "hasFlower": 0, "hasSeason": 0}, {"id": "100", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,5,9,E.72,7,C.80,4,A,E.96,8.A0,2,4,A,C,E.B6,8.C0,2,C,E.D4,6,8,A.E0,2,C,E;102,C.16,8.20,3,B,E.43,5,7,9,B.60,E.84,A.90,6,8,E.A3,B.B6,8.C1,D.D3,B:WTMBTLCO1T20TYDYZXS51RSROA2BZEGCLA25Z8BIGWR1O4XLY04DXEMI0RC1IZB8X5CYWIS02OL8WMMJ58JSJJ", "hasFlower": 0, "hasSeason": 0}, {"id": "101", "q": "000,2,5,7,A,C.20,5,7,C.32,A.44,8.51,6,B.63,9.70,5,7,C.82,A.90,4,6,8,C.B0,2,4,6,8,A,C.D1,3,9,B.F0,5,7,C.G3,9;100,6,C.20,5,7,C.44,8.56.63,9.70,5,7,C.82,A.90,6,C.B0,2,4,8,A,C.D1,B.F0,5,7,C.G3,9:18TZTIKBIW4FDPZ0BQ3U5YH5MO71MDWH0I4PKYOFQ0WL7QHY3UPZ5IW1QD4TL3PM4UOU58BMTZOH1DB03Y", "hasFlower": 0, "hasSeason": 0}, {"id": "102", "q": "001,4,6,8,A,D.23,5,7,9,B.30,E.42,4,6,8,A,C.50,E.62,4,6,8,A,C.70,E<PERSON>82,7,C.94,A.A0,6,8,E.B2,4,A,C.C0,6,8,E<PERSON>D3,B.E0,5,7,9,E;101,4,A,D.17.30,3,6,8,B,E.51,3,6,8,B,D.72,7,C.94,A.A6,8.B4,A.C0,6,8,E.E7:C9CYD21BYA3X5MM9A454132ARB2VYTXE6KNS5MACLUUEULJTTYLK5BDVNBSKLVRTED36V6U3M6RDCSERSKJ2", "hasFlower": 0, "hasSeason": 0}, {"id": "103", "q": "000,3,5,7,9,C.20,2,4,6,8,A,C.41,3,6,9,B.60,3,6,9,C.80,2,5,7,A,C.A0,4,8,C.B6.C1,3,9,B.D6.E0,3,9,C.F5,7.G0,2,A,C;100,3,5,7,9,C.20,2,6,A,C.41,3,9,B.63,9.70,6,C.82,A.90,C.A4,8.C1,B.D3,9.E0,C.F6.G0,2,A,C:SNJF1GZGGJZOB6FJE3VEU8AOG5KNLPYLN8KEL16P2FYA9VHP2CUH932X3ZV2VUEB36KLN6S1ZP5OUKOWF1CXWJ", "hasFlower": 0, "hasSeason": 0}, {"id": "104", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,3,5,7,9,C.60,2,6,A,C.82,6,A.90,C.A2,4,6,8,A.B0,C.C2,5,7,A.D0,C.E3,5,7,9;101,5,7,B.20,3,9,C.35,7.56.61,B.76.82,A.A1,4,6,8,B.C1,5,7,B;201,B.20,C.56.A1,6,B;320,C.56.A6:1RZ5UB9GLW4EJUNJMKTZ392L00ND1G2LEM4KC3B40FTWQM5EMB3C1GEZW4KCWBKRRFZL03N5NFFGD5R99Q1C", "hasFlower": 0, "hasSeason": 0}, {"id": "105", "q": "000,3,5,7,9,B,E.20,2,5,7,9,C,E.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.80,2,4,7,A,C,E.A0,3,B,E.B5,7,9.C0,2,C,E.D4,6,8,A.E1,D.F3,5,7,9,B.G1,D;104,6,8,A.27.30,5,9,E.56,8.61,D.80,3,B,E.B5,9.C7.D5,9.F7.G1,D:WT3QQJACQGXF3MUHWM70C7C2P04VHGNY1HMW16L4D5A4XPF253WH53R6LQIT7M964DVCV9V2T7YJ5R2Z6ZTNUI", "hasFlower": 0, "hasSeason": 0}, {"id": "106", "q": "000,3,9,C.16.20,2,4,8,A,C.40,2,4,6,8,A,C.61,5,7,B.80,2,6,A,C.A1,5,7,B.C0,3,5,7,9,C.E0,2,4,8,A,C.G1,3,6,9,B;100,C.13,6,9.31,4,8,B.46.76.80,2,A,C.A6.C0,3,5,7,9,C.E1,4,8,B.G3,6,9;213,9.34,8.82,A.B6.E4,8.G3,9:6RK97RXKFQQ9DLX1FKUZF85TG9OUEKQTMEGLMDXVCQF1E8OG1EY77JCDDL769NRZUW8RNUSZSYLP5SVPZSG8JWX1", "hasFlower": 0, "hasSeason": 0}, {"id": "107", "q": "001,4,6,8,A,D.20,2,6,8,C,E.34,A.40,2,C,E.61,3,5,7,9,B,D.80,3,5,7,9,B,E.A0,2,4,7,A,C,E.C1,D.D3,5,9,B.E0,7,E<PERSON>F2,<PERSON><PERSON>G4,7,A;101,4,6,8,A,D.21,7,D.33,B.40,E<PERSON>52,C.65,9.73,B.85,7,9.93,B.A0,E<PERSON>C1,D.D5,9.E0,E.F2,7,C.G4,A:CTJRMDASFJDPIX51KOTVOLXOABIFJ55RHPLB8VPDDM8W2NAFMV1MHP2AVIC5W66BK38CSG8BN6W6WIFSR3QGRCJQOS", "hasFlower": 0, "hasSeason": 0}, {"id": "108", "q": "000,4,6,8,C.21,3,5,7,9,B.40,2,4,6,8,A,C.60,2,6,A,C.80,2,4,6,8,A,C.A0,2,5,7,A,C.C1,3,5,7,9,B.E2,4,6,8,A.G0,3,5,7,9,C;100,4,8,C.16.23,9.40,3,5,7,9,C.60,C.72,A.84,6,8.91,B.B5,7.C2,A<PERSON>D4,8.E2,A.F4,8.G6:419Z0C14GCF5IJ33ZVDYVQ9AD0EHAN2JQWFIG9YR4LCY3VW3GMH7AICLU5MD7E51WUAH5G1967NW6HD2R7IYV4", "hasFlower": 0, "hasSeason": 0}, {"id": "109", "q": "001,3,5,7,9,B,D.20,2,5,7,9,C,E.40,4,7,A,E.52,C.60,4,A,E.76,8.80,2,4,A,C,E.97.A0,3,B,E.B5,9.C0,2,C,E.D4,7,A.E0,2,C,E.F5,7,9.G0,2,C,E;105,7,9.12,C.25,9.30,E.44,A.84,7,A.B5,9.D0,4,A,E.F1,5,9,D;205,9.44,A.D0,4,A,E:LV6VY1499LOCQWJQZ6Z7AHI1F29O33BJMCLMVJ1I75356J31OISCHFY05QIBS02C2WTGWQA7LWVOG7ZZG5Y26Y4GT9", "hasFlower": 0, "hasSeason": 0}, {"id": "110", "q": "000,2,4,7,A,C,E.20,3,5,9,B,E.37.42,5,9,C.57.60,2,4,A,C,E.77.80,2,5,9,C,E.97.A1,3,5,9,B,D.C0,2,4,7,A,C,E.E2,4,6,8,A,C;104,A.20,5,9,E.45,9.60,2,7,C,E.80,6,8,E.92,C.A4,A.C1,D.D7.E3,B;204,A.20,E.61,D.80,6,8,E.92,C:W99R1ETNDG858YX77HX9ADE65SZQZREBBZY18B7D4SA14W7UTGUYNMV4N4HGQE0VHOPGZD1NP6PX0HPKX8BKVYM9VO", "hasFlower": 0, "hasSeason": 0}, {"id": "111", "q": "000,2,4,8,A,C.20,3,5,7,9,C.40,3,5,7,9,C.61,3,5,7,9,B.80,2,4,6,8,A,C.A1,4,6,8,B.C0,3,6,9,C.E0,2,5,7,A,C.G0,4,8,C;100,2,4,8,A,C.20,3,5,7,9,C.43,9.56.61,B.75,7.81,B.94,8.A6.C0,6,C.E1,5,7,B.G0,4,8,C:RZSGNC8R6X0DP2JORIIAII044UTSE65GTXX7188BJDJAWQ5W8US14617BPZNQMFJOWXH16F42REVMTSVWETECH", "hasFlower": 0, "hasSeason": 0}, {"id": "112", "q": "001,3,5,7,9,B,D.20,3,5,7,9,B,E.40,4,7,A,E.60,2,5,7,9,C,E.80,2,4,6,8,A,C,E.A1,3,5,7,9,B,D.C0,2,4,6,8,A,C,E.E0,3,7,B,E;101,5,9,D.13,B.34,A.40,7,E.61,5,9,D.82,C.94,A.A1,D.B4,7,A.E3,B;240,7,E.61,D.A4,A.B7:MAEOUIK2JO728HW90K07MY6WEPZ0NODJ7DAZ8HKOCWY91WJ1DSNNFQMF0UK7SHYLZSHMLPUZEJENUSQPI6YPC22D", "hasFlower": 0, "hasSeason": 0}, {"id": "113", "q": "004,6,8,A.12,C<PERSON>20,5,7,9,E<PERSON>32,<PERSON><PERSON>44,6,8,A<PERSON>51,D<PERSON>63,6,8,B.70,<PERSON><PERSON>82,C.94,6,8,A.A1,D.B3,5,9,B.C7.D0,3,5,9,B,E;106,8.12,C.20,6,8,E.44,6,8,A.51,D.63,7,B.70,E.97.A1,5,9,D.C5,9.D0,3,<PERSON>,<PERSON>;206,8.26,8.45,9.51,D.63,7,B.97.A1,5,9,D.C5,9:PEXVRMLWHHQDLYRDJOPJW2YPAXBEO3FEF3MWLOL91MB1FA92O291R14FVAHCC5PW2J5YI56DY539DMVHA6XRJE4Q3XVI", "hasFlower": 0, "hasSeason": 0}, {"id": "114", "q": "000,2,4,6,8,A,C,E.20,3,5,7,9,B,E.40,3,5,7,9,B,E.60,2,5,7,9,C,E.84,7,A.A2,4,7,A,C.B0,E.C2,4,6,8,A,C.D0,E.E4,6,8,A.F0,E.G2,4,A,C;100,2,7,C,E.14,A.26,8.43,5,7,9,B.61,5,7,9,<PERSON><PERSON>A2,4,A,C.B7.C1,3,B,D.D5,7,9.G4,A:TH5YEG9OQ0GLSYHQ6B96LTW573DW4AYIR9L0XTNRWSW46CNB7NEBPNJIPKCIJY2B02ZEJO57A57IZLK9X36J0XXTED", "hasFlower": 0, "hasSeason": 0}, {"id": "115", "q": "000,2,4,6,8,A,C.20,3,5,7,9,C.40,3,5,7,9,C.63,6,9.80,3,5,7,9,C.A3,5,7,9.C0,2,4,6,8,A,C.E2,4,8,A.F0,6,C.G2,A;102,4,8,A.16.24,8.40,3,6,9,C.80,3,5,7,9,C.A4,8.C0,C.D2,4,8,A;215,7.40,3,9,C.83,9.D3,9:0S6B4U07Y5213FA1NF2VG6VXTTFB48GZQCYXK8AI9V5HZY7HJIC83JKOSCX6FO69UKXVSNHQROCOURH8YKUS", "hasFlower": 0, "hasSeason": 0}, {"id": "116", "q": "004,6,8.10,C.22,5,7,A.30,C<PERSON>43,5,7,9.51,B.64,6,8.70,C.82,4,6,8,A.A0,3,5,7,9,C.C1,5,7,B.D3,9.E0,5,7,C<PERSON>F2,A.G0,5,7,C;105,7.10,C.26.30,C.43,9.64,6,8.85,7.A3,5,7,9.D3,9.E0,C.F6.G0,<PERSON>;205,7.10,C.65,7.85,7.A3,9.G0,C;385,7.A3,9:B81W2TPBQ6STFDUXR5H7F7FVX5GWXUFNGDBCGHX835HCGA7A9WCVWNAERUDS5ARC366NJDVN6BV7HQ1R3P2JT3TEU9", "hasFlower": 0, "hasSeason": 0}, {"id": "117", "q": "001,6,8,D.13,B.20,6,8,E<PERSON>34,A<PERSON>40,6,8,E<PERSON>52,C<PERSON>64,6,8,<PERSON><PERSON>71,D.83,5,9,B.90,<PERSON><PERSON>A2,4,7,A,C.C0,2,5,7,9,C,E.E0,4,6,8,A,<PERSON><PERSON>F2,C<PERSON>G0,6,8,E;117.34,6,8,A.56,8.74,A<PERSON>A4,<PERSON><PERSON>C0,6,8,E<PERSON>E4,6,8,A.G7;227.47.74,A.A4,A<PERSON>C6,8.E6,8:I8T3BEJN6EKTMJ3OG6937INXAJ13PWZ7CRMLORVDRAACV5QBDH5XPRLN9ANK6144TO8B68HGOSSTWZVQVJ8B", "hasFlower": 0, "hasSeason": 0}, {"id": "118", "q": "000,3,5,7,9,C.20,3,5,7,9,C.40,2,4,6,8,A,C.64,6,8.72,A.80,4,8,C.92,A.A4,6,8.C2,4,6,8,A<PERSON>D0,C.E2,4,6,8,A<PERSON>F0,C.G2,4,8,A;103,5,7,9.25,7.30,3,9,C.65,7.82,4,8,A.B5,7.D1,B.E3,5,7,9.F1,B;203,5,7,9.25,7.66.B6.D1,B.E6:B8M00BSNGQXC0EY7IIZGXLQMD1H549BNLPJASMJ48JYQAKE68UWNF57T64APPJT10IEHEDFN5CIQP5UBZAWK8M94", "hasFlower": 0, "hasSeason": 0}, {"id": "119", "q": "000,2,4,7,A,C,E.20,2,5,7,9,C,E.40,3,5,7,9,B,E.61,3,5,7,9,B,D.81,5,7,9,D.93,B.A0,7,E.B3,5,9,B.C0,E.D2,5,7,9,C.E0,E;103,B.11,D.25,9.40,5,7,9,E.62,5,7,9,C.85,9.97.B0,5,9,E.D2,5,9,C;225,9.45,9.62,C.B5,9.D2,5,9,C:OH19XMFNTCCKWOKMN08C04YO8NHEY90NTPZ1EW91WSGT4ZS4X0JJX3KM6GTKHAVS9C4SWPOZYJVXZ8MJH3FYA1F6F8", "hasFlower": 0, "hasSeason": 0}, {"id": "120", "q": "000,3,5,7,9,B,E.25,9.30,7,E.42,4,A,C.50,6,8,E<PERSON>62,4,A,<PERSON><PERSON>70,7,E.83,B.90,5,7,9,E.A2,C.B0,4,6,8,A,E.D0,2,4,A,C,E.E6,8.F0,2,4,A,C,E.G7.H5,9.I2,7,C;105,9.30,7,E.42,4,A,C.50,E.77.83,B.97.A2,C.B5,9.E0,3,5,9,B,E.G7.I2,C:IJ9A44DF2FJW5ZVUAHX7O9XZ5MA0OOYWKUI91GGUFDH17ZDJJYGHFWX14UV0OMYXKWMGDI9VA5MH1Y0V024IZ52772", "hasFlower": 0, "hasSeason": 0}]