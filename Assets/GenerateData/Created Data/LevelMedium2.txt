[{"id": "1", "q": "003,7.10,A.24,6.30,A.42,5,8.50,A.62,5,8.70,A.82,8.90,5,A.B2,5,8.C0,A<PERSON>D4,6.E0,A;103,7.20,4,6,A.41,5,9.60,A.72,8.90,A.B2,5,8.C0,A<PERSON>D4,6;245.B5.C0,A;345.B5:3P63H0JCK5TJO565U0KAHKCJPEJN0ETX5OOTXMVPWHTAD0KPNOU1MEW1VDEH", "hasFlower": 0, "hasSeason": 0}, {"id": "2", "q": "001,3,5,7,9.20,3,5,7,A.40,3,7,A.55.61,3,7,9.75.80,2,8,A.94,6.A0,A<PERSON>B3,7.C0,5,A.D3,7.E0,A;103,7.15.20,A.33,7.63,5,7.85.B3,7.D0,A;263,7.B3,7;3B3,7:8TDH4U6GH8HGN34Q3O7FAUDIAQ40TNU6486H6OJG38GJIUA0FDTQT37DQA", "hasFlower": 0, "hasSeason": 0}, {"id": "3", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,3,6,9,C.60,2,4,8,A,C.80,3,5,7,9,C.A0,2,4,6,8,A,C.C0,2,4,6,8,A,C;102,5,7,A.10,C.33,9.40,6,C.85,7.90,C.A3,9.B0,C:I6SV7F8Q402PBFOC9UTOGMRYOJJIHNUVB2958K3YQOR5TAK3WSHW640CPGLN7AML", "hasFlower": 0, "hasSeason": 0}, {"id": "4", "q": "000,3,5,7,9,C.21,3,5,7,9,B.40,3,5,7,9,C.61,3,6,9,B.80,2,4,6,8,A,C.A0,2,4,6,8,A,C.C0,2,4,6,8,A,C;104,8.22,5,7,A.84,8.91,B.B2,5,7,A:6PSEWXEFVAX0NX0EILUN135UFGLY2L6IGAGQS2VQNEYU1L3U0N0XPWBGB5", "hasFlower": 0, "hasSeason": 0}, {"id": "5", "q": "000,2,4,6,8,A,C.23,5,7,9.30,C.42,4,8,A.50,C.62,4,6,8,A.70,C.83,5,7,9.A0,2,4,6,8,A,C.C1,5,7,B;100,C.13,6,9.30,3,9,C.52,A.65,7.73,9.A0,3,6,9,C.C6:P5HDQCN3UJ4G965NJU9A2I21C6XFHQ4A0HUT81D3L37LC8KHU0GKFIX37QPTQC", "hasFlower": 0, "hasSeason": 0}, {"id": "6", "q": "000,2,4,6,8,A.20,3,7,A.35.40,3,7,A.55.60,3,7,A.75.80,2,8,A.94,6.A0,2,8,A.B5.C0,2,8,A.D4,6.E1,9;105.10,3,7,A.30,4,6,A.53,7.60,A.80,A.93,7.A1,9.B5.C0,A.D4,6:TRJCL3LWAC0GVIDYKESJAJIBIVDUQQ00IBVJYTFYZAESKR3ZWEVFRSEAQGURSQ0Y", "hasFlower": 0, "hasSeason": 0}, {"id": "7", "q": "000,2,4,6,8,A,C.20,3,5,7,9,C.40,2,4,6,8,A,C.60,3,5,7,9,C.80,2,4,6,8,A,C.A0,2,4,6,8,A,C.C1,3,5,7,9,B;105,7.20,C.44,8.65,7.85,7.A2,A.C2,A:IAH2W2QT5EYMOIEOH5VABEFH2BVT5TFITWEHMWF2VIQWMAOOFYBQ5ABVYQYM", "hasFlower": 0, "hasSeason": 0}, {"id": "8", "q": "000,3,6,9,C.20,3,5,7,9,C.40,2,4,6,8,A,C.60,2,4,6,8,A,C.82,4,6,8,A.90,C.A2,4,6,8,A.B0,C.C2,4,6,8,A;110,C.24,8.42,A.50,C.65,7.82,A.B4,8;210,C.82,A:I8SPIWYSV3UUCYTWS1MCP13MK8DDD1RDTTPW6BIIGR6SPR136VKWCBCRMG6T3M", "hasFlower": 0, "hasSeason": 0}, {"id": "9", "q": "001,3,5,7,9.20,3,7,A.41,3,7,9.55.60,2,8,A.74,6.80,2,8,A.94,6.A1,9.B3,7.C0,<PERSON><PERSON>D3,7.E0,5,A;103,5,7.41,3,7,9.55.60,2,8,A.74,6.82,8.95.A1,9.B3,7:W6YOYQOJGJM1C7HMZ5KRZRJXCUO2JGB2545B71G4OHKWT03UXT3GFQ6F50", "hasFlower": 0, "hasSeason": 0}, {"id": "10", "q": "000,2,5,7,A,C.20,2,4,6,8,A,C.40,2,5,7,A,C.60,4,6,8,C.80,2,4,6,8,A,C.A0,2,4,8,A,C.B6.C1,4,8,B;105,7.22,6,A.30,C.42,A.60,4,6,8,C.91,B.A4,8.B6;216.A4,8:6SMJH8WI24I4P9AOVG08LBXJOZHST3NS096C2MPTGDALC5UQWKSKDFQNVF35ZBUX", "hasFlower": 0, "hasSeason": 0}, {"id": "11", "q": "000,2,6,A,C.22,4,6,8,A.40,2,4,6,8,A,C.60,2,5,7,A,C.80,2,A,C.95,7.A0,2,A,C.B4,6,8.C1,B.D3,6,9.E0,C;100,6,C.24,6,8.42,5,7,A.66.71,B.96.A1,B.B5,7.C1,B.D6:SX6KYX7C3F3F9EE1SK3ZS1FV16HQ2927KH1VZAK5YYESAF7YV79QA9EC53WWAV66", "hasFlower": 0, "hasSeason": 0}, {"id": "12", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.80,2,4,7,A,C,E.A0,2,5,7,9,C,E.C0,2,4,6,8,A,C,E.E1,3,5,9,B,D;106,8.26,8.30,E.70,E.C0,E:EW43Q362RN2F46DE4IRCLDDEIIQ40CXFX2Q2WX6ZRIRZ0QFN3ZCWEL6FD00PNXZCW3LPLN", "hasFlower": 0, "hasSeason": 0}, {"id": "13", "q": "000,2,4,6,8,A,C.23,5,7,9.30,C.42,5,7,A.50,C.64,8.70,C.82,4,6,8,A.A0,3,5,7,9,C.C0,2,4,8,A,C;100,C.13,6,9.35,7.40,2,A,C.60,C.83,6,9.A4,8.C1,3,9,B;235,7.83,9:FVOTZHVERYK1NSRCR7YKH5OO57TICV7TCFZSRNM1YNFKCTVKY5NI7S1SMM5ZZEMO1F", "hasFlower": 0, "hasSeason": 0}, {"id": "14", "q": "001,3,5,7,9.22,4,6,8.30,A.43,5,7.50,A.62,5,8.70,A.82,4,6,8.90,A.A2,5,8.B0,A<PERSON>C4,6.D2,8.E0,4,6,A;104,6.12,8.34,6.60,5,A.90,5,A<PERSON>C4,6;204,6.60,5,A.90,5,A;304,6:XEELGX0AQG8X8QFA7XFLDGI44E9BAVB49F7LQ7OGYDYF00LDNOE8A08I4NQ7OODV", "hasFlower": 0, "hasSeason": 0}, {"id": "15", "q": "000,2,4,6,8,A,C.22,4,6,8,A.42,4,6,8,A.50,C.63,5,7,9.70,C.83,6,9.A1,3,5,7,9,B.C0,2,5,7,A,C;100,3,9,C.25,7.33,9.65,7.A3,9.B5,7;200,C.33,9.A3,9:6M6BL35KY58170ALIHLYSKUV64PPI153BF2D82U5KHVSFMSDL6KDDAQQ470S", "hasFlower": 0, "hasSeason": 0}, {"id": "16", "q": "000,2,4,6,8,A,C.22,4,6,8,A.30,C.42,5,7,A.50,C.62,4,6,8,A.70,C.82,5,7,A.A0,3,5,7,9,C.C0,2,4,8,A,C;100,5,7,C.23,6,9.40,C.52,A.82,A.A4,8.C1,4,8,B:Z5QBYUP6AHF62U852QNLHLN85058A2PUPBRHZYLZF7PN72HRLZRRUF08YYQQNF", "hasFlower": 0, "hasSeason": 0}, {"id": "17", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.81,3,5,7,9,B,D.A0,2,4,6,8,A,C,E.C0,4,6,8,A,E.D2,C.E0,4,6,8,A,E.F2,C.G0,4,7,A,E:1VR26A3111MMTOMUAUGO2B5KJTFKWULFB2QYBTI55G9J3VHJIT6FNQ9BYJU5HLMN2RFW", "hasFlower": 0, "hasSeason": 0}, {"id": "18", "q": "001,3,5,7,9.21,5,9.33,7.40,A.53,7.60,5,A.72,8.80,A.93,5,7.A1,9.B3,7.C1,5,9.E2,4,6,8;105.11,9.25.40,A.53,7.60,A.80,A.93,7.A1,9.C5.E2,8;211,9.60,A.E2,8:VN0AE3C3GTEG8ANTVY82RP4QWNQSZ13A4AWZPP118R8CS306QP1RR26VVQNY", "hasFlower": 0, "hasSeason": 0}, {"id": "19", "q": "000,4,6,A.12,8.20,4,6,A.32,8.45.50,2,8,A.71,5,9.91,9.A4,6.B1,9.C5.D0,A<PERSON>E2,4,6,8;100,4,6,A.21,3,5,7,9.42,5,8.50,A.75.A1,4,6,9.C5.E3,5,7;205.21,4,6,9.42,5,8.A1,4,6,9.E4,6;3A1,9:96YUT1ZD7GO5OBW73Y6SVLW2KR0GKDC9NRF8KVU3ZTPD1OZA9B9QE0L2FSEONPKADQ5CZ855", "hasFlower": 0, "hasSeason": 0}, {"id": "20", "q": "000,5,7,9,E.12,C.24,6,8,A.30,2,C,E.44,7,A.50,2,C,E.65,7,9.70,2,C,E.87.90,3,5,9,B,E.B0,3,5,7,9,B,E.D1,3,6,8,B,D;106,8.12,C.30,E.43,B.51,D.72,7,C.94,A.B0,3,B,E;206,8.B0,E:3I9SO4FGXGA4HAXGWY4164IKKX6Y10A35P39XYNKHWEW5I5WK9E11PA5O7PP0NGNN7Y9ISF3", "hasFlower": 0, "hasSeason": 0}, {"id": "21", "q": "000,3,5,7,9,C.20,3,5,7,9,C.40,3,5,7,9,C.60,2,4,6,8,A,C.80,2,4,8,A,C.96.A0,2,A,C.B4,6,8.C0,2,A,C.D4,6,8.E1,B;103,5,7,9.10,C.23,9.35,7.55,7.61,B.74,8.82,A.B1,5,7,B:HKN2US1L7NTDHBZ88XDAVVOVNV27X3KWUT7SW1YUCHHT7ZJ3TYLFUOONQCJC2AORCRBF2Q", "hasFlower": 0, "hasSeason": 0}, {"id": "22", "q": "000,4,6,A.12,8.24,6.30,2,8,A.45.50,2,8,A.70,2,4,6,8,A.90,2,8,A.A4,6.B0,A<PERSON>C2,5,8.E1,3,5,7,9;105.12,8.24,6.30,A.45.51,9.70,2,4,6,8,A.90,2,8,A.A4,6.D2,8.E4,6;235.70,2,8,A.90,2,8,A.D2,8:PI65X2CIKFOX8OFJHTIT52DV5CQZX0EFOJZE8PQXCAP40Z829VWVK0IWP6H42AEFO6V8ZEC6509D", "hasFlower": 0, "hasSeason": 0}, {"id": "23", "q": "002,4,6,8.10,A.22,5,8.41,3,7,9.60,2,4,6,8,A.80,2,4,6,8,A.A0,3,7,A.C1,9.D3,5,7.E0,A;102,8.10,5,A.42,8.60,2,8,A.75.90,3,7,A.D3,7.E0,A;202,8.15.42,8.60,A.75.90,A;342,8:CLBQQRZJLDGKJ28YG3OZ8CQJR44KDQKUOBX2WRM4BWYYMZ9ZU2OUX9O2FUKB3EFJYRE4", "hasFlower": 0, "hasSeason": 0}, {"id": "24", "q": "000,2,6,A,C<PERSON>21,3,9,B.35,7.43,9.50,6,C<PERSON>63,9.70,5,7,C<PERSON>82,A<PERSON>95,7.A0,2,A,C.C0,2,4,8,A,C.E1,3,6,9,B;101,B.21,B.33,6,9.50,3,9,C.66.70,C.82,5,7,A.A0,C.B2,A<PERSON>C0,4,8,C.E3,6,9:UXALA6KY3NODFPSCDC3TJKDNFXH6YGZMJ8XX01HL8HFIZA8U0KIMDHPT8A1STWTOGKWFMM", "hasFlower": 0, "hasSeason": 0}, {"id": "25", "q": "004,6,8.10,2,A,C.24,6,8.30,2,A,C<PERSON>44,6,8.50,2,A,C<PERSON>64,8.70,2,6,A,C.84,8.90,C.A2,4,6,8,A.B0,C.C2,5,7,A.D0,<PERSON><PERSON>E2,4,6,8,A;105,7.22,5,7,A.45,7.64,8.71,B.94,8.B5,7.D2,5,7,A:LLVE2ZR4EZH7ZOVU6ZGHGO42KQ1161SIE7INUHW0UOSWRORI7EW0NW1G4RKKQNNI74UGHK", "hasFlower": 0, "hasSeason": 0}, {"id": "26", "q": "000,3,5,7,9,C.22,5,7,A.30,C<PERSON>43,5,7,9.50,C<PERSON>64,8.71,B<PERSON>85,7.91,B.A3,5,7,9.B0,C<PERSON>C2,5,7,A<PERSON>E0,3,5,7,9,C;103,5,7,9.22,A.35,7.50,C.64,8.86.91,B.A3,6,9.C2,5,7,A.E3,9;236.50,C.A3,9.C6:RY9NITZG1CS7VX1GK3XU30INNSLLDUBDKRTAPV6SS16Z9JA710NL6G6L7RJQGZRPOB7OCQYZ", "hasFlower": 0, "hasSeason": 0}, {"id": "27", "q": "000,2,4,6,8,A,C.20,2,4,8,A,C.36.40,2,A,C.54,6,8.60,C.72,5,7,A.80,C.92,4,6,8,A.A0,C.B3,5,7,9.C0,C.D2,5,7,A<PERSON>F0,2,4,8,A,C.G6;101,3,9,B.54,8.66.70,C.86.91,B.A5,7.C5,7.G6:PL7MUS0W4U3NMWPS6C1U3Y628O72DJNPC11MN4627J89P6M2GAGLLAQ9ZUNYDL1OZ979Q0", "hasFlower": 0, "hasSeason": 0}, {"id": "28", "q": "000,2,5,7,A,C.20,2,5,7,A,C.41,3,6,9,B.60,2,5,7,A,C.83,5,7,9.90,C.A2,4,6,8,A.C0,2,5,7,A,C.E0,2,4,8,A,C;102,5,7,A.20,2,A,C.42,A.66.83,9.96.B2,A.C5,7.D0,2,A,C:5IUEARRK9GNGW8W4JSEWHR41J39ZYBN81D43PYHZ83GB22GISJPSJADSR4P2P5U3WK28", "hasFlower": 0, "hasSeason": 0}, {"id": "29", "q": "000,2,5,7,A,C.21,4,6,8,B.40,2,4,8,A,C.56.60,2,4,8,A,C.81,3,5,7,9,B.A0,2,5,7,A,C.C0,2,5,7,A,C.E0,2,4,6,8,A,C.G0,3,5,7,9,C;100,5,7,C.24,8.55,7.71,B.84,8.A1,B.C5,7.E2,6,A.F4,8:O1WGQI6RBL9KL1HL29MAHFG3XKLTRWF1PPHBVFMH3UOOR92G62IIK3OXT92AR1XW3IGVKFWXQQQU", "hasFlower": 0, "hasSeason": 0}, {"id": "30", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,3,5,7,9,C.60,4,8,C.76.80,3,9,C.A0,3,9,C.B5,7.D0,2,4,6,8,A,C;101,4,6,8,B.22,6,A.30,4,8,C.46.50,4,8,C.70,6,C.90,3,9,C.B5,7.D2,6,A;206.35,7.54,8.76.B6:GJBP6ZHG9GUI49UZO2OQFK241YOU38KJH8E6IAE86BH0Q4G4ZZ62FTH8KOA1AA9JEJ903TUP2EYPPK", "hasFlower": 0, "hasSeason": 0}, {"id": "31", "q": "002,4,6,8,A,C.20,4,6,8,A,<PERSON><PERSON>32,<PERSON><PERSON>44,7,A<PERSON>51,<PERSON><PERSON>65,7,9.70,2,C,E.85,7,9.90,2,C,E.A4,6,8,A.B2,<PERSON><PERSON>C4,6,8,A.D1,<PERSON><PERSON>E3,5,7,9,B.F0,E.G2,4,A,C;103,B.25,7,9.32,C.57.61,D.75,9.87.95,9.B4,7,A.D6,8.E4,A.G4,A:JXNKYSX1YC1017RCT47JYTXFSER3C1MTQIFISNUYMUQSJ7EJN03M7T4Q0U44KREMFCN0QFIU3KKE3RXI", "hasFlower": 0, "hasSeason": 0}, {"id": "32", "q": "000,2,5,7,A,C.22,4,6,8,A.30,C.42,5,7,A.60,2,4,6,8,A,C.80,2,5,7,A,C.A1,3,6,9,B.C0,3,5,7,9,C.E0,2,4,6,8,A,C;100,C.12,A.24,8.42,6,A.61,4,8,B.91,B.A3,9.B6.D4,8.E2,A:EQEEC9SERFGL8SR5QKMMDWBL3QRRMWXZ81X7PPD66MCDCK3BUZ10GC7US3SZLFQ503LZ9D", "hasFlower": 0, "hasSeason": 0}, {"id": "33", "q": "001,3,5,7,9,B.23,5,7,9.31,B.43,9.50,6,C.63,9.70,5,7,C<PERSON>82,A.94,6,8.A0,C<PERSON>B3,5,7,9.C0,C<PERSON>D2,4,8,A.F3,5,7,9;103,6,9.23,5,7,9.43,9.56.60,3,9,C.75,7.82,A.94,8.A0,C.B3,5,7,9.D3,9.F3,5,7,9;263,9.82,A.B3,9;382,A:MDMBEEYQM8RBA3R13QO9AJFATHYYJ59Y86WW7HTQVOA9O85TFW3HBJ6JT9NFD6DW7BRVR6HN173DOM8EF7EQ", "hasFlower": 0, "hasSeason": 0}, {"id": "34", "q": "000,3,5,7,9,C.20,2,5,7,A,C.40,2,4,8,A,C.61,3,5,7,9,B.80,3,6,9,C.A0,2,4,8,A,C.C0,3,5,7,9,C.E0,2,4,6,8,A,C;105,7.21,5,7,B.51,4,8,B.A0,4,8,C.C0,3,5,7,9,C.E1,3,9,B;206.A0,C.C5,7.E1,B;3C6:FNEKJ3F3BKOK8BD90H8BFB93L5THQLF958E8T7N3PU5J7YSJO9QLCJ0QH5C70STCCLUSQ0KHTNNPS7YD", "hasFlower": 0, "hasSeason": 0}, {"id": "35", "q": "000,2,4,6,8,A,C.23,5,7,9.31,B.44,6,8.50,2,A,<PERSON><PERSON>65,7.71,B.85,7.93,9.A0,C<PERSON>B4,6,8.C1,B.D3,5,7,9.E1,B;100,5,7,C.34,6,8.41,B.55,7.61,B.75,7.93,9.A0,C<PERSON>B5,7.E1,B;205,7.36.41,B.55,7;306:WJQGMWMHQ1YZP1B1JSQS7MR71SWJSGAY3RGE8KKP38E3QKEHCG7EAHZ3MJWRBYRCKHYP788P", "hasFlower": 0, "hasSeason": 0}, {"id": "36", "q": "001,3,5,7,9,B.20,2,4,6,8,A,C.40,5,7,C.52,A.60,4,6,8,C.80,2,5,7,A,C.A0,3,5,7,9,C.C0,3,5,7,9,C;102,4,8,A.20,2,4,8,A,C.36.40,C.55,7.60,C.75,7.80,C.96.A0,C.B3,9.C0,C;213,9.30,C.55,7.75,7.A0,C:B3TRBKZO0D563LCN136BP36Y0XY74AA8PAZ1CT8WO6DKAWOHY4O0WK8WZ7RYU847Z7KHUN1L1T4B5X0T", "hasFlower": 0, "hasSeason": 0}, {"id": "37", "q": "000,2,4,8,A,C.16.20,2,A,C.34,6,8.41,B.55,7.60,2,A,C.75,7.80,3,9,C.A2,5,7,A.B0,C.C2,5,7,A.E0,2,4,6,8,A,C.G0,2,4,6,8,A,C;102,A.16.22,A.45,7.60,C.76.A6.B0,C.D5,7.F2,A.G6:MWE7B5O1SOP9MWES53A9TPCC3AH1HEACMB35BSAW25HTTI79BP2MYH7ITC9OWSXYEP3XO7", "hasFlower": 0, "hasSeason": 0}, {"id": "38", "q": "001,3,5,7,9,B.20,2,5,7,A,C.40,2,4,6,8,A,C.60,2,5,7,A,C.80,2,5,7,A,C.A0,2,4,6,8,A,C.C0,2,4,8,A,C.D6.E0,3,9,C;101,B.16.32,5,7,A.50,6,C.62,A.91,B.A4,6,8.B1,B.D3,9:B5QV5KNAJ9AN7W25UEP9VD1J6Y38AK7C1IDDC998UY2DBYQ53E6JP464IJBAIIWKB446YK", "hasFlower": 0, "hasSeason": 0}, {"id": "39", "q": "000,2,4,6,8,A.20,2,4,6,8,A.40,2,4,6,8,A.60,2,4,6,8,A.80,2,5,8,A.A0,3,7,A.C1,5,9.D3,7.E0,5,A;102,4,6,8.10,A.25.33,7.51,9.63,5,7.70,A.82,8.90,A.D5.E0,A;203,7.64,6.70,A:L282GH8WJ4VSVIQCSWE76AKUOL097ACTUMOQ1GB9I4CZE0BWPHNV6IWIMZNTTP55CVJ1TK", "hasFlower": 0, "hasSeason": 0}, {"id": "40", "q": "000,2,4,6,8,A,C,E.21,3,5,7,9,B,D.42,4,6,8,A,C.50,E.62,4,6,8,A,C.70,E.82,4,6,8,A,C.A0,2,7,C,E.B4,A.C0,2,7,C,E.D4,A.E2,6,8,C;100,E.22,C.42,5,9,C.57.62,C.70,7,E.92,C.A7.B2,<PERSON>.C0,E.D2,C:PRJJXTUV8XZBXBZ608N60UVGW8YPDVLRIZKNPUITR81NGWDPDI01BL1V6GG6TYX0UBNDJJZKY1YTIR", "hasFlower": 0, "hasSeason": 0}, {"id": "41", "q": "000,3,5,7,9,B,E.20,2,4,7,A,C,E.40,2,4,6,8,A,C,E.63,7,B.70,5,9,E<PERSON>82,7,C.90,E.A2,4,A,C.B7.C0,2,4,A,C,E.D6,8.E1,3,B,D.F5,7,9.G0,2,C,E;113,B.20,7,E.40,4,7,A,E.63,B.77.80,E.A3,B<PERSON>C4,7,A.E1,6,8,D.G1,D:PGE9GECCJ5QMFSL5V3ZI08E49GS7MVKSJ9WO6X8SFTZZ3DFX4P64GTD58Z28K0KWL29C22Q7E1CW5I1KW4OF", "hasFlower": 0, "hasSeason": 0}, {"id": "42", "q": "000,2,5,7,A,C.20,3,5,7,9,C.40,2,6,A,C.62,5,7,A.70,C<PERSON>82,6,A.A1,3,9,B.B6.C0,3,9,C.D5,7.E0,2,A,C;100,2,5,7,A,C.20,3,5,7,9,C.40,C.56.62,A.70,6,C.82,A.A3,9.C3,6,9.D0,C.E2,A:J838W1S7MJMLW70DK3OI1ZOYOS7UQVW966XY8WBJVS5RUBXDRDVDOL90KVS0J7N60NI6ZQ58", "hasFlower": 0, "hasSeason": 0}, {"id": "43", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,2,4,6,8,A,C.60,2,4,6,8,A,C.80,2,4,8,A,C.96.A1,3,9,B.B6.C1,3,9,B;105,7.13,9.20,C.32,A.50,C.63,5,7,9.70,C.A2,A.C3,9;213,9.C3,9;313,9.C3,9:X9GFKBXR924UUK19IG5MSCFWWKX3PE6CQIMVR3IEN1MK1QRB5QMS9CDQ6IDX4P3RE3EVCN12", "hasFlower": 0, "hasSeason": 0}, {"id": "44", "q": "000,2,4,8,A,C.16.21,3,9,B.36.40,3,9,C.56.60,2,A,C.75,7.80,C.95,7.A0,2,A,C.B6.C0,3,9,C.D6.E0,2,4,8,A,C.G0,2,4,6,8,A,C;100,C.36.40,3,9,C.60,6,C.85,7.A0,2,A,C.B6.D0,3,6,9,C.G0,<PERSON>;276.A2,A.D6;3A2,A:891O6RZM64N72T6TJ21UTMOPG79K3U7OANXIKJGM1NRJORI39XZ85X6UNPA94M78G1R5IIBGJAXTAUB8", "hasFlower": 0, "hasSeason": 0}, {"id": "45", "q": "000,4,6,8,C.21,3,5,7,9,B.40,3,5,7,9,C.62,4,8,A.76.81,3,9,B.A0,3,5,7,9,C.C0,3,5,7,9,C.E0,3,6,9,C;105,7.21,3,9,B.35,7.40,3,9,C.72,A.A3,5,7,9.B0,C.C3,5,7,9.D0,C.E6;221,B.40,C.A3,9.B0,C.D0,C:QPJROW8MCC2ZHQUR7PM2RU261HLG7GZYW62E4RHAE55OMP5CGX4J8YDGNNSJXH7ALDJSUPIN5U1NCIM7", "hasFlower": 0, "hasSeason": 0}, {"id": "46", "q": "000,3,5,7,9,B,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.81,4,6,8,A,D.A0,2,4,6,8,A,C,E.C1,4,6,8,A,D.E0,5,9,E.F2,7,C.G0,5,9,E;113,5,7,9,B.31,7,D.62,C.75,9.A5,9.G5,9;214,A:2E4D25IAYK5PKCNHDLZODA54IPOZ8TMYZKILZHD4LEP4TACYOXC1TMXEOYI2HT8CKML8N1AM85E2NNPH", "hasFlower": 0, "hasSeason": 0}, {"id": "47", "q": "000,2,5,7,A,C.20,2,4,8,A,C.40,2,4,8,A,C.60,3,6,9,C.80,4,6,8,C.A1,5,7,B.C0,4,8,C.D2,A;100,5,7,C.12,A.20,4,8,C.32,A.44,8.76.A1,5,7,B.C0,4,8,C.D2,A;200,5,7,C.12,A.76.A6;376:CI4CHS4Y8ZHXOCYM60FDDZVPIZY6QI8I7Q1REXL5ZHU5PYXWLRVFHW7P1MCONOPE02X2USON", "hasFlower": 0, "hasSeason": 0}, {"id": "48", "q": "000,5,7,C.12,A.24,8.30,2,A,C.45,7.50,2,A,C.64,6,8.71,B.84,6,8.90,C<PERSON>A2,4,6,8,A.C0,2,4,8,A,C.E0,2,6,A,C;105,7.12,A.30,C.45,7.50,2,A,C.64,8.90,4,8,C.A2,A.B4,8.C0,C.E1,B;245,7.64,8.94,8.B4,8.E1,B:P957WCIRDWSBTD93B7AD0GZBS5R8R102SL2Z6LI779UOHGWOADZTBGR516S98ZO68T38HOCG3P5UT6W3", "hasFlower": 0, "hasSeason": 0}, {"id": "49", "q": "000,2,5,7,9,C,E.22,4,A,C.30,6,8,E.43,B.51,6,8,D.63,B.75,7,9.82,C.90,4,6,8,A,E.B0,3,5,7,9,B,E.D2,4,A,C.E6,8;100,5,7,9,E.30,E.43,6,8,B.51,D.77.90,5,9,E.B0,3,5,7,9,B,E.E7;205,9.77.B4,A:DE6FMOKJIUN2PX6S0DRXR2ZKS250WOEJWKECRKC8YSEP7NXV5J5W37MIFV54HHR9J2WZ834MYSM9UX", "hasFlower": 0, "hasSeason": 0}, {"id": "50", "q": "001,3,9,B.16.31,3,5,7,9,B.50,2,4,8,A,C.66.70,2,A,C.91,3,5,7,9,B.B3,5,7,9.C1,B.E0,4,6,8,C;131,3,5,7,9,B.51,3,9,B.66.70,2,A,C.91,3,6,9,B.B4,6,8.C1,B<PERSON>E5,7;234,8.53,9.92,A.B4,8.E5,7;3E5,7:3YRTH1BIMMQCH3NVIYAHKDPCIE3M0MIKJSNJBSPBXNPSC9RQQPRAVBHS1FO0XR31KNKUE1CQ9OTUDF", "hasFlower": 0, "hasSeason": 0}, {"id": "51", "q": "000,2,4,6,8,A.20,2,4,6,8,A.40,2,4,6,8,A.60,2,8,A.74,6.80,A.92,8.A0,4,6,A.B2,8.C0,5,A.D2,8.E4,6;101,3,7,9.21,3,7,9.40,2,5,8,A.62,8.70,5,A.92,8.A0,5,A.B2,8.D2,8.E4,6:SZIZG0WNUGH0RMQR6GINO59OSUYZ6H03LRTLL6ZPM5GWLNPTI8I9RSPYO3SNQ06HPOT8HT", "hasFlower": 0, "hasSeason": 0}, {"id": "52", "q": "000,2,4,6,8,A,C,E.20,3,5,7,9,B,E.40,3,7,B,E.55,9.60,2,7,C,E.74,A.80,2,7,C,E.94,A.A1,6,8,D.B3,B.C0,6,8,E<PERSON>D3,B.E0,5,7,9,E;102,4,A,C.17.40,7,E.70,2,C,E.A6,8.D6,8;272,C.A7:ZO2OHYIUZYI7KJ24VU3UHJDKT300SHD3T170NOU1KVVNDJIN7OTVYDNJT4KH2Y0327IS4S4S", "hasFlower": 0, "hasSeason": 0}, {"id": "53", "q": "001,3,5,9,B,D.17.20,2,4,A,C,E.37.40,2,4,A,C,E.56,8.61,3,B,D.75,7,9.80,2,C,E.94,6,8,A.A1,D.B3,5,7,9,B.C0,E.D2,4,6,8,A,C.E0,E;101,3,B,D.23,B.42,C.63,B.75,9.94,A.B3,B.D3,B:DK10R956IH1YUDA9GDUH0EHDPKRNSII0SR65ENPYKGG059IH1GRPYUS5EY6NAU6KE19SANPA", "hasFlower": 0, "hasSeason": 0}, {"id": "54", "q": "001,5,7,B.13,9.20,6,C.33,9.40,5,7,C<PERSON>53,9.60,5,7,C<PERSON>72,A<PERSON>80,4,8,C.92,6,A.A0,4,8,C.B2,6,A.D0,2,4,6,8,A,C;123,9.30,6,C.55,7.72,A.84,8.A1,5,7,B.C6.D1,3,9,B;246.84,8.A6.D2,A;384,8:Q5IDMDL9KSKJM73J1DYEY4N63CMWSY845K9S7NSDLOM8E9YCLI86O6W73IJJK9W6QL83W1I7", "hasFlower": 0, "hasSeason": 0}, {"id": "55", "q": "001,4,6,9.21,3,7,9.41,3,5,7,9.61,3,5,7,9.80,2,4,6,8,A.A0,2,8,A.B4,6.C0,2,8,A;101,4,6,9.22,8.41,3,7,9.55.61,9.81,5,9.A0,A.B2,5,8.C0,A;204,6.22,8.43,7.A0,A.B2,8;304,6;405:KSPWISQHD9CXKUK0MC7UJNXNUTQF95U5IWKDA3OJCZTHAOH1I7HZ0L1G4WCLPW3F4I33GM", "hasFlower": 0, "hasSeason": 0}, {"id": "56", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.61,4,A,D.76,8.80,3,B,E.95,9.A0,2,7,C,E.B4,A<PERSON>C0,6,8,E.D2,4,A,C.E0,6,8,E;105,9.12,7,C.24,A.31,7,D.C7.D1,3,B,D.E6,8;224,A.D3,B:OH28CS7ZTLJF5Q38ENY0KW6YHIHRN68JKENJYZ6AT6WHI59N8RY2QFFZLJATKOCZ0K3FA79ST9A9", "hasFlower": 0, "hasSeason": 0}, {"id": "57", "q": "000,2,4,6,8,A,C,E.21,3,5,7,9,B,D.40,2,4,6,8,A,C,E.62,4,6,8,A,C.70,E.82,5,7,9,C.A0,2,4,6,8,A,C,E.C0,2,4,6,8,A,C,E.E0,2,4,6,8,A,C,E;142,7,C.63,B.A0,7,E.B2,C:ARC2HHZMNGAZ32VUAORI727Q9C8VIMONHRG3A6RH9O96Q7C268NNZ9Z8GII3QO678GUCQ3", "hasFlower": 0, "hasSeason": 0}, {"id": "58", "q": "000,3,5,7,A.21,9.34,6.40,2,8,A.54,6.71,3,5,7,9.93,5,7.B0,2,5,8,A.D1,9.E4,6;104,6.21,9.34,6.40,2,8,A.54,6.72,4,6,8.95.B0,2,5,8,A.D1,9.E5;234,6.40,A.55.72,5,8.A5.B0,A.E5;334,6.65:Q3W7DQMJ2353F278SR36QNWSB444MG8MZ54DBFJBW6NJJM5ZZR72DF6S7R2QDZNN6WG5SRFB", "hasFlower": 0, "hasSeason": 0}, {"id": "59", "q": "000,2,4,7,A,C,E.20,2,6,8,C,E.34,A<PERSON>41,6,8,D<PERSON>53,B.60,6,8,E.72,4,A,C.86,8.90,2,4,A,C,E.A7.B1,3,5,9,B,D.D0,2,6,8,C,E.E4,A;100,2,C,E.17.37.53,7,B.73,6,8,B.91,7,D.A3,B.B5,9.C2,C.D6,8:K09CQJBNEPQSXZK9H6YCBPWCJU6HAUK0MZS5S6YNAV7S8FR6WFDWRKLXV92OYB8572MBWDCE9YOL", "hasFlower": 0, "hasSeason": 0}, {"id": "60", "q": "000,2,6,A,C.14,8.21,B.35,7.40,2,A,C<PERSON>54,6,8.62,A.75,7.83,9.A0,3,5,7,9,C.C0,2,5,7,A,C.E1,3,9,B;102,A.21,B.35,7.42,A.54,6,8.93,9.A6.C1,5,7,B.E2,A;202,A.21,B.35,7.54,8.C5,7.E2,A:MJQQ7HAHWFM7YW79C9BFXYALK6SQX3KWZ3X0ZOKAWAFHMTSK7SCZYBJXOFHZ9MJS09J6QYTL", "hasFlower": 0, "hasSeason": 0}, {"id": "61", "q": "000,2,5,7,9,C,E.20,2,4,7,A,C,E.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.80,2,4,6,8,A,C,E.A0,2,4,6,8,A,C,E.C0,2,4,6,8,A,C,E.E0,2,4,6,8,A,C,E;105,9.43,B.60,E.90,E.A2,C:5NEE1TTUI0WUT81W0B5XY8AJW954UB81CN7I9NJIWA37B793904EB7344NU0IY5X3T8CLEL1", "hasFlower": 0, "hasSeason": 0}, {"id": "62", "q": "000,3,5,9,B,E.17.20,2,4,A,C,E.36,8.40,3,B,E.55,7,9.60,2,C,E.74,6,8,A.80,2,C,E.94,6,8,A.A1,D.B3,5,7,9,B.C0,E.D3,5,7,9,B.E0,E.F2,4,6,8,A,C.G0,E;103,B.21,D.56,8.81,D.B3,B.C6,8.F1,D:XIWXAFMMZDMGG6OAQK13ND1E7FOUFYW6W7HINYAZ6936GE0QRHSBPHWKFMUQ3D503SBHPQ5RGAD9", "hasFlower": 0, "hasSeason": 0}, {"id": "63", "q": "000,5,7,9,E.20,4,6,8,A,E.40,2,7,C,E.55,9.67.70,2,4,A,C,E.87.95,9.A0,3,7,B,E.C0,3,5,7,9,B,E.E0,4,6,8,A,E;105,9.24,A.30,E.47.55,9.70,3,B,E.96,8.B0,7,E.D4,A;224,A.70,E.D4,A:WUBYES183YLW63UJHHDXHBUSFH25ESLJDB735Y62XY1F8UJJ57MEKWWM7B3K1SKKED71DZZ5", "hasFlower": 0, "hasSeason": 0}, {"id": "64", "q": "001,3,6,9,B.21,4,6,8,B.40,2,5,7,A,C.60,3,5,7,9,C.80,2,4,8,A,C.96.A2,4,8,A.B6.C0,2,A,C;106.11,B.36.40,2,A,C.56.60,C.80,2,4,8,A,C.A2,6,A.C0,C;236.40,2,A,C.80,2,A,C.A2,6,A.C0,C;380,C.A2,A:AKYG9QVZDV2Y53UC1WK3W2GVVBGZKQ5XKCXCLY7U2TT92Y7D3ITQWIA9QBDWZD39BULZUG7CT1BX7X", "hasFlower": 0, "hasSeason": 0}, {"id": "65", "q": "000,2,4,6,8,A.20,2,4,6,8,A.40,2,4,6,8,A.61,4,6,9.80,2,5,8,A.A0,2,4,6,8,A.C1,3,7,9.D5.E0,2,8,A;101,9.15.20,2,8,A.40,4,6,A.65.80,A.95.C2,8.E1,9;215.20,A.40,A.80,A.C2,8:GBTE6D6ZC01TRO35AHOJB5ILLJW0UPS71J7AS3NYEQWP6UY2CIHLG29QTDNL6T9R4ZRR4J", "hasFlower": 0, "hasSeason": 0}, {"id": "66", "q": "000,2,5,7,9,C,E.20,2,4,A,C,E.36,8.41,3,B,D<PERSON>56,8.60,3,B,E.75,9.80,2,C,E.95,7,9.A1,D.B3,7,B.C0,E<PERSON>D2,4,A,C.E0,7,E;105,9.10,2,C,E.24,A.31,7,D.56,8.63,B.75,9.82,C.96,8.A1,D.C3,B.E0,7,E:GWAKSUYUY8LC3TBHGZ9NQTU0HVY7H3QZ8BGGABNNUVVCC3ZKSC7FHQ70B7YS3LKFL9VLKW99NQZS", "hasFlower": 0, "hasSeason": 0}, {"id": "67", "q": "000,2,4,6,8,A,C.21,3,5,7,9,B.40,2,A,C.54,8.60,2,6,A,C.74,8.80,6,C.92,A.A4,8.B0,2,A,C.C6.D1,3,9,B<PERSON>E5,7;101,3,5,7,9,B.21,3,5,7,9,B.40,2,A,C.63,6,9.70,C.A2,A.B0,C.D1,3,6,9,B;205,7.63,9.A2,A;305,7.63,9:QNIHM7HKO4HGUS8KZF85I784B4J0W5O1V99UHVIQXQBIXFZMW9X8JZJLE4SJQ7VZFV0BB0N9MDLMDG70FXE1", "hasFlower": 0, "hasSeason": 0}, {"id": "68", "q": "000,2,4,6,8,A,C.20,2,5,7,A,C.41,4,6,8,B.60,6,C.72,4,8,A.80,6,C.93,9.A1,5,7,B.C0,2,4,8,A,C.D6.E0,2,4,8,A,C.F6.G0,2,A,C;111,6,B.36.56.70,3,9,C.86.A1,B.C1,B.E3,6,9.F1,B;226.70,3,9,C.F1,B:CH7SQP7HV5PA6H6DP9XUDLXN3D1K0NM3MJRQA27U1UMJMK9KLVCP0V4KR4J0D71CHC052SX33X1JUV", "hasFlower": 0, "hasSeason": 0}, {"id": "69", "q": "000,2,4,6,8,A,C,E.20,2,4,7,A,C,E.41,4,6,8,A,D.61,4,7,A,D.80,2,4,6,8,A,C,E.A0,2,4,6,8,A,C,E.C2,5,7,9,C.D0,E<PERSON>E5,9;104,A.22,7,C.44,A.51,D.64,A.90,3,5,9,B,E.C2,C;222,C.54,A.95,9:2DXM9L51HXLSE1RMKDR5UFKJID4G1EXIWCWF3S3KJCHEV5CGR129GG47577272DKX3WCEV4R34WU", "hasFlower": 0, "hasSeason": 0}, {"id": "70", "q": "000,3,5,7,9,C.20,2,4,6,8,A,C.40,2,4,6,8,A,C.60,2,4,8,A,C.76.80,2,A,C.96.A0,3,9,C.B5,7.C1,3,9,B.D5,7.E1,3,9,B.F5,7.G0,2,A,C;103,5,7,9.23,5,7,9.30,C.42,4,8,A.50,C.62,A.76.80,2,A,C.96.B6.C1,B.D4,8.E1,6,B:BIO82PH8PKPW233WGSFSBKTUTAS2HYUAOXJI1121BJG3YHF6UYJ8HKWTC5OW556XKPUTCG813AG5BFYAJOSIIF", "hasFlower": 0, "hasSeason": 0}, {"id": "71", "q": "000,2,A,C.14,6,8.21,B.34,6,8.40,C<PERSON>52,4,6,8,A.70,2,4,6,8,A,C.90,2,5,7,A,C.B1,5,7,B.C3,9.D0,5,7,C.F1,3,5,7,9,B;100,C.21,4,6,8,B.40,4,8,C.63,9.75,7.95,7.B1,B.C4,8.D0,C.F1,4,6,8,B:P0PS6M7GEST1B2F32YOE5305QPYZ0TFML71QD3CJSBE6EDD05H5G37TCXY21OZX7HD1PJLSTG2YG", "hasFlower": 0, "hasSeason": 0}, {"id": "72", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,2,5,7,A,C.60,2,6,A,C.74,8.80,2,A,C.96.A0,2,A,C.B5,7.C1,3,9,B.D5,7.E0,2,A,C;101,3,9,B.20,2,A,C.35,7.62,A.70,C.92,A.B6.C2,A;211,B.62,A.70,C.92,A.B6:HGHJYER8XYD4C6PFE5QMPOVFHM7V8DWR84XTDMSAT5BM44OHFQ7RXAP5OEBRWCPFQGDXO86EQS5J", "hasFlower": 0, "hasSeason": 0}, {"id": "73", "q": "000,3,5,7,9,B,E.20,2,5,9,C,E.37.40,2,4,A,C,E.61,3,6,8,B,D.81,3,5,9,B,D.A0,2,5,7,9,C,E.C0,3,5,9,B,E.D7.E1,3,5,9,B,D.G2,5,9,C;120,2,C,E.42,4,A,C.72,C.85,9.91,D.A5,9.B0,E.D4,6,8,A.F2,C:3ZG2S3E0EK6TCWIDJEFCTK2YW088RJLJDSGCY020ZJLRSY8KK2778IG5F7L6EDDCWGTT3FMMF53S7YWL", "hasFlower": 0, "hasSeason": 0}, {"id": "74", "q": "001,4,6,9.20,4,6,A.32,8.40,4,6,A.52,8.64,6.70,2,8,A.84,6.92,8.A0,4,6,A.B2,8.C0,5,A<PERSON>E0,2,4,6,8,A;101,5,9.20,4,6,A.40,A.52,4,6,8.72,4,6,8.93,7.A0,A<PERSON>C0,5,A<PERSON>E1,3,7,9;215.40,A.54,6.74,6.B0,A.C5;354,6;455:DPKS9KC2FHITHEN6B6N56CZBZTK2EOB2DFN6YYF9UZTS29SF9T0NHABO0EKSO0IPAOP5UPCCEH5ZIDI0D5", "hasFlower": 0, "hasSeason": 0}, {"id": "75", "q": "000,3,5,7,9,C.21,3,5,7,9,B.40,2,5,7,A,C.60,2,4,8,A,C.80,2,4,6,8,A,C.A4,8.B0,2,A,C.C5,7.D0,2,A,C.E6.F1,3,9,B.G5,7;103,6,9.21,5,7,B.45,7.52,A.71,B.85,7.B2,A.E1,6,B:9OMQO1F94DOO48QG54MD2K2MS01F65S4Q08GDN1YG9AQY269FGS6K26AANK5DF5S1MKANN", "hasFlower": 0, "hasSeason": 0}, {"id": "76", "q": "000,2,4,6,8,A.21,3,7,9.35.40,2,8,A<PERSON>54,6.61,9.75.80,2,8,A.95.A1,9.B3,7.C0,A;102,5,8.21,3,7,9.40,2,5,8,A.80,2,8,A.95.A1,9.B3,7.C0,A;223,7.40,2,5,8,A.80,A.95.B3,7;345.95:M5MBBBWVG2XQGWQIRHIXP4TGV5Y64G6VV6XI6WYP4PPRHKK2I5QWMRTBR4YMYHQ25H2X", "hasFlower": 0, "hasSeason": 0}, {"id": "77", "q": "000,3,5,7,9,C.20,2,5,7,A,C.40,4,8,C.56.60,3,9,C.75,7.81,B.93,5,7,9.A0,C.B4,8.C0,2,A,C.D5,7.E0,2,A,C;100,3,6,9,C.20,C.44,**********,B.94,8.B4,8.C1,B.E1,B;203,9.56.A4,8;356:Q02L4WJPN8Z91HLHV0219V5IQITI8NN0LKBCBC7KOORD7AM08P483TNDLMWJF3RSSFIZA5", "hasFlower": 0, "hasSeason": 0}, {"id": "78", "q": "002,4,6,8,A.10,C.22,4,8,A.40,3,5,7,9,C.60,3,5,7,9,C.81,5,7,B.93,9.A0,C.B3,5,7,9.C1,B.D3,5,7,9.E0,C;103,6,9.10,C.22,A.40,3,5,7,9,C.65,7.81,5,7,B.A0,C.B3,5,7,9.D5,7.E0,C;243,9.B3,9.D5,7;3D5,7:4JYN5G5DMOSB10RXRBBVOFLGJVU0JSP2N4RXD30SAV1UVJME2BY5Y3YXUM0623U3A445EMPH26LSRFHX", "hasFlower": 0, "hasSeason": 0}, {"id": "79", "q": "000,3,5,7,9,C.21,3,5,7,9,B.40,2,4,6,8,A,C.61,4,8,B.76.80,2,4,8,A,C.96.A0,2,A,C.B4,8.C1,6,B.D3,9.E1,5,7,B.F3,9.G0,5,7,C;104,6,8.22,4,8,A.36.41,3,9,B.64,8.76.80,2,4,8,A,C.A0,C<PERSON>B4,8.C1,6,B.D3,9.E5,7.F3,9.G5,7:K13VZQK3DN4IKXRAR0ZNFUQIU5AP2GOXOZW4KRA2WPLF7OIDD7W09JPPMLGIV2G2WFZN109FM5RVOGJAD70NJJ7V", "hasFlower": 0, "hasSeason": 0}, {"id": "80", "q": "000,5,9,E.13,7,B.21,5,9,D.33,7,B<PERSON>41,D<PERSON>53,5,7,9,B.70,2,4,7,A,C,E.93,5,7,9,B.A0,E.B3,7,B.C5,9.D1,7,D.E3,5,9,B;100,5,9,E.13,B.21,5,7,9,D.43,B.56,8.63,B.70,7,E.84,A.A0,3,7,B,E.C6,8.D1,D.E3,B:ZVBG5GZD3XFP2LAVUR5ZBHP9QQX30S9L1G426F4OOL70S1C9MW7D9YALEZROQOIRYQIMGRXFUE6FXWHC", "hasFlower": 0, "hasSeason": 0}, {"id": "81", "q": "000,2,6,8,C,E.14,A.20,6,8,E.32,4,A,C.40,6,8,E<PERSON>53,<PERSON><PERSON>60,5,9,E<PERSON>72,7,C<PERSON>84,A.91,6,8,D.A3,<PERSON><PERSON>B0,5,9,E<PERSON>C3,7,B.D0,5,9,E.E2,7,C.F0,4,A,E.G2,6,8,C;114,A.31,6,8,D.50,E.72,7,C.96,8.B3,B.C0,6,8,E.E1,D.G2,C;214,A.G2,C;3G2,C:79LCNCERQBKX77BW1JX1MTKIQ6NYE0GEZO7LQCP0CLXAHZV6QVI9AOW693MY9Z6RH3OANAXTJFZEGOBDN0PFL0BD", "hasFlower": 0, "hasSeason": 0}, {"id": "82", "q": "000,2,4,6,8,A,C,E.20,2,4,7,A,C,E.40,3,5,7,9,B,E.60,2,5,9,C,E.77.80,2,4,A,C,E.96,8.A0,2,4,A,C,E.B6,8.C0,2,4,A,C,E.D7.E0,2,4,A,C,E;114,A.27.40,E.65,9.70,E.95,9.A0,E.D7.E4,A;2A0,E:30BB7GNT6BT0J3C4CZFT4ZFKM0J3AFHHFI7PKBH7MYJ1991RYGNT9NYIPNHJ90X3YA6C7ZGRXGZC", "hasFlower": 0, "hasSeason": 0}, {"id": "83", "q": "000,2,4,6,8,A,C.21,3,5,7,9,B.40,3,5,7,9,C.60,3,5,7,9,C.81,3,5,7,9,B.A0,2,4,6,8,A,C.C2,5,7,A.E0,2,4,8,A,C;104,6,8.11,B.23,9.40,4,8,C.66.74,8.82,A.96.A1,3,9,B.B5,7.E1,3,9,B;205,7.23,9.B6:TBAAZJ8TTB8B2WT1EP2Z0KKCYCURWV2APUR1B0GWPE80EZ2IC10RRGYYUZXVJIAI17PWEGVYUCGI7V8X", "hasFlower": 0, "hasSeason": 0}, {"id": "84", "q": "000,2,5,9,C,E.20,2,4,A,C,E.36,8.40,3,B,E.55,7,9.62,C.77.82,C.95,7,9.B0,3,5,9,B,E.C7.D0,2,4,A,C,E.E6,8;100,5,9,E.12,C.33,6,8,B.56,8.72,7,C.96,8.B3,B.D1,3,7,B,D;233,B.46,8.72,7,C.96,8.C3,B.D1,7,D;396,8:IPSQCZ6JNZ8JXSESACBKYPPAXLZR7GWN0WLE5EPBJ56YMX06BRY7EGWKQCJWAS6RLLRN2AQCNZ28IXYQM8B8", "hasFlower": 0, "hasSeason": 0}, {"id": "85", "q": "000,2,5,7,A,C.20,3,9,C.35,7.41,B<PERSON>54,6,8.61,B.74,6,8.80,2,A,C.94,8.A0,6,C.B3,9;101,6,B.20,3,9,C.41,5,7,B.64,6,8.80,3,9,C.B3,9;201,B.20,3,9,C.41,6,B.64,8.80,C.B3,9;323,9.B3,9:FBRPIDN594BD9KY7IQU3Y43N9ILFKD5PK79KJJF7NPRYRPYNUVJFJDL5IBRV57V33BQ4VU4U", "hasFlower": 0, "hasSeason": 0}, {"id": "86", "q": "000,2,6,8,C,E.20,3,5,7,9,B,E.41,3,7,B,D<PERSON>55,9.60,E<PERSON>72,5,7,9,C.80,E.92,5,7,9,C.A0,E.B4,6,8,A<PERSON>C1,<PERSON><PERSON>D3,7,B.E0,5,9,E<PERSON>F3,7,B.G0,5,9,E;106,8.25,9.37.60,5,9,E.77.81,D.96,8.B5,9.E0,4,6,8,A,E:B63K5WOWPP3BRAQEB8TRTQGEKOOQGKDIPA5Y6I3VDEDMGAVKSVMR6WD3SMJMOVR2BGY68P2WQ88JEA", "hasFlower": 0, "hasSeason": 0}, {"id": "87", "q": "001,3,5,7,9.20,2,8,A.40,2,4,6,8,A.60,2,4,6,8,A.80,2,4,6,8,A.A4,6.B0,2,8,A.D0,2,8,A<PERSON>E4,6;102,8.21,9.40,3,5,7,A.61,4,6,9.82,8.94,6.B0,A<PERSON>C2,8.E4,6;202,8.44,6.61,4,6,9.94,6.E4,6:4XIIYXBY4WO6T9J2NG69C9ZBGM8TMNZBKIWXG82Q2KYKY6TCGCBWMQ2JTHONZNZJCHKIJM6WX9", "hasFlower": 0, "hasSeason": 0}, {"id": "88", "q": "000,2,4,6,8,A,C.20,3,6,9,C.42,4,6,8,A.50,C.62,4,6,8,A.80,3,6,9,C<PERSON>A2,4,8,A.B0,6,C.C2,4,8,A.D6.E0,2,4,8,A,C.F6.G0,2,A,C;102,A.26.43,9.63,6,9.83,9.B1,3,6,9,B.D3,9.F6.G2,A;226.53,9.66.B6.C3,9.F6;353,9.C3,9:MQWWWNVC75YA89H9AZLM8ZAJL1AYIJIL0SC5HJTS9SZ95UUWUHZ008VIQKUVNQTHVKKNJMYN7S5YI0Q81MKL", "hasFlower": 0, "hasSeason": 0}, {"id": "89", "q": "000,2,4,6,8,A,C.21,4,8,B.36.40,3,9,C.55,7.61,3,9,B.75,7.80,2,A,C.94,6,8.A0,2,A,C.B5,7.C0,3,9,C.E0,2,4,6,8,A,C.G0,3,5,7,9,C;102,A.36.40,C.62,4,8,A.82,6,A.94,8.A0,C.D0,C.E2,6,A.G3,5,7,9;202,A.86:LHC18F47WMVCR34M9FN9YSQ37SHXQ8A618A6OJTA7FUN3TD9L7AZ2ZXGSFH0TDTRSL59Y8LUOWH3JV2G50", "hasFlower": 0, "hasSeason": 0}, {"id": "90", "q": "001,4,6,8,B.21,3,9,B.35,7.40,2,A,C<PERSON>55,7.60,2,A,C.75,7.83,9.A1,3,5,7,9,B.C0,3,5,7,9,C.E0,5,7,C.F3,9.G1,B;101,6,B.21,3,9,B.35,7.41,B.60,C.75,7.A2,4,8,A.C0,3,9,C.D6.F3,9.G1,B;201,6,B.21,B.75,7.A3,9.D6;301,6,B.21,B.75,7:3MKTQYMV4WAOKWJRLI7LKIZF9L3MBQGWDAS2JTSZVDRFMV6L6IFE9Z515GRBDO6TD76TIZ1GRV2OF7GY4CKQWC7QEO", "hasFlower": 0, "hasSeason": 0}, {"id": "91", "q": "001,3,5,9,B,D.20,2,4,7,A,C,E.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.80,4,6,8,A,E.A1,4,6,8,A,D.C0,2,5,9,C,E.D7.E0,3,B,E.F5,7,9.G0,2,C,E;103,B.22,4,A,C.44,6,8,A.50,E.65,7,9.70,E<PERSON>84,A.A6,8.C0,E.D7;245,9.57.65,9.A6,8:JUUZGQH28HB42TLLMY8T6NSZ6KPZIGQDBORX4YJFGSYZTEXAPKDMWQKGMAKM11P8EOYJQ2AJFWEEFI2P81AFN1RIIT", "hasFlower": 0, "hasSeason": 0}, {"id": "92", "q": "000,6,8,E.13,B.21,5,7,9,D.33,B.40,5,9,E<PERSON>53,7,B.60,5,9,E.72,7,C.90,4,6,8,A,E.B0,2,4,6,8,A,C,E.D1,4,6,8,A,D;100,6,8,E.13,B.33,B.40,5,9,E.60,6,8,E.72,C.87.90,4,A,E.A6,8.B0,3,B,E.C6,8:PJ1DUFU33NCUCS8IQN2IJ2JS841FKK5R34FVOH8OFGDBRUQ25I03BKG5C1HHSCVKHIO1SOP7J78052", "hasFlower": 0, "hasSeason": 0}, {"id": "93", "q": "000,2,4,6,8,A,C,E.20,3,5,7,9,B,E.42,6,8,C.50,4,A,E.62,6,8,C.70,4,A,E.86,8.90,2,4,A,C,E.A6,8.B2,C.C0,4,6,8,A,E.E0,4,7,A,E;113,B.20,E.47.53,5,9,B.76,8.B7.C0,5,9,E.E4,A;247.54,A.B7.C0,E;3C0,E:MZ8RH66KL8LRXILH433MWC8HRFZEZV4K2OFV6FWMEC2IF342COI4VX8XDOWLCRODVKEHMI23ZXDKD6WE", "hasFlower": 0, "hasSeason": 0}, {"id": "94", "q": "000,4,6,8,C.12,A.20,5,7,C.32,A.44,6,8.51,B.63,5,7,9.70,C.82,4,8,A.90,C.A3,5,7,9.B0,C.C3,5,7,9.D1,B<PERSON>E3,5,7,9.F0,C.G2,4,8,A;100,4,6,8,C.12,A.25,7.46.D6.F3,9;204,8.16.36.F3,9;304,8.16.F3,9:T8PCFVPECG68PC0EMRXP6XMZQ50HV5QQVAMJOVTH58DUFCYAJ6RHLGHX8MYAAQLOZG5J6XJ7UDG7", "hasFlower": 0, "hasSeason": 0}, {"id": "95", "q": "000,2,4,6,8,A,C.20,2,5,7,A,C.41,3,6,9,B.63,5,7,9.70,C.84,8.91,B.A3,6,9.C0,6,C<PERSON>D3,9.E0,5,7,C<PERSON>F2,A<PERSON>G4,6,8;101,3,5,7,9,B.21,5,7,B.43,9.56.63,9.70,C.91,B.A3,6,9.C6.D3,9.E6.F2,A<PERSON>G4,6,8;204,8.16.43,9.D3,9.E6.G6;3F6:PQGW3HDUH0NL1RZGEDNIXB8HQERFZLJDGEFI04GB7PQJPLJN8CFQWOBWDIZ0F41UC7IXHZP3ONUL0EUCBCWJ11", "hasFlower": 0, "hasSeason": 0}, {"id": "96", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.42,4,6,8,A,C.50,E.62,5,9,C.77.81,3,5,9,B,D.A0,2,4,7,A,C,E.C4,6,8,A.D0,2,C,E.E4,6,8,A;101,D.13,7,B.21,D.36,8.50,E<PERSON>65,9.72,C.A0,E.C6,8.D1,D.E4,7,A;201,D.21,D.D1,D:4QX43EHL8XN1VBP2SZQJG8UGUHA1L3NNZJW37RDSQF41P43200QXDPNUS7HFBIRPF2XRUA155FIE2VSRDWDH", "hasFlower": 0, "hasSeason": 0}, {"id": "97", "q": "000,3,5,7,9,C.20,2,6,A,C.34,8.40,2,A,C<PERSON>55,7.60,3,9,C.75,7.81,B.96.A0,2,4,8,A,C.B6.C0,3,9,C.D5,7.E2,A<PERSON>F0,6,C.G2,A;100,4,8,C.22,6,A.30,C.42,A.50,C.66.86.A1,3,5,7,9,B.C0,C.D5,7.F2,6,A;286.D5,7;3D6:QO03JWTAE0DOA4JHPO29ZNLVBRM9EGKQSH1NBGE0W0R57FKIBN6ZS4B2TE7P1J5NJMKOFLKI37V7D6TT", "hasFlower": 0, "hasSeason": 0}, {"id": "98", "q": "000,2,4,8,A,C.20,2,4,8,A,C.36.40,2,4,8,A,C.56.61,3,9,B.75,7.80,C.94,8.A0,6,C.B2,4,8,A.C0,6,C.D2,4,8,A.E6.F0,2,4,8,A,C;102,4,8,A.30,4,6,8,C.42,A.61,6,B.A5,7.B1,B<PERSON>C5,7.D2,A<PERSON>E4,6,8:MN55NB3OBPPFAE88BVH6XH8X8X1AFJFM26H23C3VEJ2ECJO5O6WVF2ENAN6VW51CH1BCX3J1OMMA", "hasFlower": 0, "hasSeason": 0}, {"id": "99", "q": "000,2,4,6,8,A,C.20,2,4,8,A,C.36.41,3,9,B.55,7.60,2,A,C<PERSON>74,6,8.80,C.93,5,7,9.A0,C.B2,5,7,A<PERSON>C0,C.D2,4,6,8,A<PERSON>E0,C<PERSON>F2,4,8,A.G0,6,C;105,7.13,9.20,C.46.52,A.76.A6.B1,B.D6.E4,8.F0,C.G6:P370EXNQXBSKAGILAP80QZ5SAZ6BXNKCLKZZ293N1K5DYYHD78619L2NHLHICE5XOOHG5QASSQ", "hasFlower": 0, "hasSeason": 0}, {"id": "100", "q": "000,2,4,6,8,A,C,E.21,3,5,9,B,D.37.40,2,4,A,<PERSON>,E.56,8.60,2,C,E.74,6,8,A.80,2,C,E.94,6,8,A.A1,D.B3,5,7,9,B.C0,E.D2,4,7,A,C.E0,E.F2,5,9,C;105,9.12,C.24,A.31,D.47.60,E.73,B.80,E.A4,A.C7.E1,D;224,A.C7;324,A:XRDOT8BF7W6X8096NO6NKVSX3KNWLQPAXKRS3Z4MQIBDQMI9AMVIOA4SF1MIE31O6QKRZ8P3ASLR8ETN0711", "hasFlower": 0, "hasSeason": 0}, {"id": "101", "q": "000,2,4,6,8,A.21,4,6,9.40,2,4,6,8,A.61,3,5,7,9.80,2,4,6,8,A.A0,2,4,6,8,A.C0,5,A.D2,8.E0,4,6,A;100,2,4,6,8,A.24,6.41,3,7,9.55.71,4,6,9.95.A1,3,7,9.C0,5,A.D2,8.E0,A;215.55.A2,8.C0,A.E0,A;315:URHWYFT3583XNT4X4NTRRHYAU53SS8YYF6I0I68KLLW0KJ3FU405JTJ8HGIKFKB5LLWARBW4H0GIZZJU", "hasFlower": 0, "hasSeason": 0}, {"id": "102", "q": "000,2,6,A,C.14,8.21,6,B.33,9.40,5,7,C<PERSON>52,A.60,5,7,<PERSON><PERSON>72,A<PERSON>84,8.91,B.A5,7.B0,C.C2,4,6,8,A.D0,C.E2,4,8,A.F6.G0,2,4,8,A,C;100,2,6,A,C.14,8.40,5,7,C.62,6,A.84,8.A6.C0,3,6,9,C.E2,4,8,A.G1,3,9,B:2FN1CPX6WASRWDGYVKY1JJ1LS7KTS7S6X3QKC936D46N4PULNY51ZDFEA9VG2DKIILQ5UREYNZVTLV33", "hasFlower": 0, "hasSeason": 0}, {"id": "103", "q": "000,2,4,6,8,A,C.21,4,8,B.42,6,A.50,4,8,C.62,6,A.80,2,4,8,A,C<PERSON>A1,6,B.B3,9.C0,5,7,C.D2,<PERSON><PERSON>E0,4,8,C.G0,3,6,9,C;101,4,6,8,B.24,8.42,6,A.54,8.62,6,A.81,3,9,B.A1,6,B.B3,9.C5,7.D1,B.E4,8.G0,6,C:1OJEPP8FG2XJTUMZ6434RCNQDOT9CX65GENU2XETQR6R2S3TG68URJ33ESOMF5X5OG29NDM1DMZJ5DNU", "hasFlower": 0, "hasSeason": 0}, {"id": "104", "q": "000,3,5,7,A.21,3,7,9.35.41,9.53,5,7.60,A.73,5,7.81,9.93,7.A0,5,A.B2,8.C0,4,6,A.E1,3,7,9;105.13,7.45.53,7.60,A.74,6.81,9.93,7.B0,5,A<PERSON>E1,3,7,9;253,7.60,A.81,9.B0,A.E1,9;360,A.81,9.E1,9;460,A.81,9:Y0V7CJ0VDWHL8SE23RS0MQ8E7O6HEX1BBSQXWGCP7P9H39GJVODJW96XX7MGWY3VJRE39MS2L0MGH1", "hasFlower": 0, "hasSeason": 0}, {"id": "105", "q": "000,E.13,5,7,9,B.21,D.33,6,8,B.40,<PERSON><PERSON>52,4,7,A,<PERSON><PERSON>60,<PERSON><PERSON>72,5,7,9,C.80,E.92,4,6,8,A,C.A0,E.B2,6,8,C.C0,4,A,E.D6,8.E0,3,B,E;100,E.14,7,A.22,C.40,7,E.67.94,7,A.A0,E<PERSON>B2,7,C.C0,5,9,<PERSON><PERSON>E0,<PERSON>;222,C.57.A0,7,E.D0,E:XHUIVB5PLBYDPV96ZA8IGW89QCOYBRHUDMXJTBXMI3RPQFT41Z8GJXKW2OK2AL1Q11C35Q85IPO69O49CC5F", "hasFlower": 0, "hasSeason": 0}, {"id": "106", "q": "005,7.10,2,A,C.24,8.30,C.42,4,8,A.50,C.62,5,7,A.80,2,6,A,C.A2,4,8,A.C0,5,7,C.D2,A.E0,4,8,C.G0,2,4,8,A,C;111,B.30,4,8,C.50,2,A,C.66.72,A.80,C.A2,4,8,A.C0,5,7,C.E0,4,8,C.G1,3,9,B;240,C.62,A.C0,C.G2,A;340,C.62,A:9Q2INCNHCFFY3I0HDD05I44MVZWEFQDFKVHXEOXKNTI34RTY2CMO59X32VE4539KH0XKD5OCRWV0ETY9OM2ZYMNT", "hasFlower": 0, "hasSeason": 0}, {"id": "107", "q": "000,2,5,7,A,C.20,2,5,7,A,C.44,6,8.50,C.62,4,6,8,A.70,C.82,4,6,8,A.90,C.A3,5,7,9.C0,4,6,8,C.E0,2,4,8,A,C.G1,4,6,8,B;101,6,B.22,5,7,A.45,7.60,3,9,C.75,7.80,2,A,C.94,6,8.C4,8.E0,2,4,8,A,C.G4,6,8:15T963JXKMWICI46OGFUSQXPOM8SQ5UPO051Z76DUGK23JEZ8ZI4Z53UF96O1PP7E2LW4LBIX8CX1D38T4B0", "hasFlower": 0, "hasSeason": 0}, {"id": "108", "q": "000,2,4,8,A,C.20,3,5,7,9,C.40,3,5,7,9,C.60,2,4,6,8,A,C.80,2,4,8,A,C.96.A0,2,4,8,A,C.C1,6,B.D3,9.E0,5,7,C;100,4,8,C.20,3,6,9,C.40,3,5,7,9,C.61,5,7,B.73,9.80,C.92,5,7,A.B1,B.C6.D3,9.E6:ZQPA9EWUA4U9OZPNJWXUXQAWFH6NF3ZE8796OFH88OJ4X7N6Q87Y9A4NU463EY3YQX3YFZEJHOHJW7PP", "hasFlower": 0, "hasSeason": 0}, {"id": "109", "q": "000,2,5,7,9,C,E.21,3,5,9,B,D.40,2,4,6,8,A,C,E.60,3,6,8,B,E.81,3,5,9,B,D.A1,4,7,A,D.C0,2,4,A,C,E.E0,2,4,7,A,C,E;102,5,9,C.22,5,9,C.41,4,A,D.57.63,B.81,3,B,D.A1,7,D.B4,A.D2,C.E0,4,7,A,E;205,9.12,C.57.A7;312,C:AC2PU0W448F6IZXKDUYCJT8XMTTX90TCFA8S6K4WP8R2ID3Y1S6V05AY1F4FV5YI5RZ5K3A6IW01P91KXCRPJWUURM", "hasFlower": 0, "hasSeason": 0}, {"id": "110", "q": "001,3,9,B.16.20,2,A,C.34,8.40,2,6,A,C.60,2,5,7,A,C.83,5,7,9.90,<PERSON><PERSON>A2,6,A.B0,C.C2,4,8,A.D0,C<PERSON>E2,6,A;101,3,9,B.20,C.32,A.40,C<PERSON>52,A.60,6,C.83,9.90,6,<PERSON>.A2,A.B0,C.C2,A<PERSON>D0,C.E2,A;203,9.42,A.50,C.83,9.A0,C.B2,A:REJ4B772G7VD4WYDCLBDE5BH6UM8EGH8T5VABWPTM9TRADEPMGHU5OL9C2564RM2PIW827GS8H4YIORTWJSP", "hasFlower": 0, "hasSeason": 0}, {"id": "111", "q": "000,5,9,E.12,7,C.24,A.30,2,C,E.45,9.50,2,7,C,E.64,<PERSON><PERSON>70,2,C,E.85,7,9.90,2,C,<PERSON>.A4,A<PERSON>B1,7,<PERSON><PERSON>C5,9.D0,2,C,<PERSON><PERSON>E4,<PERSON><PERSON>F1,6,8,<PERSON><PERSON>G4,A;117.23,B.31,D.62,C.80,2,5,7,9,C,E.B7.C5,9.D0,<PERSON><PERSON>E4,<PERSON><PERSON><PERSON>,<PERSON>.G4,A;217.72,C.80,5,7,9,E.B7.D0,E.E4,A:VL3DJF0EGEGCY2YMLZCMV3XADIGUUMZXX02AKVCQP8EIYBJ2W0UFLDPP8VY0FJC3BGLWE3BUXAIFWJQPABZZWMK2DI", "hasFlower": 0, "hasSeason": 0}, {"id": "112", "q": "002,4,6,8,A.10,C.22,6,A.30,4,8,C<PERSON>42,A.55,7.60,3,9,C<PERSON>83,9.90,5,7,C.A3,9.C0,2,4,8,A,C.D6.E0,2,4,8,A,C;103,5,7,9.11,B.26.31,3,9,B.55,7.73,9.95,7.A3,9.C1,3,9,B.D5,7.E1,3,9,B;231,B.55,7.C1,B.D5,7:KX2LRV6WZI2H45ZCVA35I6PJ3S4WCSLYXWQA3XUY54LYJ3R9HUY40Z25H1BLXK1K0TWICT9BHKQ2ZPIC", "hasFlower": 0, "hasSeason": 0}, {"id": "113", "q": "000,2,5,9,C,E.20,3,5,7,9,B,E.40,3,6,8,B,E.60,2,4,7,A,C,E.80,2,5,7,9,C,E.A0,2,5,9,C,E.C0,2,5,9,C,E.D7.E0,3,B,E;102,C.10,E.26,8.40,E.53,7,B.60,E.80,E.95,9.B0,2,C,E.C5,9.D0,E.E3,B;202,C.57.D0,E.E3,B;3D0,E.E3,B:AWM0UBWRNIYU4F2XDPDYTIE65PBR54U4J1BGWW6Q4ONC95DG9PMDNEKSVFBPNMXO8CA19U08CK6M0TQJ0CSV2596", "hasFlower": 0, "hasSeason": 0}, {"id": "114", "q": "000,2,4,6,8,A,C.21,4,6,8,B.40,2,4,6,8,A,C.60,2,6,A,C.80,2,5,7,A,C.A1,3,5,7,9,B.C0,3,5,7,9,C.E0,2,4,6,8,A,C.G0,2,5,7,A,C;103,9.16.21,B.34,6,8.41,B.56.61,B.85,7.A3,5,7,9.C4,6,8.E1,4,6,8,B.G0,2,6,A,C;226.41,B.B6.F6:7DPWX5O8WM22A28QMSNM5SXKO0RN7ABJEGQJROBGQEGJDFNE0FEC2RFQ8SCW8P0P0KAFK5DBPD7AXNRKXMOWJCGC5S7B", "hasFlower": 0, "hasSeason": 0}, {"id": "115", "q": "001,5,7,B.13,9.20,6,C.32,A.40,5,7,C<PERSON>52,A.60,4,8,C<PERSON>72,<PERSON><PERSON>80,4,8,C.92,A<PERSON>A4,8.B0,2,A,C.C6.D0,<PERSON><PERSON>E3,5,7,9;113,9.20,6,C.40,2,6,A,C.60,C.72,A.A2,4,8,A.B0,C.D0,6,C.E3,9;220,C.36.40,C.72,A<PERSON>A2,A.B0,C.D0,C;320,C.D0,C:1E4GQLJ6HFF2OJWJJK562H3AATBGK13T579ZO9RFPYL4A79QVVDDHKEYZ0596YIBWHIR6D44AEKF5YL0EDPL", "hasFlower": 0, "hasSeason": 0}, {"id": "116", "q": "000,2,5,7,9,C,E.20,2,4,A,C,E.36,8.40,2,4,A,C,E.56,8.60,2,C,E.74,6,8,A.80,2,C,E.95,7,9.A0,2,C,E.B4,6,8,A.C0,E.D2,4,A,C.E0,6,8,E;101,D.40,3,6,8,B,E.71,4,A,D.A0,2,C,E.B6,8;243,B.A1,D:VDTHSJKE9FFNJNNIVDDCYV2KM9I1DPQBIO703JPNWVY6A1AKIW13TK26M1QOSE7JCCL4H0L4C0SB0S", "hasFlower": 0, "hasSeason": 0}, {"id": "117", "q": "000,3,5,7,9,B,E.20,4,6,8,A,E.32,C<PERSON>45,7,9.50,3,B,E<PERSON>65,9.70,2,C,E.85,7,9.90,2,C,E.A4,6,8,A.B0,2,C,E.C4,6,8,A.D1,D<PERSON>E3,5,7,9,B.F0,E.G2,4,6,8,A,C;116,8.32,7,C.55,9.80,5,9,E.B3,6,8,B.D6,8.E3,B.F5,7,9:W2RUTWS6FA2PXACI3SS3VDLA6WF0R0OAO8H3OQPW0TOVTCBBPI380RP6R88SBHHXD6QBU29TEILICX9EC2XH", "hasFlower": 0, "hasSeason": 0}, {"id": "118", "q": "000,3,5,9,B,E.20,3,5,7,9,B,E.40,4,A,E.57.62,4,A,C<PERSON>70,6,8,E.82,C.90,5,7,9,E.A2,C.B4,6,8,A<PERSON>C0,<PERSON><PERSON>D4,<PERSON><PERSON>E0,7,E<PERSON>F3,5,9,B.G1,D;104,A.20,3,B,E.40,E.64,A.95,9.B4,6,8,A.D0,E<PERSON>E4,7,A;204,A.95,9.B6,8.E4,7,A;304,A.95,9.B7.E7:KERUYSHLWSNLYWRTZGDX3GW2H8YH5GT6Z2UKXX5R73G6HK9S78ZK3JTTR7NDUEYU9282X953S6DJ9DN678N5ZWLL", "hasFlower": 0, "hasSeason": 0}, {"id": "119", "q": "000,2,4,7,A,C,E.20,2,4,6,8,A,C,E.41,3,5,7,9,B,D.63,5,7,9,B.70,E.82,4,6,8,A,C.A1,4,A,D.B6,8.C0,2,4,A,C,E.D6,8.E0,2,4,A,C,E;101,7,D.14,A.20,7,E.34,A.41,6,8,D.54,A.66,8.70,3,B,E.A1,4,A,D.B6,8.C1,4,A,D.D7.E1,4,A,D:8XTZSYLWPC3F72DPX8R400J24WWGOA87R5O1Q24WBYJJK7T2LXZ0M9P7CQRKMSDTF4XTZBSPUAOZ9AGK10R8UJO3A5KS", "hasFlower": 0, "hasSeason": 0}, {"id": "120", "q": "001,5,7,B.13,9.20,5,7,C.33,9.41,5,7,B.53,9.60,5,7,C<PERSON>72,A.84,6,8.90,C<PERSON>A2,4,8,A.C0,3,5,7,9,C.E0,2,4,6,8,A,C;115,7.33,5,7,9.53,9.A3,9.C3,5,7,9.E0,5,7,C;215,7.36.C6.E5,7;315,7.E5,7;416.E6:CP699560TR8SUC6IU8GAO10L1DC0I39GZT2Z1O47CHLAZ3P4J3OS257F32V2DOZ9V0HPPRFTT61J", "hasFlower": 0, "hasSeason": 0}, {"id": "121", "q": "000,2,5,7,9,C,E.30,2,4,6,8,A,C,E.50,2,6,8,C,E.64,<PERSON><PERSON>71,D.85,9.92,C.A0,6,8,E.B4,A.C0,2,6,8,C,E.E0,2,4,7,A,C,E;102,5,7,9,C.30,4,6,8,A,E.50,2,C,E.85,9.A0,E.B7.C0,E.D2,C<PERSON>E0,7,E;202,6,8,C.37.40,E.52,C.B0,7,E.D1,D.E7;306,8.40,E.B0,E:6JTLPTGUU3VX0A31PSJJR3SRI0DXIA3T71PRBD627TQOQGGPKD1SU1V2UOMB20SRJ9BBXQA0LVKVILQ97LDK9IOM9X7AKGO2", "hasFlower": 0, "hasSeason": 0}, {"id": "122", "q": "000,2,7,C,E.15,9.22,7,C<PERSON>34,A<PERSON>41,<PERSON><PERSON>53,5,9,B.60,7,<PERSON><PERSON>72,<PERSON><PERSON>84,6,8,A.91,D.A3,6,8,B.B0,E.C2,4,6,8,A,<PERSON>.E4,A.F2,6,8,C.G0,E;102,7,C.15,9.22,7,C.41,4,A,D.72,7,C.84,A.91,7,D.A3,B.C2,4,7,A,C.F2,7,C;217.84,A.C7;3C7;4C7;5C7:EQVZ5XOUWKTJT656I7IUPMPVK048MIKNWW6R5KTZQJ0WPVJQ8I7URO7X0NJ5E40NX6RUO884VOTQER4NPXEMM7", "hasFlower": 0, "hasSeason": 0}, {"id": "123", "q": "000,3,5,9,B,E.17.20,3,B,E<PERSON>36,8.41,3,B,D<PERSON>55,7,9.61,D<PERSON>74,7,A.80,E<PERSON>95,7,9.A0,2,C,E.B4,6,8,A.C1,D.D3,5,9,B.F0,3,6,8,B,E;100,E.13,B.27.33,B.51,5,9,D.67.74,A<PERSON>87.95,9.A2,7,C.C1,5,9,D.E3,B.F6,8;223,B.55,9.67.74,A.A7.C5,9.E3,B.F6,8;3F6,8:GAPT2S27I1PYJC2LPDCL3MGSMG80UB016TUJ6VD12VG80F3U1OLJJMWXK5VD7UHBYDVXPLFAORKIK63BSMWH3B858SR06K", "hasFlower": 0, "hasSeason": 0}, {"id": "124", "q": "000,2,4,7,A,C,E.20,3,5,7,9,B,E.42,4,A,C.50,6,8,E.63,B.70,6,8,E.82,C.90,5,7,9,E.B1,3,5,7,9,B,D.D2,4,A,C.E6,8.F0,3,B,E.G5,7,9;113,7,B.34,A.53,6,8,B.60,E.80,2,C,E.B1,3,B,D.F6,8;260,E.81,D:A8KWMUCV8RQYFYPKM0PPG5UUTSVK0YWQSMAQTF2LBK727WWB1LM5Q0C0824XV83GBSRSPXU32FFVYB41", "hasFlower": 0, "hasSeason": 0}, {"id": "125", "q": "000,2,4,8,A,C.20,2,4,6,8,A,C.40,2,4,6,8,A,C.60,2,4,8,A,C.76.80,3,9,C.95,7.A2,A.B0,4,6,8,C.D0,2,4,6,8,A,C;100,2,4,8,A,C.22,4,6,8,A.41,3,6,9,B.60,2,4,8,A,C.80,3,9,C.96.A2,A.B6.C4,8.D2,6,A:49TVN50V7NEI7J2WD0R95Y13B8MWHBJH43NRB3I534W7PL1DJ8EPGX58TGN9MY2LT4GJWEYY287XGET9B2", "hasFlower": 0, "hasSeason": 0}, {"id": "126", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.42,4,6,8,A,C.65,7,9.85,7,9.93,B.A0,5,7,9,E<PERSON>B2,<PERSON><PERSON>C4,6,8,A.E5,7,9.G3,5,7,9,B.I2,4,6,8,A,<PERSON><PERSON>J0,E.K2,4,6,8,A,C;100,2,5,7,9,C,E.23,7,B.42,4,6,8,A,C.65,7,9.85,9.A5,9.C5,9.E5,9.F7.H4,6,8,A.J3,7,B:O6GE5D64AK8CVW1JJAFDG1KON8QEIF0XWVF4ZLZNPNIPWMJ7Z5FGVKX37PO4DZCA7V63KLN5D36MAEC480OEQP7J00W5GC8M3M", "hasFlower": 0, "hasSeason": 0}, {"id": "127", "q": "000,5,7,C.13,9.21,5,7,B.33,9.40,5,7,C<PERSON>53,9.70,2,6,A,C<PERSON>91,5,7,B.A3,9.C0,3,5,7,9,C<PERSON>E0,2,6,A,C<PERSON>G3,5,7,9;105,7.13,9.25,7.33,9.40,C.70,2,A,<PERSON><PERSON>B3,9.C0,6,C<PERSON>E1,B.F6.G3,9;213,9.26.33,9.40,C.70,C<PERSON>B3,9.C0,C.F6.G3,9;323,9.40,C.70,C;423,9;523,9:PPXKRF7SYJRDXDY9WAHSTNPYMTXUKHW9DQH0UO7DJFLO58Q04U4XT58FR0YCM6OLVM7PL7ORFCNTQU06SMQNLSWV6NHWA6", "hasFlower": 0, "hasSeason": 0}, {"id": "128", "q": "000,2,4,6,8,A,C,E,G.22,4,6,A,C,E.30,8,G.43,5,B,D<PERSON>51,7,9,F.65,B.70,2,7,9,E,G.84,C.90,6,8,A,G.A2,4,C,E.B0,7,9,G.C2,4,C,E.D0,6,8,A,G.E2,4,C,E.F6,A.G0,4,8,C,G;115,B.30,G.67,9.70,G.87,9.95,B.A0,G.B7,9.C0,3,D,G.D6,A.F4,C.G8:D33RCCU6SNVKCDRT0S83G2OKU41LHIWJGHXGNS83AWRTD629WOJICKSM1LDH89VE47VTUB7IQV2MGB2TRWU8IKXAQXHX0E", "hasFlower": 0, "hasSeason": 0}, {"id": "129", "q": "000,3,5,7,9,B,E.22,4,7,A,C.30,E.42,4,7,A,C.60,2,4,6,8,A,C,E.80,2,5,7,9,C,E.A0,2,4,6,8,A,C,E.C1,3,5,7,9,B,D.E0,2,4,6,8,A,C,E.G0,3,6,8,B,E;100,3,6,8,B,E.23,B.31,7,D.53,B.70,E.87.A0,3,B,E.C3,6,8,B.D1,D.E6,8.F0,3,B,E.G7:PPW80FGEX3O2B83QIDCL7VMKH1FBVSMWU9XI2XOC2CO8IHFL4W8UPZLDDQOGPMSIK0S2S314J1KM43CEZDUJ4KWFX719UNLN", "hasFlower": 0, "hasSeason": 0}, {"id": "130", "q": "000,2,4,6,8,A,C,E.21,3,5,9,B,D.40,3,6,8,B,E.60,2,5,7,9,C,E.80,3,6,8,B,E.A2,4,6,8,A,C.B0,E.C2,5,7,9,C.D0,E.E2,4,6,8,A,C.G0,4,7,A,E;101,3,B,D.24,A.50,6,8,E.70,6,8,E.83,B.A2,6,8,C.B0,E<PERSON>C6,8.D2,C.E6,8.F4,A.G7;203,B.76,8.A6,8.D6,8:CU3HCYB3H6P25Q8JADQDDAQJY5GV16KOPPUV2EHVUA8JUOEF32GBN9J6AI8FPKO1NKNGHLVN6Y598L1YBF5LFDICLC32O1GKQB", "hasFlower": 0, "hasSeason": 0}, {"id": "131", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.81,3,5,7,9,B,D.A1,3,5,7,9,B,D.C0,2,4,6,8,A,C,E.E0,2,4,6,8,A,C,E.G0,2,4,6,8,A,C,E.I0,2,4,6,8,A,C,E.K0,2,4,6,8,A,C,E:GKEKZD4P1QS7XQGOF7MLP3KZTNHRZTH9U1EP1W0NQL9OLRFZ1X7FDK3T5BW3ELHOIPMHDIX0UDO5BTFQ74E3SX", "hasFlower": 0, "hasSeason": 0}, {"id": "132", "q": "000,2,4,6,8,A,C.20,3,5,7,9,C.40,2,4,6,8,A,C.60,2,5,7,A,C.80,2,4,6,8,A,C.A0,3,5,7,9,C.C0,2,4,6,8,A,C.E1,3,5,7,9,B.G1,3,5,7,9,B;102,A.16.30,3,6,9,C.51,6,B.75,7.80,C.A5,7.B0,C<PERSON>C6.E3,6,9.F1,B.G3,6,9;230,C.51,6,B.B0,C.C6.F2,A:6NL7TXUU1LO5ITA9TEB6BHRXNYBK8JHJ8UT8Z7L5EJF0G1RPNLHNOAGPXI5BFOAOP9997HGYU2G7AE6RVKP2RJY8V50XZEY6", "hasFlower": 0, "hasSeason": 0}, {"id": "133", "q": "000,2,5,7,9,C,E.21,3,6,8,B,D.41,3,7,B,D.55,9.60,2,7,C,E<PERSON>74,A.81,7,D.94,A.A0,6,8,E<PERSON>B3,B<PERSON>C1,5,7,9,D.D3,B.E0,5,9,E;101,5,9,D.17.21,D.37.41,D.60,2,C,E.74,7,A.A0,E.C2,5,7,9,C;211,D.41,D.77.A0,E.C5,9;311,D;411,D;511,D:7MDWQVA5KED09U7VXLO881W4VM138TL9JKCNUDAFVXUURH35CLEDR6CE7RM8H79FLQTRW6EAAWKJXOCJ0NM49JXK", "hasFlower": 0, "hasSeason": 0}, {"id": "134", "q": "000,5,7,9,E.25,7,9.30,2,C,E.44,6,8,A.51,D.63,5,7,9,B.70,E.83,7,B.90,5,9,E.A7.B0,4,A,E.C2,6,8,C.E0,2,4,6,8,A,C,E.G1,4,6,8,A,D.I0,2,4,7,A,C,E.K6,8;105,9.25,9.30,E.45,9.51,D.63,5,7,9,B.87.95,9.A0,E<PERSON>B4,A<PERSON>C2,7,C.E1,3,5,9,B,D.F7.G1,D.I0,E:GAQSNGZYXY1OBQEKMT4FM7217CY1Z3NH0IE0NIS40VABQ1XPHX3EVJPLFGXPHF47EQGAC4CCMDNHTMLAVK2IDOYFVLLP337IZ0JZ", "hasFlower": 0, "hasSeason": 0}, {"id": "135", "q": "000,2,4,6,8,A,C,E,G.21,5,8,B,F.33,D.41,6,A,F.54,C.60,7,9,G.72,5,B,E.80,8,G.95,B.A0,2,7,9,E,G.B4,C.C0,2,6,A,E,G.D4,C.E0,2,6,A,E,G.F8.G1,3,5,B,D,F;102,4,6,A,C,E.21,F.67,9.70,5,B,G.95,B.A0,G.C0,3,D,G.D5,B.E0,G.G2,4,C,E;221,F.D5,B:DFM6B0YOKYG4WNV9UGFJMCQKAAMA9MXADRBBD543PJ9XGW6O460UVO6RB5WJY0Y47NU87OCGP779K0QKQJP3X38DC35XQCWU5P", "hasFlower": 0, "hasSeason": 0}, {"id": "136", "q": "001,5,7,B.13,9.20,5,7,C.40,2,4,6,8,A,C.60,2,5,7,A,C.80,2,4,6,8,A,C.A0,3,9,C.B5,7.C1,3,9,B.D5,7.F0,2,4,6,8,A,C;101,5,7,B.25,7.41,3,5,7,9,B.65,7.70,2,A,C.86.A0,3,9,C.B5,7.C1,B.D5,7.F5,7;205,7.41,5,7,B.65,7.70,2,A,C.A0,C.B5,7.D5,7:WR2MDAO99DBZC5XUYHW7HJ7ALB3GH25KA0UY0Y6IC6XCDU7XG27Y3A931ZLMSZGGMBXH553ZS6BO2ORDIMWWIJO1CL6IKL900U", "hasFlower": 0, "hasSeason": 0}, {"id": "137", "q": "000,2,4,A,C,E.17.20,2,4,A,C,E.40,3,5,9,B,E.60,3,5,7,9,B,E.81,3,6,8,B,D.A0,2,4,6,8,A,C,E.C0,2,4,A,C,E.E0,2,5,7,9,C,E;103,B.10,E.23,B.30,E.43,B.65,7,9.82,6,8,C.A4,6,8,A.B2,C<PERSON>C0,4,A,E.E0,5,9,E;220,E.66,8.96,8.D0,E;367.97;467.97:GJEQN2L1X60XP8B8TJRLT7CH9LPPN6BBTRJCOEH57TQC7B6GZG90XHQZV2V01VNO06LUNPRJ817UU19CXO52GZ92HZ8ROQUV", "hasFlower": 0, "hasSeason": 0}, {"id": "138", "q": "000,2,5,9,C,E.17.20,2,4,A,C,E.36,8.40,2,4,A,<PERSON>,<PERSON><PERSON>56,8.61,4,A,<PERSON>.76,8.80,E<PERSON>92,4,7,A,<PERSON><PERSON>A0,E<PERSON>B5,7,9.C0,2,C,<PERSON><PERSON>D4,6,8,A<PERSON>E1,<PERSON><PERSON>F3,5,7,9,<PERSON><PERSON>G0,E<PERSON>H2,4,A,C<PERSON>I0,7,E;101,5,9,D.22,C.37.44,A.61,4,A,D.76,8.90,3,7,B,E.B7.C0,2,C,<PERSON><PERSON>D4,A.F4,6,8,A.H2,<PERSON>.I0,E:VTPWTD16C4HU0BX1JP8TUCLHZDMAYQK4YCU59BWPKPQH01YB77I5L54BAT1IEAMJVIAEC677YQ3363ZV9EWUXZ99I56HZ8QWV4E3", "hasFlower": 0, "hasSeason": 0}, {"id": "139", "q": "000,3,B,E.15,7,9.20,E.33,6,8,B.40,<PERSON><PERSON>53,5,7,9,B.61,<PERSON><PERSON>74,<PERSON><PERSON>82,C.95,7,9.A1,3,B,D.B7.C0,3,B,E.D5,7,9.E0,3,B,E;103,B.10,6,8,E.33,6,8,B.40,E<PERSON>54,A.74,A<PERSON>95,7,9.A1,D.B7.C3,B.D5,7,9.E0,3,B,E;203,B.26,8.33,B.74,A.B7.C3,B.D5,9;3B7.D5,9:LP0P6CB5UMOJQB4O2B0GV72RFVPG43C2RG56ZU57Y67RAYMYUJ3GF3VF2JZ0AM7CLAZJCOQAOB4UVP5LYZ0MFQ4LQ6R3", "hasFlower": 0, "hasSeason": 0}, {"id": "140", "q": "000,3,5,7,9,B,E.20,2,5,9,C,E.37.40,2,4,A,C,E.56,8.60,2,C,E.74,6,8,A.80,E.93,6,8,B.A1,D.B3,5,9,B.C7.D0,2,4,A,C,E.E6,8.F0,2,C,E.G4,6,8,A;100,3,7,B,E.15,9.20,E.37.50,6,8,E.62,C.70,4,A,E.86,8.A1,3,B,D.B5,9.E7.F0,E.G5,9;207.56,8.70,E;307:1Q23HG2MO10Y2GBBEMXFHAPB95AK83I82IEZUGXLUXY45I45KIEYZ3FPQ8ZX9MBGK04PK9QE4LOO0LHZ53PULUMFHFA9QY0OA8", "hasFlower": 0, "hasSeason": 0}, {"id": "141", "q": "000,6,8,E.12,4,A,C.20,6,8,E.32,<PERSON><PERSON>44,6,8,A.52,C.60,4,6,8,A,E.80,5,7,9,E.93,B.A5,7,9.B1,3,B,D.C6,8.D0,2,4,A,C,E.E6,8;100,6,8,E.12,C.26,8.43,7,B.60,E<PERSON>76,8.93,B.A6,8.B3,B.C1,6,8,D.D3,B.E7;212,6,8,C.47.60,E.A3,6,8,B.D3,B.E7:HCJH3G3S5QJKNGP6MYLWS4Z7CO3O98ZAGEYUO5KMLP6U93TLR6SJ8WIAPF8E8IQYOXLREE7M7C47KPFG6J1SMYKCXNT1", "hasFlower": 0, "hasSeason": 0}, {"id": "142", "q": "000,2,5,9,C,E.17.20,2,4,A,C,E.37.40,3,5,9,B,E.57.60,2,C,E.75,9.80,2,C,E.97.A1,3,5,9,B,D.B7.C0,2,C,E.D4,6,8,A.E0,E;101,D.17.20,2,4,A,C,E.37.43,B.50,E.72,C.A2,6,8,C.C0,7,E.D4,A;211,7,D.23,B.37.43,B.C7;317:TVSCGRHQEEXKR6B0AHS46QPCXLXKHV5S30FQCKV65A4B5E3GREKGQLAPILFLJFCJG6FXV5A0S30T3IHTRTBB", "hasFlower": 0, "hasSeason": 0}, {"id": "143", "q": "001,3,5,7,9,B.20,3,6,9,C.40,2,4,6,8,A,C.60,3,6,9,C.80,2,5,7,A,C.A0,3,5,7,9,C.C0,2,5,7,A,C.E1,3,5,7,9,B;101,6,B.23,6,9.40,5,7,C.66.81,5,7,B.A4,6,8.B0,C.C2,5,7,A.E4,8;281,5,7,B.A4,8;386;486;586:JXASGPHK4CDEX6MHDPNEND6KS4ND6C4KCSVE9UMCGGUHBQQQAPLSMA8NFIBJV0HF6MPG4VVF0EIF8A9LKQ", "hasFlower": 0, "hasSeason": 0}, {"id": "144", "q": "000,2,4,A,C,E.17.20,2,C,E.34,6,8,A.40,2,C,E.54,6,8,A.60,2,C,E.80,2,4,A,C,E.A2,C.B0,5,9,E<PERSON>C3,7,B.D0,E<PERSON>E3,5,7,9,B.F0,E.G2,7,C;100,3,B,E.21,7,D.42,6,8,C.54,A.60,2,C,E.82,4,A,<PERSON><PERSON>A2,C.B0,5,9,E.C7.E6,8;221,D.42,6,8,C.84,A.C7;347.84,A:4ZNOB95RMSNW95NQ3TKHC2B198D8MC6OPX921QCI26DZUA051KWTZUR1MD3EPXKR7DFWI05AB40HA0ZS2REKA47CSBS4MNWF", "hasFlower": 0, "hasSeason": 0}, {"id": "145", "q": "000,2,4,7,A,C,E.20,3,5,7,9,B,E.40,2,4,6,8,A,C,E.60,3,7,B,E.75,9.80,7,E.93,B.A1,7,D.B4,A.C0,2,6,8,C,E.D4,A.E0,2,6,8,C,E;101,3,7,B,D.24,6,8,A.30,E.42,5,9,C.70,5,9,E.A1,D.C0,2,5,9,C,E;201,D.26,8:ZFPRDYL091PZLDPWWCM1R0EN3UDUAWLMHZANXLEDXCAMHFY144MCY5959EYA53UEX51O0ZOUWN93NPCXFF30", "hasFlower": 0, "hasSeason": 0}, {"id": "146", "q": "000,2,6,A,C.21,4,8,B.36.42,A.50,4,6,8,C<PERSON>71,3,5,7,9,B.91,3,9,B.A5,7.B3,9.C0,5,7,C<PERSON>D2,<PERSON><PERSON>E4,6,8.F1,B.G3,5,7,9;100,2,A,C.24,8.36.54,6,8.72,4,8,A.92,A<PERSON>A4,8.C5,7.E5,7.G3,9;200,C.24,8.36.56.72,A.C6.E6;300,C.36.D6;400,C;500,C:8SCC6ZZNR94LEGBPGV1BO8URJ4OC9L3GRVAHJZPN2FDUSKSFPD8KLUH6JPEK9129A7GJ8FU35ZSRKD1B1BCD5LF7", "hasFlower": 0, "hasSeason": 0}, {"id": "147", "q": "000,2,4,6,A,C,E,G.18.20,2,5,B,E,G.37,9.41,5,B,F.53,D.60,6,8,A,G.72,4,C,E.90,2,4,6,A,C,E,G.B1,3,5,B,D,F.C7,9.D0,2,4,C,E,G.E6,A.F1,8,F.G3,5,B,D.H0,7,9,G.I3,D;111,5,8,B,F.31,8,F.53,D.60,8,G.72,E.84,C.92,E.A4,C.B2,E.C8.D0,G.F8.H3,7,9,D:SXSVWR8QEW7KX809EU3RGB1RXIQZEPFSMN80MUHU705F1PX7ZUN3SV1QYR0ZEIDV3PB9VIYD8FKFBGBQIWHH1MWMKHDGP3ZGKD57", "hasFlower": 0, "hasSeason": 0}, {"id": "148", "q": "001,4,6,8,A,<PERSON><PERSON>22,6,8,C.34,<PERSON><PERSON>40,<PERSON><PERSON>54,6,8,A.60,2,<PERSON>,<PERSON><PERSON>76,8.80,E.93,5,9,B.A0,E<PERSON>B5,7,9.C0,2,C,<PERSON><PERSON>D4,7,<PERSON><PERSON>E0,<PERSON><PERSON>F3,5,9,B.H2,6,8,C.I4,A;101,7,D.50,4,A,E.62,7,C.80,E.93,5,9,B.B0,5,9,E.C7.D4,A<PERSON>E0,E<PERSON>F5,9;250,E.62,7,C.C7.E0,E;362,C.C7;462,C:TALIBWEM09UHXKI15WL5LI2RTK9RV2UVTVHHAWMS3OYLXKAE9MY23S5FFAESX1TY7SE91WO37XOY7KIFH725RFB0M3O1VR", "hasFlower": 0, "hasSeason": 0}, {"id": "149", "q": "000,3,5,9,B,E.21,3,5,7,9,B,D.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.80,2,4,7,A,C,E.A1,3,5,9,B,D.C0,2,C,E.D4,6,8,A.E0,2,C,E.F4,6,8,A.G0,2,C,E;113,B.21,D.35,9.40,2,C,E.64,A.70,E<PERSON>91,D.D0,2,C,E.E5,9.F1,D;235,9.E5,9:BF1UPC7R741EC8IIZW6YBZZ9F19OJBZ6C6XEG0UYLEAPWTL09090Y8JAOXCR4XIMML18YM7GWWL4TPE8APBA7X46IM", "hasFlower": 0, "hasSeason": 0}, {"id": "150", "q": "000,2,4,6,8,A,C,E.20,2,4,7,A,C,E.40,2,4,A,C,E.56,8.60,3,B,E.75,7,9.80,2,C,E.94,6,8,A.A0,2,C,E.B5,7,9.C0,2,C,E<PERSON>D4,6,8,A.E2,<PERSON><PERSON>F0,4,A,E.G2,6,8,C.H0,4,A,E.I6,8;100,E.34,A.56,8.75,7,9.92,C<PERSON>C5,7,9.G4,A;256,8.76,8.C6,8;357:0WBHS7GS5WRPC349ILBPTURS7B8TI3M0TGVLW6ZQZ5P81BP1HTR96M1S3U9LZ84HG45QR948M6CZHLWG73Q7QM165CVC", "hasFlower": 0, "hasSeason": 0}, {"id": "151", "q": "000,2,5,7,9,B,E,G.20,2,4,6,8,A,C,E,G.41,3,5,7,9,B,D,F.60,2,4,6,8,A,C,E,G.80,2,4,6,8,A,C,E,G.A0,2,4,6,8,A,C,E,G.C0,2,4,6,8,A,C,E,G.E0,2,4,6,8,A,C,E,G.G0,2,4,6,8,A,C,E,G.I0,2,4,6,8,A,C,E,G:R6M5LNW1IOIVHB8HIR8SMVKL7Z8L1YO0NVRB0AW5ABM6XWS7M0SZ8A6RL5N110YKXVQKBHYXOHQKIW6X5OYNAQSQ", "hasFlower": 0, "hasSeason": 0}, {"id": "152", "q": "000,2,5,7,9,C,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.81,3,5,7,9,B,D.A0,3,5,7,9,B,E.C1,5,7,9,D.D3,B.E0,5,9,E;120,2,4,7,A,C,E.40,E.52,6,8,C.60,4,A,E.76,8.94,7,A.C1,6,8,D.D4,A:2O6OXCYWPX35553YU2TIQOTWVS3WOUX1VZF2QYWPYTF9Z9C6SRIRSVGC6X12SCR36TUVGGF11PIR9IP5FUG9", "hasFlower": 0, "hasSeason": 0}, {"id": "153", "q": "001,4,6,8,A,D.20,2,4,6,8,A,C,E.40,2,C,E.54,6,8,A.60,2,C,E.74,6,8,A.80,2,C,E.94,7,A.A0,E<PERSON>B2,4,A,C<PERSON>C0,6,8,E.D2,C.E0,4,6,8,A,E.F2,C.G0,4,6,8,A,E;101,D.14,A.21,D.40,E.53,B.66,8.71,4,A,D.94,A.B2,4,A,C.D0,7,E.F1,4,A,D;271,D.F4,A:QKFEN2OHE78FTH5LG7VVVHUG8YAL935QPDP75F4ASYGD6NVX17FR2KYP1G9O4N2UAPQ4TA3LE5RLHT9D2T8Y6SDEX49QNR8R", "hasFlower": 0, "hasSeason": 0}, {"id": "154", "q": "000,2,5,7,9,C,E.20,2,5,9,C,E.37.40,2,4,A,<PERSON>,E.56,8.62,C.70,5,7,9,E<PERSON>83,<PERSON>.95,9.A0,2,7,C,<PERSON>.B4,A<PERSON>C1,6,8,<PERSON><PERSON>D3,B.E0,5,9,E;100,2,5,9,C,E.22,5,9,C.30,7,E.44,A.52,6,8,C.77.A2,7,C.B4,A<PERSON>C7.D3,B.E0,5,9,E:JO6X5Q1BU26R26HY1BIGE5ZIEQ44IAZ41UMHQJZ63SBJJOXH70HLYZISS3L7PGRM0DPSU13AB37UQ7EED4", "hasFlower": 0, "hasSeason": 0}, {"id": "155", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.41,3,5,9,B,D.57.60,2,4,A,C,E.76,8.80,2,4,A,C,E.96,8.A0,2,4,A,C,E.B7.C1,3,5,9,B,D.D7.E0,3,B,E.F5,9.G0,2,7,C,E.H4,A.I0,2,6,8,C,E;103,6,8,B.33,B.61,D.85,9.92,C.F0,E;203,B:WM2XOB28JXYZYCYK45Q764PLSR6HJO6O9ZPKN1R9914O1VBC2SHYWEZKJBECKED6P4BEZRSRJ7PNL9V1DCSM825Q", "hasFlower": 0, "hasSeason": 0}, {"id": "156", "q": "000,3,5,7,9,C.21,3,5,7,9,B.42,4,6,8,A.60,2,4,6,8,A,C.80,4,6,8,C.A1,3,5,7,9,B.C1,3,5,7,9,B.E1,4,8,B.F6.G1,4,8,B;100,C.13,5,7,9.21,B.34,8.52,4,6,8,A.75,7.80,C.A5,7.C2,4,6,8,A.E4,8.F1,6,B;216.53,9.C3,9;316;416:3DHOW4FIV32ISSXLFOAH2XLZYLWF4ANTO3YNUZUL4HYCIKC1GCTGKWA4XO3UDVYDIA1TZSKUZXNNVWHC1KSDFTV1", "hasFlower": 0, "hasSeason": 0}, {"id": "157", "q": "000,3,6,9,C.21,3,5,7,9,B.40,2,4,6,8,A,C.62,4,6,8,A.70,C.82,4,8,A.96.A0,2,4,8,A,C.B6.C0,2,4,8,A,C.D6.E0,2,4,8,A,C;103,6,9.23,5,7,9.40,4,8,C.52,6,A.64,8.71,B.83,9.96.A0,2,4,8,A,C.C2,6,A.E2,4,8,A:HM97U0DMWLQ95BVLQOM1R80EB4IUUY5QIQLU41E19MY7IOWI8V5HE8CCYK3WYS93VDSR0OWED50VCD8CKOL1", "hasFlower": 0, "hasSeason": 0}, {"id": "158", "q": "000,3,5,9,B,E.17.20,2,4,A,C,E.36,8.43,B.50,5,7,9,E<PERSON>62,<PERSON><PERSON>76,8.80,3,B,E.96,8.A1,3,B,D.B6,8.C0,3,B,E.D5,7,9.E0,2,C,E.F4,7,A.G0,E;103,5,9,B.10,7,E.24,A<PERSON>36,8.50,5,9,E.62,C.76,8.80,3,B,E<PERSON>96,8.A2,C.B6,8.C0,3,B,E.D5,7,9.E0,E.F4,A.G0,E:RWGXSMUCGF66P9W661J2N12TCBP4HJRBGPV2X4RU7QSHWA18XX1N9ZBPR87H9BJTCMTMZZNZJW2FCTS7UAHMGSFV7A9UNAFQ", "hasFlower": 0, "hasSeason": 0}, {"id": "159", "q": "000,4,6,A,C,G.12,E.20,5,7,9,B,G.41,7,9,F.61,3,6,8,A,D,F.80,2,4,8,C,E,G.A0,2,4,6,8,A,C,E,G.C2,7,9,E.E0,7,9,G.F5,B.G2,7,9,E;104,6,A,C.11,F.25,7,9,B.48.51,F.66,8,A.72,E.84,C.91,F.A4,6,A,C.B2,8,E.D8.E0,G.F5,7,9,B;215,B.27,9.A5,B.E8;3A5,B:941ULDXL8902TDZJHDJJN745EP4V65P0UNZSTRH6U201AHEJB2BSVU36BEV6NX8PA74PX0XEDB28T3TS9A37RRV59HN358AZSR7Z", "hasFlower": 0, "hasSeason": 0}, {"id": "160", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,3,5,7,9,B,E.60,2,4,6,8,A,C,E.80,2,4,6,8,A,C,E.A0,2,4,6,8,A,C,E.C0,2,4,6,8,A,C,E.E0,2,4,6,8,A,C,E.G0,2,4,6,8,A,C,E.I0,3,5,7,9,<PERSON>,<PERSON>;105,9.30,E.54,A.74,<PERSON><PERSON>C4,<PERSON><PERSON>E4,<PERSON><PERSON>G0,E.I5,9:RYIC67CK52M35FFX9SIXIB0CD350AHAP2BOFT0Z2S4UTVZXV0BC4M16TQE3EOH91GTRKYH5UAMGQ3H97BSOAOXPSFI9M2D", "hasFlower": 0, "hasSeason": 0}, {"id": "161", "q": "002,7,C.10,5,9,E<PERSON>23,B.31,5,7,9,D.50,2,6,8,C,E.64,A.70,2,7,C,E.85,9.90,2,7,C,E.B0,2,4,7,A,C,E.D3,B<PERSON>E0,5,7,9,E;102,7,C.10,5,9,E.35,7,9.41,D.56,8.60,3,B,E.81,D.97.A0,E.E0,5,7,9,E;215,9.36,8.56,8.60,3,B,E.81,<PERSON>.E5,9;315,9.36,8.E5,9;415,9.E5,9:LXL26NG1564KXSA1804J3K8JSUDW5Q2IHYSAH5954ACGDQSWB0YII381YNAW8UP24QK13UYPG2KG9C9DU6B9DH6H0BP3QIBC0PCW", "hasFlower": 0, "hasSeason": 0}, {"id": "162", "q": "000,2,4,7,A,C,E.20,2,4,7,A,C,E.41,3,5,7,9,B,D.61,3,6,8,B,D.81,3,5,7,9,B,D.A0,2,7,C,E.B5,9.C0,2,7,C,<PERSON><PERSON>D4,A<PERSON>E2,6,8,C.F4,A<PERSON>G1,6,8,D.H3,B.I0,6,8,E;100,E.13,B.31,7,D.61,3,6,8,B,D.85,9.91,D.B5,9.C1,<PERSON>.D3,<PERSON><PERSON>E5,9.G1,7,D.I0,6,8,E:IBRNMIV1TSJ9FKHZC0JZTRT4G0Z7077CS3H0K3CMID3HF3IMBGVMVGEEJNRCNDU14U7QSGQU1ZKB4RUE4TDFJV1KNEFHDBS9", "hasFlower": 0, "hasSeason": 0}, {"id": "163", "q": "000,3,7,B,E.15,9.20,2,C,E.34,6,8,A.41,D.53,5,7,9,B.60,E.72,4,7,A,C.80,E.92,5,9,C.A0,7,E<PERSON>B4,A<PERSON>C0,2,6,8,C,E.D4,A.E1,6,8,D;100,3,B,E.20,2,C,E.34,6,8,A.53,5,7,9,B.60,E.72,4,A,C.90,2,5,9,C,E.A7.B4,A.C0,6,8,E.E1,6,8,D:LYR6JARSVYMM08C8TOO9VBI1HES6F9DI52QXFFEMI8VFS2OROPWKVTQMYW2WJRW1AQYKB8EHI3S1GHC3EPQDXH1L520G", "hasFlower": 0, "hasSeason": 0}, {"id": "164", "q": "002,4,6,8,A,C<PERSON>10,E.22,5,7,9,C<PERSON>43,5,9,B.51,D.64,6,8,A.70,2,C,E.84,7,A.90,2,C,E.A6,8.B0,4,A,<PERSON><PERSON>C6,8.D3,<PERSON><PERSON>E5,7,9.F0,2,C,E.G4,6,8,A;103,7,B.11,5,9,D.43,B.61,4,A,D.77.81,4,A,D.97.B4,A<PERSON>C6,8.D3,B.F0,2,C,E.G4,6,8,A;211,D.61,D.87.C6,8;311,D.87:WANI0YIFW7U0ZZFZ20NA4OJCWX29ARQQA8Z4NQ899R72U4YE2OEH0FS3SH3YCIJ8Y3EKXRJFHQC73JKCU4UXIXOS87WN9SREOH", "hasFlower": 0, "hasSeason": 0}, {"id": "165", "q": "000,2,4,6,8,A,C,E.20,2,5,7,9,C,E.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.80,2,5,9,C,E.97.A0,2,5,9,C,E.C0,2,4,6,8,A,C,E.E0,2,4,6,8,A,C,E.G0,2,4,6,8,A,C,E.I0,2,4,A,C,E;102,C.16,8.22,C.56,8.64,A.80,E.B0,E.C3,B.E6,8.G2,C:8FY3HW8HMWUV0NCMQ6EVZ3EBBFZOGPRLR0NXEUMBR1KN3POY3FN81Z0PWL0YMLGGIQPCE118XHY46BZHCI4UCUGQWLQRFK", "hasFlower": 0, "hasSeason": 0}, {"id": "166", "q": "000,3,7,9,D,G.20,3,5,7,9,B,D,G.41,4,6,8,A,C,F.62,4,6,8,A,C,E.80,2,4,7,9,C,E,G.A1,3,5,7,9,B,D,F.C0,3,5,7,9,B,D,G.E1,5,7,9,B,F.F3,D.G1,F;103,D.20,3,D,G.36,8,A.44,C.58.63,D.80,2,4,7,9,C,E,G.A2,E.B4,C.C8.D5,B.E8.F3,D;203,D.20,G.44,C.58.63,D.B4,C.C8;358.C8:Y5FFY5T6M866O0RVBYOZVOND5MW6X29US3AA8AXARN9KKSTZ20V240VJDNZWQYT8WKN9S2D91W4J35FQOBS3BQ04QRRXUX4M8UUTBK1D3ZFM", "hasFlower": 0, "hasSeason": 0}, {"id": "167", "q": "003,5,7,9,B.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,4,6,8,A,E.81,4,7,A,D.A1,6,8,D.B4,<PERSON><PERSON>C0,7,<PERSON><PERSON>D4,<PERSON><PERSON>E0,2,6,8,C,E.F4,A<PERSON>G0,2,C,E.H4,6,8,A;106,8.13,B.21,6,8,D.40,2,7,C,E.66,8.81,4,A,D.B4,A.D7.E0,<PERSON><PERSON>F2,<PERSON><PERSON>G0,<PERSON><PERSON>H6,8;231,7,D.66,8.81,<PERSON>.E0,E;381,D:O8HSU5A3QPG7P825BAJA0CLO8WZ2Z1T0MKJYXX4S5O1TLFUTU0ENY2PF7CCMFSL1M36FNE6SPQQKB5L2GMQ3034UWT1O8HJJ6A6C", "hasFlower": 0, "hasSeason": 0}, {"id": "168", "q": "001,3,5,7,9,B.23,5,7,9.40,2,4,6,8,A,C.62,4,6,8,A.70,C.83,6,9.A4,8.B0,2,6,A,C.C4,8.D6.E0,2,4,8,A,C;102,4,8,A.24,6,8.42,4,8,A.56.62,4,8,A.B2,4,8,A.D4,6,8;225,7.42,4,8,A.62,4,8,A.B2,4,8,A.D5,7;344,8.B4,8;444,8.B4,8:9W9HT0CA5BYUFVTHSFK568D8FS86V0M54QQ4QKUNS5BT64YPPK6D7FMW9YM7UWH4CTCHNDBPDNMW0CA0PKYSQ9UNB8", "hasFlower": 0, "hasSeason": 0}, {"id": "169", "q": "000,2,4,7,A,C,E.20,2,5,9,C,E.37.40,2,C,E.54,7,A<PERSON>60,2,C,E<PERSON>74,6,8,A.80,2,C,<PERSON><PERSON>94,<PERSON><PERSON>A1,D<PERSON>B3,5,7,9,B.C1,D<PERSON>D3,5,7,9,B<PERSON>E0,E<PERSON>F2,7,<PERSON><PERSON>G5,9.H0,2,C,E.I6,8;100,3,B,E.21,D.37.40,E.52,7,C.70,E<PERSON>82,4,A,C.A1,4,A,D.D7.E0,E.F7.H1,D.I6,8;203,B.47.52,<PERSON>.A1,D.E7:GNZKZB10C5TA693HPYAPE08H9YSCGEZ0N590J5Z6KSN1SACD3IHEPMQTBBYNCBHT6773S7Y9GP4A4QKEJD4FF4V5M378KV6MGMTI", "hasFlower": 0, "hasSeason": 0}, {"id": "170", "q": "004,6,8,A.10,E<PERSON>22,5,7,9,C<PERSON>30,E<PERSON>43,5,7,9,B.50,E<PERSON>65,9.70,2,7,C,E.85,9.90,E<PERSON>A5,9.B3,7,<PERSON><PERSON>C0,5,9,E<PERSON>D2,7,<PERSON><PERSON>E0,5,9,E;104,7,A.10,E.22,6,8,C.40,4,6,8,A,E.60,5,9,E<PERSON>72,C.85,9.90,E.A5,9.B7.C5,9.D0,2,7,C,E;210,E.44,6,8,A.60,E<PERSON>72,C.90,E.B6,8:89HAGMOHQANGFEVZM69791YYRZTZA0ILE45DWIDTB4C1SG9KAE86FRLN7E1SBG445SKO8TKSIBKXLBC1V8LT055INXRZWQNR", "hasFlower": 0, "hasSeason": 0}, {"id": "171", "q": "000,3,5,7,9,B,D,G.20,2,5,7,9,B,E,G.40,2,4,7,9,C,E,G.60,3,5,7,9,B,D,G.80,2,5,8,B,E,G.A0,3,5,7,9,B,D,G.C1,3,5,7,9,B,D,F.E0,2,4,6,8,A,C,E,G.G0,2,4,6,8,A,C,E,G;106,8,A.28.43,D.60,3,D,G.80,5,B,G.B3,D.D3,D.E6,A.F8.G4,C;260,3,D,G.85,B.B3,D:BBA8HOD30O180XK70ZXUJDG7HP6Z0YCY5CP6P5M6NHDXYKU5AV4TEGMO89UNCLZ4AJNEVTXS3UJAV13B3VRTN56GRLPJBOSGCTZ8Y9DH", "hasFlower": 0, "hasSeason": 0}, {"id": "172", "q": "000,3,5,7,9,B,E.21,3,5,9,B,D.37.40,2,4,A,C,E.56,8.60,E<PERSON>74,6,8,A.80,2,C,E<PERSON>94,7,A.A1,D.B7.C1,4,A,D.D6,8.E0,3,B,<PERSON><PERSON>F5,9.G1,7,D;106,8.13,B.31,D.43,7,B.60,6,8,E<PERSON>74,A.80,7,E.94,A.A7.C4,7,A<PERSON>E0,E<PERSON>G1,D;213,B.31,D.43,B.60,6,8,E.84,A.B7.G1,D;384,A:5W8W30PV1RAOOJUWBZVCNF08GGS1NTTOBJM1OPFZUU46GPSS44W6BM20RAQ6SQQCTT1B3ZQ5D32DGJX0XND4NAR3DRCJU6PZAC", "hasFlower": 0, "hasSeason": 0}, {"id": "173", "q": "000,2,5,7,9,C,E.20,3,7,B,E.35,9.41,7,D.53,5,9,B.60,7,E<PERSON>72,5,9,C.87.91,3,B,D.A5,9.B0,2,7,C,E<PERSON>C5,9.D1,7,D.E3,5,9,B.F0,7,E.G2,5,9,C;100,2,5,7,9,C,E.27.41,6,8,D.53,B.65,7,9.82,7,C<PERSON>A1,5,9,D.B7.C1,5,9,D.D7.E3,B.F5,7,9;200,E.27.41,D.87.F7:GP7WLE7B8FWJI1X0HG0ZF4AULTTKBWUH4BOQ1HXZG1FPKNNTLXDJEQ4OEJKDOANKHZ8QBPIN1A7T4UWZXE7U8IQ8AOGIFL0PJ0", "hasFlower": 0, "hasSeason": 0}, {"id": "174", "q": "000,5,7,9,E.12,C<PERSON>24,6,8,A.30,2,C,E<PERSON>44,6,8,A.50,2,<PERSON>,E<PERSON>66,8.71,3,B,D.85,7,9.90,3,B,E.A7.B1,3,5,9,B,D.C7.D0,2,C,E<PERSON>E5,7,9.F0,2,C,<PERSON><PERSON>G4,7,A.H2,C.I0,6,8,E;122,7,C.30,E.44,A.61,D.83,5,9,B.B3,5,9,B.D1,D.H2,7,C.I0,E:BYL13M2X7SBIJ61HRF0ZSRKFHWEZ9P66AHEQILQLEN3FRFSMNP650DHX71ZWWQDNB5ANESX21BLQYRJ7YW9YK7XZ", "hasFlower": 0, "hasSeason": 0}, {"id": "175", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.47.50,2,5,9,C,E.67.70,2,4,A,C,E.90,2,4,6,8,A,C,E.B0,2,5,9,C,E.D1,3,6,8,B,D.F7.G0,3,5,9,B,E.H7.I0,2,5,9,C,E;101,3,6,8,B,D.21,4,A,D.50,7,E.71,D.97.B0,E.D6,8.G4,A.I0,E;201,D.21,D.71,D.97.D7:ZHGCJJRFL1OACM92MORGO2MNSKZ3JN7BLGWAAFROBBWFNWQCW9XN31LKIQ7HSI1K92FKQBH3A3G9LCRXZMJ7T1ZXVHTV72XQ", "hasFlower": 0, "hasSeason": 0}, {"id": "176", "q": "001,6,B.13,9.20,C<PERSON>34,6,8.40,2,A,<PERSON><PERSON>55,7.60,2,A,C<PERSON>74,6,8.80,C.92,A.A5,7.B0,2,A,<PERSON>.C4,6,8.D0,2,A,<PERSON>.E4,8;113,9.20,C.35,7.40,C.52,6,A.60,C.74,8.80,C.A2,6,A.B0,C.C3,5,7,9.D0,C.E4,8;213,9.20,C.36.40,C.56.60,C.74,8.A6.B0,C.C6.D0,C:E4CV50O7PVWTFQPXAT4OUK7LALO6AFW1EKBE9FEQQ5C0LQFK91VKU7X4G51GPVUGU591WL3A93MWG33MTPOTX7B64X", "hasFlower": 0, "hasSeason": 0}, {"id": "177", "q": "000,2,4,6,8,A,C,E.20,3,5,7,9,B,E.41,5,7,9,D<PERSON>53,<PERSON><PERSON>61,7,D.73,B.80,5,9,E<PERSON>92,7,C.A0,4,A,E.B6,8.C1,3,B,D.D7.E0,2,4,A,C,E.F6,8.G0,2,4,A,C,E.H6,8.I2,4,A,C;101,7,D.15,9.23,B.36,8.52,C.91,7,D.B7.C1,D.D3,B.E0,E.F2,4,6,8,A,C.G0,E.H2,C:VE01ALT7YHL7N3FW5WVRNS5S4E8TSWWCL94RT1L03KFNA3KH1R6FR8K7S4C68KNA65FEECHC45T79Y03PYPY1HVVP09P98A6", "hasFlower": 0, "hasSeason": 0}, {"id": "178", "q": "000,2,4,6,A,C,E,G.18.20,2,5,B,E,G.38.40,4,6,A,C,G.52,8,E.60,5,B,G.72,7,9,E.84,C.90,6,8,A,G.A2,4,C,E.B0,6,8,A,G.C4,C.D1,6,8,A,F.E4,C.F0,2,6,8,A,E,G.G4,C.H0,6,8,A,G.I2,4,C,E.J0,6,A,G.K2,4,8,C,E;128.48.68.84,7,9,C.A6,A.C6,8,A.D1,4,C,F.E7,9.G6,A.H8.I6,A:UY314YIO1BP29NCM9SIOAOCBPN62WUUPW5JBFIGOA6PGK34JD3H2WUC1J9GH7Z2TCEEFNF7S3A1EENYJ9MGQKTAWSYK44MFQIMBZQ7QK75SD", "hasFlower": 0, "hasSeason": 0}, {"id": "179", "q": "000,2,C,E.14,6,8,A.20,2,C,E.34,6,8,A.41,D.55,9.60,3,B,E<PERSON>75,7,9.80,2,C,E<PERSON>94,A.A0,6,8,E<PERSON>B3,<PERSON><PERSON>C0,6,8,E<PERSON>D2,<PERSON><PERSON>E4,6,8,A;100,E.12,5,9,C.20,7,E.34,A.41,D.64,A<PERSON>76,8.82,C.94,A.A0,6,8,E<PERSON>C6,8.D2,C<PERSON>E5,9;211,D.27.64,A.94,A.A7;364,A.94,A:857D7EHN1MJLDGW0ND1PRIA1MOS6O5PHS69VLWNAG3GI0DG389H84NJ6HV9A5FF4F6RJLWF80IJ3ALW59EMR1M7I07R3", "hasFlower": 0, "hasSeason": 0}, {"id": "180", "q": "000,2,5,7,9,C,E.20,2,5,9,C,E.42,6,8,C.50,E.63,5,7,9,B.70,E.82,5,7,9,C.A0,3,7,B,E.C0,2,4,6,8,A,C,E.E5,9;106,8.10,2,C,E.25,9.42,7,C.50,E.66,8.82,5,9,C.97.A0,3,B,E.B7.C1,3,5,9,B,D.E5,9;207.12,C.57.A3,7,B.C1,4,A,D;3A3,B;4A3,B:CW2F0CRGGNLOUYW23JKEMDIRLGMC3QBJM4W1WDHKXXD8IXL8NZLS048RFKFQVB9OD3EO3HZZK1HQU8ZYFGQNVNMSH9OCXR", "hasFlower": 0, "hasSeason": 0}, {"id": "181", "q": "000,2,4,7,9,B,E,G,I.20,3,5,7,B,D,F,I.40,3,5,7,9,B,D,F,I.60,2,6,C,G,I.74,8,A,E.81,H.94,8,A,E.A2,G.B4,7,9,B,E.C0,2,G,I.D6,C.E0,2,8,A,G,I.F4,6,C,E.G0,8,A,I.H3,5,D,F.I0,7,9,B,I;100,2,9,G,I.14,E.20,6,C,I.34,E.46,9,C.79.84,E.99.A2,G.B4,9,E.C0,I.E1,H.F5,7,9,B,D.G0,I.H3,F:H0UQVZCN2OMLNRU9ZB5WZYUJAQXY81U94R2762IZGAIDK6K7VFJGIM009DHV81N44WBI4X581GYCH0RBHVO8YOKWNQGA6KLA6M9XMD5512ROBWFJXQDJ", "hasFlower": 0, "hasSeason": 0}, {"id": "182", "q": "000,2,4,7,A,C,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,3,B,E.75,7,9.80,2,C,E.97.A1,5,9,D.B3,B.C0,6,8,E.D2,4,A,C.E0,6,8,E.F2,4,A,C.G7;100,2,7,C,E.14,A.20,6,8,E.34,A.42,7,C.50,E.70,5,9,E.91,D.A5,9.C0,3,B,E.D7.E4,A.G7;217.25,9.37.60,E<PERSON>C3,B.G7;325,7,9:D58WWN2MICTK24XTQUN38K0XMYX1JIHMYU30E5EDXSW1D4TYZ152NUKL0HSLSIETUIZZ804LCJ452Z6QQ6DEM3K13SNWQL8C66CHHYJJ", "hasFlower": 0, "hasSeason": 0}, {"id": "183", "q": "000,2,5,7,9,B,E,G.20,4,7,9,C,G<PERSON>32,E<PERSON>44,6,A,C.50,2,8,E,G.72,4,6,A,C,E.80,8,G.93,5,B,D.A0,7,9,G.B3,5,B,D.C1,8,F.D5,B.E2,E.F7,9.G0,2,E,G.H4,6,A,C.I1,F.J4,7,9,C.K0,2,E,G;106,A.27,9.33,D.51,F.72,4,C,E.88.A0,4,C,G.C8.E2,E.F8.G1,F.H4,C.J1,7,9,F;228.33,D.51,F.73,D.88.A4,C.F8.G1,F.H4,C.J1,8,F:TWW4ZPBXLEUTQCU96T84XQ1RUX817NN05PG7GSOOPDPRSMLCL5F15OC96WDD3M942HEZ8S9X2DFH33BGHZGH3271E0NTUE2NRZQ0BFW4C50OMQMF6786LBSR", "hasFlower": 0, "hasSeason": 0}, {"id": "184", "q": "000,2,4,7,9,C,E,G.20,2,4,6,A,C,E,G.38.41,3,5,B,D,F.58.60,2,5,B,E,G.77,9.80,2,4,C,E,G.96,8,A.A1,3,D,F.B5,7,9,B.C0,G.D2,4,6,8,A,C,E.E0,G.F2,4,6,A,C,E.G0,8,G;107,9.10,G.26,A.32,E<PERSON>44,C.52,E.78.81,F.A3,8,D.C7,9.D2,4,C,E.F0,6,A,G;210,G.44,C.52,E.A3,D.C8.D4,C:6XS5VITM5HO89534RYQIK6NKU4QRWW9MXDOKHWKI011ZOSJ05J1N08GYPYSHVNSGV3DGUTX61H67PUX437IR3V0QJP7ZRJ9TO7G9TQWUP4YN", "hasFlower": 0, "hasSeason": 0}, {"id": "185", "q": "002,4,6,8,A,C.10,E.22,4,A,C.37.42,4,A,C.56,8.60,2,4,A,C,E.76,8.80,2,4,A,C,E.96,8.A0,2,4,A,C,E.C2,4,6,8,A,C.E1,7,D.F4,A.G0,2,C,E;105,7,9.10,2,C,E.33,B.47.52,4,A,C.74,7,A.80,E.93,5,9,B.B2,C.C4,A.D7.F1,D;210,2,C,E.52,C.64,A.80,4,A,E.F1,D;352,C.F1,D:LB56GDNE1O0X2YDGPK3CEJCPLMJXF4M3Y7PTC6I3KMIIXS36NS1FGNYMRSDWTT7RK2WY0RF4WWNXCOTH1JHG2465HRPF1I4HSD7JK2B7", "hasFlower": 0, "hasSeason": 0}, {"id": "186", "q": "000,3,5,7,9,B,D,G.20,3,5,7,9,B,D,G.40,2,5,7,9,B,E,G.60,2,4,6,8,A,C,E,G.81,3,5,7,9,B,D,F.A1,3,6,8,A,D,F.C0,2,4,6,8,A,C,E,G.E0,2,5,7,9,B,E,G.G2,4,6,8,A,C,E.H0,G.I2,6,8,A,E;100,3,5,7,9,B,D,G.20,G.40,G.H0,G.I2,E;204,8,C.10,G.30,G;310,G:53PVM5RL2QPYIDLO2BMJ4M369PIYJ32WRVAIJBKJ97Q7UQVY3XWO05BWFCURMDCSB4Q6DK5C6P2KAXYRA0W4C0F0LSOVAK69O9I77LD4", "hasFlower": 0, "hasSeason": 0}, {"id": "187", "q": "000,2,4,6,8,A,C,E.20,2,4,7,A,C,E.41,5,9,D.57.61,3,B,D.75,7,9.81,3,B,D.95,9.A0,3,7,B,E.B5,9.C0,2,C,E.D4,6,8,A.E0,E.F2,C.G0,4,7,A,E.I1,5,7,9,D.J3,B.K0,5,7,9,E;102,C.10,E.24,A.31,D.45,9.61,D.73,B.85,9.A0,5,7,9,E.C0,E.D4,6,8,A.E0,E.F2,C.I1,5,9,D.K0,5,9,E:M4A8HQO89TYJRC2MRO01VIYMEQ13SEBFWDE301YWWFF3JDBSIGR4T24AEHVGGLS0TBV8VCGL3QXF8JLQ91SZ9DTZDHX2YRLH9CI0BJ4C2IMW", "hasFlower": 0, "hasSeason": 0}, {"id": "188", "q": "002,4,6,8,A.10,C.22,4,8,A.40,2,4,6,8,A,C.60,2,4,6,8,A,C.80,2,5,7,A,C.A0,3,5,7,9,C.C0,2,4,6,8,A,C.E1,3,5,7,9,B.G0,3,5,7,9,C;102,5,7,A.33,9.41,5,7,B.53,9.76.80,2,A,C.95,7.A3,9.C1,3,9,B.D5,7.E1,3,9,B.G6;233,9.45,7.95,7.D5,7.E3,9;346.D6:QF0UEJVLGXFKCK3W6457P9W3F6EMCI00CV7NWKZQO7QEX4LZHT8WX68N8TX95HMTS6QGUMPLTKOJHSZSSM5Z3FV7CILJ385JV0EH", "hasFlower": 0, "hasSeason": 0}, {"id": "189", "q": "000,2,7,C,E<PERSON>14,A.20,6,8,E<PERSON>34,A<PERSON>41,<PERSON><PERSON>53,5,7,9,B.70,3,6,8,B,E.90,3,5,9,B,E.B0,3,6,8,B,E.D0,3,5,9,B,E.F2,6,8,C.G0,4,A,E.H2,C<PERSON>I4,7,A<PERSON>J0,E<PERSON>K3,6,8,B;107.10,4,A,E.41,D.53,5,9,B.67.70,E.83,B.A3,B.B6,8.C3,B.D0,5,9,E.F2,6,8,C.H3,B.K7;214,A.53,B.67.70,E.83,B.A3,B.B6,8.C3,B.D0,E.F2,7,C:3ZVEFOP2DJ7KNA1AW52K66DVX7HOCG3CVXHW7GURJ0MQGQOAPXBJHWD5YK2PP7K0TY1JZCOTDEMGANHUYZR46WBE46TRX2ERTZU4CM5FUVM5BY411B33", "hasFlower": 0, "hasSeason": 0}, {"id": "190", "q": "000,2,6,8,C,E.14,A.21,6,8,D<PERSON>33,B<PERSON>41,6,8,D.53,B.66,8.70,3,B,E.85,7,9.90,3,B,E.A6,8.B3,B<PERSON>C1,5,7,9,D.D3,<PERSON><PERSON>E0,7,E<PERSON>F2,4,A,C.G0,6,8,E;106,8.11,D.33,B.57.63,B.76,8.80,3,B,E.96,8.B3,6,8,B.D3,B<PERSON>E0,<PERSON><PERSON>F4,A.G6,8;206,8.67.B7.C3,B.E0,E.G6,8;3C3,B.G6,8;4G7;5G7:265MUNZ8M6Q8Y7FEX5WR3H2WNDJ35NCFEGYYH2XHDRV7OJZXZF2AUWAUJYG7OEMVWAMCQVQVXQZDE7CHJRF5CGUO8G638O3RN6AD", "hasFlower": 0, "hasSeason": 0}, {"id": "191", "q": "000,3,5,7,9,B,D,G.21,3,5,7,9,B,D,F.40,2,4,6,8,A,C,E,G.60,2,4,6,8,A,C,E,G.80,2,4,6,8,A,C,E,G.A0,2,4,6,8,A,C,E,G.C0,2,4,6,8,A,C,E,G.E0,2,4,6,8,A,C,E,G.G0,2,4,6,8,A,C,E,G.I0,2,4,6,8,A,C,E,G;105,B.25,7,9,B.45,B.65,B.71,F.B0,G.D5,B.F5,B.H5,7,9,B:IEN53WIH1TS46FEU8RN1WP3SA4K85JJGJWGFC6QM7QFA7PS0QHKVJ7UHMNMVIEP4R30K2426VI0U8E1VU5TT0A2217CPM38NTRAKSFHRQW5CC6", "hasFlower": 0, "hasSeason": 0}, {"id": "192", "q": "000,2,4,6,8,A,C,E,G.20,3,5,8,B,D,G.40,2,6,8,A,<PERSON>,G<PERSON>54,C.60,7,9,G.73,5,B,D.81,F.93,5,B,D.A0,G.B7,9.C0,3,5,B,D,G.D8.E0,2,5,B,E,G.F8.G1,3,5,B,D,F;102,6,8,A,E.10,G.24,C.38.54,7,9,C.84,C.B8.C4,C.D8.F2,5,B,E;208.48.B8.D8.F2,E;3C8:YZCCZMSBS8SZLBY5CCVLMUO7OJQP1O7B3GKLBR06FEDRZGV87RUFY0PSDPK16OUEKLYX5W4X54Q08U83QTWK5GQPGTJ70R", "hasFlower": 0, "hasSeason": 0}, {"id": "193", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.41,4,6,8,B.61,3,5,7,9,B.83,5,7,9.A0,2,4,6,8,A,C.C1,3,6,9,B.E1,3,5,7,9,B.G1,3,5,7,9,B;100,3,9,C.15,7.23,9.31,6,B.55,7.74,8.A4,8.B6.D1,B.E6.F3,9.G1,5,7,B;215,7.74,8.A4,8.G1,5,7,B;374,8.A4,8.G1,B;474,8.A4,8:KLSCPI9V0OAYXX3ZLMIEG25OPLRVC0O524JNBFLRH5AVIVBZCYO4XJCAUSX09NGYHE5KAH27MU0IRY34FUE29N74GFNGHF9EZZUR", "hasFlower": 0, "hasSeason": 0}, {"id": "194", "q": "000,2,5,9,C,E.20,2,5,9,C,E.37.40,3,B,E<PERSON>55,7,9.62,C<PERSON>74,6,8,A.81,D.93,5,9,B.A0,7,E.B2,5,9,C.D1,4,6,8,A,D.F2,5,7,9,C.G0,E<PERSON>H3,7,B.I0,E.J2,5,9,C.K0,E;105,9.10,2,C,E.43,7,B.55,9.62,C.74,6,8,A.81,D.93,B.A6,8.D1,4,6,8,A,D.F2,5,9,C.G7.H3,<PERSON><PERSON>K0,E;275,9.D5,9.G7:X5IHL0A7HOX9Y9YJ986A72K071HY26PE95A8ZX4OLNFTTS0YTCC1NLSZLPPKPA08JN5ZE4J7JID5SR8ZREIN6CE6XTHFC2DRRDVSD121VI", "hasFlower": 0, "hasSeason": 0}, {"id": "195", "q": "000,3,5,7,9,B,D,G.22,4,7,9,C,E.30,G.42,4,7,9,C,E.60,2,5,8,B,E,G.80,2,4,8,C,E,G.96,A.A0,3,D,G.B5,8,B.C0,2,E,G.D5,8,B.E2,E.F0,4,7,9,C,G.H2,4,7,9,C,E;104,6,A,C.23,8,D.31,F.43,8,D.61,5,B,F.78.80,G.93,D.A0,G.B8.C0,G.D5,B.F4,8,C.H3,8,D;204,6,A,C.28.61,F.H8:57NYB0NM7EBE44YHD1878RVFYUDXTBHQU92CXJWE26YH24S3MB21TESMF6Z9W81KST3XRNW0XZVKCO5Z66H05R03TSJ8Z4ONWQR7531OUUMO", "hasFlower": 0, "hasSeason": 0}, {"id": "196", "q": "000,2,4,8,A,C.20,2,5,7,A,C.40,2,4,6,8,A,C.60,2,4,6,8,A,C.81,3,5,7,9,B.A0,3,5,7,9,C.C0,2,4,6,8,A,C.E0,2,4,8,A,C.F6.G0,2,4,8,A,C;102,A.26.32,A.46.53,9.66.71,B.85,7.A0,C.B6.C3,9.E2,A.F6.G1,3,9,B;246.71,B.F2,A:9ZIFS8KMK7EK405SZTTXU2992ECMVQXUZOF16UI22IN7OOVN0I0C874U4SZOQV9QK101CMC54E6MN5V5N1EFF7SQ", "hasFlower": 0, "hasSeason": 0}, {"id": "197", "q": "000,2,4,A,C,E.16,8.21,3,B,D.37.40,3,5,9,B,E.60,2,4,6,8,A,C,E.82,4,6,8,A,C.90,E.A2,6,8,C.B0,E.C2,4,6,8,A,C.E1,3,5,7,9,B,D.G0,2,4,6,8,A,C,E.I0,2,4,7,A,C,E.K1,3,5,7,9,B,D;182,4,6,8,A,C.90,E.A2,6,8,C.B0,E.C2,4,6,8,A,C:WMXG6ZN7475GQTD0S2APUP0HBDWS6GI5T3HQQSYPZUGMCWXO5BR8NJR1ESQIOB8D2ZIPMZEXCABXYY405JJ7AI1A0DYMJ7W3", "hasFlower": 0, "hasSeason": 0}, {"id": "198", "q": "001,3,6,8,B,D.20,3,6,8,B,E.40,2,5,9,C,E.57.61,3,5,9,B,D.80,2,4,6,8,A,C,E.A0,2,4,7,A,C,E.C0,3,5,9,B,E.D7.E0,3,B,E.F5,9.G0,2,7,C,E.I0,3,5,9,B,<PERSON>;103,B.17.30,E.52,5,9,C.71,4,A,D.90,2,C,E.C4,A.D0,7,E.G0,2,C,E:R54XWUUIBYN3NMD1ALA16RQ952ZVY5K7UQVP9QL517WLPKYBPK7ZZ16KB2RHP4R3F6WYHQHZNBVWD7M6IAUNFLHVXA", "hasFlower": 0, "hasSeason": 0}, {"id": "199", "q": "000,3,5,7,9,C.21,3,5,7,9,B.42,4,8,A.50,6,C.62,4,8,A.70,6,C.82,4,8,A.A0,2,4,6,8,A,C.C0,2,5,7,A,C.E0,3,5,7,9,C.G0,2,4,6,8,A,C;100,5,7,C.21,3,5,7,9,B.42,4,8,A.56.60,2,4,8,A,C.76.82,4,8,A.A0,3,5,7,9,C.C1,6,B.E5,7.F0,3,9,C.G6:T5TAWJCUHDC9MONXXPCCDMXX95R65VP7N54RAYF76LFU6MHV449YAJ0VYWJ87TPW9ETLE08O64AFL7HEO0JWRMDLDHOYF0VERP", "hasFlower": 0, "hasSeason": 0}, {"id": "200", "q": "002,4,6,8,A,C,E.10,G.22,4,C,E.30,6,8,A,G.42,4,C,E<PERSON>56,A.60,2,8,E,G.80,2,5,7,9,B,E,G.A2,8,E.B0,5,B,G.C2,7,9,E.D4,C.E0,2,6,8,A,E,G.F4,C.G0,2,6,8,A,E,G;102,4,7,9,C,E.22,4,C,E.36,A.42,E.60,8,G.72,E.80,6,8,A,G.A2,E.B0,8,G.D2,E.E5,B.F2,8,E:VLSD4QJOBJ3SLRHXN37B6N97J4TT1QJ5OW1XLXQHFDROVVV84SZWFA34H5QTFMRNZE779EE61EMB6TNDWS3BRA9XDLHZ1Z6OW955F8", "hasFlower": 0, "hasSeason": 0}]