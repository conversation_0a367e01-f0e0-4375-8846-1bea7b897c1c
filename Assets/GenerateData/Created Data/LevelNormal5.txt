[{"id": "1", "q": "002,4,6,8.10,A.22,4,6,8.30,A.42,5,8.62,4,6,8.70,A.82,4,6,8.90,A.A3,5,7;102,4,6,8.23,7.52,8.73,7.93,7:6W5W6Z5W0YYZZ5W650W0006LL6ZL5L0LY6ZLYYZW5Y", "hasFlower": 0, "hasSeason": 0}, {"id": "2", "q": "000,2,5,8,A.20,2,4,6,8,A.40,4,6,A.52,8.60,4,6,A.84,6.90,2,8,A.A4,6;100,5,A.20,5,A.44,6.51,9.64,6.84,6.90,A.A5:5Z12BVNCBXVKG2VQXNQV5HXGBHX71OG7ZXXGO26V6BVK2C", "hasFlower": 0, "hasSeason": 0}, {"id": "3", "q": "000,2,4,6,8.21,3,5,7.41,7.60,2,4,6,8.80,2,6,8.A0,3,5,8.C1,3,5,7;100,8.12,6.24.31,7.60,2,6,8.A0,4,8.C1,3,5,7:7YDDCE88LMYK77YMLCC788KDNS7NYNDMKYCHNKDYEH7MSD", "hasFlower": 0, "hasSeason": 0}, {"id": "4", "q": "000,3,7,A.15.20,3,7,A.35.40,2,8,A.54,6.60,2,8,A.83,7.90,5,A.B0,3,5,7,A;113,7.25.30,A.52,8.A0,A.B3,5,7:YXXIYXVPUGC9UIUXPGUPF9X779GGGLXFGOD9DVCOLP", "hasFlower": 0, "hasSeason": 0}, {"id": "5", "q": "000,2,6,8.14.20,2,6,8.42,4,6.62,6.74.80,2,6,8.A0,3,5,8;101,7.13,5.32,6.44.52,6.73,5.80,8.A0,3,5,8;201,7.14.44.74.A4;301,7:ZHLIH4ZNY7GNL7YD0ODLGOH22GUHUYA3GU8843I8AH0U8LHY", "hasFlower": 0, "hasSeason": 0}, {"id": "6", "q": "000,2,4,6,8.20,3,5,8.40,3,5,8.60,4,8.80,3,5,8.A1,3,5,7;104.20,3,5,8.40,3,5,8.64.70,8.83,5.A1,3,5,7:75LE8O1EEE7WELRMQOQR1UGM4UQWWGOW8I151OQEI4", "hasFlower": 0, "hasSeason": 0}, {"id": "7", "q": "000,2,4,6,8,A.20,2,4,6,8,A.40,2,4,6,8,A.60,4,6,A.80,3,7,A.A0,3,5,7,A.C1,3,5,7,9;102,5,8.23,7.30,A.50,A.80,A<PERSON>A0,A.B3,7.C5:R85M6M723831M2MR1FEY82R8R6OF2QOH7O2MY3Y2OHAA7M5EY37Q", "hasFlower": 0, "hasSeason": 0}, {"id": "8", "q": "000,4,8.12,6.20,4,8.32,6.44.50,2,6,8.64.70,8.82,4,6.A0,2,4,6,8.C0,2,6,8;110,8.43,5.51,7.93,5.A1,7.C0,8;210,8.C0,8:GUDPBUWWHBGUU166BBPBH1G11WWUWU6PDPDDDWD11PGB6P", "hasFlower": 0, "hasSeason": 0}, {"id": "9", "q": "000,2,6,8.14.21,7.34.41,7.53,5.61,7.80,2,4,6,8.A1,3,5,7;111,4,7.51,4,7.80,3,5,8.A1,4,7;211,7.A1,7;311,7.A1,7:UNGHHDXNGUBUXBX33XXNDGD3HUNXDUHUG3BBGHB3HBG3", "hasFlower": 0, "hasSeason": 0}, {"id": "10", "q": "000,2,4,6,8,A.21,4,6,9.42,4,6,8.50,A.62,4,6,8.80,2,8,A.94,6.A0,2,8,A.B4,6.C0,2,8,A;101,9.14,6.45.51,9.81,9.95.A2,8.C2,8:ZLLDZU3XL5TUTX03533TLLDTXDDX0LF5XU3U3Z5UZDZTUXTFDZ", "hasFlower": 0, "hasSeason": 0}, {"id": "11", "q": "000,2,4,6,8,A.20,2,4,6,8,A.40,2,8,A.54,6.61,9.73,5,7.80,A.92,8.A4,6.B0,2,8,A.C4,6;101,9.15.20,2,8,A.51,9.65.B0,2,8,A.C5:RA0S0OMIDEVC9RVSE2ZA5LV505D2L9VOIMEC025EZM5TJ5TM2J", "hasFlower": 0, "hasSeason": 0}, {"id": "12", "q": "000,2,4,6,8.21,3,5,7.40,3,5,8.60,4,8.80,3,5,8.A0,2,4,6,8.C1,3,5,7;100,3,5,8.22,4,6.40,3,5,8.60,8.80,3,5,8.A0,3,5,8.C1,7:AM737B22SWBGNLH2B4VB73ZAICZ4MCIYWYH237C8CP3RLGPN8VRS", "hasFlower": 0, "hasSeason": 0}, {"id": "13", "q": "000,2,4,6,8,A.20,3,7,A.35.40,3,7,A.55.60,A.74,6.81,9.93,5,7.A0,A.B3,7.C0,5,A;100,2,4,6,8,A.20,3,7,A.44,6.65.81,4,6,9.A0,3,7,A.C5:MTT6ECT163DDAC4C4G1OM3YGEMAMY1DECJ4ECLTFF3DJLO431LCOLO", "hasFlower": 0, "hasSeason": 0}, {"id": "14", "q": "002,4,6,8.10,A.22,5,8.30,A.43,5,7.50,A.62,4,6,8.70,A.82,4,6,8.A0,2,4,6,8,A.C0,3,5,7,A.E0,2,4,6,8,A;103,5,7.20,2,8,A.62,5,8.92,5,8.D0,A<PERSON>E4,6:YYJJ42J2ZI9D422CFT6BU8XEIXT8B6OZCJCUFZMEFCBU488DZ4CCO6BMU9F6", "hasFlower": 0, "hasSeason": 0}, {"id": "15", "q": "001,3,5,7,9.20,5,A.32,8.40,5,A.61,3,7,9.75.80,2,8,A.94,6.A0,A.B2,5,8.D0,5,A.E2,8;102,4,6,8.20,5,A.32,8.61,9.80,2,8,A.94,6.A0,A.C5.D0,A:W2Y3JH86DKHRDH9N38NGLJLDUO3LKVYUGJHV595RD2YD3LRRLWOJL6YD", "hasFlower": 0, "hasSeason": 0}, {"id": "16", "q": "000,2,5,8,A.20,3,7,A.35.40,3,7,A.61,3,7,9.81,4,6,9.A0,3,5,7,A.C0,4,6,A;101,5,9.20,3,7,A.40,3,7,A.62,8.85.A0,3,7,A.B5;201,9.23,7.40,A:THNA0JWFDUNTNNUAT4UZ6JY60AYH6NF48YD4Z4FWW6YUFAYWTZNWWY8Z", "hasFlower": 0, "hasSeason": 0}, {"id": "17", "q": "002,4,6,8,A.10,C.23,5,7,9.30,C.43,5,7,9.51,B.63,5,7,9.70,C.82,4,6,8,A.A0,2,4,6,8,A,C.C0,2,5,7,A,C;102,A.10,C.26.33,9.51,B.66.A3,9.B6.C0,C:60D5CV3PZV3LZC03QS2LP30N1Z21LJQ5U1ZV0JDZD3VL322VPSPUV61NDZ", "hasFlower": 0, "hasSeason": 0}, {"id": "18", "q": "000,6,C.12,A.20,6,C.41,3,5,7,9,B.60,3,5,7,9,C.80,2,5,7,A,C.A0,6,C.B2,A.C6;106.20,6,C.42,A<PERSON>54,8.60,6,C.80,2,A,C.A6.C6;206.20,6,C.66.81,B.A6.C6;316.20,C.B6:6UTMISQ34XLC1YFOGHUF0S3LRBPMDRNOB6AYRPRW8QR2IA07HNG142XDC7TRW8", "hasFlower": 0, "hasSeason": 0}, {"id": "19", "q": "000,2,4,6,8,A.21,4,6,9.40,2,8,A.54,6.60,2,8,A.74,6.81,9.93,7.A1,5,9.C0,2,8,A;100,4,6,A.21,4,6,9.42,8.54,6.60,2,8,A.92,8.A5.B1,9:SAZRHVSR6VHAZCSS6VS1TVJTFFRRVZ5W15VZTZ6AW6ZJH1H6CS61TA", "hasFlower": 0, "hasSeason": 0}, {"id": "20", "q": "000,A.15.21,3,7,9.35.40,3,7,A.55.60,2,8,A.74,6.80,2,8,A.94,6.A0,A.B2,4,6,8.C0,A;115.22,8.34,6.40,A.60,A.82,8.90,5,A.B2,4,6,8;222,8.50,A.82,8.95.B2,8:D8MCTIBD1VR3OT8IRVD4C7CEI7EIRMO33BCR3DB4ODE3VID13OBCEECE8IV8", "hasFlower": 0, "hasSeason": 0}, {"id": "21", "q": "000,2,5,8,A.20,2,5,8,A.41,3,5,7,9.60,2,4,6,8,A.80,5,A.92,8.A0,4,6,A;112,8.25.44,6.51,9.64,6.80,A.92,5,8;212,8.54,6;312,8:J1JF4FH22F1H4P1JOJ4HOHHJ122POOF1F24JP2HF4QP41QOPOP", "hasFlower": 0, "hasSeason": 0}, {"id": "22", "q": "000,2,4,6,8,A.22,4,6,8.30,A.42,4,6,8.50,A.63,5,7.70,A.82,4,6,8.90,A<PERSON>A2,5,8.C1,4,6,9;101,4,6,9.24,6.42,8.50,A.80,A.92,8:YOITYBF9OU0YTPI0960D75F6U9B7FQS0PDDQ500DN5T5NSYFT9", "hasFlower": 0, "hasSeason": 0}, {"id": "23", "q": "001,7.13,5.20,8.33,5.40,8.54.61,7.73,5.80,8.93,5.A0,8.B3,5.C0,8;101,7.13,5.20,8.33,5.40,8.54.73,5.80,8.A0,3,5,8;233,5.40,8.90,8.A3,5:DD00U6DMH0626NS202HS2MNN0U02MUDH2S6SSFHUN6SM6DNHHFDN", "hasFlower": 0, "hasSeason": 0}, {"id": "24", "q": "000,2,5,8,A.20,2,4,6,8,A.40,2,8,A.54,6.60,A.75.81,3,7,9.A0,2,4,6,8,A.C0,2,4,6,8,A;100,A.32,8.54,6.82,8.A2,4,6,8.C1,3,7,9:7GPYBTQIWE99CFWTXBJ4IMJERFZDZ4P4D2X5Y7QCL4GCCM5D2LRD", "hasFlower": 0, "hasSeason": 0}, {"id": "25", "q": "000,2,5,8,A.21,4,6,9.40,2,4,6,8,A.60,2,4,6,8,A.83,5,7.90,A.A2,4,6,8.C0,2,4,6,8,A;111,9.25.31,9.53,7.61,9.83,7.B5.C1,9:1ALALH2ASW90P84W0FAHXE4PYC6HH218Y1AYY6C291C2ECAFXS", "hasFlower": 0, "hasSeason": 0}, {"id": "26", "q": "000,4,8.20,3,5,8.40,2,4,6,8.60,2,4,6,8.80,2,4,6,8.A0,2,4,6,8.C1,4,7;104.24.43,5.50,8.63,5.80,8.93,5.B1,4,7;204.43,5.93,5:CXEX0CI8CIAX8Q0QAA3ECEI83QCI707QAEEE7IQA0X0A0Q8I7C", "hasFlower": 0, "hasSeason": 0}, {"id": "27", "q": "001,3,5,7,9.24,6.30,2,8,A.44,6.51,9.64,6.70,A.82,5,8.A0,2,8,A.B4,6.C0,A;101,5,9.24,6.54,6.70,A.82,5,8.B0,4,6,A;201,9.24,6.B4,6:XHMGJJRMFX9F3HX9743HNYDWWRWYX78937D0433M7N38GRW90HMR", "hasFlower": 0, "hasSeason": 0}, {"id": "28", "q": "000,2,4,6,8.21,3,5,7.40,2,4,6,8.60,2,6,8.80,2,4,6,8.A0,2,6,8.C0,3,5,8;102,4,6.21,7.42,6.62,6.82,4,6.A2,6.C0,4,8;202,6.21,7:ESEZ0B3HQLSQIRKOGBHVO7NNLAIJ89A9OYOMG7ZOM3OYJXXV8R0K", "hasFlower": 0, "hasSeason": 0}, {"id": "29", "q": "000,2,5,8,A.22,4,6,8.30,A.44,6.51,9.63,5,7.81,4,6,9.A0,2,4,6,8,A.C0,3,7,A;100,A.12,5,8.30,4,6,A.54,6.74,6.91,4,6,9;212,8.25.44,6.94,6:R1855N58185811R78RS73NS7GXX3SX7NX3RNXG8S3XS553N7GRGRN37S", "hasFlower": 0, "hasSeason": 0}, {"id": "30", "q": "001,5,9.13,7.20,5,A.32,8.50,3,5,7,A.70,2,5,8,A.90,3,5,7,A.B1,9.C3,5,7.D0,A<PERSON>E2,4,6,8;105.13,7.50,3,5,7,A.72,8.93,7.B1,9.C3,5,7.E2,5,8:8QDBXNUR7U3A8VIM2GQPAB4RND731ZGI0WPMTH8LK621VZ4HTW8K06XL", "hasFlower": 0, "hasSeason": 0}, {"id": "31", "q": "000,2,4,6,8,A,C.20,3,5,7,9,C.42,5,7,A.50,C.62,4,6,8,A.70,C.82,5,7,A.A0,3,5,7,9,C.C0,3,5,7,9,C.E1,3,5,7,9,B;100,5,7,C.25,7.B4,8.D5,7:OWQFLJLWQ9OPOQ47UO0MTPW4PNN99R93RQUTCFLL0Z3W25ZJLMCL5B27BP", "hasFlower": 0, "hasSeason": 0}, {"id": "32", "q": "000,2,4,6,8,A.21,4,6,9.41,3,5,7,9.60,2,4,6,8,A.81,5,9.93,7.A1,5,9.C0,4,6,A;100,3,7,A.42,4,6,8.62,4,6,8.81,9.93,7;242,4,6,8.81,9;342,5,8:FHQHHF28LCEP6NH832EQN3LP6KH60H2QK0E5QFK5E6LN2KPN0KNL0PNFCK", "hasFlower": 0, "hasSeason": 0}, {"id": "33", "q": "001,3,5,7,9.23,7.30,5,A.42,8.54,6.60,A.72,4,6,8.80,A<PERSON>A2,4,6,8.C0,2,4,6,8,A.E0,4,6,A;101,4,6,9.35.54,6.72,8.A2,4,6,8.C1,4,6,9.E0,5,A:0YLPX67IIANGUU03OS1PR19DA977A1NLALXYG70L3D0SOR3AA1R3N6RN", "hasFlower": 0, "hasSeason": 0}, {"id": "34", "q": "001,3,5,7,9.20,2,5,8,A.40,A.52,4,6,8.60,A.82,4,6,8.90,A<PERSON>A2,4,6,8;102,8.20,2,5,8,A.51,3,7,9.83,7.90,5,A<PERSON>A2,8;220,A.51,3,7,9.90,A;353,7:WWGHN9G99GN64G94ESG49RR4R9E34WHEHHHRSWG4ESRR3HS63WSWESE3", "hasFlower": 0, "hasSeason": 0}, {"id": "35", "q": "000,2,5,8,A.21,4,6,9.40,2,4,6,8,A.60,2,4,6,8,A.80,3,7,A.95.A0,2,8,A.B4,6.C0,2,8,A;101,9.21,9.42,8.62,4,6,8.B0,3,7,A;252,8.B0,A:SE33MKJ5JDB48CVEK72KMVI828DNJCND7KDJEFV79B8VIE54S9FV7V", "hasFlower": 0, "hasSeason": 0}, {"id": "36", "q": "000,2,4,6,8,A.20,2,4,6,8,A.41,3,5,7,9.60,3,7,A.82,4,6,8.A0,3,5,7,A.C0,4,6,A;100,2,8,A.15.20,A.32,5,8.53,7.73,7.95.A0,3,7,A.C5:YNWNQNTZXW0WDCY5F9Y80DTYQBFXZQ9Q96CD5W0C8D6NN0BN8Y8Y9C", "hasFlower": 0, "hasSeason": 0}, {"id": "37", "q": "000,2,4,6,8,A,C.23,6,9.30,C.43,6,9.50,C.62,4,6,8,A.70,C.82,4,6,8,A.A0,3,5,7,9,C.C1,4,6,8,B;101,B.16.23,9.43,9.61,3,9,B.82,4,8,A.A4,8.C4,6,8:48NJCJRRJJ8ZDHC1CRQR4DC818ZDNC84HZDHDJ1CNN4QN4J11DR81HZ4RNZZ", "hasFlower": 0, "hasSeason": 0}, {"id": "38", "q": "003,5,7.11,9.23,5,7.30,A.42,4,6,8.62,8.81,3,7,9.A0,2,5,8,A.C1,4,6,9;105.11,9.24,6.30,A.62,8.81,9.A0,A<PERSON>C1,4,6,9;211,9.30,A.62,8.81,9.A0,A<PERSON>C1,9;330,A.A0,A:O2R6M81139NR02NRO03839EAAH013MK3G8EKAKAAKH68J8Y0K166KJAGYU3RU8", "hasFlower": 0, "hasSeason": 0}, {"id": "39", "q": "000,2,4,6,8,A,C.21,3,5,7,9,B.40,2,4,6,8,A,C.60,2,5,7,A,C.80,3,5,7,9,C.A0,2,4,8,A,C.B6.C1,3,9,B.D5,7.E0,2,A,<PERSON>;115,7.23,9.66.A4,8.C2,A.D5,7:F3NP3Q61KJBZVS83PJIN5YK308IRC65JBVIZ1T02SQ27HCYWIHRFO171WOTJ", "hasFlower": 0, "hasSeason": 0}, {"id": "40", "q": "000,2,4,6,8,A.21,5,9.33,7.40,5,A.52,8.60,5,A.72,8.80,4,6,A.92,8.A0,4,6,A.B2,8.C0,4,6,A;100,3,7,A.15.33,7.40,A.60,2,5,8,A.84,6.90,A.A3,7.B0,5,A:J5XHZ5ISXGRHWSQEQXZEA8G31G1QREH9A91EFF5QQIPEPEJ5GTH138THWQXH", "hasFlower": 0, "hasSeason": 0}, {"id": "41", "q": "000,3,5,7,9,C.20,2,4,6,8,A,C.40,3,5,7,9,C.60,2,4,8,A,C.81,5,7,B.93,9.A1,B.B3,5,7,9.C1,B;114,8.20,6,C.34,8.40,C.63,9.91,B.B1,3,5,7,9,B:V6CVV9V9X6VJ6G69CJPJ262J2GECCVP9XEEE6XCG9J2XPPP2E2PXCEX9JG", "hasFlower": 0, "hasSeason": 0}, {"id": "42", "q": "000,2,4,6,8,A.20,2,4,6,8,A.40,3,7,A.55.60,3,7,A.75.80,2,8,A.94,6.A2,8.B0,4,6,A.C2,8.D0,4,6,A.E2,8;104,6.12,8.30,A.43,7.60,A.B3,7.C0,A.E2,8:33G933R7G32NDJ96J732JDJNGN2RR679D7J97J926NG66GG9NNR276RD2R", "hasFlower": 0, "hasSeason": 0}, {"id": "43", "q": "000,2,8,A.15.20,3,7,A.35.40,2,8,A<PERSON>54,6.60,A.72,4,6,8.80,A.93,5,7.A1,9.B3,5,7.D0,3,5,7,A;100,A.15.35.40,A.55.70,5,A.93,7.A1,5,9.B3,7.C5;275.A2,4,6,8:KRG07J7LBJ7KPXLD7KPBZKGDBBBDP66LL6XX60ZZRPZRBJGGKPJPG77KDGRX", "hasFlower": 0, "hasSeason": 0}, {"id": "44", "q": "000,2,5,7,A,C.20,3,6,9,C.41,3,5,7,9,B.60,3,5,7,9,C.80,3,5,7,9,C.A4,6,8.B0,C.C4,8;100,C.16.23,9.42,4,6,8,A.80,3,6,9,C.A4,8;200,C.23,9.46.83,9.A4,8;323,9:1PWX2XUSIDVO6L5S8FFP1PGW6HRRDHROQ0R37Q532UZQ8ZDQ3HIR0D3RE7ELPGHV", "hasFlower": 0, "hasSeason": 0}, {"id": "45", "q": "000,3,5,7,9,C.21,4,6,8,B.40,2,5,7,A,C.60,2,4,6,8,A,C.82,5,7,A.90,C.A2,4,6,8,A.C0,2,4,6,8,A,C;100,5,7,C.21,B.35,7.41,B.55,7.61,B.75,7.91,B.A4,8.C4,8:A694T76AJZFI254UGT7PDYQY8IDHAEK370HPJ0K71F92USESK931RQ8GW5RA9WKZ", "hasFlower": 0, "hasSeason": 0}, {"id": "46", "q": "000,2,4,6,8,A,C.20,2,4,8,A,C.36.40,2,4,8,A,C.56.60,2,A,C.80,2,5,7,A,C.A0,2,4,6,8,A,C.C1,3,5,7,9,B;104,8.11,B.33,9.46.50,C.80,C.96.A3,9.C1,4,8,B:H9X0YBJRV9FJ9ZPP7YB7Z5689FR8R9BLXMYFPSBP0JJHBR6VS59BHMILPHYPFI", "hasFlower": 0, "hasSeason": 0}, {"id": "47", "q": "001,4,6,9.20,2,5,8,A.40,2,4,6,8,A.60,2,4,6,8,A.80,2,4,6,8,A.A0,3,5,7,A.C1,3,5,7,9.E1,3,5,7,9;111,5,9.31,5,9.53,7.60,A.90,A<PERSON>A3,7.C1,5,9.E1,5,9:DUEKJUK0YGARC996GRED9Y3MY0A6ADDPG9DMYJG3GG6AUTRT3J39UJP6DRCUU9", "hasFlower": 0, "hasSeason": 0}, {"id": "48", "q": "000,2,4,6,8,A,C.21,3,5,7,9,B.40,3,5,7,9,C.60,2,4,6,8,A,C.81,4,8,B.96.A0,2,4,8,A,C.B6.C1,3,9,B;102,A.15,7.21,B.45,7.62,A.81,B.95,7.A0,C.C2,A:H33R6NQ2HQTRCHLPI2LLMIPALCC6MQRPRINIPH6TTRQAHRMHM26TT2NNMCTM", "hasFlower": 0, "hasSeason": 0}, {"id": "49", "q": "001,3,5,7,9.20,2,4,6,8,A.40,4,6,A.60,2,4,6,8,A.81,4,6,9.A2,4,6,8.B0,A.C2,4,6,8;101,4,6,9.22,4,6,8.30,A.45.50,A.62,4,6,8.84,6.A4,6.B2,8.C4,6:2EAR5F9L05ESTY9QK40RFEDAWG8VUHKGJ626LYT84WXCCEHJ3DSPU7VQ37PX", "hasFlower": 0, "hasSeason": 0}, {"id": "50", "q": "000,2,6,A,C.14,8.21,6,B.33,9.40,5,7,C.60,2,4,6,8,A,C.80,4,6,8,C.92,A.A4,8.B1,6,B<PERSON>C4,8;101,6,B.26.33,9.50,5,7,C.63,9.70,6,C.93,9.B4,6,8;201,B.16.B5,7:Z6RV3RRVA1WDSJVZWRZ8E68SW3ZVYYNN8634R1YRA644W0ASS4FY8EAYFD0YJ3", "hasFlower": 0, "hasSeason": 0}, {"id": "51", "q": "000,2,5,7,9,C,E.20,2,4,6,8,A,C,E.41,3,5,7,9,B,D.60,4,6,8,A,E.72,C.80,4,6,8,A,E.A0,2,4,7,A,C,E.C1,3,5,9,B,D.E0,2,4,6,8,A,C,E;106,8.27.35,9.42,C.B2,<PERSON><PERSON>C5,9.E5,9:OI5OK2Q2QZODMOD5UKMOZPDQDU7OPQ2UZK5IUMI7ZZZQPDMI7D27K2KI27Q5UIK55PPP7U", "hasFlower": 0, "hasSeason": 0}, {"id": "52", "q": "002,5,7,A.10,C.22,5,7,A.41,6,B.61,4,8,B.80,2,4,8,A,C.A0,5,7,C.C1,3,5,7,9,B.E0,2,5,7,A,C;111,5,7,B.41,6,B.71,B.B5,7.C2,A<PERSON>E1,5,7,B;215,7.41,B.71,B.C2,A<PERSON>E5,7;315,7.E5,7:U1SZM22IAFMQM7F54S7T326TX2ZBH7SXSVI264MFF7VBMU9F36HDSPIPM5ODF9I1ASO26Q", "hasFlower": 0, "hasSeason": 0}, {"id": "53", "q": "000,2,5,7,A,C.20,2,5,7,A,C.41,3,5,7,9,B.60,2,4,6,8,A,C.80,2,4,6,8,A,C.A0,3,6,9,C.C0,2,4,6,8,A,C;101,5,7,B.20,2,6,A,C.42,5,7,A.60,6,C.81,3,9,B.96.A3,9.B0,6,C:U1CJ5Z42USHIJ1P9E1OBWBQV135ZZBVY9QKP2TOPW9BY9ECQE1U9EU93CHPITZ4EECK1QS", "hasFlower": 0, "hasSeason": 0}, {"id": "54", "q": "000,2,4,6,8,A,C.20,3,5,7,9,C.40,2,A,C<PERSON>54,8.60,6,C<PERSON>73,9.81,5,7,B.A0,3,5,7,9,C.C0,2,4,6,8,A,C.E0,3,5,7,9,C;102,A.14,8.30,C.66.85,7.A5,7.C0,2,A,<PERSON><PERSON>D5,7.E3,9;214,8.66.95,7.D6:AOOGFNZXP1CFNGAXSNNAXS7V51AGFSN1ZPNX17PAFXDPGOCS1PDGOP5CVFOAXV5F1S5GSOVC", "hasFlower": 0, "hasSeason": 0}, {"id": "55", "q": "000,2,A,C.14,8.20,2,A,C.34,6,8.40,2,A,C.54,6,8.60,2,A,C.80,2,4,6,8,A,C.A0,2,4,6,8,A,C.C1,3,5,7,9,B;100,C.34,6,8.42,A.54,8.60,C.84,8.92,A.A4,6,8.C5,7;254,8.60,C.84,8:UCTD80OR4UN11HDPL6BNQD4PH0RPHN3H3VNNTBYMP5QH886W4TVUU8OBWMLC4DTY5XBXNH", "hasFlower": 0, "hasSeason": 0}, {"id": "56", "q": "000,3,5,7,9,C.21,4,6,8,B.40,6,C<PERSON>53,9.61,5,7,B.73,9.81,5,7,B.93,9.A0,5,7,C<PERSON>C0,3,6,9,C.E0,2,5,7,A,C;104,6,8.46.53,9.65,7.71,3,9,B.85,7.93,9.A0,C.B6.C3,9.D0,C.E6;256.71,B.A0,C.C3,9;3C3,9:W4VHSG9HKM5229KW3M9GI9LK55VWSKLISGMG45453722H34H459S237KSG2MGISWLMMK3LLI3L94", "hasFlower": 0, "hasSeason": 0}, {"id": "57", "q": "000,2,4,6,8,A,C.20,2,5,7,A,C.40,2,5,7,A,C.60,2,5,7,A,C.80,2,5,7,A,C.A2,4,6,8,A.B0,C.C2,4,6,8,A.D0,C.E3,5,7,9;100,4,8,C.20,2,A,C.46.61,5,7,B.80,2,6,A,C.B6.C2,A.D0,C:ACQTCJ1ONCVNM8GCJJHZTAMZO2WHQAMHRC45VWH3CM7AW3BOPKOB8J36P6E135427R11EKGW", "hasFlower": 0, "hasSeason": 0}, {"id": "58", "q": "001,3,6,9,B.20,2,4,8,A,C.36.40,2,A,C<PERSON>54,6,8.61,B<PERSON>73,9.80,5,7,C.92,A.A4,8.B1,6,B.C3,9.D0,5,7,C.F0,2,4,8,A,C;111,3,9,B.32,A.51,5,7,B.80,6,C.92,A.C6.F0,3,9,C;213,9.32,A.55,7:Y0NGB8SRDHHPG9FDCSVOO8PR69FVCQQ9VBO1ZRXRY0SKCJ6VPXXMRXFNNRZXPSXNO19KCFJM", "hasFlower": 0, "hasSeason": 0}, {"id": "59", "q": "001,3,6,9,B.20,5,7,C.33,9.41,B<PERSON>54,6,8.60,C.73,6,9.80,C.92,4,6,8,A.B0,5,7,C<PERSON>C2,<PERSON><PERSON>D4,8.E0,6,C.G1,3,5,7,9,B;101,3,9,B.20,5,7,C.33,9.83,9.B5,7.D4,8.F6.G3,9;201,3,9,B.20,C.83,9.B5,7.D4,8:IJC5CAWJA040WJWXWS5O49CC4GTB11GBX9OABZA0TSBXZC0TUT08AZ5AUWT5IUUCT90J4ZX9558W", "hasFlower": 0, "hasSeason": 0}, {"id": "60", "q": "001,3,5,7,9,B.21,4,8,B.36.41,3,9,B.55,7.63,9.71,5,7,B.90,5,7,C.A3,9.B5,7.C0,2,A,<PERSON><PERSON>D4,8.E0,2,6,A,C.F4,8.G1,B;106.11,4,8,B.53,9.65,7.A4,6,8.C0,2,A,C.D4,8.E0,2,A,C.G1,B;211,B.65,7.G1,B;311,B.G1,B:ZX8HKODO8XYXPGTJHNPLNGMZLO7TOTUHXMKUP8YXJ8HGLLNMKRU87DRWRYPKNU8OGRTYR6RMULULOW6X", "hasFlower": 0, "hasSeason": 0}, {"id": "61", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.41,3,5,7,9,B.61,3,6,9,B.80,3,5,7,9,C.A0,2,6,A,C.B4,8.C0,2,6,A,C.D4,8.E1,6,B;101,B.24,8.31,B.43,9.51,B.A1,B.B3,9.C1,B.D4,8;243,9.B3,9:YQBFLOSDYVFNLX4GGJDNBG4BVXVV4XBDJFNJYJLOJFNYFOYG4LSOFLD4XNQGDDX4JXYGNL", "hasFlower": 0, "hasSeason": 0}, {"id": "62", "q": "000,2,4,8,A,C.20,2,4,8,A,C.40,2,4,8,A,C.60,2,4,6,8,A,C.80,2,5,7,A,C.A0,2,4,8,A,C.C1,3,5,7,9,B;101,3,9,B.20,4,8,C.41,3,9,B.60,2,4,8,A,C.81,5,7,B.A0,C.B4,8.C2,6,A:N6FL5TU6TNTT6JUFUBT555LB8UHJFHNBBB6JH8JHU5U668NJX3HLX8J37N33L573B3TLLNFH", "hasFlower": 0, "hasSeason": 0}, {"id": "63", "q": "000,3,5,7,9,C.20,3,5,7,9,C.44,6,8.50,2,A,C<PERSON>64,8.71,B.84,6,8.90,2,A,C.A5,7.B2,A.C0,5,7,C<PERSON>E0,2,6,A,C.G0,2,6,A,C;105,7.13,9.25,7.52,A.74,8.81,6,B.C5,7.E0,6,C.F2,A.G6;205,7.25,7.81,B.C6:7EXGP0LGD7OZ4ZGM0F49437O3ORM10E0TGIR9RGTDF08KKLX11GRXYPDCOXUC175T03T5I3U84DY", "hasFlower": 0, "hasSeason": 0}, {"id": "64", "q": "000,2,4,8,A,C.16.20,2,4,8,A,C.36.41,3,9,B.55,7.61,3,9,B.75,7.80,3,9,C.96.A1,3,9,B.B6.C0,3,9,C;104,8.10,2,A,C.26.31,B.46.51,B.65,7.96.A1,B.B3,6,9.C0,C;210,C.31,6,B.51,B.A1,6,B.B3,9.C0,C:SELLV6VVS1VVVC60TC0AKLF1TFFSHACG0CH1T6SFSTG1A6H1FGK1KKGKLKSECT0LH6LTACF600AA", "hasFlower": 0, "hasSeason": 0}, {"id": "65", "q": "001,3,6,9,B.20,2,4,6,8,A,C.40,3,5,7,9,C.60,3,5,7,9,C.82,A.90,4,8,C.B0,2,4,6,8,A,C;101,3,9,B.24,8.30,C.44,6,8.63,5,7,9.94,8.A0,C.B4,8;202,A.24,8.44,6,8.65,7.94,8.B4,8;345,7.66:EM838FY08K86IY0I666NK1FV3232EKMF6EY01MNYKM60IVYN8N8V3NIFI1V3MN3EYKM1V2VK2I", "hasFlower": 0, "hasSeason": 0}, {"id": "66", "q": "000,3,5,9,B,E.17.21,3,B,D<PERSON>36,8.40,3,B,E<PERSON>55,9.61,3,B,D.75,7,9.80,2,C,E.95,9.A0,3,7,B,E.C3,5,9,B.D0,E<PERSON>E2,6,8,C<PERSON>F4,<PERSON><PERSON>G0,7,E;105,9.23,B.36,8.55,9.82,5,9,C.A3,B.C5,9.E6,8;205,9.36,8.82,C.E6,8;382,C:OWX91CQFB97S4W0XEEWODGL7NWOJ3GDKZJNAXHAN9SLOHWZ6M6738CKYNZ6RYR19WQ8X0CMT7FZCT4B6", "hasFlower": 0, "hasSeason": 0}, {"id": "67", "q": "000,4,8,C.12,A.24,8.30,2,6,A,C.50,2,5,7,A,C.70,2,4,6,8,A,C.90,3,6,9,C.B0,3,5,7,9,C.D2,4,8,A.E0,C;113,9.31,6,B.50,5,7,C.70,4,6,8,C.A0,6,C.B3,9.D2,A.E0,C;255,7.70,5,7,C;355,7:PDJNXR6WPHGU3NRXAOW1ZVRLX9U5PZQV1CE46X79LBGYBRD7PVV47OVW3YB5WV8JHCM7BAMQ8E", "hasFlower": 0, "hasSeason": 0}, {"id": "68", "q": "001,3,6,9,B.20,2,4,8,A,C.40,5,7,C.52,A.60,4,6,8,C.80,2,4,8,A,C.96.A0,3,9,C.C0,2,4,8,A,C;103,6,9.11,B.23,9.30,C.50,2,A,C.70,C.82,A.A0,3,9,C.C1,3,9,B;211,B.23,9.52,A.82,A.B3,9.C1,B;352,A.82,A:3LYE63JY1QW91WQB4L13XQ4J4LJX8TBX6WEW09YQJBJW893L04T11QBJ18X3B008BYY904Y4L8Q38W0L", "hasFlower": 0, "hasSeason": 0}, {"id": "69", "q": "000,2,5,7,A,C.20,2,5,7,A,C.40,2,5,7,A,C.60,2,5,7,A,C.80,3,5,7,9,C.A1,3,5,7,9,B.C1,3,5,7,9,B.E0,3,9,C;121,5,7,B.40,2,A,C.70,5,7,C.93,9.B2,A.C5,7.D3,9;225,7.31,B.B2,A.D3,9;3B2,A:3PH9WR1SYQQBTZCUY53088IVR00COA1WXSQEQF2WEZUWUF71HUI9XP2OMPT05G5V5CXGB1C7AMPX", "hasFlower": 0, "hasSeason": 0}, {"id": "70", "q": "000,2,4,6,8,A,C.20,2,4,8,A,C.41,4,6,8,B.60,2,5,7,A,C.80,3,5,7,9,C.A0,2,6,A,C.B4,8.C0,2,A,C.D4,8.E0,2,A,C;102,A.10,C.44,6,8.60,C.83,9.90,C.A2,A.B4,8.E0,C;244,8.B4,8:OAB<PERSON>H<PERSON>YTMATQQWBGBQGW3HMYDGH5GMDDMDBGWIO3B3MMBO5O55HQATATIAOGHD53QOQ5", "hasFlower": 0, "hasSeason": 0}, {"id": "71", "q": "000,2,4,8,A,C.16.21,B.33,6,9.41,B.55,7.60,2,A,C<PERSON>75,7.82,A.94,6,8.A0,<PERSON><PERSON>B2,<PERSON><PERSON>C0,5,7,C<PERSON>E1,3,6,9,B.G0,2,4,6,8,A,C;100,C.21,6,B.33,9.41,6,B.60,6,C.86.B0,C<PERSON>C5,7.E1,3,6,9,B.G0,5,7,C;200,C.36.56.C6.E6;356.C6:GL84F0PL18R16Z9YSOH06ZHV8YFO9PG0HQ294R0O2LR0PZ9QV1XAA01XHPG8AGYRLSYXZPOGXGEWPW8EA8", "hasFlower": 0, "hasSeason": 0}, {"id": "72", "q": "004,6.10,2,8,A.24,6.30,2,8,A.44,6.50,A.62,4,6,8.70,A<PERSON>82,8.94,6.A0,A<PERSON>B3,5,7.C0,A.D2,4,6,8.E0,A;104,6.10,2,8,A.30,4,6,A.61,4,6,9.94,6.B5.C0,A.D3,7.E0,A;212,8.20,A.65.95.D0,A:VNCNCRRV1PMJ160LH8LSJ6PL3UY2G8KH15UG45SYMSQK0L53JL2QUZDSL3ZZKZ43KJ15DU", "hasFlower": 0, "hasSeason": 0}, {"id": "73", "q": "001,4,7,A,D.20,2,5,7,9,C,E.40,2,5,7,9,C,E.61,3,5,7,9,B,D.80,2,5,7,9,C,E.A0,2,6,8,C,E.C0,3,5,9,B,E.D7.E3,5,9,B;101,7,D.22,C.36,8.41,D.55,9.62,7,C.81,D.97.A2,C.D4,A;207.41,D.62,C.D4,A;362,C:7JZ9WEG49KKEGV3FE1MYJ5FWJVPTHWHPK95FWFY7ZHQ95RCYRHR4VMJ5GKMC5M10WGW35ZVQ6E0Z6TYR", "hasFlower": 0, "hasSeason": 0}, {"id": "74", "q": "000,3,6,9,C.20,3,5,7,9,C.40,3,6,9,C.60,3,6,9,C.83,9.90,6,C.A3,9.B0,6,C<PERSON>C2,A.D0,4,8,C.E2,A;100,C.16.23,9.36.40,3,9,C.56.60,C.90,C.A3,6,9.B0,C.D3,9;200,C.43,6,9.60,C.90,C;343,9:JFS7WU9F12S7G9FQ4WO4CJ2SRA2VD6G1Z5SASFPG2AJ1UCOA0F04FJDWQS1WQQZPGW6WV4R5", "hasFlower": 0, "hasSeason": 0}, {"id": "75", "q": "000,2,5,7,9,C,E.21,3,5,7,9,B,D.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.81,3,5,7,9,B,D.A0,2,5,7,9,C,E.C0,2,4,6,8,A,C,E.E0,5,7,9,E;100,5,9,E.33,6,8,B.53,6,8,B.71,D.96,8.B5,7,9.C3,B.E5,9:ZXOIOGZOZ6BGZGCB9ZJBXTJGCL9CIX9LOHV6IIOLO96JCTLXG9JVXTHLHT0LTX6BIZICBTJB9CJ0GH66", "hasFlower": 0, "hasSeason": 0}, {"id": "76", "q": "000,2,4,6,8,A,C,E.22,4,7,A,C<PERSON>41,6,8,D.53,B.60,5,9,E<PERSON>73,7,B.90,2,5,9,C,E.A7.B1,D<PERSON>C3,6,8,B.E5,9;102,4,6,8,A,C.22,4,A,C.37.53,B.73,7,B.92,C.A7.C3,6,8,B.E5,9;203,7,B.23,B.37.77.A7.C6,8:SP0QJQJJD0PNN1ARQ4ERPNSNRDSPJA11XN10XA14JQ40DAJ14SX0ED02PSPEDSXERNQX4Q4XEDE2", "hasFlower": 0, "hasSeason": 0}, {"id": "77", "q": "000,2,6,A,C<PERSON>14,8.21,6,B.34,8.41,6,B<PERSON>53,9.60,5,7,C<PERSON>73,9.80,6,C.93,9.A0,5,7,C<PERSON>B2,<PERSON><PERSON>C4,6,8.D1,B<PERSON>E4,8.F1,6,B.G3,9;100,C.24,6,8.46.63,9.80,3,6,9,C.A5,7.C4,6,8.E4,8.F6.G3,9;224,8.46.63,9.E4,8:PPNU09XLN89JZW9EWNL0WDLJNNJJUDEUP00DWWYLY8XTJZUFFPFTDPTJLL8FR8NFDWR9XDEPXEFT", "hasFlower": 0, "hasSeason": 0}, {"id": "78", "q": "000,2,4,8,A,C.16.20,3,9,C.35,7.40,2,A,C<PERSON>54,6,8.60,<PERSON><PERSON>74,8.81,B.93,5,7,9.A0,C<PERSON>B2,6,A<PERSON>C4,8.D0,6,C.E2,4,8,A;100,C.13,6,9.35,7.41,B.54,6,8.60,C.74,8.81,B.96.A0,C.C4,6,8.E2,4,8,A;213,9.36.41,B.C6.E3,9:TL17C8PA7PHRVAYTCA3NU32VLP8X2MIV1LOAIJ7OA3JZHUUHXJ22MRH7CJEUZVIYN3OE3LUUAOP3<PERSON>CL", "hasFlower": 0, "hasSeason": 0}, {"id": "79", "q": "000,5,7,C.20,3,5,7,9,C.40,2,5,7,A,C.60,2,6,A,C.80,3,5,7,9,C.A1,4,6,8,B.C0,4,8,C;105,7.23,5,7,9.30,C.42,A.60,2,A,C.83,5,7,9.A1,4,8,B.C4,8;205,7.24,8.62,A.83,5,7,9.A4,8;305,7.94,8:6K4ZW7XCFXHV0ITGSUAHGNUNU58I00J6IDYN29VG08JDCKKKHFBI2YB57S9KA3N4HLTGW3USSKLZ", "hasFlower": 0, "hasSeason": 0}, {"id": "80", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.41,3,5,7,9,B.60,3,5,7,9,C.80,3,5,7,9,C.A0,3,5,7,9,C.C1,3,5,7,9,B.E0,3,5,7,9,C;100,2,5,7,A,C.23,9.35,7.41,B.54,8.66.74,8.93,6,9.C4,8.E3,9;241,B.74,8.93,9.C4,8;393,9:70DGUPSXSJ0PSVQALLSD1A3NLWUBXJFTEZ9QW9TFAS3F3VZUGB14C4ZFC4A076N9G3SBBNGCLU64E0Z09CN0", "hasFlower": 0, "hasSeason": 0}, {"id": "81", "q": "000,3,5,7,9,C.20,4,6,8,C.41,3,5,7,9,B.60,2,6,A,C<PERSON>74,8.80,2,A,C.94,8.A0,2,A,C.B6.C0,3,9,C<PERSON>D5,7.E1,3,9,B.F5,7.G0,2,A,C;103,9.16.24,8.42,5,7,A.61,6,B.74,8.82,A.A0,C.B6.C0,C.D3,5,7,9.E1,B;261,6,B.A0,C.B6:M1GQ6EG85QKABPDX3J52YVYSIDG923SXA0386LI79AVJ6O52OY227O6YXO2HGXKULEVUMG13GH15P1BU0VAU", "hasFlower": 0, "hasSeason": 0}, {"id": "82", "q": "000,2,4,6,8,A,C,E.20,2,5,7,9,C,E.40,2,4,7,A,C,E.60,2,4,6,8,A,C,E.80,2,4,6,8,A,C,E.A0,2,5,7,9,C,E.C0,2,4,A,C,E.D6,8.E1,3,B,D.F5,7,9.G0,2,C,E.H5,7,9.I1,3,B,D;115,9.21,D.37.40,2,C,E.65,9.77.83,B.F2,C.G7.H1,D:7EE36CS235NIE71NBIQI3WQSIB7JU17NLWUUUJJ566W26SNFC6EBNCQ25CIIEB5FW2WW1LEFSQNC7J313U37LLUFC6", "hasFlower": 0, "hasSeason": 0}, {"id": "83", "q": "000,2,4,6,8,A,C.22,4,8,A.30,C<PERSON>43,9.55,7.61,B.74,6,8.95,7.A1,B.B6.C2,<PERSON><PERSON>D0,4,8,C.E2,<PERSON><PERSON>F4,8.G0,6,C;100,3,5,7,9,C.33,9.65,7.A1,6,B.D0,2,4,8,A,C.G0,C;200,C.66.A1,6,B.D0,C.G0,C;3A6.D0,C:Y9HO2RRTTKL2GUR2LF9R2J9HJUOJPOKYJYO2THY1UWB1TK3QWULWH3YLOOLTQP9XW2TXLKYGBF", "hasFlower": 0, "hasSeason": 0}, {"id": "84", "q": "000,2,5,7,9,C,E.20,2,4,6,8,A,C,E.40,2,4,7,A,C,E.60,4,6,8,A,E.81,3,6,8,B,D.A0,4,A,E.B2,7,C.C4,A.D0,2,6,8,C,E;101,5,7,9,D.20,E.32,C.40,4,A,E.57.60,E<PERSON>76,8.A0,E<PERSON>B4,<PERSON><PERSON>C2,<PERSON><PERSON>D0,<PERSON>;206,8.20,E.57.D0,E;306,8:0PO49LNEHI8JBP1JRD90P8X9INXWMNZCXAAN4J32WZLIPH0KDJ394AIBS1X2APRCPZGKYROEXSZRYXWWGM04", "hasFlower": 0, "hasSeason": 0}, {"id": "85", "q": "000,2,4,6,8,A,C,E.20,3,B,E<PERSON>36,8.41,D<PERSON>53,5,7,9,B.60,E<PERSON>73,5,7,9,B.80,E.93,5,7,9,B.A0,E.B2,6,8,C<PERSON>C4,<PERSON><PERSON>D0,E<PERSON>E3,5,9,B;102,4,7,A,C.46,8.60,3,7,B,E.75,9.90,3,7,B,E.B6,8;246,8.63,B.93,B.B6,8:Y7QG7AS6WICRVH6YS8R89GAV5AWOPCHFOPFMY5H7O9N6XBP4SIYHAQ74QTS6OSIESV6EINMBPXQTV6", "hasFlower": 0, "hasSeason": 0}, {"id": "86", "q": "000,5,7,9,E.20,6,8,E.32,4,A,<PERSON><PERSON>50,6,8,E<PERSON>64,A<PERSON>70,6,8,<PERSON><PERSON>82,C.90,5,9,E.A3,7,B.B1,5,9,D.C7.D1,3,B,<PERSON>.E6,8.F0,E<PERSON>G5,9;100,6,8,E.20,6,8,E.33,B.56,8.76,8.81,D.95,9.B1,5,9,D.C7.D2,C.F0,E;210,E.26,8.33,B.66,8.B5,9:T3MWFR1J3KITU7ZF4MWRWKUMBR3FFP4RZIT2I1443M1J2B7FRP1WMB7T3KTJTF4IZ1K27K1BKZBMRWB423JW", "hasFlower": 0, "hasSeason": 0}, {"id": "87", "q": "000,2,4,6,8,A.20,3,5,7,A.40,3,7,A.55.60,3,7,A.81,3,7,9.A0,2,4,6,8,A.C1,3,5,7,9.E0,3,5,7,A;102,5,8.20,3,7,A.43,7.50,A.73,7.91,9.A4,6.C2,4,6,8.E4,6;220,A.33,7.50,A.73,7.A4,6.C3,7.D5;320,A.73,7:8QV8SU1NTN0E08JQJPTD3ODCQFKC3P8YKZCSAPEATK8P3V3ZL1OZP0OJFU3LOTYAZ0VUCPSS8AUQVK3J", "hasFlower": 0, "hasSeason": 0}, {"id": "88", "q": "000,3,5,7,9,C.20,3,5,7,9,C.40,2,4,6,8,A,C.61,3,6,9,B.80,4,6,8,C.92,A.A0,4,6,8,C.B2,<PERSON><PERSON>C0,5,7,C.D3,9.E1,5,7,B;114,8.20,6,C.33,9.41,6,B.63,9.80,C.93,5,7,9.B1,6,B.D6.E1,B;241,B.94,8.B1,B:FTWFMXEWWUNM93L1YY8L8MAXNWX8NWXTEEJ166MN9MFLL55LF8EX6JTYMXT6WYF3NUA68ENLT8E6TF", "hasFlower": 0, "hasSeason": 0}, {"id": "89", "q": "000,3,9,C.15,7.20,3,9,C.36.40,2,4,8,A,C.56.61,B<PERSON>73,9.80,C<PERSON>92,A.B1,5,7,B<PERSON>C3,9.D0,5,7,<PERSON><PERSON>E2,<PERSON><PERSON>F0,5,7,<PERSON><PERSON>G2,A;100,C.14,8.26.30,3,9,C<PERSON>51,6,B.73,9.92,A.B1,5,7,B.C3,9.D0,C.E2,5,7,A.G2,A;200,C.56.B6.D0,C.E2,A;3D0,C:U1UCBBZ0U7ICOCJXC0QLO2GO6BZZ27LZ7SRRXB60IQOLQF76GR86E2S26LOG3S1RXEJIIS6OXX83QUFBGXB0", "hasFlower": 0, "hasSeason": 0}, {"id": "90", "q": "000,3,5,7,9,C.21,3,6,9,B.41,3,6,9,B.71,4,6,8,B.90,2,5,7,A,C.C1,3,6,9,B.E1,B.F3,6,9.G0,C;100,4,6,8,C.21,B.33,6,9.41,B.71,4,8,B.86.92,A.C2,6,A.E1,B.G0,C;204,8.21,B.74,8.86.C2,A.E1,B.G0,C:836B8GBTPC11TCTE6BWDIGEWOVVAIV32CWV2DOGWPWE91A14EOVITWVCPBPGUUTPIOPCGEOTOG4C9E", "hasFlower": 0, "hasSeason": 0}, {"id": "91", "q": "000,2,4,7,9,C,E,G.21,3,5,7,9,B,D,F.41,4,7,9,C,F.62,5,8,B,E.70,G.82,5,7,9,B,E.90,G.A2,4,7,9,C,E.C0,4,6,8,A,C,G.D2,E.E4,6,8,A,C.F1,F.G3,5,7,9,B,D;102,8,E.22,5,8,B,E.41,8,F.62,E.81,5,B,F.C4,6,8,A,C.E4,C.F8.G3,D:E87XCAEZJXR6ADNSZB11IJJ2XVS18JC17E59Q8AZQ9FEJ98A6198I5VBTBAFSXA9R6V69DBTFZ25RBRVNSFZZVBJ185V", "hasFlower": 0, "hasSeason": 0}, {"id": "92", "q": "001,3,6,9,B.20,2,4,6,8,A,C<PERSON>41,5,7,B.53,9.60,5,7,C<PERSON>72,A.80,6,C.93,9.A0,5,7,C.C0,3,5,7,9,C.E0,2,4,8,A,C;120,2,A,C.41,B.53,5,7,9.60,C.72,6,A.90,3,9,C.A5,7.B0,C.C4,8.D0,C;221,B.55,7.A5,7.C4,8;3C4,8:16USDA9BCQJ3BW5RY61S68BB8LFXD90ZSDYUX8MD31WS2RNJ61IQDMOL3NO9K0ZZ83AKUD9CUJ5FJ2NZNI", "hasFlower": 0, "hasSeason": 0}, {"id": "93", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,2,4,6,8,A,C.60,3,5,7,9,C.80,4,6,8,C.A1,3,5,7,9,B.C0,2,4,6,8,A,C.E2,4,6,8,A;110,2,5,7,A,C.42,A.54,6,8.75,7.A1,4,6,8,B.D2,A<PERSON>E5,7;215,7.56.A1,6,B.E5,7:A94FFFNZTT93A3NTI43NF9ITX6XR4ZWNR6O3XA6W6X8O9A6ONTR8Z8CRWWZRWTRCGWN9OXOOG46Z9XZ8", "hasFlower": 0, "hasSeason": 0}, {"id": "94", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,2,4,6,8,A,C.60,3,6,9,C.80,3,5,7,9,C.A2,4,6,8,A.B0,C.C2,4,6,8,A.D0,C.E2,4,6,8,A.F0,C.G2,6,A;102,A.20,C.35,7.41,B.85,7.A2,4,8,A.C2,4,8,A.E5,7.F0,C:NBBGTMQMJ0JXDO2DJ6OV20FI22BI1BXGSJFQDYQVQSIX7ROD7IOYO6NBWFXG9RTQW91VTOBTVQGJJF", "hasFlower": 0, "hasSeason": 0}, {"id": "95", "q": "001,3,5,7,9,B.20,2,4,8,A,C.40,2,4,8,A,C.56.60,2,4,8,A,C.80,2,4,6,8,A,C.A0,2,5,7,A,C.C0,2,5,7,A,C.E1,3,5,7,9,B.G0,2,5,7,A,C;101,4,8,B.24,8.30,C.42,4,8,A.50,C.81,B.A5,7.C0,2,A,C.E3,9.F5,7;201,B.42,A.C1,B:L38A0II2BBNJNPPF8RPTC6K3BFFRN5VV0VP7XKXYYXORVYNXJGT600ALJCZAXBCWVV6XGRWUU4A7F5Z5CJ6Y2O54", "hasFlower": 0, "hasSeason": 0}, {"id": "96", "q": "000,2,5,7,A,C.21,3,6,9,B.41,4,8,B.62,4,8,A.76.81,3,9,B.95,7.A1,3,9,B.C0,2,4,8,A,C.E1,3,5,7,9,B.G1,4,6,8,B;112,A.26.31,B.54,8.72,A.86.93,9.A1,B.C3,9.E1,5,7,B.G5,7;272,A.93,9.E6;393,9:7513X185SYKNBJ7JRO8W2F3S1WE7F5EP23V788DHWWKP3M7DYBH7EASU1AXVON54R4OE515OS1MU", "hasFlower": 0, "hasSeason": 0}, {"id": "97", "q": "000,2,4,6,8,A,C.20,2,4,8,A,C.36.41,3,9,B.55,7.60,2,A,C.75,7.80,2,A,C.94,8.A0,2,6,A,C.B4,8.C0,2,6,A,C.E0,2,5,7,A,C.G0,2,5,7,A,C;102,5,7,A.20,3,9,C.36.42,A.60,2,A,C.80,2,A,C.A1,B.C2,A.E1,5,7,B.G6:0QDHTIGDSZ6GZ5YNECK4JWW6ZHVDLPAL1FCI0WT4HC1EAUKVDU3PI9TB189CD4Q1WDOFJO8H3STN95YZI94B", "hasFlower": 0, "hasSeason": 0}, {"id": "98", "q": "001,3,6,8,B,D.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,3,5,7,9,B,E.81,3,5,9,B,D.97.A0,2,4,A,C,E.B6,8.C0,3,B,E.D5,7,9.E0,2,C,E;106,8.11,D.46,8.73,5,9,B.81,D.A1,7,D.E0,E:PWC59XN8IPAW885X5R9L72NWA7O8CHPPRCI7IJRWRO758WJPWLL2JCJD7A79H8AJ9LDILJPL", "hasFlower": 0, "hasSeason": 0}, {"id": "99", "q": "001,5,7,B.13,9.20,5,7,C.32,A.40,4,8,C<PERSON>52,A.65,7.71,3,9,B.86.93,9.A0,5,7,C<PERSON>B2,A.C0,4,6,8,C.D2,A<PERSON>E0,4,8,C;113,9.26.32,A.44,8.66.93,6,9.B2,5,7,A.D2,A;213,9.32,A.B6.C2,A;332,A.C2,A:ZAHTGYPB2D120SYVDPIVKSNN4SXNLTV9YGAEKV1Y9IYLY10S1TZTUH0BUXBEN0BHOO004H11", "hasFlower": 0, "hasSeason": 0}, {"id": "100", "q": "000,3,5,7,9,B,E.20,2,4,7,A,C,E.40,2,4,7,A,C,E.60,2,4,6,8,A,C,E.80,2,4,6,8,A,C,E.A0,2,4,6,8,A,C,E.C3,5,9,B.D0,7,E.E2,4,A,C;104,A.21,3,7,B,D.40,7,E.62,6,8,C.70,4,A,E.87.92,C.A6,8.C4,A.D7.E2,C:3FCMEYPPQCC0IAEIUXBXYRE7P1VYJIQBVKBVAC3CVB2MEZBRMFX20ZN3NZ0Z3J0ZX1ICUYYBQP73Z3FMKFYQ", "hasFlower": 0, "hasSeason": 0}, {"id": "101", "q": "001,3,5,7,9,B.20,2,4,6,8,A,C.41,3,5,7,9,B.61,3,5,7,9,B.80,2,4,6,8,A,C.A0,2,4,8,A,C.C0,2,4,6,8,A,C.E2,4,6,8,A.F0,C.G2,4,8,A;111,B.35,7.52,4,8,A.71,3,9,B.A1,3,9,B.C2,4,8,A.E5,7;211,B.72,A.A2,A:2KRDYQ0JSXZX1LAL1JM6YQPK562Z4QE5L2J2AO0X4E6X8KS5ERD09PML4JA0J6EKA4R5OK9K99R7JLT87LTQ", "hasFlower": 0, "hasSeason": 0}, {"id": "102", "q": "001,4,6,8,B.20,2,4,8,A,C.40,2,4,6,8,A,C.60,2,4,6,8,A,C.80,2,4,8,A,C.A2,4,6,8,A.B0,C.C2,4,6,8,A.D0,C<PERSON>E3,5,7,9.F0,C.G2,6,A;101,5,7,B.23,9.31,B.45,7.50,C.62,5,7,A.80,4,8,C.A5,7.B2,A.C0,4,8,C.E3,9:HHM5BBUMY4VHHUM1C9UV50DTBM0OCC1YYLMTM2YOFCYLBL40STTDT2F25LVDLTOO20JOO5L9DVBYJHHB9S9U", "hasFlower": 0, "hasSeason": 0}, {"id": "103", "q": "004,8.10,2,6,A,C<PERSON>31,4,8,B.52,5,7,A.60,C.80,5,7,C.A0,C.C2,5,7,<PERSON>.D0,C.E2,4,8,A.F0,<PERSON>.G2,6,A;104,8.10,2,6,A,C.31,B.52,5,7,A.C2,5,7,A.E1,3,9,B.G2,6,A;210,2,A,C.55,7.C5,7.E3,9.G2,A;355,7.C5,7;455,7.C5,7;556.C6:IRQBRBQZ4JWRALXZKAL3OO33LXZRJOOZI4JGWZ22WO1R4U4BIGVBLZ1KJIRLA3AXLXQVAA2QWOG3G2IJ3IUJ", "hasFlower": 0, "hasSeason": 0}, {"id": "104", "q": "001,B.14,8.26.30,4,8,C.42,6,A.50,4,8,C.62,6,A.70,C.82,4,6,8,A.A0,4,8,C.B6.C0,4,8,C;114,8.26.30,4,8,C.52,4,8,A.66.71,B.84,8.A0,4,8,C.B6.C4,8;224,6,8.52,A.B4,6,8;324,8.52,A.B4,8;424,8.B4,8:6F3F5LK8Y3Y2W856LO1KK25N6HKHH811JL82OJDFLJFDY1NW8FDYH2L1K65DJ83Y3K636Y1F3HHL", "hasFlower": 0, "hasSeason": 0}, {"id": "105", "q": "000,2,4,6,8,A,C,E.23,6,8,B.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.80,3,5,9,B,E.A0,2,4,6,8,A,C,E.C2,4,6,8,A,C.E1,3,5,7,9,B,D;100,6,8,E.36,8.54,6,8,A.A4,6,8,A.C6,8.E2,7,C;255,9.A5,9:H1IU1S0DDCBSC787I7H058IBJJ1C8578S7HBDB50S15U58SHHID1UDC0BJ0UJ75CI18CIHJUUSBD0J", "hasFlower": 0, "hasSeason": 0}, {"id": "106", "q": "000,2,4,7,A,C,E.20,2,4,A,C,E.36,8.40,2,C,E.54,7,A<PERSON>60,2,C,E.74,6,8,A.80,2,C,E.96,8.A0,3,B,<PERSON><PERSON>B5,9.C1,7,<PERSON>.D3,B<PERSON>E0,5,7,9,E.F2,C.G0,4,6,8,A,E;101,D.14,A.37.62,C.74,7,A.80,2,C,E<PERSON>96,8.D3,7,B.F7.G0,4,A,E:NU0EBEICNU6Q7PU7IPLEX4X6PY4E7I46L6CDCXEDC4ULICQECNPB7LUN4IQ0X0PUD7Y06N6Y7Y4Y0YQPN0DI", "hasFlower": 0, "hasSeason": 0}, {"id": "107", "q": "000,3,6,9,C.20,2,5,7,A,C.40,3,5,7,9,C.60,2,5,7,A,C.80,3,5,7,9,C.A0,2,4,8,A,C.B6.C0,3,9,C.D5,7.E0,2,A,<PERSON><PERSON>F5,7.G0,2,A,C;103,6,9.10,C.25,7.30,C.45,7.61,5,7,B.84,8.A1,B.C3,6,9.E0,5,7,C.G0,C:6JYVBY6ENT60NDEK6VSY9OEOSB4QCTE5NQ1F19KUB4HVJVUR3FA3YXM1L1RZAB9WMRNDL9R7WC0HZX75", "hasFlower": 0, "hasSeason": 0}, {"id": "108", "q": "002,4,6,8,A.10,C.22,4,8,A.30,C<PERSON>43,5,7,9.50,C<PERSON>65,7.71,3,9,B.90,5,7,<PERSON><PERSON>A2,A.B4,8.C0,2,A,C.D4,8.E0,6,C;103,6,9.10,C.22,A.30,C.46.65,7.71,B.90,5,7,C.B2,A.C0,C.E0,C;220,C.66.96.D0,C;320,C.66.96.D0,C;466.96:JWI1EF02J1W9IA4SLY1U0Y29S9L44UIS1OFY709AWO1Z15SKIBVZYJEVB5JKL0SDRS4DRBFBZF7WLZDD", "hasFlower": 0, "hasSeason": 0}, {"id": "109", "q": "002,4,8,A.10,C<PERSON>23,6,9.30,C.42,5,7,A.60,2,4,8,A,C.76.80,2,A,C<PERSON>95,7.A0,3,9,C<PERSON>B5,7.C1,<PERSON><PERSON>D5,7.E0,<PERSON><PERSON>F3,6,9.G0,C;103,9.20,6,C.42,6,A.62,4,8,A.76.95,7.A3,9.B5,7.D6.F0,6,C;226.52,A.64,8.76.95,7.B5,7.E6;364,8.76.96.B6;476.A6;576.A6:W19Y6T1FPF52P2NJXRIR8I5JE8YOF6491D5XX8XFJXN0E1WI8TO8W49I48DGDGX5JS6FYD9GYOI4NN0GMWFIOS6M", "hasFlower": 0, "hasSeason": 0}, {"id": "110", "q": "001,4,6,8,B.21,5,7,B.41,3,6,9,B.61,4,8,B.80,5,7,C<PERSON>A1,3,5,7,9,B.C0,3,6,9,C.E0,4,8,C.G1,3,6,9,B;101,5,7,B.31,6,B.51,B.64,8.80,C.A1,6,B.B3,9.E0,C.G1,6,B;206.31,6,B.64,8.A1,6,B.G1,6,B;306.31,B.A6;4A6:HXQRSH4UCRCU13ID52VUTHIEQEX5DNG71MO1T1O31EO3YDD3E2M7NHOR5UGS13DY342GOR4G2VXO5X4IDI", "hasFlower": 0, "hasSeason": 0}, {"id": "111", "q": "000,2,5,7,9,C,E.20,2,4,7,A,C,E.40,2,5,7,9,C,E.60,2,4,7,A,C,E.80,2,4,6,8,A,C,E.A0,2,6,8,C,E.C0,2,4,A,C,E.D7.E0,2,4,A,C,E;102,C.17.30,E.42,C.57.61,3,B,D.82,4,A,C.A7.B2,C.C0,E.E1,3,B,D:3XCO1CO1CBXK6BLZ3CMOIZL69LXKZBKB6JKLLJ9HH6B2XDX3BJKHZ36IO888HDLOM8H93HJ2J936KXJO", "hasFlower": 0, "hasSeason": 0}, {"id": "112", "q": "002,8.14,6.20,A.32,4,6,8.40,A.52,4,6,8.60,A.72,8.80,A.92,5,8.A0,A<PERSON>C0,2,4,6,8,A.E2,4,6,8;114,6.30,A.42,8.50,5,A.62,8.82,8.90,A<PERSON>C0,A<PERSON>D3,5,7;214,6.30,A.42,8.50,A<PERSON>C0,A<PERSON>D4,6;330,A.50,A<PERSON>C0,A<PERSON>D5;430,A.50,A.C0,A;550,A:SGO9Y9779YHGHYGY1HO1MO8IS7NHN08O8Y0NMV0VVO1S8N701N98S7N8MOMY9V9GIVI7IMS00HI1SGIHMG1V", "hasFlower": 0, "hasSeason": 0}, {"id": "113", "q": "001,3,6,9,B.21,3,5,7,9,B.40,2,5,7,A,C.62,4,8,A.76.80,2,4,8,A,C.96.A1,3,9,B.B5,7.C0,C.D2,5,7,A.F1,3,5,7,9,B;103,6,9.11,B.25,7.41,5,7,B.63,9.76.84,8.96.A2,A.B6.C0,C.D5,7.F1,5,7,B;203,9.25,7.45,7.84,8.D5,7.F1,5,7,B;326.E5,7:B68VAHHVQH61XM4A6IQFHH341188FJYXJBXQVY1M8I1MQAF8JMBIQ48B6JH44J3XVVYABFI3A16Q3BFXM64YFMYVYJAX", "hasFlower": 0, "hasSeason": 0}, {"id": "114", "q": "000,2,5,7,9,C,E.20,2,4,7,A,C,E.43,7,B.50,5,9,E.62,7,C.70,5,9,E.91,4,6,8,A,D.B0,2,7,C,E.C5,9.D3,7,B.E0,5,9,E.F2,7,C;100,5,7,9,E.24,7,A.62,5,9,C.70,E.85,9.A1,7,D.D4,6,8,A.E0,E.F7;206,8.24,A.65,9.70,E.D7.E0,E;307.65,9;407:5GNNPZUYUHU0607SVV7EXLXAEYH0ELDGFPODB7GS6X037FNUNOHP36U6BNEEGBD7DA66BL5ZLMGBGVF1EXBUHN1MFPV7", "hasFlower": 0, "hasSeason": 0}, {"id": "115", "q": "001,3,6,8,B,D.20,3,6,8,B,E.40,2,5,7,9,C,E.61,3,5,7,9,B,D.80,5,9,E.92,7,C.B0,2,5,7,9,C,E.D0,3,6,8,B,E;103,7,B.26,8.41,5,7,9,D.61,4,A,D.92,C.A7.B1,5,9,D.C7;226,8.46,8.51,D.A7;326,8;427:SDKZA0YK875N7QSOKE55NSAJJ7EAD78EQZOIN3ENZZYEKJYNYGD357SJQQD0AO75A0AQ0GE5Q88OIN", "hasFlower": 0, "hasSeason": 0}, {"id": "116", "q": "000,3,5,7,9,C.20,2,4,6,8,A,C.41,3,6,9,B.60,2,4,6,8,A,C.80,2,4,6,8,A,C.A0,2,4,8,A,C.B6.C0,2,A,C.D4,6,8.E2,A;104,8.10,C.23,5,7,9.31,B.46.51,3,9,B.70,3,9,C.90,2,A,C.B0,2,6,A,C.D3,5,7,9:L8NU915928N9RJ11B5L82X5LQJIC9CSS22SQKQN2JIOE40WU1JOXE8E58XIRKIJNJE2QLSW5EX0BE854", "hasFlower": 0, "hasSeason": 0}, {"id": "117", "q": "001,3,6,8,B,D.20,4,6,8,A,E.41,3,7,B,D.63,5,9,B.70,7,E<PERSON>82,5,9,C.A3,B.B1,7,D.C3,B.D0,5,7,9,E;101,6,8,D.24,7,A.42,7,C.65,9.70,7,E<PERSON>85,9.A3,B.B7.C3,B.D7;201,7,D.37.65,9.70,E.A3,B.B7;365,9.A3,B.B7;465,9.B7;565,9:UWWXRQJUA80AGR03JOSG8PUQJ1OKDD3G88AUMJKQM13KM1N31K8X8GOW3322M2N1QOQR1WDQANWP2S0NW0DR", "hasFlower": 0, "hasSeason": 0}, {"id": "118", "q": "001,3,9,B.15,7.20,3,9,C.35,7.40,C<PERSON>52,4,6,8,A.60,C.72,4,8,A.80,6,C.A0,4,8,C.C0,2,4,6,8,A,C;103,9.20,3,5,7,9,C.40,5,7,C.61,3,9,B.80,C.B0,C.C2,4,8,A;225,7.46.62,A.80,C.C4,8;380,C;480,C:IPYFNNJH9Z7P0ENY857NLYLH9LHZ22H8AE80P4V070YIH7F5VV5ZV4LLJ8OO55OZHOLYYRARP5EE", "hasFlower": 0, "hasSeason": 0}, {"id": "119", "q": "005,9.10,2,7,C,<PERSON><PERSON>25,9.30,2,7,<PERSON>,<PERSON><PERSON>44,A<PERSON>50,2,7,C,<PERSON><PERSON>64,<PERSON><PERSON>71,6,8,D.84,A<PERSON>92,6,8,C.A0,4,A,E.B7.C0,2,5,9,C,E.E0,2,4,7,A,C,E;105,9.11,D.25,7,9.32,C.53,7,B.74,A.92,4,6,8,A,C.C2,5,9,<PERSON>.D0,E<PERSON>E4,A;215,9.57.C5,9.E4,A;3E4,A:B7Z4V6EIEU73971UEBZW9NFEEKSIS4VCFWWFZ6J5698ZC3JH97ESZUK255OHBBBHFWFB6ZI5OUNNHIIS81N323IF", "hasFlower": 0, "hasSeason": 0}, {"id": "120", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,2,4,6,8,A,C.60,3,5,7,9,C.81,3,5,7,9,B.A1,3,5,7,9,B.C0,2,5,7,A,C.E2,4,6,8,A;103,5,7,9.11,B.33,6,9.50,4,6,8,C.75,7.81,B.A4,6,8.C6.E6;204,8.46.75,7.A5,7:882WAR2TKKV284R8EA7Q24271LQVRK71TEEKLT1EQRAKRQWADLDW1A2SAD4LTQ4T7TDD8SQL77RKWL8D", "hasFlower": 0, "hasSeason": 0}, {"id": "121", "q": "000,2,4,6,8,A,C.20,2,4,8,A,C.36.41,4,8,B.56.60,2,4,8,A,C.83,5,7,9.A0,3,9,C.B5,7.C0,3,9,C.D5,7.E0,2,A,C<PERSON>F4,8.G0,2,A,C;103,9.24,8.31,6,B.64,8.83,6,9.B4,6,8.D5,7.E0,C.F4,8;224,8.E0,C.F4,8;324,8.F4,8:EJIUTGQLQSF119I063M4SGBKABZRW3GS9QM32LUK6IDP6QTKR3PF3ZILLL1PPV4Z20AVW3S6JEG1LFKDZF", "hasFlower": 0, "hasSeason": 0}, {"id": "122", "q": "000,2,4,6,8,A,C,E.20,3,5,9,B,E.44,6,8,A.51,D<PERSON>63,6,8,B.71,D.83,5,7,9,B.90,E.A2,4,6,8,A,C.C0,4,6,8,A,E.E1,4,7,A,D;114,A.34,A.67.72,C.97.A2,4,A,C.C4,6,8,A.E4,A;214,A.34,A.C4,A.E4,A:Y070A7JWP0UJUAUHVCQLSDCUA11CHQHACLHS4UO66SW0YPDO0AY6PYPWOCHOVLJJ70WAL6S7CH4U", "hasFlower": 0, "hasSeason": 0}, {"id": "123", "q": "000,3,5,7,9,C.24,6,8.31,B.44,6,8.52,A.60,4,8,C<PERSON>76.81,3,9,B.A2,4,6,8,A.B0,C<PERSON>C3,5,7,9.E2,4,8,A;100,5,7,C.31,5,7,B.53,9.82,A.A3,5,7,9.C3,5,7,9.E2,4,8,A;205,7.36.82,A.A5,7.B3,9.C6.D4,8;382,A.A5,7:LO7HRC1HZTER9B3EZ6DZRMOHH6RC33M10CEBTCHBCDRPMPDM7PP6ZOLE9MCT0DPB3T7R77MHO317P316", "hasFlower": 0, "hasSeason": 0}, {"id": "124", "q": "002,5,7,9,C.20,2,4,6,8,A,C,E.40,2,7,C,E.55,9.71,3,5,7,9,B,D.94,6,8,A.A0,2,C,E.B7.C1,D.D3,6,8,B.E1,D;102,7,C.21,6,8,D.40,2,C,E.71,4,6,8,A,D.97.A1,D.C1,7,D;202,7,C.26,8.71,4,A,D.C7;317.74,A.C7;417.C7;5C7:AYMV5EM2IJ3EVUI5X05SAX5223UF6IJW2ZY5QC9FS0WZMDD69FJUAXXX4XI32J6LFJKCMAU5AQC6K2C4J3AL", "hasFlower": 0, "hasSeason": 0}, {"id": "125", "q": "000,3,5,9,B,E.21,3,5,7,9,B,D.40,2,4,7,A,C,E.61,3,5,7,9,B,D.80,2,4,6,8,A,C,E.A0,2,4,6,8,A,C,E.C1,3,5,7,9,B,D.E3,5,7,9,B.F1,D.G6,8;100,5,9,E.33,B.40,E.72,C.80,6,8,E.A2,C.C1,<PERSON><PERSON>D4,A<PERSON>E6,8.G7;272,C.86,8.A2,C:N2NU9R66D79T6XPTD8C4IGCHM9M2JU2A8EJOW2C9OFGCSE7MV4PJ27CRXOH06F07JIHNP2KWTV1AK1PHTNSO8MC8", "hasFlower": 0, "hasSeason": 0}, {"id": "126", "q": "001,3,5,7,9,B,D.21,4,6,8,A,D.40,3,5,7,9,B,E.60,2,5,7,9,C,E.86,8.90,2,C,E.A4,6,8,A.B0,E.C2,4,7,A,C.E1,4,A,D;140,E.60,2,C,E.76,8.90,2,C,E.B0,E;240,E.60,E.76,8.90,E.B0,E;360,E.76,8.90,E;476,8;576,8:2EZTPUZHQBAN0WL0JYZSKK8NPSHRZFLHPU285Y5MV2P1W4MF5RLTS22L1GRJHBKYHEAD52SR0HY4QKDGV0", "hasFlower": 0, "hasSeason": 0}, {"id": "127", "q": "002,5,8.10,A.24,6.31,9.43,7.50,A.63,5,7.70,A.82,4,6,8.90,A<PERSON>A2,4,6,8.B0,A<PERSON>C2,4,6,8.E0,3,7,A;102,5,8.10,A.43,7.50,A.64,6.70,A.84,6.A0,5,A<PERSON>B3,7.D3,7.E0,A;202,5,8.10,A.43,7.60,4,6,A.95.B3,7.D3,7.E0,A;302,8.64,6.D3,7:B7XL3DA3523XG7BXN5LZDNOEGD6ODAO7YZ8XBR8YS6H0SLHZHT0OR63N60DHZMZY7TMY7ZA2EBTDC0LCTA7GNG", "hasFlower": 0, "hasSeason": 0}, {"id": "128", "q": "001,3,5,7,9.20,5,A.32,8.40,5,A.52,8.60,4,6,A.80,4,6,A.92,8.A0,4,6,A<PERSON>B2,8.C0,4,6,A<PERSON>E1,3,5,7,9;101,4,6,9.20,5,A.32,8.40,A.55.60,A.75.90,3,5,7,A.B0,3,7,A.C5.E2,4,6,8;201,9.65.85.B3,7.E2,4,6,8;301,9.E3,5,7:QPIKEPRJ8837TQ2PP5882IK5C2AT73P72UGAUCJI29H89AT2TAG9HAJ7NRR9REI33PN3NJAKJK7Q8Q3CNC7J", "hasFlower": 0, "hasSeason": 0}, {"id": "129", "q": "001,3,6,8,B,D.21,3,5,7,9,B,D.41,4,6,8,A,D.64,A<PERSON>70,7,E.83,5,9,B.A1,4,A,D.B6,8.C0,3,B,E.D5,7,9.E0,3,B,E;101,3,6,8,B,D.27.44,6,8,A.64,A.70,E.83,B.A4,A.B6,8.C0,E.D7.E3,B;203,7,B.27.45,7,9.64,A.83,B.B7.D7:FCRQKQBCQIIFI8V8BKCRTFBK4TR76428482IBVV767VQ2TTR62KTCRKIFCBQC48672T7B74FVQ26F8KRI6V4", "hasFlower": 0, "hasSeason": 0}, {"id": "130", "q": "000,2,4,6,8,A,C,E.20,2,C,E.40,E<PERSON>60,E<PERSON>80,E.A0,E.C0,2,C,E.E0,2,4,A,C,E;100,2,6,8,C,E.21,D.40,E.70,<PERSON><PERSON>B0,<PERSON><PERSON>D1,D<PERSON>E4,A;200,6,8,E.21,D.40,E<PERSON>70,E<PERSON>B0,E.D1,D.E4,A;306,8.21,D.40,E.70,E.B0,E.D1,D.E4,A;406,8.E4,A;506,8:NZQ2FK6R7W2ST1XSZ7VSS1A8O8ZZKN4TW4XU86QNBMTOVRRFD1NO64S8YEI4ORBN16NIUMSZYATEZUDHH1U1", "hasFlower": 0, "hasSeason": 0}, {"id": "131", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.42,4,8,A.50,C.62,5,7,A.70,C.82,5,7,A.A0,2,4,8,A,C.C0,3,5,7,9,C.E0,2,5,7,A,C;100,2,A,C.14,8.21,B.42,4,8,A.62,5,7,A.82,5,7,A.A3,9.B0,C.E0,5,7,C;201,B.44,8.B0,C:0B1MG34Z3LCS25OASJNMJG8MLCR0TN3TC0T1TB3A850CM2P1Z4NH8D2D008S21LP4TJOHJ1U1SNTMURML4", "hasFlower": 0, "hasSeason": 0}, {"id": "132", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.80,2,5,7,9,C,E.A1,4,6,8,A,D.C0,2,4,6,8,A,C,E.E0,5,9,E;101,3,B,D.16,8.33,7,B.40,E.56,8.71,7,D.A1,6,8,D.B4,A.C7;217.33,B.47.A6,8;333,B;433,B;533,B:A2ROB3M5DAX6G2L466Y8HM6LXUGD6BR2OXRXYSO5BOVVA55MDG86LU2WRYYMD4UM28LY2B4H538LL58OYOWM4GGGSAU8", "hasFlower": 0, "hasSeason": 0}, {"id": "133", "q": "002,5,7,9,C.10,E.22,5,7,9,C<PERSON>30,E.43,5,9,B.50,7,E<PERSON>63,B.70,5,9,E.83,B.96,8.A0,E.B3,5,9,B.C0,E.D2,5,7,9,<PERSON>.E0,E;105,7,9.21,D.40,3,B,E.57.63,B.70,E.83,B.96,8.A0,E.B3,B.C0,E;205,9.21,D.63,B.C0,E;3C0,E:LZ8VG11EPAZX0QQ179CWJJTSH3INV7E6U11W6DTOJT80F16GDONDHUBQT32AT2YSTDQBCYLFPJYY9XI6", "hasFlower": 0, "hasSeason": 0}, {"id": "134", "q": "001,3,6,8,B,D.20,2,5,9,C,E.40,2,5,9,C,E.57.60,5,9,E.72,7,C.80,4,A,E.96,8.A2,C.B0,5,9,E<PERSON>C3,B.D0,5,9,E.E7;101,3,6,8,B,D.25,9.31,D.45,9.57.65,9.84,A.96,8.B5,9.D5,9;201,3,6,8,B,D.84,A;301,3,B,D;401,3,B,D;502,C:ZQHMVEV84SM66EJ6PXFXVZSPE418VE3TQDDXM223TZDT2E346QXJ3GM4T121MTHVSDQTPJ2JZVXF2XPGE1SMDD", "hasFlower": 0, "hasSeason": 0}, {"id": "135", "q": "001,3,5,7,9,B.20,2,5,7,A,C.40,4,8,C.56.60,4,8,C.76.80,4,8,C.96.A1,3,9,B.B5,7.C1,3,9,B.D5,7.E1,B;101,4,6,8,B.20,2,6,A,C.50,4,6,8,C.70,C.96.A2,A.B5,7.D6.E1,B;206.20,C.55,7.96.B5,7;355,7.B6;455,7.B6:VF9XVIYOTKLAE02MHKT9QJH92KKM1XNAMFH9HLL8XD22BYJD1F1SNKE4L8323MX0VOZKB2ZFSILQV14L", "hasFlower": 0, "hasSeason": 0}, {"id": "136", "q": "000,2,5,7,A,C.20,4,6,8,C.32,A.45,7.50,3,9,C.65,7.70,2,A,<PERSON><PERSON>85,7.91,B.A3,9.B0,5,7,C<PERSON>C3,9.D1,5,7,B.E3,9.F0,5,7,C.G2,A;115,7.32,6,A.53,5,7,9.75,7.B4,8.C6.D2,4,8,A.E6.G2,A;226.32,A.46.53,9.66.C4,8.D6.G2,A;346.53,9.C4,8.D6.G2,A;453,9:VRV8Z30EW89ARM5MXESYXEBVWRAW5ZA3EB0MXBSDYMWKKSSA55MRKVZA33MD88DUEXZ99E3D0R0SDDS93U9ARKBK9BKB", "hasFlower": 0, "hasSeason": 0}, {"id": "137", "q": "000,2,6,A,C.14,8.21,B.33,5,7,9.40,C<PERSON>52,4,6,8,A.72,4,8,A.80,6,C.A2,4,8,A.C2,4,6,8,A.D0,C.E2,5,7,A.G0,2,4,6,8,A,C;101,6,B.24,8.36.40,3,9,C.56.63,9.B3,9.C6.D0,C<PERSON>E5,7.G2,A;206.24,8.46.D6.G2,A;324,8.G2,A;4G2,A:EC28ULUPUISWS3SIT28MIPWTTWLLWUI36P3IUS6EU86MM23T3LSM2CTEMS6TC268WEI6PCMC3P2WE8ELPC8L", "hasFlower": 0, "hasSeason": 0}, {"id": "138", "q": "003,5,9,B.10,7,E.22,4,A,C<PERSON>36,8.40,2,4,A,C,E.56,8.60,2,C,E<PERSON>74,7,A.81,D.93,6,8,B.A0,E.B2,5,7,9,C.C0,E.D2,4,6,8,A,C.F1,3,5,7,9,B,D;110,4,A,E.27.46,8.51,D.74,A.81,D.96,8.B0,E<PERSON>C5,7,9.F7;227.74,A.81,D.97.F7:JKCLUL1XDD96LJ6X9OZU1X7O19O7LXVU7KU7JCJOLUZZ6ZZ6KKCCLOVJ9D070KJDCC6KV6ODD09V007U09Z1", "hasFlower": 0, "hasSeason": 0}, {"id": "139", "q": "000,2,5,7,9,C,E.20,3,6,8,B,E.41,3,5,9,B,D.57.63,B.70,6,8,E.83,B.95,7,9.A0,2,C,E.B4,7,A<PERSON>C0,E<PERSON>D3,5,9,B.E0,7,E<PERSON>F3,B.G0,6,8,E;100,7,E.20,7,E.45,9.67.70,E.A0,2,C,E.B7.C0,E.D5,9.F0,7,E;200,E.67.A1,D.B7;300,E:QG3RQGTQRCFLXROHAOKOIAFXHOKXIAGL3OHOAIIGTQ3IXIH3FHKQTXTRATKKF3CCCARCLR3CFXLGLKFQHLGT", "hasFlower": 0, "hasSeason": 0}, {"id": "140", "q": "002,5,7,A.10,C.22,4,6,8,A.40,2,5,7,A,C.61,5,7,B.80,6,C.92,A.A0,4,6,8,C<PERSON>B2,A<PERSON>C4,6,8.D0,2,A,<PERSON>.E4,6,8;102,5,7,A.23,9.41,5,7,B.61,B.76.90,C.A2,4,6,8,A.C4,8;202,6,A.51,B.A2,5,7,A.C4,8;302,A.A5,7;402,A.A6;5A6:6W36CFF7D4COHVQ3D7F7OQKVWDKQ3BV34BVK4QFHQ4DBHBOHCF373KFCWQODO6K676VHWB4CC67DWVO4KWBH", "hasFlower": 0, "hasSeason": 0}, {"id": "141", "q": "001,3,5,7,9,B.20,2,5,7,A,C.40,2,5,7,A,C.60,2,6,A,C.74,8.81,B.94,8.A0,2,6,A,C.C1,4,6,8,B.E4,6,8.F0,2,A,C.G5,7;101,4,6,8,B.21,5,7,B.41,5,7,B.60,6,C.74,8.94,8.A0,6,C.C1,4,6,8,B.E5,7.F1,B:RM1HUMYW35VUSTZZM15UR5YVS5C1TYNUTR1U5C5NNRMTSZMS6TSVZH66CCLL6V6NVSYMWTVCCU3ZZ161", "hasFlower": 0, "hasSeason": 0}, {"id": "142", "q": "002,4,6,8,A,C.21,3,B,D.40,2,4,A,C,E.61,3,5,7,9,B,D.80,2,4,A,C,E.A0,2,4,6,8,A,C,E.C1,3,B,D.E1,3,B,D.G1,3,5,9,B,D;103,5,7,9,B.22,C.40,3,B,E.61,4,A,D.80,2,C,E.A0,4,6,8,A,E.B2,C.D3,B.F2,C.G4,A:NUI4VU9WZJJ7FD10IRJJJJRBNRU776IND09TFDWSUNFT7I4R69D67VBVW0900SZVF104FT46U4UF997WTRR4", "hasFlower": 0, "hasSeason": 0}, {"id": "143", "q": "000,2,5,B,E,G.17,9.22,4,C,E.30,6,A,G.43,D.56,8,A<PERSON>60,2,E,G.74,6,8,A,C.81,F.93,5,7,9,B,D.A0,G.B2,6,8,A,<PERSON><PERSON>C4,<PERSON><PERSON>D0,6,A,<PERSON><PERSON>E3,<PERSON><PERSON>F5,7,9,B.G0,2,E,G;100,G.23,D.57,9.62,E.75,7,9,B.96,A.A8.B2,E<PERSON>E3,D.F8.G0,G;257,9.75,B.F8;357,9;457,9:ZPFDNLZMSNC111NC2CXWHWFBQV2NRQRNN7GGHHQSZCSVGVDF7BKW1WHKQF7B1R1BZFGKHMZXZLSKWF7QVVVDPCHCWRDQ", "hasFlower": 0, "hasSeason": 0}, {"id": "144", "q": "001,4,6,8,B.21,3,6,9,B.41,3,5,7,9,B.60,2,4,6,8,A,C.80,3,5,7,9,C.A1,5,7,B.B3,9.C0,5,7,C.D2,A<PERSON>E0,4,6,8,C.F2,A.G0,4,8,C;101,5,7,B.21,3,9,B.53,6,9.75,7.83,9.A1,5,7,B.C6.D0,C<PERSON>E4,8.F1,B;221,3,9,B.D0,C.F1,B;322,A;422,A:W1Y0Y41SF407WWSY0JN0S2T72YQFNTSSJL1276127JJ4NNQ4QQ6L4FTJTWQ167T0226FWL4LFLTLN6YW0N1F7SQJ6Y", "hasFlower": 0, "hasSeason": 0}, {"id": "145", "q": "002,4,6,8,A.10,C.25,7.30,2,A,<PERSON><PERSON>44,6,8.63,9.71,5,7,B.91,4,8,B.B3,9.C6.D0,3,9,C.F1,5,7,B;105,7.10,C.25,7.31,B.46.71,6,B.91,4,8,B.C6.D0,3,9,C.F1,5,7,B;206.10,C.71,B.C6.D3,9;310,C.71,B.C6.D3,9;471,B.C6.D3,9;571,B:IQBKX753JLZN6J8TQTKB3X5RUVSZJUL3XLQT44GSLY8JRQOT7U5VTOZ7V8N84I6IUR4GJ5VKIJYK7XZR3T33", "hasFlower": 0, "hasSeason": 0}, {"id": "146", "q": "000,2,4,7,A,C,E.20,2,4,6,8,A,C,E.40,4,6,8,A,E.61,3,5,7,9,B,D.81,3,5,7,9,B,D.A0,4,6,8,A,E.C0,2,4,6,8,A,C,E.E1,3,5,7,9,B,D;102,4,A,C.30,4,A,E.63,B.84,A.A0,E.B4,A.C0,2,C,E;203,B.84,A.C2,C:E3K4E52B4RLTQ7FD3KFRKBD2L345B5L3Q3FQLLFE4QQER2BK4FKBTTDDTRR7T2F3K7254QL57E2DBD5TER", "hasFlower": 0, "hasSeason": 0}, {"id": "147", "q": "003,9.10,6,C.30,2,4,6,8,A,C.50,2,4,6,8,A,C.74,8.80,2,6,A,C.A0,2,4,6,8,A,C.C0,2,6,A,C;103,9.30,3,6,9,C.50,2,6,A,C.80,2,6,A,C.A0,3,6,9,C.C1,B;203,9.30,3,6,9,C.A0,3,6,9,C.C1,B:OO1V0DREGNZ5RBQ35VGFD5G2O92EUOE179NR0FVN30EFOODNXZDUQZ5VQREG71VPXZFRPBRE1VQ0", "hasFlower": 0, "hasSeason": 0}, {"id": "148", "q": "000,3,6,9,C.20,2,A,C<PERSON>34,6,8.40,2,A,C<PERSON>54,8.61,6,B.73,9.85,7.91,3,9,B.A5,7.B0,2,A,C.C4,6,8.D0,2,A,C.E4,6,8;100,3,6,9,C.21,B.33,5,7,9.41,B.54,8.61,6,B.73,9.91,3,5,7,9,B.B1,5,7,B.C3,9.D1,6,B:NJ9IYYJVQA0ELMN8WGVUNJSE31LHNJUCSDU3OWONAXTXI9G02JMHZ1I3NDPEWJIZCUR3EQBORTP82WOB", "hasFlower": 0, "hasSeason": 0}, {"id": "149", "q": "000,2,4,6,8,A,C,E,G.20,2,4,6,A,C,E,G.38.41,3,5,B,D,F.58.60,2,6,A,E,G.74,8,C.81,6,A,F.93,8,D.A0,5,B,G.B2,7,9,E.C0,4,C,G.D2,6,8,A,E.E0,4,C,G.F2,6,A,E.G4,8,C;105,7,9,B.10,G.42,E.68.A8.C2,4,C,E.G8:ZSGAAEXTAEDSGQXZ4ZA45X4AA02REGR50D0RXI45S4GIR0G2Q5RQ2TE2Z4QEQTXEGI5X0DS225TZTZIDRQ0T", "hasFlower": 0, "hasSeason": 0}, {"id": "150", "q": "000,2,4,6,8,A,C,E.21,3,5,7,9,B,D.40,2,4,6,8,A,C,E.62,4,7,A,C.80,2,4,7,A,C,E.A3,6,8,B.C1,3,5,7,9,B,D.E1,5,7,9,D.F3,B.G0,5,7,9,E;100,3,5,9,B,E.21,3,B,D.36,8.40,2,C,E.54,A.72,4,A,C.B6,8.C4,A.D1,D.E6,8.F3,B.G5,9:YY7KKYDDDO2M7F2Y11FIFYL7O02LLAMH5G0LG7U0M2UAGK0211FO5FOMD6I86A1GGO7L165L5KDK525HG6MFMDYK7O8A", "hasFlower": 0, "hasSeason": 0}, {"id": "151", "q": "001,4,6,8,B.21,4,8,B.40,2,4,8,A,C.60,5,7,C.73,9.85,7.93,9.A0,6,C.C0,2,5,7,A,C.E0,3,5,7,9,C.G1,3,5,7,9,B;101,B.31,4,8,B.50,C.65,7.73,9.85,7.93,9.A0,C.B6.C0,C.D5,7.F3,6,9;265,7.84,6,8.A0,C.C6;3A0,C:LG4MALPBX49T7A9TGBPTRS2A83PB3ARB7ZM39G49TV82RZAGZ3BBSGVS332RP2848I84OAG47EOIZE7X8S", "hasFlower": 0, "hasSeason": 0}, {"id": "152", "q": "000,2,5,7,9,C,E.20,3,5,7,9,B,E.41,3,B,D.56,8.60,E.72,4,6,8,A,C.80,E.95,7,9.A0,2,C,E.C2,4,6,8,A,C.D0,E.E3,5,7,9,B;106,8.10,E.25,7,9.56,8.60,E.74,6,8,A.80,E.C2,C.D0,5,7,9,E.E3,B;210,6,8,E.76,8.D0,E:YZHXXJTNA6TGTOZP7JBJ7RR4YH6XKLEEXWV4181G8PO2CQER266VLLMCEJK6ENE9ZA3CMZQT6WR9LBPPUCU3", "hasFlower": 0, "hasSeason": 0}, {"id": "153", "q": "000,3,5,7,9,C.20,2,4,6,8,A,C.40,3,6,9,C.60,4,6,8,C.72,A.80,4,6,8,C.A2,4,6,8,A.B0,C.C2,4,8,A.D0,6,C.E2,4,8,A.F0,6,C.G2,A;104,8.16.21,3,9,B.46.50,C.65,7.71,B.86.A5,7.C0,3,9,C.D6.E0,C;250,C.66.71,B.C0,C:6LWWXMF7DWV5V6N6W6YXHDLF2M7DX7VWYHDWD2MY5FVLFXYLM2X5FDMYHN6VL5N2N657YL52N7HXV7HMNHF2", "hasFlower": 0, "hasSeason": 0}, {"id": "154", "q": "000,2,4,7,A,C,E.20,5,7,9,E<PERSON>33,<PERSON><PERSON>41,5,7,9,D<PERSON>53,B.60,6,8,E.72,4,A,C.80,6,8,E.92,4,A,C.A6,8.B1,3,B,D.C7.D0,5,9,E.E2,7,C;100,7,E.27.33,B.45,9.53,7,B.60,E.72,6,8,C.95,9.A2,7,C.D7;227.57.95,9.A7.D7:D82I4LMI0002KKUCNICDXXF0GTF6CDNM2K66CDDBMILMX0XBKUFGFFGNCBX0IGF28CDLXLB6TKK4I2N2", "hasFlower": 0, "hasSeason": 0}, {"id": "155", "q": "003,7.10,A.23,5,7.30,A.42,4,6,8.50,A.64,6.70,A.85.90,<PERSON><PERSON>A2,4,6,8.C0,2,5,8,A.E0,2,4,6,8,A;103,7.10,A.24,6.30,A.43,7.64,6.85.A3,7.C0,2,5,8,A.E0,2,5,8,A;210,A.25.30,A.43,7.C0,A.D5.E0,2,8,A;310,A.D0,A:14VUWIZTGZFWF1NR6DKJ4B98R0BV56NN4CFLTT5JEMRHXTIED9MGRC9ZBFL8M4MXNTB91KXLZL0UX1TH", "hasFlower": 0, "hasSeason": 0}, {"id": "156", "q": "000,2,4,7,9,C,E,G.20,4,6,8,A,C,G.32,E.44,6,8,A,C.50,2,E,G.64,7,9,C.70,2,E,G.84,7,9,C.90,<PERSON>.A2,4,6,8,A,C,E.B0,G.C2,4,6,A,C,E.D8.E0,2,5,B,E,G.F7,9.G0,2,4,C,E,G;107,9.14,C.37,9.44,C.57,9.74,C.A4,<PERSON>.C6,<PERSON><PERSON>F1,F.G4,C:J63KN5JBUM5C2QWGN09HRCYD5EQTKE12AA7ZK49DYWGQ4P95T9T43J1XQNDGEMMLXZ2J7TGK49U2WLW9D60MBRPHNE", "hasFlower": 0, "hasSeason": 0}, {"id": "157", "q": "000,7,E.12,4,A,C.27.30,2,4,A,C,E.46,8.50,2,4,A,C,E.70,2,4,6,8,A,C,E.90,2,4,A,C,E.B0,3,5,7,9,B,E.D1,4,7,A,D;100,E.12,4,A,C.27.30,E.43,5,9,B.51,D.63,B.82,4,A,C.90,E.A3,B.B5,9.D1,4,7,A,D:DAOKU9VKZXUQ6K9U7F6FUO27V272FFVQQF26DQKV2AAZD27O7XAXZ6ZAA9DV99FVOKXUDQU9QO6KOD67", "hasFlower": 0, "hasSeason": 0}, {"id": "158", "q": "000,2,4,6,8,A,C,E.20,4,A,E<PERSON>32,7,C<PERSON>44,A<PERSON>50,6,8,E<PERSON>64,<PERSON><PERSON>71,6,8,D.83,B.90,5,7,9,E.B2,4,6,8,A,C.D0,4,A,E.E6,8;100,3,5,7,9,B,E.34,7,A.55,7,9.71,7,D.83,B.90,5,7,9,E.B2,4,7,A,<PERSON>;206,8.47.95,9.B4,7,A:N553RRNZ00JQL0JX8L7K3JNOKRRJ7Z0P5ZX3K0PPLOR2QX57OZP7OLK888NHL5R835JK7Z7Z0283KJ3LHX", "hasFlower": 0, "hasSeason": 0}, {"id": "159", "q": "000,2,4,8,A,C.21,3,5,7,9,B.41,3,5,7,9,B.60,2,4,6,8,A,C.80,2,4,6,8,A,C.A3,6,9.B0,C.C2,5,7,A.E0,2,4,6,8,A,C.G0,4,8,C;102,A.23,9.31,6,B.43,9.55,7.62,A.74,8.80,C.93,9.C5,7.E1,6,B<PERSON>F4,8;223,9.55,7.80,C.C5,7:M1X652QSZDPNG5CR32503AR3HB7WCR79C5J661V1HHECZQD12EDR3KNCWEGAPEPS2HDXY6490CJKXOPBV4YMOX", "hasFlower": 0, "hasSeason": 0}, {"id": "160", "q": "003,5,7,9,B.10,E<PERSON>22,4,6,8,A,C.30,E<PERSON>44,6,8,A.50,2,C,<PERSON><PERSON>64,A<PERSON>70,7,<PERSON><PERSON>82,<PERSON><PERSON>90,6,8,E<PERSON>A4,<PERSON><PERSON>B2,<PERSON><PERSON>C0,5,7,9,E<PERSON>E0,5,9,E<PERSON>F2,7,C<PERSON>G0,4,A,E;104,7,A.22,5,9,C.37.44,A.50,2,C,E.82,C.96,8.C0,<PERSON><PERSON>F2,C.G4,A;204,7,A.25,9.37.44,A.96,8:3LM0VO2R6S2MMSJ00VLSVM6EVWRLEE3MNJNJS6WQC6ELV0OC00YOEN3TJ21MR11V6QAA6A3TN11NAR1OLOYN2OLE", "hasFlower": 0, "hasSeason": 0}, {"id": "161", "q": "000,4,6,8,C.12,A.25,7.30,C.44,8.50,C<PERSON>62,5,7,A.70,<PERSON><PERSON>84,8.91,B.A3,5,7,9.B0,C<PERSON>C3,5,7,9.D0,C.E4,6,8.F1,B;104,8.16.40,4,8,C.62,5,7,A.91,B.A5,7.B3,9.C5,7.D0,C.E4,6,8.F1,B;216.65,7.A5,7.D5,7.F1,B;366.A6.D6;466;566:DXI8ZZ6T6RZ85EQPFK8K53K8NO5BO57M3MZEFMQ2FIUT76JLBLEDFZXUMMHEPMYH1H1HN2RKY6NNFJDDZF", "hasFlower": 0, "hasSeason": 0}, {"id": "162", "q": "001,3,5,7,9,B.20,2,4,8,A,C.42,4,6,8,A.60,2,4,8,A,C.76.80,3,9,C.95,7.A0,2,A,C.B4,8.C1,6,B.D3,9.E0,5,7,C<PERSON>F2,A<PERSON>G4,8;102,4,8,A.21,3,9,B.44,8.60,C.76.A2,A.E0,C.F2,A.G4,8;202,A.21,3,9,B.G4,8;312,A:COEHS60Z1YS154FMH8EHOQVU7ULFOHRNCCOMZMMYO0WCSH5LV7V70FFV8H6SRE47SLMW1QOF0L1NSFEM", "hasFlower": 0, "hasSeason": 0}, {"id": "163", "q": "001,4,6,8,A,D.20,2,5,7,9,C,E.41,3,7,B,D.64,6,8,A.71,D.83,5,7,9,B.90,E.A3,6,8,B.C0,3,B,E.D7.E3,5,9,B.F0,7,E.G4,A;105,7,9.11,D.25,7,9.31,D.43,B.57.71,4,6,8,A,D.93,6,8,B.C0,3,B,E.E5,9.F7;215,7,9.21,D.74,6,8,A.97.E5,9.F7;321,D.87;487:BVV0TVQAD2AXFU21SWX102UW4ATX2XUSURVRUBR4P46207QL7461W5A5L6R10SSSRBDL7D07QLAFQP24JWL0LURJ6SB14DA1", "hasFlower": 0, "hasSeason": 0}, {"id": "164", "q": "000,2,5,9,C,E.17.20,2,5,9,C,E.37.41,3,5,9,B,D.57.60,2,5,9,C,E.77.83,5,9,B.90,E.A2,4,6,8,A,C.C0,2,4,6,8,A,C,E.E0,2,5,7,9,C,E;100,5,9,E.12,C.31,7,D.43,B.51,D.65,7,9.84,A.A3,B.B6,8.C1,3,B,D.E0,2,6,8,C,E;237.43,B.B3,B.E6,8:876D2FNJ7ARW9A0IORATTOWD2DU2BQIYOU9UE6I6T55ZZ2UEI0TMJE7M5SEUBS6Y60W5RR270NXE6XYTYFQ2TW8AAOEUDA", "hasFlower": 0, "hasSeason": 0}, {"id": "165", "q": "000,2,4,7,A,C,E.20,3,6,8,B,E.40,3,5,7,9,B,E.60,3,5,7,9,B,E.80,2,6,8,C,E.A3,7,B.B0,5,9,E.C7.D0,3,5,9,B,E.E7.F0,3,B,E.G5,7,9;100,2,4,7,A,C,E.27.30,3,B,E.45,9.50,3,7,B,E.65,9.70,7,E.A7.B5,9.C0,7,E<PERSON>D4,<PERSON><PERSON>E0,7,E.G5,9:DYGY1R8M86GOTDMDRBBRNB22NOTO144YT848RO44T6F2DF2NF6FRBFBNT6B6GY24MDOMF6ORME1YM81NN1G2D8TEGGY1", "hasFlower": 0, "hasSeason": 0}, {"id": "166", "q": "000,3,5,7,9,C.20,5,7,C.32,A.44,6,8.50,2,A,C.64,6,8.70,2,A,C.85,7.90,2,A,C.A5,7.B2,A.C0,4,6,8,C.E1,4,8,B.F6.G1,3,9,B;103,9.10,5,7,C.32,5,7,A.50,2,4,8,A,C.70,6,C.82,A.95,7.B2,A<PERSON>C0,4,8,C.E1,4,8,B.G2,A;232,A.60,C.76.82,A.E1,B;332,A:I9Q9F19YES25LLT4C6H6U6BK64WY236FVJ239IBULUT92MTSMPUUVW1JQ0MZA4KWOSE11TQB3I4BC9UQ5HO34IL0ZA6PWM4S", "hasFlower": 0, "hasSeason": 0}, {"id": "167", "q": "000,4,6,8,A,E.12,C.20,4,6,8,A,E.43,6,8,B.51,D.63,6,8,B.71,D.84,7,A.91,D.A5,7,9.B3,B.C1,6,8,D.D4,A<PERSON>E1,7,D;100,4,7,A,E.12,C.20,7,E.43,6,8,B.51,D.63,6,8,B.71,D.84,7,A.91,D.A5,9.B3,7,B.D7;220,E.37.52,6,8,C.71,D.A5,9.C7;320,E.52,C;452,C:G7IS7SXQ9WS99LIW0GX87G7EELEL96X0SQ8WMGSGFFGI2Q9862FQ9W78I6IE0SEI0L62X886M0WWFMFXMF2M0EL2XQMQ2L76", "hasFlower": 0, "hasSeason": 0}, {"id": "168", "q": "000,2,4,8,A,C.20,2,4,8,A,C.36.40,3,9,C.55,7.60,2,A,C.74,6,8.80,2,A,C.94,6,8.A1,B.B3,5,7,9.C0,C.D2,4,8,A.E0,C;100,C.12,4,8,A.33,9.65,7.70,C.86.A4,8.C3,9;200,C.12,4,8,A.65,7.86.A4,8;314,8.65,7;466:MG0GMGFY52I04K130MIGXYGDA25MIM0IX42913D95430E3595EAD42I941YY91X9542M1DKGF1XKI0K2", "hasFlower": 0, "hasSeason": 0}, {"id": "169", "q": "000,2,5,7,9,C,E.21,3,5,9,B,D.40,2,5,7,9,C,E.61,3,5,7,9,B,D.80,3,7,B,E.95,9.A1,7,D.B3,5,9,B.C1,D.D3,5,9,B.E0,E;101,5,9,D.23,5,9,B.42,5,7,9,C.61,3,7,B,D.83,7,B.A1,D.B5,9.C3,B.D5,9;246,8.52,C.A1,D:WFQVWRGYF0WGF5E5WWOQRP9775EVYS7OGVTFVQSV59P9RES7KF05Y5TYPFPTVGYRP0KG7YT99QRWS907GRPE", "hasFlower": 0, "hasSeason": 0}, {"id": "170", "q": "001,3,5,7,9,B,D.21,3,6,8,B,D.40,4,A,E<PERSON>52,7,C<PERSON>64,<PERSON><PERSON>70,6,8,E.84,A.90,6,8,E<PERSON>A3,B.B1,5,7,9,D.D0,4,A,E.E2,6,8,C.G0,2,4,6,8,A,C,E;103,5,9,B.11,D.23,7,B.44,A.74,7,A.A3,7,B.B1,<PERSON><PERSON>D4,<PERSON><PERSON>E6,8.G1,3,5,9,B,D:NTX7R2K6YO4QK4W6DMJLZZ8Q2LHICCGXW1C0SVIYGX6D1R8M0TQIT6RSHJ0KC9T7OXVE6QO6NYOE9RYI4K04", "hasFlower": 0, "hasSeason": 0}, {"id": "171", "q": "000,2,5,7,9,C,E<PERSON>23,6,8,B.31,<PERSON><PERSON>44,<PERSON><PERSON>52,7,C<PERSON>70,4,7,A,E<PERSON>93,6,8,B.B1,4,A,<PERSON>.C6,8.D3,B.E1,6,8,D;100,2,5,9,C,E.17.23,B.57.70,4,7,A,E.C6,8.D3,B<PERSON>E6,8;205,9.23,B.67.70,4,A,E.C6,8.D3,<PERSON>;305,9.67.70,E.C6,8;467.70,E;567:58DQGQL3SD5ZUJ7LDIQI5YJ3KULLXYNYIXKIL06B3S7J308LYB6G0GJNK5K75YXD3XQXUIY8ZXKI83GK075U", "hasFlower": 0, "hasSeason": 0}, {"id": "172", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,2,4,8,A,C.80,2,A,C.94,8.A0,2,6,A,C.B4,8.C0,2,6,A,C;101,B.13,6,9.20,C.32,4,8,A.40,C.81,B.A0,2,4,8,A,C.B6.C2,A;213,9.31,B.81,B.A1,B;331,B.91,B;431,B:383PC1YT1IR6FTP53561NPICONOT1O5C1OUIN86UNYY8NOUUTC3N665U3IYFPROYP385PUI156IY", "hasFlower": 0, "hasSeason": 0}, {"id": "173", "q": "001,5,7,B.13,9.20,6,C<PERSON>32,A.45,7.50,2,A,<PERSON><PERSON>64,8.71,B.83,5,7,9.90,C.A2,4,8,A.C0,2,A,C.D4,6,8.E1,B;105,7.20,C.32,6,A.50,2,A,C.64,8.86.94,8.A2,A.C0,2,A,C.D4,8.E1,B;205,7.20,C.42,A.B2,A.E1,B;342,A.B2,A:LFIHQLL7NKSENWP8TL9NEKHFW7YYTTET8Q0NFXHXFTHTVFYKKHIIYP0S9MESN88NWIEELUSSUSLVMFWH", "hasFlower": 0, "hasSeason": 0}, {"id": "174", "q": "000,3,5,7,9,B,E.21,4,6,8,A,D.40,2,4,7,A,C,E.60,3,5,7,9,B,E.82,5,7,9,C.A0,3,5,7,9,B,E.C0,3,5,7,9,B,E.E4,A<PERSON>F1,6,8,D.G4,A;103,5,7,9,B.21,4,7,A,D.41,4,7,A,D.60,5,7,9,E.82,5,9,C.A4,6,8,A.B0,E<PERSON>C4,7,<PERSON>.E4,A.F1,7,D:TETGLPXF0XS3N6GPRB199FSX41OTJ6JGRPLKFA9H096XKHAG8R8HARO15NPL4O9TTPLT53IOP881FHH9E1H8A6815I5B", "hasFlower": 0, "hasSeason": 0}, {"id": "175", "q": "002,4,7,A,C<PERSON>10,E<PERSON>22,4,A,C<PERSON>36,8.40,2,C,E.54,7,A.60,2,C,E<PERSON>74,6,8,A.80,E<PERSON>92,4,A,C.A0,7,E.B2,5,9,C.C7.D1,4,A,<PERSON>.E6,8;102,4,7,A,C.32,7,C.51,4,7,A,D.76,8.90,2,4,A,C,E.A7.C7.D1,D.E7;202,4,A,C.32,7,C.67.92,C.C7;303,B.67;403,B:IQPQRYF6QP6KVBBPUYVBOKJUPF5RI4VVYIKIFUQUJ5FFB4O4V656UYYJ56URPPFJQK4OKQR4V5OBKIRRB6YJ4JIOO5", "hasFlower": 0, "hasSeason": 0}, {"id": "176", "q": "001,4,6,8,A,<PERSON>.20,6,8,E<PERSON>33,B<PERSON>40,7,<PERSON><PERSON>52,4,A,<PERSON><PERSON>66,8.70,3,B,E<PERSON>85,7,9.90,2,C,E<PERSON>A5,9.B1,<PERSON><PERSON>C3,6,8,B.D0,E<PERSON>E3,6,8,B.F0,E<PERSON>G2,5,7,9,C;104,A.30,3,B,E<PERSON>54,A<PERSON>66,8.73,B.86,8.92,C.B1,D.D0,3,6,8,B,E.G5,9;267.86,8.92,C.D6,8.G5,9;367.92,C.D7:4UO9R5P836O086FUOQWX2XC0SENOOW5X2GGDNZ4M581G67XSH3CCD8QMOI6538DDNCPSY3GYL4ZNGEI47GC91LFS8RHC", "hasFlower": 0, "hasSeason": 0}, {"id": "177", "q": "000,2,4,6,8,A,C,E.20,2,4,A,C,E.37.41,4,A,D.56,8.61,3,B,D.75,7,9.80,2,C,E.95,7,9.A1,3,B,D.C0,4,7,A,E.D2,C.E0,5,9,E.F7.G1,3,5,9,B,D.I0,2,6,8,C,E;106,8.24,A.67.71,D.87.92,C.C0,4,7,A,E.E0,5,9,E.G4,A.I7;207.71,D.C4,A.I7;307.C4,A;4C4,A:1RT6B4TRC316T4W3LLXLCRTWOBW6O5LIOIFI43CB0CW1O0IILB6RF4C360CXBF04FIWXX56TRLO51343F10OW5X0T5XBRF15", "hasFlower": 0, "hasSeason": 0}, {"id": "178", "q": "000,2,5,7,A,C.20,2,5,7,A,C.40,2,5,7,A,C.60,2,5,7,A,C.80,2,4,8,A,C.96.A0,2,A,C.B5,7.C2,A<PERSON>D0,5,7,C.E2,A;101,5,7,B.30,2,A,C.45,7.50,C.70,2,A,C.84,8.90,C.A6.B2,A.E2,A;201,B.40,C.B2,A;3B2,A:32KD49Q882XTDXOQ485K9MW18LBJMD4RH8IBYZTQNHT8AJOFA5QT1VSULU0VW0SI4PUDZUPWYNR3WF", "hasFlower": 0, "hasSeason": 0}, {"id": "179", "q": "000,2,A,C.14,8.20,2,6,A,C.34,8.41,6,B.54,8.60,C<PERSON>73,6,9.80,C.92,4,8,A.A0,6,C.C0,2,4,6,8,A,C.E1,3,5,7,9,B.G0,2,4,8,A,C;100,C.12,4,8,A.20,C.35,7.41,B.76.83,9.91,B.A6.C2,5,7,A.E3,5,7,9.G1,4,8,B;200,C.13,9.35,7.91,B.D6;335,7:1TDNCDVQDYLVVLNYBVBCM1EYUM9NDYUZPLD1DTWRPCNRGZBQYPZ1TY9GMTP44MCVMR9TZTU9VENN1WML59BR9WU1WCC5", "hasFlower": 0, "hasSeason": 0}, {"id": "180", "q": "000,2,6,8,C,E.14,A.21,6,8,D.40,3,5,7,9,B,E.60,3,5,7,9,B,E.80,2,4,A,C,E.96,8.A0,2,4,A,C,E.B6,8.C0,2,C,E.D4,6,8,<PERSON><PERSON>E0,E;101,D.14,6,8,A.36,8.43,B.57.92,6,8,C.B7.D4,6,8,<PERSON>.E0,E;214,6,8,A.36,8.43,B.57.92,C.A7.C7.D4,A;336,8.B7.D4,A;436,8;536,8:2ZQ4TAF3R5VRX05L7L42FBFE4B8L75EAA4N20LXXAEUTYEC4FSV0XTZU5RYX0EC7R4N2EXURC75FNLAS03YYUTN8LCQ05BAFBR", "hasFlower": 0, "hasSeason": 0}, {"id": "181", "q": "001,3,5,7,9,B.20,2,5,7,A,C.42,4,6,8,A.61,4,6,8,B.80,2,5,7,A,C.A2,4,6,8,A.C2,4,6,8,A.D0,C.E2,6,A;101,4,6,8,B.20,2,5,7,A,C.45,7.65,7.71,B.85,7.A2,4,8,A.C2,5,7,A.D0,C.E2,6,A;201,B.36.66.71,B.B2,A.C6.E2,A:HUHTUOPEZW5XJ84X4JD79QEWUMWSAN44MZ7UNFIW79NIFUXIDZHZI7SH9HHX4EMIMOPO4PA5339NUGQEIGOP8T", "hasFlower": 0, "hasSeason": 0}, {"id": "182", "q": "000,2,4,6,8,A,C,E.22,5,7,9,C.30,E.42,4,7,A,C.50,E<PERSON>63,B.70,7,E.82,4,A,C.90,E.A2,4,6,8,A,C.B0,E.C2,4,6,8,A,C.E0,5,7,9,E;104,6,8,A.25,9.30,7,E.42,C<PERSON>63,B.70,E.82,4,A,C.A3,6,8,B.B1,<PERSON><PERSON>C3,5,7,9,B.E0,5,9,E:0IY29U0F92ZT0B396NFMVOEA2RY4LUHSXC36MTOBAUCV3TLDFD33ZV4DWW91PBYV1TCRHU02CB3PF7IXEDSNY7", "hasFlower": 0, "hasSeason": 0}, {"id": "183", "q": "000,2,6,8,C,E.14,A.20,7,E.32,5,9,C.40,E.52,4,A,C<PERSON>60,7,E.72,4,A,C.90,2,5,7,9,C,E.B0,2,5,9,C,E.C7.D0,3,5,9,B,E;110,E.35,9.40,2,C,E.61,3,7,B,D.91,5,7,9,D.B0,2,C,E.C5,9.D3,B;210,E.40,2,C,E.62,7,C.97.B0,2,C,E:65U0DZC0C4EGVCUT0KUVEZYKT5YPUEECPKD6TMZ5VDIP6GVU4V05T59T9MTN6ZYU94YDPML095IM4VLI0IDNIDIK", "hasFlower": 0, "hasSeason": 0}, {"id": "184", "q": "000,2,4,A,C,E.16,8.20,2,4,A,C,E.37.40,2,4,A,<PERSON>,E.56,8.60,2,4,A,C,E.80,3,5,7,9,B,E.A1,5,9,D.B3,7,B.D0,2,4,7,A,C,E.F0,2,4,A,C,E.G6,8;102,4,A,C.10,6,8,E.30,E.42,C.57.64,A.70,E.A5,9.B7.D2,C.E0,E.G6,8;202,4,A,C.17.30,E.64,A.E0,E.G7:NNC1UVC81V9NNW2058KLCE5SGW114OS8394NQM12N59FC8UBSSDQE1UDM5P28WWVBGM02YU934CW89O9I4KM0LIP5F0WY5CV", "hasFlower": 0, "hasSeason": 0}, {"id": "185", "q": "000,2,7,C,E.15,9.21,3,7,B,D.41,5,9,D.53,B.60,6,8,E<PERSON>73,B.80,5,9,E.A0,3,5,7,9,B,E.C1,3,5,9,B,D.E0,E.F2,6,8,C.G0,E;100,2,C,E.15,7,9.22,C.41,D.53,B.60,6,8,E.73,B.A0,3,B,E.B5,9.C1,3,B,D.F2,6,8,C;200,E.17.60,6,8,E.B5,9.F7;317.66,8;417:QLTH6TEVS20KM27L1DMPYDMR0KDPR3MGMUDCY5TQE7Q21RST77K3K10Q77YQQH2YVCUE1WWTETGK56URY0UDD6RKMU226URY", "hasFlower": 0, "hasSeason": 0}, {"id": "186", "q": "003,5,7,9,B.10,E.22,4,6,8,A,C.40,2,4,6,8,A,C,E.61,3,5,9,B,D.77.80,3,B,E.96,8.A0,2,C,E.B4,6,8,A.C1,<PERSON><PERSON>D3,5,9,B.E0,E.F2,4,6,8,A,C.H0,2,4,6,8,A,C,E;104,A.17.23,5,9,B.40,2,7,C,E<PERSON>54,A.73,B.80,E.97.A0,<PERSON><PERSON>B4,<PERSON><PERSON>D3,B.E0,5,9,E.F2,7,<PERSON>.G4,<PERSON>.H0,E:MN1VK12Z8U3EGK5SCFVIIKHOC5ZUEUMMKASTAFPUOYB00G5YUH4BMMFBOFXBTKX1Q70N35THXJ12XSPQ79840BM9X1BXS1JTKUHO", "hasFlower": 0, "hasSeason": 0}, {"id": "187", "q": "000,7,9,G.13,5,B,<PERSON><PERSON>27,9.32,4,C,E.40,7,9,G.52,4,<PERSON>,<PERSON><PERSON>60,6,A,<PERSON>.73,<PERSON><PERSON>80,<PERSON>.94,C<PERSON>A0,6,A,<PERSON>.B2,4,C,E<PERSON>C0,7,9,G.D2,4,C,E.E6,8,A.F2,E.G0,4,7,9,C,G;107,9.24,8,C.40,G.70,3,D,G.94,C.A0,<PERSON><PERSON>B2,<PERSON><PERSON>C7,9.E7,9.G0,7,9,G;207,9.28.73,D.C8.F8;307,9.28.F8;407,9;508:YM9OM3WA6P3AJH7DJXVF9ZO7D3PSZMZH82SS7MD329V7VYC6MVPOPHOO9XXZX2Y3WJD996L7XQAM2QHSXLV2JSAF2678YSCO3V", "hasFlower": 0, "hasSeason": 0}, {"id": "188", "q": "001,3,5,7,9,B,D.20,2,5,7,9,C,E.42,6,8,C.50,4,A,<PERSON><PERSON>66,8.71,D.86,8.90,3,B,E.A6,8.B2,C<PERSON>C0,4,A,E<PERSON>D2,7,C<PERSON>E0,4,A,E;101,3,B,D.17.25,9.42,6,8,C.66,8.71,D.87.A6,8.B2,C.C4,A.D7;202,C.25,9.42,6,8,C.71,D.B2,C.D7;325,9.47.D7;4D7:ECXH2C1UB4QAMGCWWBCBFBU2BMAUQ14AQG5542HW1G18A8XF52A4X1HQMGX2H8C5BHEUEFU8F1A4MUQQ5C4WH25E", "hasFlower": 0, "hasSeason": 0}, {"id": "189", "q": "000,2,4,6,8,A,C.21,3,5,7,9,B.40,2,4,6,8,A,C.61,3,5,7,9,B.80,2,4,8,A,C.96.A0,2,4,8,A,C.B6.C0,3,9,C.D5,7.E0,3,9,C;100,2,6,A,C.14,8.21,B.36.41,3,9,B.55,7.61,3,9,B.80,4,8,C.92,6,A.A0,4,8,C.C0,6,C:GSZ6TGPMG7SS6X3PZE2YAGETVA1M36Z2XSA42X7YT3P32MMZ1MAV7GVX4ATTV36E4632SXVAPVT6SEM4GX72", "hasFlower": 0, "hasSeason": 0}, {"id": "190", "q": "000,2,4,7,A,C,E.20,2,4,6,8,A,C,E.45,9.50,2,7,C,E.70,2,4,7,A,C,E.92,6,8,C.A0,4,A,E.B6,8.C1,4,A,D.D6,8.E1,3,B,D;102,4,A,C.10,E.22,4,A,C.51,D.67.74,A.87.A4,A.B6,8.D1,D.E3,B;203,B.10,E.24,A.51,D.74,A.B7.D1,D;303,B.24,A.51,D;424,A.51,D:YHGJJ454TXIAHTEZVP4TWX2L6LA7YR5CIO4A9GTDLXHDXM5I74ARHZGP6B6AJ9JO3WXT5X4CB2YAMH6HR6RYZIEBTLBVZ63G", "hasFlower": 0, "hasSeason": 0}, {"id": "191", "q": "000,3,5,7,9,B,E.21,3,5,9,B,D.40,2,4,6,8,A,C,E.60,2,7,C,E.82,5,7,9,C.A6,8.B0,2,C,E.C5,7,9.D0,2,C,E.E4,6,8,A.F1,D.G5,9;103,B.23,B.35,9.40,7,E.60,7,E<PERSON>72,C.86,8.A6,8.B0,E<PERSON>C5,7,9.D0,E<PERSON>E4,A;223,B.57.B7.E4,A;357.E4,A;4E4,A;5E4,A:SFDQZB8UTIV6CCVP6ICT8FQHCJ8HZE8TICJEDPB4BQTPVQD8EHBVUFIDT4ZUJCZDH4HU6UEP44QU68TSH4ZPZDPIQFJI", "hasFlower": 0, "hasSeason": 0}, {"id": "192", "q": "000,2,4,8,A,C.16.20,2,4,8,A,C.36.40,3,9,C.55,7.61,3,9,B.75,7.80,2,A,C.94,6,8.A1,B.B3,5,7,9.C0,C.D3,5,7,9.E0,C.F2,4,6,8,A.G0,C;100,2,4,8,A,C.16.24,8.36.56.75,7.95,7.B6.D5,7.F4,6,8;201,B.75,7.95,7;301,B.75,7;401,B.76:YISZQLYFVPKOBGFFLSGOHKPIC5UHHFUZGGVOQ2C45CP2CSPK8FPSOK0K5L0QSKZMBY6FOQ5LV6YO4Z8CHSVMC55P", "hasFlower": 0, "hasSeason": 0}, {"id": "193", "q": "000,2,4,6,8,A,C,E,G.20,3,5,8,B,D,G.41,3,5,7,9,B,D,F.60,2,5,7,9,B,E,G.80,3,5,7,9,B,D,G.A1,3,5,7,9,B,D,F.C0,3,5,7,9,B,D,G.E0,3,5,7,9,B,D,G.G1,4,7,9,C,F;107,9.10,G.42,E.58.62,E.75,B.87,9.95,B.C0,8,G.D3,D;242,E.62,E.86,A:89P8VCQM5ESU0EP5I5VMVPDQ44YYUQS4XUXPHQ28D5MUMD9G4PKCSEXSHWYZYGSIKDTK4TT40GT28GEPGEKUUZES5ZGVZW5X", "hasFlower": 0, "hasSeason": 0}, {"id": "194", "q": "001,3,6,8,A,D,F.20,3,6,A,D,G.41,4,6,A,C,F.58.60,2,5,B,E,G.77,9.80,2,4,C,E,G.97,9.A0,3,5,B,D,G.B7,9.C0,2,4,C,E,G.D6,A.E0,2,4,C,E,G.F6,A.G0,2,8,E,G;116,A.20,G.60,G.83,D.B0,4,7,9,C,G.E4,C.F0,G.G2,8,E;220,G.60,G.B0,4,C,G.F0,G:XJTGCDJB45NRMMED0OLOIORFO0CBNUOC4ENUGXRLML1SNE15WTII4CDETIWNZ0FCR54DDET1UX010SITRLRUDXMINZOTCE05", "hasFlower": 0, "hasSeason": 0}, {"id": "195", "q": "000,2,4,6,8,A,C,E.21,3,5,7,9,B,D.40,4,A,<PERSON><PERSON>52,<PERSON><PERSON>60,5,9,E<PERSON>72,C<PERSON>84,7,<PERSON>.91,D.A4,6,8,A.B0,2,C,<PERSON><PERSON>C4,<PERSON><PERSON>D0,<PERSON><PERSON>E2,4,A,<PERSON><PERSON>F6,8.G0,2,4,A,C,E;102,4,6,8,A,C.23,6,8,B.62,C.91,D.A5,9.B2,<PERSON><PERSON>C4,<PERSON><PERSON>E2,<PERSON><PERSON>F6,8.G2,C;202,C.26,8.F6,8;326,8.F6,8:G39P3OH8C5PGCX7TO93PH2CSC2VPAC4SA4G887OOT8H59OXAXCP985A9GGXSTAH272X5V82L45TPLLG47HA3HOS9L25X", "hasFlower": 0, "hasSeason": 0}, {"id": "196", "q": "000,4,6,8,C.12,A.20,4,6,8,C.40,3,5,7,9,C.60,2,4,6,8,A,C.81,3,5,7,9,B.A0,3,5,7,9,C.C1,6,B.D3,9.E1,5,7,B.F3,9.G0,5,7,C;100,4,8,C.12,6,A.24,8.44,6,8.63,9.76.83,9.95,7.A3,9.C1,B.D6.E1,4,8,B.G0,5,7,C:70XAVI808DRCK2MIJ0KII5WL73EHPVDAH9NNTRCV5FNL3UR0GKAVFJN6W300PGCUOKA9RO442E6IHI3HXTCM", "hasFlower": 0, "hasSeason": 0}, {"id": "197", "q": "000,2,5,7,A,C.20,2,5,7,A,C.40,2,4,6,8,A,C.61,4,6,8,B.80,3,6,9,C.A1,3,5,7,9,B.C0,2,4,8,A,C.D6.E0,3,9,C.F5,7.G0,2,A,C;100,5,7,C.12,A.26.30,2,A,C.45,7.61,4,6,8,B.80,C.A1,3,6,9,B.C2,4,8,A.D0,C.E3,6,9.G0,2,A,C:ABA49D9HUFLBE4FLEUV7REUAR55CS79LS7S4CCCUB7DRDEBBS4HLVVDSS9D9C5RCVVB9A6H6AEAU4F5LLHEDFU4V77", "hasFlower": 0, "hasSeason": 0}, {"id": "198", "q": "000,2,4,A,C,E.16,8.21,3,B,D.41,5,7,9,D.53,B.61,5,7,9,D.73,B.80,5,7,9,E.A0,2,5,7,9,C,E.C0,3,5,7,9,B,E.E0,2,4,A,C,E.F6,8.G0,2,C,E;101,D.16,8.46,8.51,D.67.80,5,7,9,E.A1,6,8,D.D3,B.F6,8.G0,2,C,E;251,D.86,8.A6,8.F7.G0,E;3A6,8;4A6,8:MBYBS45LNGBG76U3WFGUJUP0R7WC0JWYC11BC7615L77N33S5546JUNGYRCN66X5EEMWYIP1BL03P30IP6GNNJBLJ7GX5FJ3", "hasFlower": 0, "hasSeason": 0}, {"id": "199", "q": "003,5,7,9.10,C.23,5,7,9.40,3,5,7,9,C.62,4,6,8,A.81,3,5,7,9,B.A1,4,8,B.B6.C0,2,A,C.D4,6,8.E2,A<PERSON>F4,6,8.G0,C;104,8.10,C.23,6,9.45,7.62,5,7,A.83,9.C2,A<PERSON>D5,7.E2,A.F6.G0,C;210,C.46.62,A.D2,6,A.G0,C;346.D2,6,A:2H77X2OBWHWRYUYXRP8B6UR8WXFGRDUA72AFYWP0Y8HX7AZLM4X0Y0HO01M1NB8WF4GWB62DLDYUXFAZNSDS", "hasFlower": 0, "hasSeason": 0}, {"id": "200", "q": "002,4,6,8,A.10,C.23,6,9.31,B.43,5,7,9.51,B.65,7.71,B.84,8.90,C.A2,4,6,8,A.B0,C<PERSON>C4,8.D0,6,C.E2,4,8,A.F0,6,C.G2,4,8,A;102,5,7,A.10,C.31,B.45,7.51,B.65,7.71,B.90,C.A5,7.C4,8.E1,4,8,B.G4,8;202,6,A.C4,8.E4,8.G4,8;302,A.C4,8;4C4,8;5C4,8:MCOTK0ZCUY2RUULDTPK6B17YEMYS886Y0LT0MEXGYYUSC2Z0M6RSSG6OE7GTZ127GXLTDOP7CRK67E1L2T1ZEROZZ7E6KB", "hasFlower": 0, "hasSeason": 0}]