[{"id": "1", "q": "000,2,7,C,E<PERSON>14,A.22,7,C<PERSON>34,A.42,6,8,C<PERSON>50,E<PERSON>62,4,7,A,<PERSON><PERSON>70,E<PERSON>82,5,9,C.90,7,E.A3,5,9,B.B7.C1,3,B,D.D7.E0,4,A,E;100,2,7,C,E.22,7,C.34,A.42,C.57.63,B.70,E<PERSON>82,C.96,8.A4,A.C1,D.D7:79DD7DS3E9CS4RSV94BOB48RDNTCQB7B82YTE7M9WVANTESH3VA8BRM3R3H8EQWOYTV499B2", "hasFlower": 0, "hasSeason": 0}, {"id": "2", "q": "000,2,4,8,A,C.16.22,4,8,A.30,6,C.43,9.50,5,7,C<PERSON>62,A<PERSON>70,4,8,C.82,6,A.90,C<PERSON>A2,4,8,A.B6.C0,2,4,8,A,C.E0,3,5,7,9,C.G0,3,5,7,9,C;112,5,7,A.33,9.40,6,C.81,B.B6.D0,3,9,C.G5,7:3FD8OXG678D78F261M6NGO6326O2FD7M7F7G8NDF38XT6GF72T6N23N8OTN32XNNXN1OTO63", "hasFlower": 0, "hasSeason": 0}, {"id": "3", "q": "000,2,A,C.16.21,3,9,B.35,7.40,2,A,C<PERSON>54,6,8.60,2,A,C<PERSON>74,6,8.80,2,A,<PERSON><PERSON>94,8.A1,6,B.B3,9.C0,5,7,C<PERSON>D2,<PERSON><PERSON>E0,4,6,8,C<PERSON>F2,A.G6;111,B.26.46.52,A.66.71,3,9,B.A1,B.B3,6,9.D6.E3,9.F6:WDJAJC3DC36JHCD66DW6DD136WZAJJ61ZCHC31WHA36DJZWHDW6DHWWH6ZAH3Z3CZ1D6H3", "hasFlower": 0, "hasSeason": 0}, {"id": "4", "q": "000,2,4,6,8,A,C.20,2,6,A,C.41,3,5,7,9,B.60,2,6,A,C.74,8.80,2,6,A,C.A1,5,7,B.B3,9.C0,6,C.D2,A.E0,4,6,8,C;100,4,8,C.22,A.41,4,6,8,B.70,2,A,C.A5,7.D2,A.E0,C;204,8.45,7.72,A.A6;304,8:8984DEH8W4RRDEW8W6M4WVMHMWE4DDWMHRVDSM96LEW4LREVHAVHSVU8LWAW5MUVHW8MEML4DR5R", "hasFlower": 0, "hasSeason": 0}, {"id": "5", "q": "000,2,4,6,8,A,C,E.20,2,5,9,C,E.37.40,2,4,A,<PERSON>,E.56,8.60,2,C,E.74,7,A.80,2,C,E.94,7,A.A0,E.B2,5,7,9,<PERSON>.C0,E.D2,4,6,8,A,C.E0,E.F2,5,9,C.G0,7,E;101,6,8,D.30,E.67.81,D.A7.D5,9.E0,E.G7:VITIVTIDXOWIQOLYUKXUIDDQWOVIO866TWQLLLDQIDDDWUKLULYLLK868TKD8TL8ILDUVU86TIDI", "hasFlower": 0, "hasSeason": 0}, {"id": "6", "q": "001,4,6,8,A,D.21,3,6,8,B,D.40,3,5,9,B,E.57.60,2,4,A,C,E.76,8.80,2,C,E.94,7,A.A0,E.B3,5,9,B.D1,3,6,8,B,D;101,4,7,A,D.21,3,B,D.44,A.61,D.76,8.82,C.B4,A.D1,3,B,D;204,7,A.22,C.76,8.D2,C:LY3LB6EJ7U6BCDXPJXLFALC0ATCCBBYJFDYXIAULP7WTEHH963X0I9YFA3JLYU3UCAFLCL3W6Y0A30", "hasFlower": 0, "hasSeason": 0}, {"id": "7", "q": "000,2,6,A,C.14,8.20,6,C.32,A.40,5,7,C<PERSON>53,9.66.72,4,8,A.80,6,C.93,9.A1,5,7,B.B3,9.C0,6,C;100,2,6,A,C.20,C.40,5,7,C.53,9.66.72,A.80,C.93,9.A1,B.C0,6,C;206.20,C.45,7.A1,B.C6:SOO2NR2T3RB3N66O6VONX32EL2WO3TA4WNXXL42ETVRVRTAA342VXVA4OTBVU63VSUVETE", "hasFlower": 0, "hasSeason": 0}, {"id": "8", "q": "000,2,4,6,8,A,C,E.20,3,7,B,E.41,5,7,9,D.53,B.60,5,9,E<PERSON>72,7,C.85,9.90,2,C,E.A5,9.B1,7,D.D0,3,7,B,E.E5,9;100,3,5,7,9,B,E.20,7,E.41,6,8,D.53,B.60,5,9,E.72,7,C.90,5,9,E.B1,D<PERSON>C7.D0,E<PERSON>E5,9;217:99X5CAFPL69S6O59OJALFIJJ6MX99JI6WAJSR99ADCPM96POZPRJO96XSJPOOIACJRCIPADIXIZAWRAS", "hasFlower": 0, "hasSeason": 0}, {"id": "9", "q": "000,2,5,7,9,C,E.21,4,7,A,D.40,3,5,9,B,E.57.62,4,A,C.70,7,E.82,4,A,C.A0,3,7,B,E.B5,9.C0,2,C,E.D4,7,A.E1,D;100,2,5,7,9,C,E.21,4,A,D.43,5,9,B.57.62,4,A,C.77.82,C.A3,7,B.B5,9.C2,C.D4,A.E1,D:CJT8MIMP7YF50TFC5JMJGBUOD5TIDDMG05YBOUB07MCB5GY87FD0DD5M5M455JTBM7TB4F5GPBBYCYYT", "hasFlower": 0, "hasSeason": 0}, {"id": "10", "q": "000,4,6,8,A,E.12,C.26,8.30,3,B,E.45,9.50,2,7,C,<PERSON><PERSON>64,<PERSON><PERSON>71,6,8,D.93,6,8,B.A0,E.B3,5,9,B.C0,E.D2,6,8,C.E0,E;100,4,7,A,E.12,C.26,8.50,7,E.64,A.77.96,8.A0,E.D6,8.E0,E;200,E.64,A:4N1PHY22XD9DC2C6PHOXX757N11X5WBF71NF2PC25F4CR5COWRBX7NDCFXYRR24D944TT4P6", "hasFlower": 0, "hasSeason": 0}, {"id": "11", "q": "000,3,6,8,B,E.20,2,5,7,9,C,E.40,3,5,7,9,B,E.60,2,4,6,8,A,C,E.81,3,5,7,9,B,D.A0,2,4,6,8,A,C,E.C0,3,5,9,B,E.D7.E0,2,4,A,C,E;100,3,6,8,B,E.20,2,C,E.40,E.A0,E.C0,E.E0,2,C,E:VVD5DVE55L5CGI3QDGCG8LV5C933SGP9GCGVLVG5HCQ090EC8UEP9H3LUU9889ESVUDIG0QEEV0Q", "hasFlower": 0, "hasSeason": 0}, {"id": "12", "q": "000,2,4,8,A,C.16.20,2,A,C.35,7.41,3,9,B.55,7.60,2,A,C.74,8.80,2,6,A,C.A0,2,5,7,A,C.C0,2,5,7,A,C;102,A.10,C.22,6,A.41,3,6,9,B.62,A.80,2,A,C.96.A0,2,A,C.B6.C0,2,A,C:C9H6H9HMEIPPHDPDIPHDV244V0PKI4HHPIM2HHRC0HE049C4K0P92DDDR929D00D6C4DPD", "hasFlower": 0, "hasSeason": 0}, {"id": "13", "q": "000,2,4,7,9,C,E,G.20,2,4,6,8,A,C,E,G.40,2,4,6,8,A,C,E,G.62,5,7,9,B,E.80,2,4,7,9,C,E,G.A1,3,5,7,9,B,D,F.C0,2,4,6,8,A,C,E,G.E0,2,4,7,9,C,E,G.G1,3,5,8,B,D,F;114,C.37,9.55,B.C5,B.E7,9.G4,C:QB7FNFTTQRG0BRJFRJ7JZMRZR33Z0WSKNMZZF0KTQTRQZMSZ0SMMK0J9MGS3TBZNFM3BM7KT09FWKQZZKQ7N", "hasFlower": 0, "hasSeason": 0}, {"id": "14", "q": "000,2,4,6,8,A.21,3,5,7,9.40,2,4,6,8,A.62,4,6,8.81,3,5,7,9.A0,3,5,7,A.C1,4,6,9.E0,4,6,A;100,2,4,6,8,A.22,4,6,8.40,2,5,8,A.63,5,7.82,4,6,8.A0,5,A.C1,9.D4,6.E0,A;205.22,8.42,8.75:FG5BH7BGK85GGGK7H2BB8GB8F8UK75F77785FHHGHB52GFUF7KBGK878HUF5BKFB57GU875FFB55", "hasFlower": 0, "hasSeason": 0}, {"id": "15", "q": "000,2,4,6,8,A,C.20,3,5,7,9,C.41,3,5,7,9,B.60,2,4,6,8,A,C.80,2,5,7,A,C.A0,2,4,6,8,A,C.C2,4,8,A.D6.E2,4,8,A.F0,C.G2,4,8,A;103,5,7,9.20,5,7,C.33,9.51,5,7,B.70,C.82,A.A0,4,8,C.D3,9.F0,C:2ON92AX39DNO6A922XMN9OAY3HYA22YH33993RRHHHCXHY2HAHXAYAH99AXCX2D2DMOHYOXO2XYYN63D", "hasFlower": 0, "hasSeason": 0}, {"id": "16", "q": "000,5,7,C.12,A.20,4,6,8,C.40,2,5,7,A,C.60,2,4,6,8,A,C.80,2,4,6,8,A,C.A1,3,5,7,9,B.C0,3,5,7,9,C.E0,2,4,6,8,A,C;100,C.12,5,7,A.30,C.45,7.50,2,A,C.64,6,8.84,8.91,6,B.B5,7.C0,C.E0,2,4,8,A,C:37A5530R330P03TPIA5VV5PTR6T06P7ATLPV505V775IRIP64R4R6I0RIIV5IA6TL4AVRIT655TVT4AV3R", "hasFlower": 0, "hasSeason": 0}, {"id": "17", "q": "000,2,5,7,A,C.20,2,6,A,C.34,8.40,C<PERSON>52,6,A.72,5,7,A<PERSON>80,C<PERSON>92,5,7,A.B1,3,9,B.C6.D0,3,9,<PERSON><PERSON>E5,7.F0,2,A,C.G5,7;100,2,6,A,C.20,C.40,C.52,A.75,7.80,2,A,C.95,7.B1,3,9,B.D0,C.F0,5,7,C:IFUNIS51EPF1W1D5IQUWC3AW1NFYD7BI7NPEW3FNQYFS11UD93EI7TEIKIKACQI9BFA37AQUTD", "hasFlower": 0, "hasSeason": 0}, {"id": "18", "q": "000,2,4,8,A,C.16.20,3,9,C.36.40,2,4,8,A,C.63,5,7,9.70,C.82,6,A.94,8.A0,C.B2,4,6,8,A.C0,C.D3,9.E6;100,2,4,8,A,C.16.23,9.43,9.63,5,7,9.70,C.82,6,A.A0,4,8,C.D3,9.E6;253,9:4OGLJF3K3YLZG4TG7JG4YBZ3Z7Z4T3F7YJGO7GRZYYYYTZGRRTKRJK70KHYG707GGBBBH7", "hasFlower": 0, "hasSeason": 0}, {"id": "19", "q": "001,3,5,7,9,B.20,2,4,6,8,A,C.41,3,6,9,B.60,2,4,6,8,A,C.85,7.90,2,A,C.A4,8.B1,6,B.C3,9.D0,5,7,C<PERSON>E2,A;101,B.13,5,7,9.20,C.36.41,B.60,2,A,C.90,2,A,C.A4,8.B1,B.C6.D0,C:QH9KDT9B9KP49RB49RUO04PHK4POJKPKQ0ZQ444HU9KKUB0HDT0UZOKH0OKBQKH905495J", "hasFlower": 0, "hasSeason": 0}, {"id": "20", "q": "000,2,4,6,8,A,C,E.21,7,D.33,B.40,5,7,9,E<PERSON>52,<PERSON><PERSON>64,<PERSON><PERSON>70,2,7,C,<PERSON><PERSON>84,A<PERSON>92,C.A0,7,E.B2,4,A,C.C0,7,E.D2,C.E0,4,6,8,A,E;101,7,D.21,D.37.40,E.62,C.70,4,A,E.82,C.A1,D.B3,7,B.C0,E.E5,9;201,D:TPPQPQC9X8CXCQBPBC74B9C5GAP4BG8C5G75RWFBQ5BAFWPA5PG5TGPATRF8G8WCWXCGCAFCXGAT", "hasFlower": 0, "hasSeason": 0}, {"id": "21", "q": "000,2,4,6,8,A,C.20,2,6,A,C.34,8.40,6,C.52,4,8,A.70,2,4,6,8,A,C.92,4,8,A.A6.B0,4,8,C.C6.D0,2,A,C.E4,6,8;101,4,6,8,B.22,A.34,6,8.52,4,8,A.70,5,7,C.92,4,8,A.B6.D2,A<PERSON>E5,7;204,8.36.52,A.93,9.B6:N2C7CW0PNGA2GW27C770AAV7A7PZNCG0G2NW7VNVZC0CVCAWNP77APA2CZZ7VZNPVNAWWVZC2PN2VCN2", "hasFlower": 0, "hasSeason": 0}, {"id": "22", "q": "001,4,6,8,A,D.20,2,4,7,A,C,E.40,2,5,7,9,C,E.60,2,4,6,8,A,C,E.80,2,4,6,8,A,C,E.A0,2,4,7,A,C,E.C1,3,5,9,B,D.D7.E0,3,B,E.F5,7,9.G0,2,C,E.H4,7,A.I0,2,C,E;101,6,8,D.27.41,D.56,8.74,7,A.B3,B.D7.H7:J9UB66ZZNT96BUMPS9JSC6MUTPTA6ATTUZUAUUA6CJPU6TMABJATSUCP6SB6NC6JQQZSTZMJU9ZCQ9N9SCQN", "hasFlower": 0, "hasSeason": 0}, {"id": "23", "q": "000,2,6,8,C,E.20,2,4,6,8,A,C,E.40,5,7,9,E<PERSON>52,C<PERSON>64,6,8,A.70,2,C,E.84,A.92,6,8,C.A0,4,A,E.B6,8.C1,3,B,D.D5,7,9.E0,2,C,<PERSON>;106,8.20,4,A,E.37.45,9.64,7,A.83,B.97.B7.C3,B.D1,D:F2DD85O46655D5455DAF4OAWDVVFVRWFA665FRXFO4RADRV2DO6DO4DA262X5WVOVAW5584D", "hasFlower": 0, "hasSeason": 0}, {"id": "24", "q": "001,4,6,8,A,D.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,2,6,8,C,E.74,A.80,2,6,8,C,E.A1,3,5,7,9,B,D.C2,5,9,C.D0,E.E2,4,6,8,A,C;105,7,9.20,3,B,E.35,9.62,7,C.92,7,C.C5,9.D0,E.E4,A:0RUVJZJ0V3LVVRZU0J3ZLU3V0TTTRJZZTLTV0JTTRUJ03L30URU3U3JU3JUV0RT3Z0ZZ0VLLJUJ3", "hasFlower": 0, "hasSeason": 0}, {"id": "25", "q": "000,2,4,6,8,A,C.20,3,5,7,9,C.40,2,4,6,8,A,C.60,2,4,8,A,C.76.80,2,A,C.95,7.A0,C.B2,4,8,A.C0,6,C.D2,4,8,A.E6.F0,3,9,C.G6;100,4,8,C.24,6,8.30,C.42,4,6,8,A.50,C.63,9.71,6,B.90,5,7,C.B0,3,9,C.C5,7.D2,A.E6:FFG9FLD6M0XC0HX9B9B3P033I5MXIHQQ3LBZB6PZFCP3XPH0CBC6XMHFXF33ZFO6ZIGCHZIIT93T5DFBHOIZCM", "hasFlower": 0, "hasSeason": 0}, {"id": "26", "q": "000,3,5,7,9,B,E.20,2,4,6,8,A,C,E.40,4,6,8,A,<PERSON><PERSON>52,<PERSON><PERSON>66,8.73,B.80,5,9,E<PERSON>93,B.A5,7,9.B1,3,B,<PERSON>.C6,8.D0,4,A,E.E2,6,8,C.F0,4,A,E.G2,7,C;105,9.10,E.27.40,7,E.73,B.93,B.C6,8.D0,E<PERSON>F1,7,D:EXGX00GEPXDGDDU8BUEXG7QEN787QUEXB0800G8D88778NXDGPPXXQGE7DQ8DUQDDPD8NUQU0NG8", "hasFlower": 0, "hasSeason": 0}, {"id": "27", "q": "000,2,4,6,A,C,E,G.18.20,2,4,6,A,C,E,G.40,2,4,6,A,C,E,G.58.60,2,4,6,A,C,E,G.78.81,3,5,B,D,F.97,9.A0,2,4,C,E,G.B6,8,A.C0,3,D,G.D5,7,9,B.E1,3,D,F.F5,7,9,B.G0,2,E,G;103,D.31,6,A,F.83,D.E1,6,A,F:JL1CQLIASAO1C10WAHOWQAKJXH7CLL1ISH1KOJ70L0KLOII1SCSTIQHQCSHKSSWXQSHS0ATKWHSLCAHKQJIL", "hasFlower": 0, "hasSeason": 0}, {"id": "28", "q": "002,4,6,8,A.10,C.22,4,6,8,A.30,C.43,5,7,9.50,C<PERSON>62,A<PERSON>74,8.80,2,A,C.A1,4,8,B.C0,3,5,7,9,C.E0,2,4,8,A,C.F6.G0,2,A,C;104,8.10,2,A,C.30,C.43,9.50,C.62,A.B4,8.C0,6,C.D3,9.E0,C.F2,A.G0,C:2JG5JVV2XG5MNNXGLN552XL2NNXVMJXLGL5GGXMVMXML2LJVNVGJ2VXJXJ5G5XMVVM5V2J2M5NN5", "hasFlower": 0, "hasSeason": 0}, {"id": "29", "q": "001,3,5,7,9,B.23,9.30,5,7,C<PERSON>42,A.50,4,6,8,C.70,2,6,A,C.90,2,5,7,A,C.B0,3,5,7,9,C.D1,3,6,9,B.F3,9.G5,7;101,3,5,7,9,B.45,7.66.71,B.86.91,B.A5,7.B3,9.C6.E3,9;204,8.45,7.66.71,B.A5,7.C6:NPJ1E1101KOEIUTCOTB7D7IDWDPNTT0JKCC0NJNWW1WEW3JCKEIPEEPE7IN3B1UTKFFWWWNT70DE", "hasFlower": 0, "hasSeason": 0}, {"id": "30", "q": "001,3,6,8,B,D.20,3,5,7,9,B,E.40,3,5,7,9,B,E.62,5,7,9,C.80,2,4,7,A,C,E.A1,3,5,7,9,B,D.C1,3,5,7,9,B,D.E0,2,4,7,A,C,E.G0,2,4,7,A,C,E;101,D.36,8.57.87.A3,B.B1,7,D.C3,B.D7.F1,D.G4,A;201,D:OLOMQCGHTC5XNXLFGKEXAXWQMJ5ONLHRG52ENEX56JF3NN2R69NNBLOG5LKEH511WR22WBNWL3RAXT9H", "hasFlower": 0, "hasSeason": 0}, {"id": "31", "q": "002,4,6,8,A.20,2,5,7,A,C.41,3,5,7,9,B.60,2,4,8,A,C.76.81,4,8,B.96.A2,A.B0,4,8,C.D1,3,5,7,9,B.F0,2,5,7,A,C;102,4,6,8,A.26.31,B.44,8.60,3,9,C.75,7.81,B.B0,4,8,C.D4,8.E1,6,B;263,9:WVJ9ZUIZ1JIWTG19Z1G661ZILWVVTGZG1GUWUTW6Q7G7L7GIQUUL1WU7WJJT6UL1GZ1ZWGUZTGTV", "hasFlower": 0, "hasSeason": 0}, {"id": "32", "q": "001,3,5,7,9,B.20,4,6,8,C<PERSON>32,A<PERSON>44,6,8.51,B.63,5,7,9.80,3,6,9,C.A3,5,7,9.B0,C<PERSON>C3,5,7,9.D1,B<PERSON>E3,5,7,9.F0,C<PERSON>G2,4,6,8,A;104,8.32,A.55,7.73,6,9.93,6,9.B0,5,7,C.D2,A.G2,A;255,7:2967OWN462D7O5Y55Y99Y2WD2Y5Y222ND2CONNN4Y26996Y77N6Y45C4O6W765D9DCC2D67W", "hasFlower": 0, "hasSeason": 0}, {"id": "33", "q": "000,2,5,7,9,C,E.20,2,4,7,A,C,E.40,2,4,7,A,C,E.61,4,6,8,A,D.80,2,4,6,8,A,C,E.A0,2,4,6,8,A,C,E.C0,2,4,A,C,E.D7.E0,3,B,<PERSON>;105,9.20,7,E.33,B.64,A<PERSON>71,D.94,A.A0,E<PERSON>B4,<PERSON><PERSON>C0,E.D7.E3,B:EKEEC2AC1ATDYE0IYT1P7CRCCKA1U0LRY9DURP9TBSMIHY72HIKU1WTLJUA77DSMRWCEYYIBKEJD", "hasFlower": 0, "hasSeason": 0}, {"id": "34", "q": "001,5,7,9,D.13,B.20,5,9,E.32,C.40,4,6,8,A,E.52,C<PERSON>60,6,8,E.72,4,A,C.80,6,8,E.92,C.A4,7,A.B0,E.C2,4,6,8,A,C.D0,E.E2,4,6,8,A,C;101,6,8,D.25,9.40,3,B,E.56,8.71,3,5,9,B,D.97.B0,E.C5,7,9.D1,D:FLFFBQLBMQMBLCMQCBFQBBLQL434BMB3MBC4C34M4QQ33FMMCCM4LFL3LQ3Q4CFFFB3L34CCCQF3ML44", "hasFlower": 0, "hasSeason": 0}, {"id": "35", "q": "000,2,5,7,A,C.21,5,7,B.40,2,4,6,8,A,C.61,3,5,7,9,B.80,2,4,6,8,A,C.A1,4,6,8,B.C0,5,7,C.D2,A.E0,5,7,C;100,6,C.21,5,7,B.40,2,4,8,A,C.56.62,A.74,6,8.80,C.95,7.B5,7.C0,C.D5,7.E0,C:2SYEZ5H0ZHXET3LXZ929MV9X0DYSDX3TT9HVTE23HYD5X3T3Y3L29ZMSMS5EM0M3MZZZ3T09Z5XD", "hasFlower": 0, "hasSeason": 0}, {"id": "36", "q": "001,3,7,B,D.15,9.20,2,7,C,E.34,A.40,2,7,C,E.54,A.60,2,6,8,C,E.74,A.80,2,6,8,C,E.A0,2,4,6,8,A,C,E.C0,2,4,6,8,A,C,E.E2,4,6,8,A,C;112,C.31,3,B,D.47.51,4,A,D.71,D.A1,4,A,D.B6,8.C1,3,B,D.E2,4,A,C:KBU70GKE0E7RG0HL0D9E7R7LH0X07HB04K0KXBEH07GRG7GBD4GXBDXHLR4DBXX4LUHRLH9EL0H7E7EGER7G", "hasFlower": 0, "hasSeason": 0}, {"id": "37", "q": "000,2,6,8,C,E.21,3,6,8,B,D.40,2,5,7,9,C,E.60,2,4,A,C,E.77.80,2,4,A,C,E.A0,2,4,7,A,C,E.C0,2,5,9,C,E.D7.E2,C;100,6,8,E.23,6,8,B.40,2,7,C,E.64,A.72,7,C.94,A.A0,2,C,E.C2,C.D7;200,7,E.23,B.A2,C.C2,C:MMQALKICJ1TKIJPCTYGCPGAQLDFSD65JIFA4KZY434J18JMK65SU1JID3I8EZ64ZLEMLJUIJLDLCL6ZAA1AL", "hasFlower": 0, "hasSeason": 0}, {"id": "38", "q": "001,4,6,8,A,D.21,3,5,9,B,D.37.40,2,4,A,C,E.56,8.62,C<PERSON>70,7,E<PERSON>82,C<PERSON>96,8.A1,3,B,D.B5,7,9.C0,2,C,E.D4,6,8,A.E0,2,C,E;105,7,9.11,D.23,B.37.43,B.70,2,C,E.96,8.A1,3,B,D.C0,7,E.D2,4,A,C.E0,E;2D4,A:AAT5N3ZT55NZ5T2A33TZN3532ZN3E5N2NENEAEZE25NT2533EAT3T5ETAAZ2NZTEZZENZA2TE23225AA", "hasFlower": 0, "hasSeason": 0}, {"id": "39", "q": "000,2,4,6,8,A,C,E.21,3,5,9,B,D.37.40,2,4,A,<PERSON>,E<PERSON>56,8.61,3,B,<PERSON><PERSON>76,8.80,2,C,E.95,7,9.A0,2,C,E.B5,7,9.C0,2,C,E.D4,6,8,A.E0,2,C,E.F4,A.G1,6,8,D;105,9.13,B.32,C.51,D.63,B.81,7,D.A0,2,C,E.C1,D.D6,8.E2,C:4C6QP414OU7SQS9N17TYVSVIIGGTT4SSFUF8Q7Q1ASMC7SKAFYIP9GN144IT6ZUYKVFSF68GOVYGUZFGY6YM", "hasFlower": 0, "hasSeason": 0}, {"id": "40", "q": "000,3,5,7,9,B,E.20,3,5,9,B,E.37.40,2,4,A,C,E.57.62,4,A,C.76,8.81,4,A,D.97.A0,3,5,9,B,E.B7.C0,2,4,A,C,E.D6,8.E0,2,C,E;105,9.10,3,B,E.25,9.30,E.44,7,A.62,C.76,8.A4,6,8,A.C0,2,C,E.E0,2,C,E;262,C.C2,C:ZZH7ZEBZP3OJXP3XXHW2W3DSKWEBYZWZZ83IXJX8XEWA2NXNH2U82IP3PE7BANDBYX7WHZXHH7NO3NKXSNU8", "hasFlower": 0, "hasSeason": 0}, {"id": "41", "q": "000,3,6,9,C.20,2,4,6,8,A,C.40,3,5,7,9,C.60,2,A,C.74,6,8.80,2,A,C.94,8.A0,2,6,A,C.C0,3,5,7,9,C.E3,6,9.F0,C.G2,4,6,8,A;103,9.16.21,3,9,B.74,6,8.82,A.94,8.A6.C3,9.F3,6,9;274,8.82,A:EZ39FFXZZ19QUZUZQU1Q1DOUXLFJ31ZUQFFOUQXUOO30FXZL9FZU9O1J1DL30E9F3X9XLU311OUQ", "hasFlower": 0, "hasSeason": 0}, {"id": "42", "q": "001,4,6,8,B.20,3,5,7,9,C.40,3,5,7,9,C.60,2,6,A,C.81,3,5,7,9,B.A0,2,5,7,A,C.C1,3,5,7,9,B.E4,8.F0,6,C.G2,4,8,A;101,6,B.14,8.20,C.33,6,9.40,C.56.60,C.76.81,3,9,B.A0,5,7,C.C2,4,6,8,A.F0,4,8,C.G2,A:UIINZGTZIGNGZN5U5T4ZYT44IN5GUGU34TZN45Z354NG35TZ44I5UY3I3T5TG4U5ZUY5NY3NGYUNI4N3T3YI", "hasFlower": 0, "hasSeason": 0}, {"id": "43", "q": "001,4,8,B.16.20,2,A,C.34,6,8.41,B.53,5,7,9.60,C.73,5,7,9.80,C.93,5,7,9.A0,C.B2,4,6,8,A.D1,3,5,7,9,B.F0,2,5,7,A,C;104,8.11,6,B.31,6,B.44,8.56.60,C.75,7.80,3,9,C.A0,5,7,C.B2,A<PERSON>C4,6,8.D2,A.E6.F0,2,A,C:NNBMJSJTJ8NCC8S2B62J6NCTCC6C2SH28JSMSJCBB28T2JTCB8MMC2T6MTM2J6NT2S68NH26NT8CS8BNN6SN", "hasFlower": 0, "hasSeason": 0}, {"id": "44", "q": "000,3,6,8,B,E.20,2,5,9,C,E.41,5,7,9,D.60,3,7,B,E<PERSON>75,9.80,2,7,C,E.94,<PERSON><PERSON>A1,6,8,D.B3,B<PERSON>C1,7,D.D5,9.E0,3,B,<PERSON><PERSON>F5,9.G1,7,D;100,3,6,8,B,E.20,2,C,E.35,9.57.63,B.76,8.81,D.A1,6,8,D.B3,B<PERSON>C1,7,D.D5,9.E0,3,B,E.G1,7,D:B1N91DD3KK9O9D693GY6OD3BYX1QD6GGYKXQ6DOK111KOKY9BDI16NY6NG6O3OIGY91161XBGIIGNIIND6NYGYX6", "hasFlower": 0, "hasSeason": 0}, {"id": "45", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.41,3,6,9,B.60,2,5,7,A,C.81,3,5,7,9,B.A0,2,4,8,A,C.B6.C0,2,4,8,A,C.D6.E0,3,9,C.F6.G0,2,4,8,A,C;100,5,7,C.13,9.26.32,A.46.81,4,8,B.A3,9.D6.F6.G3,9:A91402BY7FJJ41KDJZKG9DIBZYZ7GI1KIUU7IA0KY2PZB474OOOEOF1IEKIYZYLPBK9YJ9LG2ZG2", "hasFlower": 0, "hasSeason": 0}, {"id": "46", "q": "000,2,5,7,9,C,E.20,2,6,8,C,E.34,A.40,7,E<PERSON>52,4,A,C<PERSON>66,8.70,2,C,E.85,7,9.91,3,B,D.A5,7,9.B0,3,B,E.C5,7,9.D0,2,C,E.E4,6,8,A;105,9.10,7,E.30,7,E.54,A.66,8.86,8.A4,A.B0,6,8,E.D1,5,7,9,D:KVKDDZC33CMJT3VKMVF3DJRWFWK2M34RMVMWVVLV3WFKJTJWMJKD3CVTFJLHMC2HTMJCZW4CR2R23J", "hasFlower": 0, "hasSeason": 0}, {"id": "47", "q": "000,2,4,6,8,A,C,E.21,4,6,8,A,D.40,3,5,7,9,B,E.62,4,7,A,C.70,E.82,5,9,C.90,E.A2,6,8,C.B4,<PERSON><PERSON>C0,E.D3,5,7,9,B.E0,E.F2,4,6,8,A,C;100,2,4,7,A,C,E.21,4,6,8,A,D.40,3,5,9,B,E.62,7,C.85,9.C0,4,A,E.D6,8.E3,B.F5,7,9:1TPM0H5TTN1MIIQMWH85I9RH5HYPM1JT1RRQ15JJHNHLN8M8MI0JWPHLPIRJPYT1TI91QPJ5HQN15JMHJLR8MH55LR", "hasFlower": 0, "hasSeason": 0}, {"id": "48", "q": "000,2,5,9,C,E.17.20,3,5,9,B,E.37.40,2,4,A,C,E.57.63,5,9,B.71,7,D.84,<PERSON><PERSON>90,6,8,E.A3,<PERSON><PERSON>B0,5,9,E<PERSON>C2,7,C.D5,9.E0,3,7,B,E.G0,2,4,6,8,A,C,E.I0,2,4,6,8,A,C,E;116,8.24,A.43,7,B.64,A.87.B0,5,9,E<PERSON>E3,B.F7.G2,5,9,C.H7:QNOA53PARNYFOXF67MRACPXFYX2MQ5E7JF1Q7QO24AKCON4F1W22E3MJ7PYHQKFXKKMOWKA6H5AOHSNMMPYQKSH5", "hasFlower": 0, "hasSeason": 0}, {"id": "49", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,4,6,8,A,E.60,2,5,7,9,C,E.80,4,6,8,A,E.A1,3,5,9,B,D.B7.C1,4,A,D.D6,8.E1,3,B,D.F6,8.G0,2,4,A,C,E;103,6,8,B.10,E.23,B.35,9.40,E.56,8.84,A.A3,B.E1,3,6,8,B,D.G0,4,A,E;2E3,B:Z0FZ0ZQ6QYFZT0GFG4A4HAHG4G6F66GQHAKGGQHAA6FKTS64ZSZKQHGKQ6YKKYGYHYGZSKQZASSQKTQFTSQTZZ0T6Y", "hasFlower": 0, "hasSeason": 0}, {"id": "50", "q": "000,2,5,9,C,E.20,4,6,8,A,E.32,C.40,4,7,A,E<PERSON>63,B<PERSON>71,5,9,D.83,7,B.91,5,9,D.B3,B.C1,5,7,9,D.D3,B.E0,7,E<PERSON>F4,A<PERSON>G1,6,8,D;101,5,9,D.20,5,9,E.33,7,B.71,D.86,8.91,D.C1,3,7,B,D.E0,7,E.G7;201,D.15,9.81,D.C1,D:OF77L7MOTEGIFTMLGILFLE7LT77LGOMO1G1IMFGGG1OFG1FO1E1OLTLO71FTMFOETTLTTMFFMGIOMII1TGL7", "hasFlower": 0, "hasSeason": 0}, {"id": "51", "q": "001,3,5,7,9,B,D.21,3,5,9,B,D.37.40,2,4,A,C,E.61,4,7,A,D.80,3,6,8,B,E.A2,4,6,8,A,C.C1,3,5,9,B,D.D7.E1,4,A,D.F6,8.G1,D;102,4,7,A,C.22,4,A,C.40,4,A,E.64,A.77.80,E.96,8.A4,A.B2,<PERSON><PERSON>C5,9.D1,7,D.F7;202,C.40,E.64,A:EVH2Z2HZHQ0XZH9VUEE2UMQEUHM2U0QXMXEXZX0EZUV00HV00V2QXEXX2V920UQH9HQ2UMQEUQ9UVUHEHXV2EZ2X", "hasFlower": 0, "hasSeason": 0}, {"id": "52", "q": "000,2,5,7,A,C.20,2,5,7,A,C.40,2,5,7,A,C.63,5,7,9.82,A.94,8.A2,6,A.B0,C.C2,4,6,8,A.D0,C.E2,4,8,A;100,2,5,7,A,C.20,2,5,7,A,C.40,C.55,7.63,9.92,4,8,A.A6.B0,C.C3,6,9.D0,C.E2,A;201,B.16.20,C.40,C.B0,6,C.C3,9.D0,C.E2,A:J8DUMBSQFQK72U38XB0W2EKSWM7SS1XQMWUSKM16YQ2UK3EWSKKS2J10I1LSG7IFDL93IQWIEM7Q36W9SEYSMGEE77", "hasFlower": 0, "hasSeason": 0}, {"id": "53", "q": "001,5,7,9,D.13,B.20,5,7,9,E<PERSON>32,C<PERSON>40,5,7,9,E<PERSON>52,<PERSON><PERSON>64,7,A<PERSON>72,C.80,5,7,9,E<PERSON>92,C<PERSON>A4,6,8,A.B1,<PERSON><PERSON>C3,5,7,9,<PERSON>.D0,E<PERSON>E2,4,7,A,C.F0,E.G2,4,6,8,A,C;107.20,7,E.35,9.42,C.62,7,C.87.A5,7,9.C3,B<PERSON>E0,7,E.F3,B;217.42,C.77.A7.E0,E:6NMTNZ8ZS6DS3T34DL4S4NTMNGQ8JJTZ6464TT6G6ZN8QM384SS446QBD4JTGQQGM6L3JD8NNLGGQB6TSN8T6TL4", "hasFlower": 0, "hasSeason": 0}, {"id": "54", "q": "000,3,5,7,9,C.22,6,A.30,C.42,4,8,A.50,6,C.62,A.70,4,8,C.82,6,A.A0,2,4,8,A,C.B6.C0,2,4,8,A,C.E0,3,5,7,9,C.G0,3,5,7,9,C;100,3,5,7,9,C.22,A.40,2,4,8,A,C.60,C.72,4,8,A.92,A.A4,8.B0,<PERSON><PERSON>C2,A<PERSON>D0,4,8,C.F0,C.G3,5,7,9:27CRF4FFL88EWFFL7U287E7Y2FW2OCMF74E2ME37C7RM8FR2L7F2CM407OOU300LOF20CWRY702R2CUUYWY0C4UCUR", "hasFlower": 0, "hasSeason": 0}, {"id": "55", "q": "001,4,6,8,A,D.21,4,6,8,A,D.40,3,7,B,E.55,9.61,3,7,B,D.75,9.80,3,7,B,E.95,9.A0,3,7,B,E.C1,3,5,7,9,B,D.E1,3,5,7,9,B,D;101,5,7,9,D.25,9.40,E.53,B.65,7,9.73,B.93,5,7,9,B.C3,5,9,B.E3,7,B;205,9.E3,B:S10MQHHZ6QZZ3DZHMR6RDE1HMKHMK44PHR316SQM4HP4MZHQQP4QEHM3ZZEMP1HZD1ZRMPME614P031EED1Z", "hasFlower": 0, "hasSeason": 0}, {"id": "56", "q": "002,4,6,8,A,C.10,E.22,4,6,8,A,C.40,3,5,9,B,E.57.60,2,4,A,C,E.76,8.80,2,4,A,C,E.96,8.A0,2,4,A,C,E.B6,8.C1,3,B,D.D6,8.E0,2,4,A,C,E;103,6,8,B.22,4,A,C.40,3,5,9,B,E.62,C.82,C.95,9.A1,D.B3,7,B.D1,D:PFP28DVWZK8WQEYEIKPEW4CZZJC8QWD38JW8WEEMQ2W29FB3RZZIRMDJIEI82ZIDQCCEWI8JE9C8PPBCVP4Y", "hasFlower": 0, "hasSeason": 0}, {"id": "57", "q": "000,3,7,B,E.15,9.20,7,E<PERSON>33,B.40,5,7,9,E<PERSON>52,<PERSON><PERSON>64,7,<PERSON><PERSON>70,<PERSON><PERSON>83,B.90,7,E<PERSON>A2,4,A,<PERSON><PERSON>B0,E.C3,6,8,B.D1,D.E3,5,7,9,B.F0,E.G3,7,B.H0,5,9,E.I3,7,B;100,7,E.20,7,E.40,E.70,E.90,3,7,B,E.C3,6,8,B.E4,6,8,A.F0,E.G7.H0,3,5,9,B,E:14UGC5L7YYDDDBGK7AL6GMCC1M1YYLUGVD7CVCLMD1MUCGD76A4DCBDDU5LP1MK1P14U4CVUYUCLV4YU1CUU4ADMAG", "hasFlower": 0, "hasSeason": 0}, {"id": "58", "q": "001,3,6,8,B,D.20,3,6,8,B,E.41,3,7,B,D.61,4,6,8,A,D.82,4,7,A,C.A1,4,6,8,A,D.C1,4,A,D.D7.E1,4,A,D.F6,8.G0,2,C,E;102,C.16,8.20,3,B,E.37.41,D.57.61,D.74,A.87.94,A.A1,6,8,D.D1,7,D.F1,7,D;202,C.17.20,E.74,A.A7.E1,D:CLJM5SARJLJ88HHLYLEVWHJLHNAIYSV50L5A56AIA8H3EJCCM6HLDLT55SES1EAVJT0631R5EJ8EJ1SWV6D1S5CN", "hasFlower": 0, "hasSeason": 0}, {"id": "59", "q": "000,2,6,A,C.14,8.20,2,6,A,C.34,8.40,2,A,C<PERSON>54,8.61,<PERSON><PERSON>73,5,7,9.80,C.92,4,8,A.A0,C.B2,4,8,<PERSON><PERSON>C0,<PERSON><PERSON>D2,4,8,A<PERSON>E0,6,C<PERSON>F2,A<PERSON>G0,4,8,C;101,6,B.13,9.20,5,7,C.33,9.40,C.61,4,8,B.76.80,<PERSON><PERSON>A1,B.B3,9.C0,C.D3,9.E6.F0,C.G4,8:PHYQBDEPNOK2S3NOWCWPDSYRSEZNP2KSWQ2ARKSHPOKW23PGGSNROZNGQB2NNZKQYSO3YSCQYGYDDKQ3ZOR2AN", "hasFlower": 0, "hasSeason": 0}, {"id": "60", "q": "001,3,5,7,9,B,D.20,4,6,8,A,E.41,3,5,7,9,B,D.60,3,6,8,B,E.80,3,6,8,B,E.A0,4,6,8,A,E.B2,C.C0,4,6,8,A,E.E0,2,4,6,8,A,C,E;102,4,6,8,A,C.20,4,A,E.42,4,7,A,C.66,8.73,B.96,8.B0,2,4,7,A,C,E.D0,4,A,E.E6,8:MY3DJ639LVTE1J83F9ARVWECDHLE60H8FH90D3D6YODTQFFZ3QMGDHGWTOA9ECDCD9EECDTZ3E0H6DFE9F011RH1", "hasFlower": 0, "hasSeason": 0}, {"id": "61", "q": "000,2,6,A,C.14,8.20,2,6,A,C.34,8.40,2,6,A,C<PERSON>54,8.60,6,C<PERSON>72,A.80,4,8,C.92,A<PERSON>A0,6,C<PERSON>B4,8.C0,2,6,A,<PERSON><PERSON>D4,8.E2,6,A<PERSON>F0,4,8,C.G2,6,A;115,7.22,A.30,C.43,5,7,9.66.70,C.82,4,8,A.A0,C.B6.C1,4,8,B.F2,A:B4BP8WBWO8P4OK90POOXW990M00BM9Z1X44K19ZK0BPMPP9B8BXZ98Y0MWY0Z10BWM1MYB1Y1WX900BK", "hasFlower": 0, "hasSeason": 0}, {"id": "62", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.41,3,5,7,9,B,D.60,2,4,7,A,C,E.81,3,6,8,B,D.A1,3,6,8,B,D.C0,2,4,6,8,A,C,E.E0,2,5,7,9,C,E.G0,2,5,7,9,C,E.I0,3,5,7,9,B,E;103,5,7,9,B.45,9.93,B.D5,9.F5,9.I3,5,9,B:VRQLZVRRZ3SX38RZQJKX0HSSN4SPJOJQXSQHRPAR988KQ3KZ0Z6P685LAPSJOSNS9RRKQPR3NSATVR4S5NTXAZPV", "hasFlower": 0, "hasSeason": 0}, {"id": "63", "q": "000,2,4,7,A,C,E.20,5,7,9,E.40,3,6,8,B,E.60,4,7,A,<PERSON><PERSON>72,C.85,7,9.91,D.A3,6,8,B.B0,E.C2,4,6,8,A,C.D0,E.F0,5,7,9,E.G2,C;102,C.17.20,E.43,6,8,B.50,E<PERSON>64,7,A.72,C.85,7,9.91,D.A3,7,B.B0,E.C2,4,7,A,C.F0,6,8,E;264,A:MQIIT5RIQGR4MRI5XRX494H774XXTXMMGRTMR9IXQ7RGHI9HQI9X4HIMGQR7M9IHTMQRQGIGQX7RHTQQ4T57M9M5", "hasFlower": 0, "hasSeason": 0}, {"id": "64", "q": "002,4,6,8,A.10,C.22,4,6,8,A.40,2,4,6,8,A,C.63,5,7,9.70,C.83,5,7,9.90,C.A3,5,7,9.C0,2,5,7,A,C.E2,4,6,8,A<PERSON>F0,C.G2,4,6,8,A;104,6,8.22,A.35,7.41,B.53,9.75,7.95,7.C0,2,A,C.E5,7.F2,A.G6;204,8.76:V47QVPXCQV7XCL27VX4Q2XVVXLPLLD2DL77CDL72QXL47QLCD4DQL242VQQCLP4PQCX7DQ4C4XX7VX7C", "hasFlower": 0, "hasSeason": 0}, {"id": "65", "q": "000,2,5,8,B,E,G.20,6,8,A,G.32,4,C,E.47,9.51,5,B,F.63,7,9,D.70,5,B,G.83,7,9,D.90,5,B,<PERSON><PERSON>A2,7,9,E.B4,C.C1,6,A,F.D3,8,D.E0,5,B,G.F7,9.G0,2,4,C,E,G;126,A.33,D.47,9.51,5,B,F.63,D.76,A.83,D.B4,C.C1,F.D3,8,D.G1,F;273,D:RPKDYNSXW0NKSY0OY8S4UU4DO4HOUINN0SXRSHHN8QXDQMKW8MKXX11DUWPUW48KKQDHIHROSMDHWX80WMRUYNQD8D", "hasFlower": 0, "hasSeason": 0}, {"id": "66", "q": "002,4,6,8,A,C.20,3,6,8,B,E.40,2,4,A,C,E.56,8.60,2,C,E.74,6,8,A.81,D.93,5,7,9,B.A0,E.B2,5,7,9,C.C0,E<PERSON>D2,4,A,C.E6,8.F0,3,B,E.G5,7,9;113,7,B.30,3,B,E.60,2,C,E.76,8.93,6,8,B.A0,E.B2,5,9,C.D3,B.F3,6,8,B:YH0R1V9JAPJRRJ0GARAJTJPTP25Y9RYAT010Y0T0JAITTI95H4JYTRG401PJ1RJRTRA00ITPP9NVI02TJYN22R", "hasFlower": 0, "hasSeason": 0}, {"id": "67", "q": "002,4,8,A.20,2,6,A,C<PERSON>42,4,6,8,A.50,C<PERSON>63,5,7,9.70,C<PERSON>82,4,6,8,A.A0,2,4,8,A,C.B6.C0,2,A,<PERSON><PERSON>D4,6,8.E0,<PERSON><PERSON>F2,6,A<PERSON>G4,8;104,8.20,2,6,A,C.43,5,7,9.50,C.63,5,7,9.70,C.83,5,7,9.A0,4,8,C<PERSON>B2,<PERSON><PERSON>C0,<PERSON><PERSON>D4,8.F2,6,A.G4,8:JEN1AOZZ2J1U38ZU1E2ZIOW2AEKEUNW23EN1UOJU2ENW8222OJJ2Z1NI3UEZZ1J8U8A3UEWZNNN8UN82A3KUN3", "hasFlower": 0, "hasSeason": 0}, {"id": "68", "q": "000,3,5,7,9,B,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.80,5,7,9,E.A0,2,5,7,9,C,E.C0,2,4,6,8,A,C,E.E0,3,5,7,9,B,E.G1,3,6,8,B,D;113,B.20,7,E.42,7,C.75,7,9.A2,5,7,9,C.C2,C.D7.F3,7,B.G1,D:UIJJBSRUIWVBIJVDSWJBWVSUVSRJBBDWIVUWWWWDUIUDR6BVBUJSJIWIVDD6RVIBSDDWJUJIUVS6BJVSUIBD6SDS", "hasFlower": 0, "hasSeason": 0}, {"id": "69", "q": "000,3,5,7,9,B,D,G.20,2,4,6,8,A,C,E,G.41,4,6,8,A,C,F.60,2,5,8,B,E,G.80,2,4,6,8,A,C,E,G.A1,4,6,A,C,F.B8.C1,4,C,F.D6,8,A.E1,F.F3,5,7,9,B,D.G0,G;104,8,C.16,A.22,E.34,7,9,C.55,B.71,F.84,C.A1,F.D6,A.E8.F5,B;234,C:O7AH2O5UVAKU7U75KOV7AHV5H5KVUKHUUU72AOUV2V2HAU5OKKKV7O55V2A2O2725A77KOU2VK5AH72HOOVHHHK5AA", "hasFlower": 0, "hasSeason": 0}, {"id": "70", "q": "000,4,6,8,A,E.12,C.20,5,7,9,E.33,B<PERSON>40,5,7,9,E.53,<PERSON><PERSON>60,5,7,9,E.81,3,5,7,9,B,D.A0,4,6,8,A,E.B2,C.C0,4,6,8,A,E<PERSON>D2,C<PERSON>E4,6,8,A<PERSON>F0,E<PERSON>G3,5,7,9,B.H0,E.I2,5,7,9,C;104,A.10,E.30,4,A,E.47.50,5,9,E.94,A.D6,8.F7.G0,4,A,E:F0GV3TI4YFR14ILUZZ91FBHU9FSULFUQ17C7931Y3I3Z1SCS5ZUYBIHT413QFCQQ5041G5IFS5T1RUL93V7YFITCL7", "hasFlower": 0, "hasSeason": 0}, {"id": "71", "q": "000,2,4,6,8,A,C,E.20,4,7,A,E.32,C<PERSON>44,6,8,A.50,2,<PERSON>,E.65,9.70,2,7,C,E.84,A.90,2,6,8,C,E.A4,<PERSON><PERSON>B1,6,8,<PERSON><PERSON>C3,<PERSON><PERSON>D1,5,7,9,<PERSON><PERSON>E3,B<PERSON>F0,5,7,9,E.G2,C;117.20,4,A,E.45,9.65,9.A4,A<PERSON>B6,8.C3,B.D1,5,9,D.F0,5,7,9,E:84C1XIC1C487IX77818X4MI8MMI7QCM47M84818Q8XQCMIC1Q474711Q18M1CX7IMIMCQ7XI7C1X4CQ4QX4M", "hasFlower": 0, "hasSeason": 0}, {"id": "72", "q": "001,3,5,7,9,B.21,3,5,7,9,B.43,5,7,9.50,C.62,4,6,8,A.70,C.82,4,6,8,A.A0,2,4,6,8,A,C.C0,3,5,7,9,C.E0,3,6,9,C;101,3,9,B.16.21,B.34,6,8.50,4,8,C.70,C.A0,2,4,8,A,C.C0,4,6,8,C.E0,6,C;254,8.A4,8:KXFX4663599HY5FMZ9MUFF29Z03ZZA45Z0KMZ0YEF9H26ZU3MCBP3H0FFYMMHEHAFFH0C3MZF6MYB59P30", "hasFlower": 0, "hasSeason": 0}, {"id": "73", "q": "000,3,5,7,9,B,E.22,4,6,8,A,C.30,E.42,4,6,8,A,C.50,E.62,4,6,8,A,C.82,5,7,9,C.A1,3,5,7,9,B,D.C0,2,6,8,C,E.D4,A.E0,2,7,C,E.F4,A.G0,2,C,E;105,9.13,B.26,8.31,4,A,D.52,C.67.72,5,9,C.92,C.A5,9.B7.C2,C.E1,4,A,D.G0,E:RQQ7IVS1SCBI5TTPRR4HBVHSTSOHCVP0VI7OOHB4MRTHMQ5QH7VM0VOMMRRHS7QM4BMHSVCTB0MP0MBPCTI4BQV1MB", "hasFlower": 0, "hasSeason": 0}, {"id": "74", "q": "000,2,4,7,A,C,E.21,3,5,7,9,B,D.40,3,5,9,B,E.57.60,2,4,A,C,E.76,8.80,2,4,A,C,E.97.A0,2,4,A,C,E.B6,8.C3,<PERSON><PERSON>D0,5,9,E<PERSON>E3,7,B.F0,5,9,E<PERSON>G2,7,<PERSON><PERSON>H4,<PERSON><PERSON>I0,2,7,C,E;113,B.26,8.33,B.40,E.57.70,2,5,9,C,E.B3,B.E0,5,7,9,E.G7:UUQPGEH3LPH3OUHBS4BU44S5LI3EEBEQPG4IBN4BOHP4BEIQOB55QQUOOHLLH4NB3GQOUHIOUH5BNGNEUO5GH4BG5H", "hasFlower": 0, "hasSeason": 0}, {"id": "75", "q": "000,2,4,6,8,A,C,E.20,2,5,7,9,C,E.40,2,4,7,A,C,E.62,4,6,8,A,C.70,E.82,4,6,8,A,C.A0,2,5,7,9,C,E.C1,3,5,7,9,B,D.E0,3,5,7,9,B,E;101,7,D.22,C.30,E.42,C.54,7,A.71,D.A2,7,C.C2,6,8,C.E0,4,A,E:7SNY70BBOTYLDT0KDK7UWPEWEYDW0OO0T0T9A0W9UNE5EA7FONY5KSU55UKK9K0T335D7EFYE75TLY09NP", "hasFlower": 0, "hasSeason": 0}, {"id": "76", "q": "000,2,4,6,8,A,C,E.20,2,4,7,A,C,E.42,4,A,C.50,6,8,E<PERSON>63,B.70,7,E.83,5,9,B.A2,5,9,C.B0,7,E<PERSON>C2,<PERSON><PERSON>D0,4,6,8,A,<PERSON><PERSON>E2,<PERSON><PERSON>F4,A<PERSON>G0,2,6,8,C,E<PERSON>H4,A.I0,2,6,8,C,E;100,4,7,A,E.21,D.33,B.95,9.B0,E<PERSON>C2,C<PERSON>D5,9.G3,6,8,B.H1,D;200,E:39L6J00JTLG63JLG0T6306QLQ69TGJTLGQ033TG6QJLQ99JG69JJ6039Q0GJJ9TL3T3L0G3L9Q066GG9QTQTT903LQ", "hasFlower": 0, "hasSeason": 0}, {"id": "77", "q": "003,5,7,9.10,C.25,7.30,2,A,C<PERSON>44,8.50,C.62,4,8,A.76.82,4,8,A.A1,5,7,B.C0,3,9,C.D5,7.E0,2,A,<PERSON><PERSON>F5,7.G0,3,9,C;105,7.10,C.25,7.31,B.50,C.62,4,8,A.83,9.A5,7.C0,3,9,C.E1,5,7,B.G0,C;205,7.31,B.62,4,8,A.A5,7.C3,9.E1,6,B:BDWW8HDPGV1KXPKVPDDGDP3BHYFW2431X2GGG21XXXPXKWPHFWWPY1HDHB4PWYYD44WDFWBX82KDFBBDP3PGW3XH", "hasFlower": 0, "hasSeason": 0}, {"id": "78", "q": "000,3,6,8,B,E.21,3,5,9,B,D.41,4,A,D.57.62,4,A,C.70,6,8,E.84,A.92,7,C.A0,4,A,E.C0,2,4,A,C,E.D6,8.E0,2,4,A,C,E;100,3,6,8,B,E<PERSON>21,D.34,A.62,C.74,7,A.92,C.A0,E.C3,B.D1,D.E3,B;203,6,8,B.34,A.77.A0,E.C3,B;334,A:WIWE1A7RIKGPYWVH37YKENV1PW8ASPIR8YYK6H7FHE6F3APUPA7G8SPRYN8KKHEWY3SPSK44I1ER4U1E3YWPY4", "hasFlower": 0, "hasSeason": 0}, {"id": "79", "q": "001,4,6,8,B.21,3,5,7,9,B.40,2,5,7,A,C.60,3,5,7,9,C.80,2,A,C.A1,3,5,7,9,B.C0,2,4,8,A,C.E0,3,5,7,9,C.G1,4,8,B;101,4,8,B.16.21,3,9,B.36.40,C.55,7.60,C.81,B.A1,3,5,7,9,B.C1,4,8,B.E0,6,C.G4,8:915F1OSC55958P921FE529861W8611M82OO1MWO2MFFF26E58221FM6M298O6FSMFFFE5M1P1C9O2625ME", "hasFlower": 0, "hasSeason": 0}, {"id": "80", "q": "000,2,4,C,E,G.20,2,E,G.40,2,E,<PERSON><PERSON>54,C.60,2,E,G.74,6,8,A,C.80,2,E,G.94,6,8,A,C.A0,2,E,G.B4,C.C0,2,E,G.E0,2,E,G.G0,2,E,G;101,3,D,F.32,E.40,G.63,D.75,7,9,B.80,G.95,7,9,B.A2,E.B4,C.D0,2,E,G.G0,G;263,D:WYLKTL6LLWX61UX66W16BKXXTTXWWB16UTL6BLXYYK1UWYYXBTLUY6YKXLKWBTK1U6UWKLKWWXB1UUY1L1X6", "hasFlower": 0, "hasSeason": 0}, {"id": "81", "q": "001,4,6,8,A,D.24,6,8,A.30,2,C,E.44,6,8,A.50,2,<PERSON>,<PERSON><PERSON>65,7,9.71,D.84,6,8,A.91,D.A3,5,7,9,B.B0,E.C2,4,A,C.D6,8.E0,3,<PERSON>,<PERSON>;104,7,A.24,7,A.32,C.44,6,8,A.51,D.65,9.71,D.86,8.A3,5,9,B.C2,4,A,C.D7.E0,E;237.44,A.51,D.A3,B:7LAVXLXISX7VSVDDDA4XXGROILZZIAGZZXGGRAXA4XOLZASADRVA7LXOLDLLX7DVLRSGSZI7SAGOIDA7SDLVVVIS", "hasFlower": 0, "hasSeason": 0}, {"id": "82", "q": "000,5,7,9,E<PERSON>12,C<PERSON>20,5,7,9,E<PERSON>32,<PERSON><PERSON>44,6,8,A.50,2,<PERSON>,<PERSON><PERSON>64,7,A<PERSON>70,<PERSON><PERSON>84,<PERSON><PERSON>90,<PERSON><PERSON>A3,5,9,<PERSON>.B0,7,E<PERSON>C2,4,A,<PERSON><PERSON>D6,8.E0,2,C,<PERSON><PERSON>F5,7,9.G0,2,C,E;105,7,9.11,D.25,9.32,C.44,6,8,A.51,D.67.74,A<PERSON>80,E<PERSON>94,A<PERSON>B3,7,B.D2,6,8,C.E0,E.F5,9.G1,D:MAS5UN5ZCQN7CS7NUS4CLNLVN0OPMS7UO4JZNU7LUM9VLA0OVOVALCVV4LVNNLZLAMOAQM5SS50JSO9QMZ4UQAVS0P", "hasFlower": 0, "hasSeason": 0}, {"id": "83", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,2,7,C,E.54,A.60,2,7,C,E.74,A.80,2,6,8,C,E.94,A.A0,2,6,8,C,E.B4,A.C0,2,7,C,E.E0,2,4,7,A,C,E.G0,2,6,8,C,E;106,8.12,C.27.53,7,B.70,4,7,A,E.A0,4,7,A,E.C7.F7.G2,C;227.77.A7.F7:X86CIR4984OD7LR8C4QUGGW9DAILA6BOTISRQ4RXGLBLLIVGA0PXPGQ8VICOCRG7RSSWQ5OI404C4LGAXSC4DT5DGU", "hasFlower": 0, "hasSeason": 0}, {"id": "84", "q": "000,2,4,A,C,E.17.20,2,4,A,C,E.36,8.40,3,B,E.56,8.60,2,4,A,C,E.77.80,2,4,A,C,E.96,8.A0,2,C,E.B4,6,8,A.C0,2,C,E<PERSON>D4,7,A.E1,D;101,3,B,D.21,3,B,D.36,8.43,B.50,6,8,E.72,4,A,C.96,8.A0,2,C,E.C3,7,B.D1,D:3SA948S3A9SAF8HB8H4HAE39B393F97444B79B4SE7S4BA4A997AB987HABF33H83H74BB7F374AS388A9B8", "hasFlower": 0, "hasSeason": 0}, {"id": "85", "q": "000,2,4,8,A,C.20,3,5,7,9,C.40,3,5,7,9,C.60,2,4,6,8,A,C.80,2,4,6,8,A,C.A0,2,4,8,A,C.C0,2,5,7,A,C.E0,3,9,C;103,9.20,4,6,8,C.40,3,5,7,9,C.60,3,5,7,9,C.81,3,5,7,9,B.A1,4,8,B.C6.D0,C:9XY03AAO3WHB0A2F90U2OUOU0A4HWX9BOHFOYFHOHA94AW3O0OO043W304UAH9AXDOF30ABXDA9B0H0H", "hasFlower": 0, "hasSeason": 0}, {"id": "86", "q": "000,4,6,8,A,C,G.12,E.20,4,6,8,A,C,G.40,2,5,B,<PERSON>,G<PERSON>57,9.60,4,C,G.72,E.80,4,8,C,G.A0,2,E,G.B4,6,8,A,C.C0,2,E,<PERSON>.D5,B.E1,3,7,9,D,F.F5,B.G0,2,7,9,E,G;104,C.11,F.25,B.41,F.64,C.72,E<PERSON>A2,E.B4,C.D1,F.E6,A.F1,F;241,F.D1,F:UAAUG2BGGBG87AUUA78888APCAPUC7A8U7CCGSSCBGSGG77S8BBCMSPBPTATCUP7BS8MCGCPT28BABTAUGSPCSBP78", "hasFlower": 0, "hasSeason": 0}, {"id": "87", "q": "000,2,4,6,8,A,C,E.23,6,8,B.30,E.42,4,6,8,A,C.50,E<PERSON>65,7,9.70,2,C,E.84,7,A.90,E.A2,4,6,8,A,C.C0,2,4,6,8,A,C,E.E0,2,6,8,C,E;101,3,5,9,B,D.17.23,B.30,7,E.55,7,9.60,E.72,C.90,4,7,A,<PERSON><PERSON><PERSON>,C.B6,8.C0,2,C,E.E2,6,8,C:U73Q2M2U9C3CCMQ97CQ2UMCTUTM9729T2273MUCQ9T32QQ97QI33MM2CQUT72II93TI2TUUCCM9QMCM3377UUTTTQ3", "hasFlower": 0, "hasSeason": 0}, {"id": "88", "q": "001,3,6,8,B,D.20,2,5,7,9,C,E.40,2,4,A,C,E.57.60,2,5,9,C,E.80,2,4,A,C,E.A0,2,5,9,C,E.B7.C1,4,A,D.E0,3,5,9,B,E.F7.G2,5,9,C;112,C.20,5,9,E.40,2,4,A,C,E.60,5,9,E.72,C.84,A.92,<PERSON>.A0,E.C1,4,A,D.E0,5,9,E.G2,5,9,C:CDGMLP3LZRVFF7MRMGA1UGEZLUBCZEAMGRRMGYFD7PE4E0ZU1DDCZULNPGVKUZLPAFMZDKUB030MDZ11ML4KKC0NAY", "hasFlower": 0, "hasSeason": 0}, {"id": "89", "q": "000,2,5,9,C,E.17.20,2,4,A,C,E.36,8.41,3,B,D.55,9.60,3,7,B,E.75,9.80,2,7,C,E<PERSON>94,A.A1,D.B3,6,8,B.C0,E.D2,4,6,8,A,C.E0,E;100,E.12,C.20,4,7,A,E.32,C.54,A.67.70,E.87.91,4,A,D.B6,8.C3,B.D0,5,7,9,E;232,C.B7.D5,9:3FDDIXIXY3FW7D37FMXMFWYWDFIXDY72F7MX7WIYD2XWDX3MYYMYMY7X7D3MWIFWDY2I72IFW3FX3DF33MIXY3", "hasFlower": 0, "hasSeason": 0}, {"id": "90", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,2,4,6,8,A,C.61,3,5,7,9,B.80,2,5,7,A,C.A0,2,4,6,8,A,C.C1,3,9,B.D5,7.E0,3,9,C.F5,7.G0,2,A,C;100,C.31,4,6,8,B.52,4,8,A.66.71,B.86.91,B.A4,6,8.B1,B.D3,5,7,9.G0,C:MA4LTFT7GFZ7G7ACEH1X3AAHO8G117Z2H3MK1923XIMN8GA8L984GKWLWLGEWWAMGCIM3Z8MFZO9H89GMMNF", "hasFlower": 0, "hasSeason": 0}, {"id": "91", "q": "000,4,7,9,C,G<PERSON>12,E<PERSON>24,7,9,C.30,2,E,G.44,6,8,A,C<PERSON>50,2,E,G.65,8,B.70,2,E,G.84,6,8,A,C.90,2,E,G.A4,6,8,A,C.B0,2,E,G.C4,6,8,A,C.D0,2,E,G.E4,6,8,A,C.F0,G.G2,4,7,9,C,E.I1,3,5,7,9,B,D,F;143,D.55,B.78.90,G.C8.D4,C:M8U8419MMBPU98BYUVO30VO37B7VP8O900UODBOP0773B8U1PPOP400OP00P9BVOMM388371MPBOB807OD18UP83Y0", "hasFlower": 0, "hasSeason": 0}, {"id": "92", "q": "000,4,6,8,A,E.12,C.24,6,8,A.30,E.42,4,6,8,A,C.60,2,6,8,C,E.74,A.80,2,6,8,C,E.A1,D.B3,6,8,B.C0,E.D2,4,6,8,A,C.E0,E;100,4,A,E.12,7,C.25,9.30,E.42,4,6,8,A,C.62,6,8,C.81,7,D.B6,8.C0,3,B,E.D5,9.E0,E:9734HAF709HYHA45YFFVAHV33A575693V0FY9A4A450749460YV4VYAV656393F90Y554YVH7HA74F90Y4V5", "hasFlower": 0, "hasSeason": 0}, {"id": "93", "q": "001,3,5,7,9,B,D.21,3,5,7,9,B,D.40,2,4,6,8,A,C,E.61,4,6,8,A,D.80,2,4,6,8,A,C,E.A0,3,5,9,B,E.B7.C0,2,4,A,C,E.D6,8.E0,3,B,E.F5,7,9.G0,2,C,E;113,B.21,6,8,D.44,7,A.64,A.86,8.A3,B.B0,E<PERSON>C2,C.D7.E0,E.F6,8:0X4P11Y0601P6668464PY0048X8XPYY6XY1585XY4815558PPY6046Y5XP0P8XY0865XX860141X541Y1810PP", "hasFlower": 0, "hasSeason": 0}, {"id": "94", "q": "000,2,5,9,C,E.20,3,B,E<PERSON>36,8.41,4,A,D.56,8.60,3,B,E.75,7,9.80,2,C,E.94,6,8,A.A1,D.B4,6,8,A.C1,D.D3,B.E1,6,8,D;100,2,5,9,C,E.20,3,B,E.36,8.41,D.57.60,3,B,E.75,7,9.82,C.95,9.A1,7,D.B5,9.D1,3,B,D.E6,8:0MAA757P9665MMD004A46AMBP5550DA4DMG0M6ABPACGO05D70DA4G77M6B65LOO79X7C6AGM75XO0PABLD6", "hasFlower": 0, "hasSeason": 0}, {"id": "95", "q": "001,4,7,A,D.20,2,4,6,8,A,C,E.40,2,4,A,C,E.56,8.60,2,4,A,C,E.77.84,A.90,2,6,8,C,E.A4,A.B0,2,C,E.C6,8.D0,2,4,A,C,E.E7;111,7,D.24,A.31,D.56,8.60,3,B,E.77.84,A.90,6,8,E.B0,E.D0,4,7,A,E;256,8.96,8;356,8:H9E0TZYZ2MTOPTY2JETYP3CN69YI92GIY5MI34HO4GJJZNY0NOIY2CPIIINDJNO4OE4I9M63NQ5YOPQEM3DZ", "hasFlower": 0, "hasSeason": 0}, {"id": "96", "q": "000,4,7,A,E.12,C.20,4,A,E.32,C.40,4,6,8,A,E.60,3,B,<PERSON><PERSON>75,9.81,7,D.94,<PERSON><PERSON>A0,<PERSON><PERSON>B3,B.C1,5,9,D.D7.E0,2,4,A,C,E.G0,2,4,A,C,E;100,4,7,A,E.23,B.30,E.46,8.75,9.94,A.A0,E.E0,2,C,E.G1,3,B,<PERSON>;207.30,E.47.E0,E.G3,B:TYPMBETWTWQHTEZQQZHZ0ZQVB00EV0YZVPHP4PMYTETZA11ZQVW040WYE1VPHAPEYP9H1H910QPHYZH0414V", "hasFlower": 0, "hasSeason": 0}, {"id": "97", "q": "001,3,5,7,9,B.23,5,7,9.30,C.42,4,6,8,A.60,5,7,C<PERSON>72,<PERSON><PERSON>80,4,6,8,C.92,A<PERSON>A5,7.B0,<PERSON><PERSON>C2,5,7,A<PERSON>D0,C<PERSON>E3,5,7,9.G0,2,4,6,8,A,C;101,4,8,B.24,6,8.42,5,7,A.65,7.71,B.83,9.A6.C2,5,7,A<PERSON>E3,6,9.G2,4,8,A;226.46.65,7.A6.D6:T8TBB2RA8PR8R8BCIP2IJCPCRTRJPJCJBA2BP2BPTAAAR28JICPTJAAPC8BTP8CAICT2J2JTJ8B2J22PARTTRA", "hasFlower": 0, "hasSeason": 0}, {"id": "98", "q": "002,4,6,8,A,C.10,E.22,4,7,A,C.30,E.44,6,8,A.50,2,C,E.65,9.77.85,9.90,3,B,E.A5,7,9.B1,<PERSON><PERSON>C3,5,7,9,B.D0,E.E2,5,9,C;102,6,8,C.14,A.22,C.40,4,6,8,A,E.75,9.90,3,B,E.A7.B1,D.C3,6,8,B.E2,5,9,C;202,7,C.47.A7:X1RVTTRRTXQV6T66T46VXR41R6X4VRXZRXQ4RQ14TXXQ4VVT6XQ6QV141ZVQZZ44RZXQZ4V1TQ1TQ1VT66R6", "hasFlower": 0, "hasSeason": 0}, {"id": "99", "q": "000,2,6,8,C,E.14,A.21,6,8,D<PERSON>33,B<PERSON>40,5,7,9,E<PERSON>53,<PERSON><PERSON>61,5,9,D.73,7,B.80,<PERSON><PERSON>92,7,C.A0,4,A,E<PERSON>B2,6,8,C<PERSON>C4,<PERSON><PERSON>D0,6,8,E<PERSON>E2,<PERSON><PERSON>F4,6,8,A.G0,2,C,E;101,7,D.14,A.21,7,D.44,7,A.62,4,A,C.77.80,E.97.A0,3,B,E<PERSON>B6,8.C4,A.D7.E2,C.F4,7,A.G2,C:SN9UENNEBXQQN0303FB03SX0ECQ370IE0RU7Q3CUE7RI0UST7IIQ9NQN0CNE7SICQCTQQ3QIFUEIN90I39CNE0N7IIU3", "hasFlower": 0, "hasSeason": 0}, {"id": "100", "q": "000,2,6,8,C,E.14,A.26,8.30,2,4,A,C,E.51,3,5,7,9,B,D.70,3,5,7,9,B,E.91,3,5,9,B,D.A7.B1,3,B,D.C5,9.D7.E0,2,4,A,C,E;107.14,A.27.30,2,C,E.52,6,8,C.64,A.76,8.83,B.95,9.A2,7,C.D7.E0,2,4,A,C,E;217.77.E0,E:ZH8MFPZ2ZM7M38F6HFJGDO8VJB216Z22B4MHVZ63M6RPLR4GHO7VOD8VJM7QF7WVOQRRQW1QWBBLVR3WR3JZ", "hasFlower": 0, "hasSeason": 0}, {"id": "101", "q": "000,2,5,7,9,C,E.21,3,5,7,9,B,D.42,4,6,8,A,C.50,E.63,7,B.70,5,9,E.87.91,3,B,D.A5,9.B2,C.C4,6,8,A.D1,D<PERSON>E3,5,9,B;100,6,8,E.21,4,6,8,A,D.42,6,8,C.63,B.77.91,3,B,D.B2,C.C5,9.D1,D.E4,A;200,E.27.46,8.91,D:R7B97QBREVC979GQI59U45RBTTRG9RIU4ZTQG59ZF9T9T5UFFURF4CFVU4R4CEVVIEZT4FZRBIEC4Q7GU949", "hasFlower": 0, "hasSeason": 0}, {"id": "102", "q": "001,6,8,D.14,A.20,2,6,8,C,E.42,4,6,8,A,C<PERSON>50,E<PERSON>63,5,9,B.70,7,E<PERSON>82,4,A,C.90,7,E<PERSON>A2,C.B5,9.C0,2,7,C,<PERSON>.D4,<PERSON><PERSON>E2,6,8,C.F0,4,A,E.G2,7,C;107.11,4,A,D.26,8.43,5,9,B.63,B.77.81,4,A,<PERSON>.A2,C.C1,D.D3,B.E5,7,9.F1,3,B,D.G7:ARV0B0POR0IVRC2CR0O2A0OAICV2O222CRVIBVBPPI0AIOARAPOP2PV2PAPIAA0P2R20C0VRVRPI0BIVCIARVOOI", "hasFlower": 0, "hasSeason": 0}, {"id": "103", "q": "001,3,5,7,9,B,D.21,3,5,7,9,B,D.41,3,7,B,D<PERSON>55,9.60,2,C,E<PERSON>74,6,8,A<PERSON>80,E<PERSON>94,6,8,A.A1,D.B3,5,9,B.C0,7,E<PERSON>D3,B<PERSON>E1,7,D.F3,5,9,B.G1,D;104,7,A.11,D.25,7,9.31,D.43,B.51,D.65,9.70,E.86,8.A5,9.C7.D3,B.E1,D.F5,9.G1,D;221,D.E1,D:JZ40ZYF44OQZQEYPJ0JE4JYYOOQP6EYP0QJJ6OY4O4JOOYQFYP64Z0PZZ0Z0QF0004OZPOOJJYQEPZQ0P4Y4PFJZ6P", "hasFlower": 0, "hasSeason": 0}, {"id": "104", "q": "002,6,8,C.10,4,A,E.22,6,8,C<PERSON>30,E.45,7,9.50,3,B,E<PERSON>65,7,9.70,2,C,E.84,6,8,A.A0,3,5,7,9,B,E.C0,3,5,7,9,B,E.E0,2,4,7,A,C,E;106,8.12,4,A,C.20,6,8,E.45,9.50,3,B,E.72,C.A0,3,5,9,B,E<PERSON>C3,7,B.D0,E.E2,4,A,C:QNL6L9SPQ2N2O22PPQSQSOOQN99PP22PLLOS6LNQ66PPS2Q69L2NN9OSP9Q6NS29SONSS99NLOLP6N9OOQ6Q2O", "hasFlower": 0, "hasSeason": 0}, {"id": "105", "q": "000,2,6,A,C.14,8.22,6,A.30,4,8,C.46.50,2,4,8,A,C.72,4,6,8,A.80,C.92,4,8,A.A6.B0,3,9,C.C6.D3,9.E0,5,7,<PERSON><PERSON>F2,A<PERSON>G0,4,6,8,C;102,A.14,6,8.22,A.30,4,6,8,C.51,3,9,B.72,4,8,A.92,A<PERSON>B0,3,6,9,C.D3,6,9.E0,C.F2,A.G0,4,6,8,C:W1IB6JVDYVE66LOU1JYFE9WZ3896BESVW6PKDV1ZU91WELLIO9OIH9YD6BWLSGNOGKHYP9VWZOIZPDII3O81N1PBFV", "hasFlower": 0, "hasSeason": 0}, {"id": "106", "q": "000,3,6,8,B,E.20,2,5,9,C,E.37.40,2,4,A,C,E.56,8.60,<PERSON><PERSON>72,4,7,A,C.80,E.92,4,6,8,A,C.A0,E.B5,9.C1,3,7,B,<PERSON>.D5,9.E0,7,E.F2,5,9,C.G0,7,E;121,5,9,D.40,3,7,B,E.67.72,C.95,9.C4,7,A.E5,9.F1,D;240,7,E.95,9.C7.E5,9:CVPIBGGRWRO1FBR2HB6H626LHVXLI28XE261P2WAX7WEEER1BSX2B7GECEKOIEAW1VSG1LL1CEVCHCIWKBF8WC", "hasFlower": 0, "hasSeason": 0}, {"id": "107", "q": "000,3,5,7,9,B,E.20,6,8,E<PERSON>32,C<PERSON>45,9.50,7,E.62,4,A,C.77.81,3,5,9,B,D.A2,4,7,A,C.B0,E.C5,7,9.D1,3,B,<PERSON>.E5,9.F0,7,E.G2,4,A,C;100,4,7,A,E.32,C.57.62,4,A,C.83,5,9,B.A4,A.B0,7,E.D2,C.G2,C;200,4,A,E.63,B.83,B.A4,A.B7;363,B:V8CXYV0N26CX8CVYI32IM8822XI40JJY7BI36YACKM68I0YA2IKSVI64MY40N7BKIXK3K3S7M664IJ3C2C37KCIJ8C", "hasFlower": 0, "hasSeason": 0}, {"id": "108", "q": "000,2,4,6,8,A,C,E,G.20,2,4,6,A,C,E,G.38.40,2,5,B,<PERSON>,G.57,9.60,2,4,C,E,G.76,8,A.80,2,4,C,E,G.97,9.A0,2,4,C,E,G.B7,9.C1,3,5,B,D,F.D7,9.E0,2,4,C,E,G.F6,A.G0,3,D,G;104,8,C.21,F.41,5,B,F.57,9.83,D.C1,7,9,F.E1,F:0RC66EHHCR7SCN4Z6EEP4ZHHA6NH4EHN6H4GPENCSCN776CN44ZEH0S6E6AESNN6S7APAASASGCEH4H44QZ64PQSEC", "hasFlower": 0, "hasSeason": 0}, {"id": "109", "q": "000,2,4,6,8,A,C,E.21,3,5,9,B,D.37.40,2,4,A,<PERSON>,E<PERSON>56,8.61,3,B,D.75,7,9.80,2,C,E.94,7,A.A0,2,C,E.B4,6,8,A.C1,D.D3,5,7,9,B.E0,E.F2,4,6,8,A,C.G0,E.H2,4,6,8,A,C.I0,E;101,D.15,9.22,C.61,D.77.91,D.C7.D5,9.F5,9.G2,C;2E5,9:0EZJBOJ0O3B01ZPOPJXX3EPBZBZZ1JZE1EOO1XJBP1XZ10EXPWJ0B0Z0PBP3WEEZO01X1OBJO31JZBJO3EE1BJ3OPE", "hasFlower": 0, "hasSeason": 0}, {"id": "110", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,3,5,7,9,B,E.60,3,5,7,9,B,E.80,2,4,6,8,A,C,E.A0,2,4,7,A,C,E.C2,5,7,9,C.D0,E.E3,5,9,B;105,9.10,E.22,7,C.40,3,B,E.57.60,3,B,E.81,3,B,D.97.A4,A.C7.E5,9;227.50,E.97.E5,9:0ATNEAJCQQ27UL96079NK07JSADK37JCA6R2EPNQL4K34K60QTLLL9KSPRAK3A72U92Q3720LJSS6E4Q4E2P0DPN", "hasFlower": 0, "hasSeason": 0}, {"id": "111", "q": "000,2,5,7,A,C.22,5,7,A.30,C.42,4,6,8,A.60,2,4,6,8,A,C.82,4,6,8,A.A0,2,4,6,8,A,C.C1,3,9,B.D5,7.E0,3,9,C<PERSON>F5,7.G0,2,A,C;100,5,7,C.25,7.32,A.44,8.52,6,A.76.82,A.95,7.B1,3,9,B.D3,5,7,9.F5,7.G1,B:SOXK9IQPIC3CPKSII9QIQQ35OENEIE3XQECSXPN3INQXPSSX5Q5IENKQSOS9X5P9QCINOO5SIP9K5QEON9", "hasFlower": 0, "hasSeason": 0}, {"id": "112", "q": "000,5,7,9,E.12,C.20,5,9,E.32,7,C<PERSON>55,7,9.60,3,B,E.77.80,3,5,9,B,E.97.A0,3,B,E.B5,7,9.D3,6,8,B.E0,E.F2,5,9,C.G0,7,E;100,6,8,E.12,C.20,E.55,9.63,B.77.80,E.A3,7,B.D6,8.E0,E.G0,E;207.10,2,C,E.55,9.D6,8.E0,E.G0,E;311,D:PEP9K25ZPX29P5727TTRFZT7ECRPCKRP5EA0V20KICE55ZCRIR77TPABIBAKPWTFXEPI9WA7F7FPTRZ9XZ5EVX7Z", "hasFlower": 0, "hasSeason": 0}, {"id": "113", "q": "000,2,6,8,C,E.20,3,5,7,9,B,E.40,3,5,7,9,B,E.60,2,4,6,8,A,C,E.81,3,5,7,9,B,D.A0,2,4,6,8,A,C,E.C1,3,5,7,9,B,D.E0,3,5,7,9,B,E.G1,3,5,7,9,B,D;100,2,C,E.20,5,9,E.50,6,8,E.62,C.82,C.A0,E<PERSON>B2,C.C6,8.E5,9.G5,9:GGGGBJ0HHJ22IHJHUGJB2MH20H2HKKI0U0G2NIJNNUM2KK0HJNK2KBGH00NKHUJ2K0IJKMKJNGNM0GGU0NN2JBIINU", "hasFlower": 0, "hasSeason": 0}, {"id": "114", "q": "000,2,4,A,C,E.17.21,3,5,9,B,D.37.40,2,C,E.60,2,C,<PERSON><PERSON>74,<PERSON><PERSON>81,D.93,5,7,9,<PERSON>.A1,D.B3,5,9,<PERSON>.C0,<PERSON><PERSON>D2,<PERSON><PERSON>E0,<PERSON><PERSON>F2,<PERSON><PERSON>G4,7,<PERSON><PERSON>H1,D<PERSON>I3,5,7,9,B;100,2,4,A,C,E.21,5,9,D.40,E<PERSON>61,D.74,A.92,4,7,A,<PERSON><PERSON>C0,<PERSON><PERSON>D2,<PERSON><PERSON>E0,E<PERSON>H1,D.I4,A:53OX5XO6X3O1D1WX771WXW763735CW6D4334367656X344O475CCO4751DD747WWDDW5W6C63DWW64D6D753OD", "hasFlower": 0, "hasSeason": 0}, {"id": "115", "q": "001,4,6,8,B.30,2,6,A,C.44,8.50,C.62,4,8,A.70,C.90,2,4,8,A,C.B0,2,4,8,A,C.C6.E0,3,6,9,C;101,B.40,4,8,C.60,2,4,8,A,C.90,2,4,8,A,C.B0,4,8,C.D6.E0,C;240,C.60,2,A,C.90,2,A,C.B0,C.D6.E0,C;340,C.60,2,A,C.90,2,A,C.B0,C:RTDGODGT9DTDR9GZ33OZ9TZGZ9DMRR9DM9D339ZM3GODTZZTOTO93O9R9RGGMRO3OZTDRGTGRMZ3ROMZ3GMMTD3MOM", "hasFlower": 0, "hasSeason": 0}, {"id": "116", "q": "000,2,4,6,8,A,C.20,2,4,8,A,C.40,2,5,7,A,C.60,3,5,7,9,C.80,2,4,6,8,A,C.A0,2,5,7,A,C.C0,2,4,8,A,C.E0,2,5,7,A,C;100,5,7,C.12,A.24,8.30,2,A,C.45,7.60,3,5,7,9,C.80,3,9,C.95,7.A1,B.C0,2,A,C.E1,5,7,B:TLCU8YT5BD2OU09YSYS82T2SU8AVCCQLB85CQSYHDK4CY9B0V88USK5T5QB4U8BY0DABS0UNNSUK2CHKODS8UQ", "hasFlower": 0, "hasSeason": 0}, {"id": "117", "q": "000,2,4,6,8,A,C,E.20,2,5,7,9,C,E.41,3,7,B,D.61,3,6,8,B,D.80,3,6,8,B,E.A0,4,7,A,E.C0,2,4,A,C,E.D7.E1,3,5,9,B,D.F7.G1,3,5,9,B,D;100,3,B,E.16,8.20,E.32,7,C.52,C.67.80,6,8,E.A4,7,A.C2,C.D4,7,A.E1,D.F3,5,7,9,B:HGXSHTHDV6R11TZ3MZUDG36321UZ6U3G2TXSH111XX33VH2DS38V6VZZ717DV8D3VUXG3GXR3DSVTS1H2VZS1UU1MG", "hasFlower": 0, "hasSeason": 0}, {"id": "118", "q": "002,4,6,8,A,C.20,2,4,6,8,A,C,E.40,3,5,7,9,B,E.60,4,6,8,A,E.72,C.80,4,7,A,E.92,C.A4,6,8,A.B0,E.C2,4,6,8,A,C.D0,E.E2,4,7,A,C.F0,E.G2,5,7,9,C;102,C.14,6,8,A.37.45,9.50,E.64,6,8,A.70,E.82,C.B4,6,8,A.C0,E.E7.G6,8:04BNWNW55D0SEHSJ9N2S4W5BORHSR0OWV0BWJDV9SJDWSD4V79VFBVVWFVJ4626TEJH93N0JW5H9T72CDD92WCV30W", "hasFlower": 0, "hasSeason": 0}, {"id": "119", "q": "001,3,6,8,B,D.20,2,4,6,8,A,C,E.40,2,5,7,9,C,E.60,3,5,9,B,E.80,2,4,7,A,C,E.A1,3,B,D.B5,9.C1,3,B,D.D5,7,9.E0,3,B,E.F5,7,9.G1,D;102,6,8,C.20,4,6,8,A,E.40,E.55,9.60,E.74,A.80,2,C,E<PERSON>B4,A<PERSON>C1,D.D4,A.E0,6,8,E.G1,D:R2ZN50AXR0CICHJ2JFX8DZG2ZNDRXI0AF22HRJIH50HKJG605A5ZAA15AN8HA2K15RH26AHRR0ACI2NJAC5R5NXNJH", "hasFlower": 0, "hasSeason": 0}, {"id": "120", "q": "000,2,4,6,8,A,C,E.20,3,5,7,9,B,E.42,6,8,C.54,A.60,2,6,8,C,E.80,3,5,9,B,E.A2,4,6,8,A,C.C0,2,4,A,C,E.D6,8.E0,2,C,E;101,3,6,8,B,D.20,3,5,7,9,B,E.42,C.56,8.60,E.80,5,9,E.93,B.A6,8.B2,C.C0,E.D2,6,8,C:SXRIO3NSILC9ACXN3RB7XSX7PCO7IMZLTOONMZA9BBOCLMA11AXBLC1INSTOTXOIBLIPZBNA7MZACTONLC1CCC", "hasFlower": 0, "hasSeason": 0}, {"id": "121", "q": "000,2,4,6,8,A,C,E,G.20,3,5,7,9,B,D,G.41,4,6,8,A,C,F.60,2,4,6,8,A,C,E,G.80,4,6,A,C,G.A0,3,5,7,9,B,D,G.C0,3,5,8,B,D,G.E0,2,5,7,9,B,E,G.G0,2,4,6,8,A,C,E,G;101,6,A,F.14,C.44,7,9,C.61,F.85,B.C5,8,B.G4,C:ZGOFCTJTETEC57J57SFG55JJESCTZFOJZZZCZCOFS57FO7TOJFFZCJFGCSO57FG5E57GTTJFO5CEGOOCJ57JCZOE7T", "hasFlower": 0, "hasSeason": 0}, {"id": "122", "q": "000,4,6,8,C.12,A.24,8.31,B.43,9.55,7.60,C<PERSON>72,6,A.91,3,6,9,B.B0,5,7,C<PERSON>C2,<PERSON><PERSON>D0,4,8,<PERSON><PERSON>E2,<PERSON><PERSON>F4,8.G1,6,B;104,6,8.12,A.24,8.31,B.56.60,C.76.82,A.96.B0,5,7,C<PERSON>C2,<PERSON><PERSON>E2,A<PERSON>F4,8.G6;204,6,8.24,8.76.96.B5,7.C2,A.E2,A.F4,8;3C2,A:QJME5EQX6RQCTODOR3ARAJX3LLLTRNEGCMD6TJQ7Q7E133L33O65J5QRZADXHDGOAX5OTOZT631DD31HNT1R33", "hasFlower": 0, "hasSeason": 0}, {"id": "123", "q": "000,4,7,9,C,G.20,2,6,8,A,E,G.40,2,5,7,9,B,E,G.61,3,5,B,D,F.77,9.80,2,4,C,E,G.96,8,A.A0,2,4,C,E,G.B6,A.C1,4,8,C,F.D6,A.E0,2,8,E,G.F6,A.G0,3,8,D,G;100,8,G.20,7,9,G.45,B.51,F.77,9.83,D.A6,A.C1,8,F.E8.F0,G:0CJMCMC7RVM1KMC10PYLMPRJL1KYVCR4PKRMRKKYPZCKRP74LLPVJMCZZJZRKCM0KZY4YLZ4177RCPVLY1C107P7", "hasFlower": 0, "hasSeason": 0}, {"id": "124", "q": "000,2,5,7,A,C.20,2,6,A,C.40,2,4,6,8,A,C.60,2,4,6,8,A,C.80,2,5,7,A,C.A0,2,5,7,A,C.C2,4,6,8,A.D0,C.E3,5,7,9.F0,C.G3,5,7,9;102,A.16.20,2,A,C.42,5,7,A.50,C.63,9.71,5,7,B.90,2,A,C<PERSON>A5,7.C5,7.D3,9.E5,7.F0,C.G4,6,8:TJL7XLXIJ0T402TIS5U0AASJI24JA7MXCCI5MA24ACA2M4700X457T7IG0LLGXCQAL5LL4CA9X9GTL4T74UC4IMGQ4", "hasFlower": 0, "hasSeason": 0}, {"id": "125", "q": "000,3,5,7,9,B,E.20,2,5,7,9,C,E.41,4,A,D.57.60,2,C,E.74,A.80,6,8,E.92,4,A,C.A0,6,8,E<PERSON>B3,B.C1,5,9,D.D3,7,B.E1,5,9,D.G0,2,4,6,8,A,C,E.I0,2,4,6,8,A,C,E;100,7,E.27.31,D.57.90,6,8,E<PERSON>C5,9.F1,5,9,D.G3,B.H7.I0,2,C,E:MSUS97TH1AUMLRRX1701YARXTFPYS19541WYW1NMGHFH19WFCBWHS3FXGPSM4Y7FS9TFBTA5N7CWL5WLL31AHRHX50", "hasFlower": 0, "hasSeason": 0}, {"id": "126", "q": "002,4,6,8,A.10,C.23,5,7,9.31,B.44,6,8.51,B.64,8.70,2,A,C.84,8.90,2,A,C.A4,8.C1,4,6,8,B.E1,3,5,7,9,B.G0,2,4,6,8,A,C;102,5,7,A.23,9.31,5,7,B.51,B.70,2,A,C.90,2,A,C.C1,5,7,B.E1,4,6,8,B.G2,A;223,9.36.C6.E4,6,8.G2,A:K9XHXKXHPIPFYKPMI8HPHH88BBBGX22B8HWZFG68PBBWM7GAXBPXA6PYXXBPKWWP626MZ72MIB8PZKGBHM9ZHMHHIK", "hasFlower": 0, "hasSeason": 0}, {"id": "127", "q": "000,2,4,8,A,C.16.20,2,A,C.35,7.40,3,9,C.55,7.60,2,A,<PERSON><PERSON>74,8.80,C.92,5,7,A.A0,C<PERSON>B3,9.C0,5,7,C<PERSON>D2,A<PERSON>E0,5,7,C;103,9.10,C.22,A.30,5,7,C.55,7.60,2,A,C.74,8.80,C.92,6,A.B0,C.C5,7.D0,2,A,C;203,9.22,A.36.96.C6.D2,A;303,9.96:7705INPNNI700NXBNBN57588X5787XP8P1PNI0BXXNB118788BBN55I70XBXNP081IPPP05IB5BXI81810B0II0I", "hasFlower": 0, "hasSeason": 0}, {"id": "128", "q": "001,3,6,8,B,D.20,2,5,9,C,E.37.40,2,5,9,C,E.57.63,B.70,6,8,E.82,4,A,C.90,6,8,E.A2,C.B6,8.C0,2,C,E.D5,7,9.E0,3,B,E.F5,9.G0,3,7,B,E;107.11,D.30,5,7,9,E.57.70,6,8,E.83,B.90,6,8,E.B7.E0,4,A,E.G0,E;2G0,E:HD7AHAWATWMCTMATCHCYY7MW7YTCDT77CMWD7AYH7CYWCHAMCHYMTWWAHCTTMWMDYTDWTDMCAYHA7HAMHWYY", "hasFlower": 0, "hasSeason": 0}, {"id": "129", "q": "000,2,4,6,8,A,C,E,G.22,4,6,A,C,E.38.42,4,6,A,<PERSON>,E.50,G.65,8,B.70,2,E,G.84,6,8,A,C.92,E.A0,5,7,9,B,G.C0,2,4,7,9,C,E,G.E2,4,6,8,A,C,E.G1,3,6,8,A,D,F;102,E.22,E.42,E.55,B.75,B.83,<PERSON>.95,B.B7,9.C4,C.D2,E.F2,E:UXCVPXU6PCCEEWZNP4PZUP6X46XZUQP6UZWQNVVVN6PP6W44WVWQXNCCXZNXQCZ4ECU66XCPUNPU4CCEXX4VWU4U", "hasFlower": 0, "hasSeason": 0}, {"id": "130", "q": "001,5,7,9,B,F.13,D.20,5,7,9,B,G<PERSON>32,E.44,6,8,A,C.50,2,E,G.64,6,8,A,C.70,2,E,G.84,6,8,A,C.90,2,E,G.A4,7,9,C.B0,2,E,G.C4,6,8,A,C.D1,F.E3,5,7,9,B,D.F0,G.G3,5,7,9,B,D;114,C.32,8,E.56,A.84,7,9,C.C6,A.E8.G4,C:KB897C3PLHJ8HOL79LNQL8LJ96AJ8739OCC39CTPYC56C3K9TRMCJ8L938ML7AQC9JK9T8KRKBL8K9AQN3TQAJY5", "hasFlower": 0, "hasSeason": 0}, {"id": "131", "q": "000,2,4,6,8,A,C,E.20,3,5,7,9,B,E.40,6,8,E.52,C.60,4,6,8,A,E.72,C.80,5,7,9,E.A2,6,8,C.B0,E.C2,4,6,8,A,C.D0,E.E2,4,6,8,A,C;104,A.16,8.20,E.57.60,3,B,E.75,9.96,8.C0,2,C,E.D6,8.E2,C;220,E.97:BELLBAUQE6M7C7AZ80I8EBCEI6Z8BLM7AZ80MUU7BLZ67JBLEL8QMM878BLL0ML7U8L6AA08A8B7J77E", "hasFlower": 0, "hasSeason": 0}, {"id": "132", "q": "000,2,4,6,8,A,C,E.20,2,5,9,C,E.37.40,2,4,A,C,E.56,8.60,2,C,E.74,6,8,A.80,2,C,E.94,6,8,A.A0,E.B2,5,7,9,C.C0,E.D2,4,6,8,A,C.F0,2,5,9,C,E.G7;101,6,8,D.22,C.42,7,C.62,C.80,2,4,6,8,A,C,E.B2,C.C6,8.D2,C.F2,C:SAYAS7ENZAA5AMZIMAJNM55MJYN7MMZN5ASI5IZUEA8ZSJ7M7E78AENSSJNNE7MJ75ZJU55ANJSINENEE55M7SJM", "hasFlower": 0, "hasSeason": 0}, {"id": "133", "q": "000,2,4,6,8,A,C.20,3,9,C.35,7.40,2,A,<PERSON><PERSON>55,7.61,B.73,5,7,9.80,C.92,5,7,A.B1,5,7,B.C3,9.D0,5,7,C<PERSON>E2,<PERSON><PERSON>F0,4,8,C.G2,A;100,2,4,6,8,A,C.20,3,9,C.35,7.40,2,A,C.56.73,5,7,9.80,C.92,5,7,A.B6.C4,8.D0,C.E2,A.F0,C.G2,A:TB05TS6W2TCIGIT6JJN8P25P06IN1P5IZNT606OB5INZ0WPW28J56BNOCJN75WUSZZ5OGBS67526IT7UI78SIO81", "hasFlower": 0, "hasSeason": 0}, {"id": "134", "q": "001,3,5,7,9,B.23,5,7,9.30,C.42,5,7,A.50,C.62,4,6,8,A.70,C.82,5,7,A.90,C<PERSON>A2,4,6,8,A.B0,<PERSON><PERSON>C2,<PERSON><PERSON>D0,5,7,<PERSON><PERSON>E2,<PERSON><PERSON>F4,6,8.G0,2,A,C;101,3,9,B.15,7.35,7.40,2,A,C.60,C.75,7.A3,5,7,9.B0,C<PERSON>D0,2,6,A,C.F2,5,7,A:IKYLMD2V80109M081MAML00AU69K0UU18UAAIVMM2J9OI06LVU0U6906U291K2NVM8NMD9YHIKNO0NUJULHU", "hasFlower": 0, "hasSeason": 0}, {"id": "135", "q": "000,3,5,9,B,E.17.20,2,4,A,C,E.36,8.41,3,B,D.55,9.60,2,7,C,E.74,A.81,6,8,D.94,A.A0,E.B2,4,6,8,A,C.C0,E.D2,4,7,A,C.E0,E.F2,4,6,8,A,C;100,3,5,9,B,E.21,3,B,D.36,8.41,D.62,C.A0,E.B2,5,9,C.C0,E.D4,A.E2,7,C;221,3,B,D.B0,E.E2,C:SCCSNQ70IBX10QNKMBXR77I7M18RNLLR9I190CIIMMXMXIMNQRCIRN97LBINCKISC90XQXKRXQC7LK1RRBS8077CKKIQ0X", "hasFlower": 0, "hasSeason": 0}, {"id": "136", "q": "000,3,5,9,B,E.17.21,3,5,9,B,D.37.40,3,5,9,B,E.57.60,3,B,E<PERSON>76,8.80,3,B,E.96,8.A0,2,C,E.B5,7,9.C0,2,C,E.D4,7,A.E0,2,C,E;104,A.17.22,C.35,7,9.40,3,B,E.57.63,B.70,E.A0,2,7,C,E.C1,7,D.D3,B;240,E.53,7,B.A7.C1,D:MPESPTSYM1MMASDDYAEAAD4S4EMDPPTEDT4TSDASTT41EAMDT14D1MEY4DPAYSYYTEAESPMTPTEP441MSDSME1", "hasFlower": 0, "hasSeason": 0}, {"id": "137", "q": "000,2,4,7,A,C,E.20,2,4,7,A,C,E.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.81,4,7,A,D.A2,4,6,8,A,C.B0,E.C2,4,A,C.D0,6,8,E.E2,4,A,C.F0,7,E.G2,4,A,C;114,A.20,E.32,C.46,8.50,2,C,E.74,A.A4,A.B2,<PERSON><PERSON>C0,E.D6,8.E2,C.F0,4,A,E:TPJJ90900Y9W0W56GFL2M6Z6VM5WLP95Q5LYZZZT90Z05WTYM92GLMZYLEP5YZ0Z90G9VWZMPTMMZGTLMVQF6YWEVT", "hasFlower": 0, "hasSeason": 0}, {"id": "138", "q": "000,2,4,7,A,C,E.21,3,5,7,9,B,D.40,2,4,7,A,C,E.60,2,4,6,8,A,C,E.80,3,5,7,9,B,E.A3,5,7,9,B.B0,E.C2,4,7,A,C.E1,3,5,7,9,B,D;101,7,D.25,9.33,B.51,3,B,D.A3,5,9,B.C3,B.E5,9;233,B.A4,A.C3,B:4ZLARGB38IAYBAZ4RZGK3UX4CLK8L35CNLGBYZRNX7S4LGLAKY4UAL5457LYK4L55BB53SIELGGE4BAR", "hasFlower": 0, "hasSeason": 0}, {"id": "139", "q": "000,2,4,6,8,A,C,E.20,3,7,B,E.35,9.41,3,7,B,D.61,3,6,8,B,D.80,3,5,7,9,B,E.A0,2,7,C,E.B4,A<PERSON>C2,6,8,<PERSON><PERSON>D4,A<PERSON>E1,6,8,D.F3,B.G1,5,9,D;100,4,6,8,A,E.23,B.41,D.53,B.80,5,9,E.B4,<PERSON><PERSON>D4,<PERSON><PERSON>F1,3,B,D.G5,9;204,A.53,<PERSON>.C4,A.G5,9:OALOIJAAU4TCOCBOJICICJBCCJ<PERSON>LLAUILO8UHACCJOLHU4U4LTILTCBT4C4LB4BA4ATBTTTBLOAOBHOJ4HIUO844A", "hasFlower": 0, "hasSeason": 0}, {"id": "140", "q": "000,2,5,7,9,C,E.22,4,6,8,A,C.30,E.42,4,6,8,A,C.50,E.62,4,6,8,A,C.70,E.82,4,7,A,C.A0,4,6,8,A,E.B2,C.C0,5,7,9,E.D2,C.E0,4,6,8,A,E;102,6,8,C.22,4,6,8,A,C.30,E.50,E.66,8.72,C.A0,E.C0,6,8,E.D2,C;207:BZR1OGZOR9OCI3O7R2QB557121Q90Z9Y0IIOO727ZZ7GB51Q3R35I1I7C1IY07ZGRY9YB079R0Q9GO3O0112", "hasFlower": 0, "hasSeason": 0}, {"id": "141", "q": "000,2,5,7,9,C,E.20,2,4,6,8,A,C,E.40,3,5,7,9,B,E.60,3,5,7,9,B,E.80,3,6,8,B,E.A1,3,5,9,B,D.B7.C0,2,4,A,C,E.D6,8.E1,3,B,D.F5,7,9.G0,2,C,E;101,D.17.23,B.36,8.53,B.60,E.73,B.87.A3,B.C3,B.E6,8.G2,C;237.63,B.87.B3,B.E7:F12J2NF2RNG8FRT2UF2HJJHGJFURJHHHHFF3T8N2A3NGARFJ12ATQGAGRUJQRA2JQRJ1QQ82RA3RTJHRN1UN8Q32GH", "hasFlower": 0, "hasSeason": 0}, {"id": "142", "q": "000,2,4,7,A,C,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,4,A,E.72,6,8,C.80,4,A,E.92,6,8,C.A0,4,A,E.C1,3,6,8,B,D.E1,3,5,7,9,B,D.G0,2,4,6,8,A,C,E;113,B.21,6,8,D.34,A.74,A.93,B.C3,6,8,B.E4,A.F1,6,8,D.G3,B:ZM9QVJOM0DUMQ9OXU9UDVJM9JJJMDPZJD93VXQ0XQZU3Z19X3QXJZQJCM9XCPZDMU1XJU1O1VZ3OZD9MXMVJPPVM", "hasFlower": 0, "hasSeason": 0}, {"id": "143", "q": "000,2,5,9,C,E.17.21,3,B,D.35,7,9.40,2,C,E<PERSON>54,6,8,A<PERSON>61,<PERSON><PERSON>73,6,8,B.80,E<PERSON>92,4,6,8,A,C.A0,E.B2,4,7,A,C.D1,3,7,B,D;101,5,9,D.17.22,C.35,7,9.40,E.55,7,9.76,8.80,E.93,6,8,B.A0,E.B7.C2,C<PERSON>D7;227.35,9.40,E.66,8.96,8.C7;3C7:MOBOFM3KPTM6BB6FDR3O3FKEKTEMR636FMFEB6KB6BR63BEEEKOB3MTBPPP336MRFFEF6PD6PM3FDE3TMKEBFDEM", "hasFlower": 0, "hasSeason": 0}, {"id": "144", "q": "000,2,6,8,C,E.14,A.20,E.32,5,7,9,C.50,2,4,7,A,<PERSON>,E.71,3,5,7,9,B,D.91,4,A,D.A6,8.B0,2,C,E.C4,7,A.D1,<PERSON><PERSON>E3,5,7,9,B.F0,E.G4,A;100,2,C,E.20,E.32,5,9,C.47.50,2,C,E.64,A.71,D.84,A<PERSON>A1,D.C7.D4,A.E6,8.F0,E;202,C.32,C.61,D.C7:BQBK0JHXPHBUKCXWUKCPBWA8CUB0HP0X00KPHQCXUX8A0QHBBQ0MBJWP0UA0CHBXMMWJAKQPHA8HJAW0KP8MWUCBQP", "hasFlower": 0, "hasSeason": 0}, {"id": "145", "q": "000,2,5,7,9,C,E.20,2,4,A,C,E.36,8.41,3,B,D.55,7,9.60,2,C,E.75,7,9.80,2,C,E.95,7,9.A1,3,B,D.B5,7,9.C0,2,C,E.D5,7,9.E0,2,C,E.F4,A<PERSON>G0,2,6,8,C,E;111,D.23,B.31,6,8,D.43,B.51,D.70,E<PERSON>82,7,C.B2,<PERSON><PERSON>C0,E.D7.E0,2,C,E.G1,D:D98DHV6ID5GUUD5CPJ9PDP6YSC8DVCURK2JSGD8HYSD2V65V8IJ9SPCS6PF8RVJFDH699JKJUPDF8H96F5YPSP9YV9", "hasFlower": 0, "hasSeason": 0}, {"id": "146", "q": "003,9.10,5,7,C<PERSON>22,A.30,4,6,8,C<PERSON>42,<PERSON><PERSON>54,6,8.60,C<PERSON>72,5,7,A.80,C.93,5,7,9.A1,B.B3,5,7,9.C0,C.D2,4,6,8,A<PERSON>E0,C;110,5,7,C.34,6,8.42,A.54,8.60,6,C.72,A.80,5,7,C<PERSON>A2,4,8,A.B6.C4,8.D0,C;236.42,A.66.71,B.A2,A.B5,7:YBS4E6HE8K7ET6S41QEYS68EBYY8Q6YYKQ11664BTYT4BET8G1HH4THBHBS6YQ78QKQKS4Q6QQ8QYHSKTYGK", "hasFlower": 0, "hasSeason": 0}, {"id": "147", "q": "000,3,5,7,9,B,E.20,2,4,6,8,A,C,E.43,5,7,9,B.51,D<PERSON>63,5,9,B.70,7,E<PERSON>82,4,A,<PERSON><PERSON>90,E.A2,C.B0,4,7,A,E<PERSON>C2,C<PERSON>D0,4,A,E<PERSON>E2,7,C<PERSON>F5,9.G3,7,B.H0,5,9,E.I2,7,C;105,9.13,7,B.35,9.47.52,C.64,A.70,<PERSON><PERSON>B0,E<PERSON>D1,4,A,D.F5,7,9.H6,8;2G6,8:98UKGXE65GUGVTUK58NK6X96LX9V5X8VU0U59L5G2K6GTENETEU8GVEXKNX5X610XG9GNV99VTNE96K4T1NXT4XKK2", "hasFlower": 0, "hasSeason": 0}, {"id": "148", "q": "000,3,6,8,B,E.22,4,6,8,A,C.30,E.42,5,7,9,C.50,E.62,4,6,8,A,C.70,E.82,4,6,8,A,C.A0,2,4,6,8,A,C,E.C0,2,4,6,8,A,C,E.E1,3,6,8,B,D;122,6,8,C.30,E.42,5,7,9,C.50,E.64,7,A.84,A.97.A0,4,A,E.B2,6,8,C.C0,E.D2,6,8,C:RRO9Z1FP3NR717F3UR3259U13U51Z217OO2RFOONR5P33OU11FRF9URF9FF125Z5NZRNFZFP532UU5N2U173Z51NPR", "hasFlower": 0, "hasSeason": 0}, {"id": "149", "q": "000,2,4,6,8,A,C,E.20,2,4,A,C,E.36,8.40,3,B,E<PERSON>55,7,9.62,C.74,7,A.81,<PERSON><PERSON>94,6,8,A.A0,2,C,E.B5,7,9.C0,2,C,E.D4,A<PERSON>E2,6,8,C;102,7,C.10,4,A,E.22,C.36,8.40,E<PERSON>56,8.77.81,D.94,6,8,A<PERSON>A1,<PERSON><PERSON>B5,9.C2,C.D4,A.E2,7,C:0PPYYX0GTPAOIAXA0XX0ITTHYHTXIP0PYGGTGTAY0YTGHAP0ITOHGXXOH0IHYOXPYTHI00APPIOYHXTYXIOP", "hasFlower": 0, "hasSeason": 0}, {"id": "150", "q": "001,3,5,7,9,B,D.20,2,4,7,A,C,E.40,2,4,6,8,A,C,E.60,4,6,8,A,E.72,C.84,6,8,A.90,2,C,E.A6,8.B3,B.C0,5,7,9,E.E0,3,5,7,9,B,E.G0,3,6,8,B,E.I0,2,4,6,8,A,C,E;111,4,A,D.32,7,C.44,A.57.65,9.96,8.D5,9.E7.G7.I0,4,A,E:UM8AG3VIQ0ZM7MGUBAQGVJM7CIU7JWYYL4M5B541GQBFG3LF7AMWMQM8IDYZUFFW5A67MDVWD5GV16C7B03YMVVDI3", "hasFlower": 0, "hasSeason": 0}, {"id": "151", "q": "002,4,8,A.10,6,C.24,8.30,2,6,A,C<PERSON>44,8.50,2,6,A,<PERSON><PERSON>64,8.70,6,C.82,4,8,A.90,C.A3,5,7,9.B1,<PERSON><PERSON>C3,5,7,9.D0,<PERSON><PERSON>E2,4,6,8,A<PERSON>F0,C.G6;102,A.14,8.32,A.40,5,7,C<PERSON>53,9.75,7.83,9.A5,7.C3,5,7,9.D0,C<PERSON>E2,A;276.83,9.A6:LRMNLG22NRN7Z2MGNMMLNMNZGM5M25G5LZ2ZR05Z0MGGL0R7Z5ZG05ZGRRMZ2RNRRZMGLGN55L5LLNNL5R", "hasFlower": 0, "hasSeason": 0}, {"id": "152", "q": "000,2,4,A,C,E.16,8.20,4,A,E.37.42,4,A,C.50,6,8,E<PERSON>62,4,A,<PERSON><PERSON>70,7,E.83,5,9,B.90,7,E<PERSON>A4,A<PERSON>B1,6,8,D<PERSON>C4,A<PERSON>D1,7,<PERSON><PERSON>E5,9.F2,7,<PERSON><PERSON>G0,4,A,<PERSON><PERSON>H6,8.I0,4,A,E;114,A.37.42,C.54,A.62,C.70,4,7,A,<PERSON><PERSON>90,7,E.B5,7,9.C1,D.F2,7,C.H6,8.I4,A:EEMLHHM2ZFE6MK2MWI0BVF6EOOO0VZDGWKZ60LBLBZZOGKKVBVLHIVFZ60WKXZZ2LV0OLHZFE6MV6VKDVOMZWXV02E", "hasFlower": 0, "hasSeason": 0}, {"id": "153", "q": "002,6,8,C.14,A.20,2,C,E<PERSON>35,7,9.40,2,C,E<PERSON>56,8.60,2,4,A,C,<PERSON><PERSON>76,8.82,C.90,4,6,8,A,<PERSON><PERSON>A2,C<PERSON>B5,9.C1,7,<PERSON><PERSON>D3,<PERSON><PERSON>E0,6,8,E<PERSON>F2,4,A,<PERSON><PERSON>G0,6,8,E<PERSON>H2,C<PERSON>I4,6,8,A;106,8.30,6,8,E.50,E<PERSON>82,C<PERSON>90,7,<PERSON><PERSON><PERSON>,<PERSON><PERSON>E0,<PERSON><PERSON>F5,9.G0,E<PERSON>H6,8;206,8.37.H6,8:TIGMAI9UH92HHN5T9U9HNHTHI5U2GAGTG925NT9M3N2G5H9992TMUI2U52U3G33AVAVHVMVMUAH3AGN39HG2MGUGN5", "hasFlower": 0, "hasSeason": 0}, {"id": "154", "q": "000,2,4,6,8,A,C,E.20,3,5,7,9,B,E.40,4,6,8,A,E.60,2,4,6,8,A,C,E.80,3,6,8,B,E.A2,4,6,8,A,C.B0,E.C3,6,8,B.D0,E.E2,4,6,8,A,C.F0,E.G2,4,6,8,A,C;100,6,8,E.35,9.50,6,8,E.64,A<PERSON>70,E<PERSON>83,7,B.A4,A.C0,6,8,E.E5,9.G2,C:6YTEYRO0D9XHVMQ940YVZIXOETEWHEVZGYWMTHV9DHORZ9REULRXZQ9EHGN0ZXHYLHR9ZOUNQ04QOYDHTIH9R9HD6O", "hasFlower": 0, "hasSeason": 0}, {"id": "155", "q": "001,4,6,8,A,D.20,3,5,7,9,B,E.41,4,7,A,D.60,3,5,7,9,B,E.80,2,4,6,8,A,C,E.A0,2,5,7,9,C,E.C0,3,5,7,9,B,E.E0,2,4,7,A,C,E;101,4,A,D.17.23,5,9,B.37.54,A.75,7,9.A2,6,8,C.C3,6,8,B.E0,4,7,A,E:OWSBFSA3EKO3EBBACMK1IWM6VOKC06CKKBVYCCWYKLEA7B8VWIA80F1QIHBV82OWW18HL5E5Q2I7MW1MWC", "hasFlower": 0, "hasSeason": 0}, {"id": "156", "q": "002,5,7,A.10,C.22,4,8,A.30,C<PERSON>42,5,7,A.50,C<PERSON>62,5,7,A<PERSON>70,C<PERSON>82,6,A.A0,3,9,C<PERSON>B5,7.C0,2,A,<PERSON><PERSON>D5,7.E0,2,A,<PERSON><PERSON>F4,8.G0,2,A,C;106.10,2,A,C.24,8.30,2,A,C.45,7.51,B.66.72,A.B6.C1,B.D5,7.E0,2,A,C.F4,8.G0,2,A,C;231,B.E1,B:JJ44JTJRT414JJT444R3PP3PT1T1313APPPPA3AIRE3RIAP3RAA1IAETJ14A31A3EEJPTAJ1RJIER34TE4PTTR", "hasFlower": 0, "hasSeason": 0}, {"id": "157", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,2,5,7,A,C.60,3,5,7,9,C.80,2,4,6,8,A,C.A0,2,4,6,8,A,C.C0,3,5,7,9,C.E1,3,5,7,9,B.G2,5,7,A;103,6,9.10,C.24,8.42,A.60,5,7,C.83,6,9.B0,5,7,C<PERSON>C3,9.E3,9.F5,7:Z5SJNILSYTCJXPS12J3I3X2VYL7JNXVVY2Z0NH5ARNHPIC9B9HZTS79VSJNVHARN2JBYZ06T96VSIYX1TY", "hasFlower": 0, "hasSeason": 0}, {"id": "158", "q": "001,3,6,8,A,D,F.20,2,4,6,8,A,C,E,G.41,3,5,8,B,D,F.60,3,5,7,9,B,D,G.80,2,5,7,9,B,E,G.A0,3,5,8,B,D,G.C0,3,5,7,9,B,D,G.E0,3,5,8,B,D,G.G0,2,4,6,A,C,E,G;117,9.20,G.41,4,C,F.58.66,A.82,8,E.A3,D.C4,8,C.F0,G:1P4NB21J41JJ12N337374B21N437PN4JN274JJB7P2JP742NJPN41272P3JBP1BJNP4NB1PP3417B3NB32B231773B", "hasFlower": 0, "hasSeason": 0}, {"id": "159", "q": "000,3,7,B,E.15,9.20,2,7,C,E.40,2,4,7,A,C,E.62,4,7,A,C.70,E.83,5,7,9,B.90,E.A3,5,7,9,B.C0,3,5,9,B,E.D7.E1,D.F3,5,7,9,B.G0,E;100,3,B,E.16,8.20,E.32,C.53,7,B.73,7,B.93,6,8,B.B4,A.E1,D.F5,7,9.G0,E;220,E.32,C.57.73,B:VTTHHLKLLJWL0MTKTKWBMLBMLMHDKJMDVH0TVDBVL0JJVKMLM0HBTKHJ0TJKJMDK0VTT00KDJVKVVT0MLVJBJDBL0M", "hasFlower": 0, "hasSeason": 0}, {"id": "160", "q": "000,2,4,6,8,A,C,E.20,2,6,8,C,E.41,3,7,B,D.55,9.61,3,7,B,D.75,9.80,3,7,B,E.95,9.A0,2,7,C,E.B4,A.C0,2,7,C,E.D4,A.E1,6,8,D.F3,B.G0,5,7,9,E;101,3,6,8,B,D.20,E.41,D.53,5,9,B.86,8.A0,E.B3,B.D1,D;2A0,E:4RB2HAKIIBHXM1IOO5SVCXES5OO2CZ4JBEA4H9VMM4RVXEKIXAMJH3CZ1AHIAAB93CAAIV5H24RE5JJ24CRC", "hasFlower": 0, "hasSeason": 0}, {"id": "161", "q": "000,4,6,8,A,C,G.12,E.20,4,6,8,A,C,G.32,E.40,4,6,8,A,C,G.61,3,6,8,A,D,F.80,3,5,7,9,B,D,G.A0,3,5,8,B,D,G.C0,3,D,G.D5,7,9,B.E1,F.F3,5,8,B,D.G0,G;107,9.15,B.20,G.36,A.48.56,A.61,8,F.76,A.95,B.C3,D.D5,7,9,B.F5,B:F9B9ZKZAZGONXBOXXKBNE2IB9NOJOIGEFOZJ4FG4XKANNJAB9BBXXBBFZXASVONK49FZSASGZFZJXO924SABJNONVJ", "hasFlower": 0, "hasSeason": 0}, {"id": "162", "q": "000,4,6,8,A,E.12,C.20,6,8,E.32,4,A,C.40,7,E.52,4,A,C.60,6,8,E.73,B.80,7,E.92,4,A,C.A0,6,8,E.B2,C.C0,4,6,8,A,E.E0,2,5,7,9,C,E;100,5,7,9,E.26,8.31,4,A,D.47.51,4,A,D.66,8.90,3,7,B,E.B0,6,8,E.C4,A.D6,8:BV20OJ3VK0XX03XJ0XKOHHB03K0BH3KHHOVVOX2KKXOJJ32V0K2BJJ3XK0K2OHJOXHHV2OX3KJBOX0BJHJOH30", "hasFlower": 0, "hasSeason": 0}, {"id": "163", "q": "000,2,4,6,8,A,C.21,3,5,7,9,B.40,2,5,7,A,C.61,3,6,9,B.80,2,4,6,8,A,C.A1,4,8,B.B6.C1,3,9,B.D5,7.E0,2,A,C.F4,6,8.G0,2,A,C;103,6,9.11,B.23,9.46.52,A.66.71,3,9,B.85,7.A1,B.B6.C2,A.D6.E2,A.F0,4,8,C;285,7.E2,A:0KWCCGY5GK160YKTYKGTT1JTR1JRWCBJCT5K0WWKCYGG116CJC1Y0BKJWR0TJKR50YWJCJ1GGCCK1J5Y1KGY1J", "hasFlower": 0, "hasSeason": 0}, {"id": "164", "q": "000,2,4,7,A,C,E.20,2,4,6,8,A,C,E.40,3,5,9,B,E.57.62,C.70,5,7,9,E<PERSON>82,C.90,4,A,E.A6,8.B2,<PERSON><PERSON>C5,7,9.D0,3,B,<PERSON><PERSON>E5,9.F0,2,C,E.G5,7,9;101,4,7,A,D.20,2,5,9,C,E.57.71,D.90,E.B7.C5,9.F0,2,5,9,C,E.G7;290,E.B7:NWY733X27ES249HSX2NMWMU8AEB3UVAN2MXYA9HHYW96NVN6AUSH4UNS5WSYX8B23E6SE96UB5US2BXHMXSH", "hasFlower": 0, "hasSeason": 0}, {"id": "165", "q": "000,2,6,8,C,E.14,A.22,7,C.30,4,A,E.42,7,C<PERSON>50,E<PERSON>63,5,7,9,B.70,E.82,4,6,8,A,C.A0,3,6,8,B,E.C0,6,8,E.D2,C.E0,4,6,8,A,E.F2,C.G0,4,A,E;112,7,C.31,D.60,7,E<PERSON>75,9.82,C.A0,6,8,E<PERSON>C6,8.D1,<PERSON><PERSON>E5,9.F2,<PERSON>.G0,E;217.A6,8.E5,9:RVJ0WZHHWLHNRVS202RMYVGNR2ZWEJ6SMVRSZZN6A2AGGWLZEMGSENGAR32ZZEUSGGYAARUVVMSR3GMZRR2A22MH", "hasFlower": 0, "hasSeason": 0}, {"id": "166", "q": "000,2,4,6,8,A,C.20,2,5,7,A,C.40,2,4,6,8,A,C.60,3,5,7,9,C.80,2,4,6,8,A,C.A0,3,5,7,9,C.C0,2,4,8,A,C.D6.E1,3,9,B.F5,7.G1,3,9,B;106.26.31,B.43,6,9.65,7.70,C.A0,4,8,C.D1,3,6,9,B.F3,6,9;206:0V3X7RIPSS7I3VYW3XIS1RESRPVXSXS1PSSVPE7PIWRP0SS7R3VRY777E7E1WPVIUPWIE0U1YUUYPPE0", "hasFlower": 0, "hasSeason": 0}, {"id": "167", "q": "001,3,5,7,9,B.25,7.31,3,9,B.45,7.51,3,9,B.65,7.70,2,A,C<PERSON>85,7.90,3,9,C.A5,7.B0,3,9,C.C6.D0,2,4,8,A,C.E6.G1,3,5,7,9,B;103,9.33,6,9.41,B.55,7.61,B.86.94,8.A0,C<PERSON>B3,9.C0,C<PERSON>D4,8.E6.G3,9;286.B3,9.C0,C.D4,8:3UZ3VFF3IQ1WLDIGFI4DQ3GLQVIL11WLCUVG43VUD4G4G3DDQZZ3D11UL3ZD4IFUZZDV1LCUVGG433ZGZI", "hasFlower": 0, "hasSeason": 0}, {"id": "168", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.42,4,6,8,A.62,4,6,8,A.70,C.83,5,7,9.90,C.A3,6,9.C1,3,5,7,9,B.E0,2,5,7,A,C.G0,3,5,7,9,C;101,3,9,B.20,2,5,7,A,C.42,4,8,A.56.62,4,8,A.80,3,5,7,9,C.B3,9.C6.D2,A.E6.F0,C;212,A.52,A:FYCFTXXJCTJNWTUUDYFNDTHFNYXYCUIFCJWZXUYUCIW5YUNDDHTI5XUWFIHHUNHUNHUYYTTNWJWYXFTZTWXCYTFWXN", "hasFlower": 0, "hasSeason": 0}, {"id": "169", "q": "000,2,4,6,8,A,C.21,3,5,7,9,B.40,3,5,7,9,C.60,3,5,7,9,C.80,2,4,6,8,A,C.A1,3,6,9,B.C0,2,4,8,A,C.D6.E0,2,A,C.F5,7.G0,3,9,C;101,4,8,B.16.22,4,8,A.36.40,3,9,C.55,7.63,9.70,5,7,C.82,A.96.B1,3,9,B.D0,C.E2,6,A.F0,C:IDI9P0CTDDF9BTTDCDJ9FFC6J006D6TD9I6J96TFD6J0PIDJPJ6IJPB9TVPBFFRR9BP999CRIITRVVTD6J6J6TVIJT", "hasFlower": 0, "hasSeason": 0}, {"id": "170", "q": "000,2,4,A,C,E.16,8.20,2,C,E.34,6,8,A.40,2,<PERSON>,<PERSON><PERSON>54,<PERSON><PERSON>67.73,5,9,B.87.94,<PERSON><PERSON>A0,<PERSON><PERSON>B2,<PERSON><PERSON>C0,4,6,8,A,<PERSON><PERSON>D2,C<PERSON>E0,4,6,8,A,E;101,D.17.20,2,C,<PERSON><PERSON>34,A<PERSON>42,C<PERSON>54,A.73,5,9,B.A0,E<PERSON>B2,<PERSON><PERSON>C4,A<PERSON>D0,2,C,<PERSON><PERSON>E4,7,A;201,D.33,B.64,A.B2,<PERSON><PERSON>C4,A.E4,A:QJZ9Q0S73UJ0YV0JV3JMQ90S950099UYSM3M07ZS5QJQYQ35QY9MY3Y79Z5SQ5UUJM953SYVJMSV75Q505YZYSJY5Q", "hasFlower": 0, "hasSeason": 0}, {"id": "171", "q": "000,4,6,8,A,E.12,C.20,5,7,9,<PERSON><PERSON>33,<PERSON><PERSON>40,5,9,<PERSON><PERSON>52,<PERSON><PERSON>64,6,8,A.70,2,C,E<PERSON>84,6,8,A.90,<PERSON><PERSON>A2,4,A,C.B6,8.C0,2,4,A,C,E.D6,8.E0,3,B,E.F5,7,9.G1,D;104,A.11,6,8,D.34,A.64,A<PERSON>72,C.80,<PERSON><PERSON>A2,C<PERSON>B4,A<PERSON>C1,7,D.E7.F5,9.G1,D;211,7,D.C7.G1,D:6SZN3QMNYRNQT34I42OSN0OZYZ6GNMRN334DIRG6GDRI6QLIGCI05396MGQ36Y4Z3NM9C93GYILWRXRZW56XNZ6T29", "hasFlower": 0, "hasSeason": 0}, {"id": "172", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.41,3,5,7,9,B,D.60,2,4,7,A,C,E.80,2,4,6,8,A,C,E.A1,3,6,8,B,D.C1,3,6,8,B,D.E0,2,4,6,8,A,C,E.G1,3,6,8,B,D;112,4,A,C.32,4,A,C.61,3,B,D.81,D.B1,3,B,D.D1,D.E4,A.G2,C:MTBEJMVBHEMTMTTH3BTEVQ33JHVJJTBV3VMMJV3HEEHBJQQVMJ3JMTHQQTQVVB3E3EQEJMQB3BEH3HVJBMEHTTBH", "hasFlower": 0, "hasSeason": 0}, {"id": "173", "q": "001,3,5,7,9,B,D.20,2,4,6,8,A,C,E.40,3,6,8,B,E.60,3,5,7,9,B,E.80,2,4,6,8,A,C,E.A0,2,4,6,8,A,C,E.C0,3,6,8,B,E.E0,2,4,6,8,A,C,E.G0,2,6,8,C,E;103,B.15,9.27.46,8.53,B.65,9.A5,9.B3,B.D6,8.E2,C.F7:4XFK4P74PKNFF7FXNNPMK7KPKF7KMNPXF4N44VKPKF7N4VPMVKMMPV4KXVMV7XNVVP7X4FXN7VNP7NFV47FX", "hasFlower": 0, "hasSeason": 0}, {"id": "174", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,2,5,9,C,E.57.60,3,B,E.75,7,9.80,2,C,E.95,7,9.A0,E.B2,4,A,C.C7.D0,3,B,E.E5,7,9.F0,2,C,E.G4,6,8,A.H0,2,C,E.I4,6,8,A;100,2,C,E.15,9.20,7,E.60,E.87.C3,B.D0,E<PERSON>E6,8.G0,E<PERSON>H4,7,A;210,E.27.H7:FEFU31KFPEK31PFU77KEA37E3BAABUA7BG3QPGU33U1P11EEQPFQEBU1BJAUB1AEUPBKJP1JQAUAAF13EABG3BBGJU13PE", "hasFlower": 0, "hasSeason": 0}, {"id": "175", "q": "001,5,7,9,D.13,B.21,5,9,D.37.40,2,C,E<PERSON>54,6,8,A.60,2,C,E<PERSON>74,6,8,A.80,E<PERSON>93,5,7,9,B.A0,E<PERSON>B2,4,A,<PERSON><PERSON>C6,8.D0,2,C,E.E4,6,8,A.F1,D.G3,5,7,9,B;106,8.21,D.37.52,6,8,C.70,5,7,9,E.A0,E<PERSON>C2,6,8,C.E5,7,9.F1,D;221,D.47.67.D7.F1,D:009NM13RG0HV6NM8VDM90CN18RM8F1TTMRG6TC090RV4T664R4C3MHT3RC8HNA86MINA3GHD4T9CI1GIF6MV8RNCRI", "hasFlower": 0, "hasSeason": 0}, {"id": "176", "q": "000,2,4,7,A,C,E.20,2,4,7,A,C,E.40,2,5,7,9,C,E.60,2,4,6,8,A,C,E.81,3,5,7,9,B,D.A0,2,4,6,8,A,C,E.C0,2,4,7,A,C,E.E0,4,6,8,A,E.F2,C.G0,4,A,E.H2,7,C.I0,4,A,E;100,2,4,7,A,C,E.20,E.H0,E;207.20,E.H0,E:RROQ3QWR9A3QGQA39YOGUG3ARURRG9RGUAYA99AW3WG5Q4OUG4U9ROYU4QUWR9YO9Q3OWYUAQ4QGQ5U9YRWU9A", "hasFlower": 0, "hasSeason": 0}, {"id": "177", "q": "000,4,6,8,C.21,3,5,7,9,B.40,2,5,7,A,C.60,2,4,6,8,A,C.80,2,4,8,A,C.A0,2,6,A,C.B4,8.C6.D0,2,A,C.F1,3,5,7,9,B;100,4,6,8,C.23,5,7,9.41,6,B.60,2,5,7,A,C.81,3,9,B.A2,6,A.C6.D1,B.F3,5,7,9;200,C.14,8.36.61,B.A6:WMRR8KMIMW8P85W7I7RWPI7RM57K5KWK7WPP5P8RM7WWR5MPI88WW5PKI88RRI7MIMPK78KK5I575MRP8RIP75IM", "hasFlower": 0, "hasSeason": 0}, {"id": "178", "q": "000,3,5,7,9,C.22,4,8,A.30,6,C.42,4,8,A.60,2,4,6,8,A,C.80,2,4,6,8,A,C.A0,2,4,6,8,A,C.C1,5,7,B.E0,2,4,6,8,A,C.G0,2,4,6,8,A,C;100,C.14,8.32,6,A.54,8.73,9.81,B.93,9.C5,7.E2,6,A.G2,4,8,A;200,C.14,8.36.E6.G4,8:F8354TGL2L3G3AFFF4KYQ2KPTKP3YR54LY33KLP4YKQYLGGN5P8AK2CKCR1B5RNR1F4RN8KBRFY5PNGP385L2G34", "hasFlower": 0, "hasSeason": 0}, {"id": "179", "q": "000,2,4,7,A,C,E.20,3,5,7,9,B,E.40,2,4,6,8,A,C,E.61,3,5,7,9,B,D.80,2,4,6,8,A,C,E.A0,2,4,6,8,A,C,E.C0,3,5,7,9,B,E.E2,5,9,C;104,A.10,E.24,7,A.30,E.42,5,9,C.65,7,9.72,C.80,5,7,9,E.A0,3,5,9,B,E.C0,5,7,9,E:8XWILLZL4QWZW33I74U8U7L2JLWHQFQ37ZFSU3N7XQ2W78SJSJ77786OS1Z07H2UN17HHFU2OQWWLLLW0UQWWYFJY6", "hasFlower": 0, "hasSeason": 0}, {"id": "180", "q": "000,4,6,8,A,E.12,C.24,6,8,A.30,2,C,E.45,7,9.51,3,B,D<PERSON>66,8.70,2,4,A,C,E.86,8.90,2,4,A,C,E.A6,8.B0,2,4,A,C,E.C6,8.D0,2,C,E.E4,6,8,A.F1,D.G3,6,8,B;104,A.17.22,C.46,8.61,3,7,B,D.84,A.A2,4,A,C.B7.D6,8.F6,8;261,D:4CAIAVCKFFYKLVYIEOLFF661I4E9FLKPOP4QOQFLEFXOK4YEQ33QY6KQPYKQ1AIAXV9Y3VKAYAAPOO3PKYP6IAFI", "hasFlower": 0, "hasSeason": 0}, {"id": "181", "q": "000,3,7,B,E.20,2,6,8,C,E.40,2,7,C,E.54,A.61,6,8,<PERSON><PERSON>73,B.80,5,9,E<PERSON>92,7,C.A4,A.B0,2,7,C,E.D0,2,6,8,C,E.E4,A;100,7,E.20,2,6,8,C,E.40,7,E.54,A.61,6,8,D.80,5,9,E.97.A2,4,A,C.B0,7,<PERSON><PERSON>C2,<PERSON><PERSON>D0,6,8,E.E4,A:K8KNTNVVTN85Y8JVTOKFG8Q8J0G5N8QJVGOPYVTQFG0KPQGVNV8N0OKT585TFOOVNNV5NKQF0NOVG5Q8GJG8", "hasFlower": 0, "hasSeason": 0}, {"id": "182", "q": "001,3,5,7,9,B,D.21,5,9,D.33,7,B.40,5,9,E<PERSON>52,7,C.60,4,A,E.72,6,8,C.80,E.92,5,7,9,C.A0,E.B2,4,6,8,A,C.C0,E.D2,4,6,8,A,C.E0,E.F2,5,9,C.G0,7,E;101,3,B,D.25,9.33,7,B.51,7,D.63,B.82,C.B3,5,9,B.C1,7,D.D4,<PERSON><PERSON>E1,D.F5,9:NOOMTTTMDPFN52ESDEWM9PEPM5MMMXWSFS5NDNTJ9J5DMJXPXPFMNDPPMWFPFS299NP2TOOEXS5PFD5SFOJWJTJF2O", "hasFlower": 0, "hasSeason": 0}, {"id": "183", "q": "000,2,5,7,A,C.20,2,4,6,8,A,C.41,3,6,9,B.61,4,6,8,B.82,A.A1,3,6,9,B.C0,3,5,7,9,C.E0,3,5,7,9,C.G0,2,6,A,C;100,6,C.12,A.20,5,7,C.32,A.51,B.64,6,8.82,A.A1,3,6,9,B.C3,9.D0,5,7,C.E3,9.F0,C.G2,6,A;2A6:KZ8KTOBB50AGWCT63OU586H3BA3OXHUX5ULNUCB38D5AOC0ENCKCCELHZT8AZDSOUU55AKO1SKW1KGHAUUTZ", "hasFlower": 0, "hasSeason": 0}, {"id": "184", "q": "000,2,5,7,A,C.20,2,4,6,8,A,C.41,3,5,7,9,B.60,3,5,7,9,C.80,2,6,A,C.A1,3,5,7,9,B.C0,3,5,7,9,C.E1,3,5,7,9,B.G0,2,5,7,A,C;101,6,B.21,5,7,B.43,9.56.76.82,A.A1,3,5,7,9,B.C0,6,C.D3,9.E5,7.F1,B:TVUT9CCT7PGOPIO6C7ZFPJ32P972GGOP2J93MT98NZ6M6TI1PUUJNGG8PJUFM7CJUU19GP2V966MJTU6UO", "hasFlower": 0, "hasSeason": 0}, {"id": "185", "q": "003,5,7,9,B.10,E<PERSON>22,4,6,8,A,C<PERSON>30,E<PERSON>43,5,7,9,B<PERSON>50,<PERSON><PERSON>64,7,A.72,C<PERSON>85,9.91,7,D.A3,5,9,B.B0,7,E<PERSON>C2,5,9,C.D7.E0,3,B,E;104,7,A.20,2,6,8,C,E.34,A.40,6,8,E<PERSON>54,A.67.72,C.91,6,8,D.A3,B.B0,6,8,E.C2,C.D7;217.46,8.96,8.B6,8.C2,C:0JDUEWUE55UM5D2MD82DUM85ER8ERU0P0W0M5W2EDD5RPW0E5M5289MWM085RUM9RUJRP8WP8D2U08MRUMDWRW25U0", "hasFlower": 0, "hasSeason": 0}, {"id": "186", "q": "001,4,6,8,A,D.21,4,6,8,A,D.40,3,5,7,9,B,E.60,3,5,9,B,E.77.80,2,C,E.95,9.A7.B0,3,5,9,B,E.C7.D1,4,A,D.E6,8;101,5,7,9,D.21,4,A,D.36,8.40,4,A,E.63,5,9,B.77.81,D.95,9.B0,4,A,E.D1,4,A,D.E7;206,8.24,A.54,A.77.C4,A:G92YJL5LL6C9G66Y992C2G95C2L966C95GLG65GLJGJL2G2CCC2L5Y6GL5CJY65Y5GJJ2J92669YYY59YCCJY5L2", "hasFlower": 0, "hasSeason": 0}, {"id": "187", "q": "002,6,8,C.20,2,4,6,8,A,C,E.41,3,6,8,B,D.60,2,4,6,8,A,C,E.80,4,6,8,A,E<PERSON>92,C.A5,7,9.B1,3,B,<PERSON>.C5,9.D0,2,C,E.E5,7,9;102,C.17.20,2,4,A,C,E.36,8.41,3,B,D.57.60,4,A,E.76,8.80,4,A,E.97.A2,5,9,C.C2,C.D0,E.E7:OHFPVBHPOMPMOVVOUVMHMUB2FOPBOMUF2BM2M2VBUH2UHVPPFMBVOFHFFOUFM2UUH2UPPHMF2OOVHFVH2P2VUP", "hasFlower": 0, "hasSeason": 0}, {"id": "188", "q": "001,4,6,8,A,D.20,2,5,7,9,C,E.42,5,7,9,C.50,E.62,4,A,C.70,6,8,E.83,B.96,8.A0,3,B,E.B5,9.C0,3,7,B,E.D5,9.E0,2,7,C,E.F5,9.G0,7,E;104,7,A.11,D.26,8.42,5,7,9,C.50,E.62,C.76,8.83,B.96,8.B0,E.C4,7,A.D0,E.E5,7,9.G0,E;207.D0,E.G0,E:C61SLCKZZT6C0L3STL0C1B1ZZ0ST16KZBMSTB30603ZZZC6CKTMK3AMTTSL1KTCKK0B3C66366TC00Z1LLKZMS3KTA6CK3", "hasFlower": 0, "hasSeason": 0}, {"id": "189", "q": "001,3,5,7,9,B.20,2,4,6,8,A,C.40,2,4,8,A,C.60,2,5,7,A,C.80,2,4,8,A,C.A1,B.B5,7.C0,2,A,C.D4,8.E0,6,C.F2,4,8,A.G0,6,C;102,4,6,8,A.21,3,9,B.42,4,8,A.61,6,B.80,C.B1,6,B.D4,8.E0,6,C.F2,A.G0,C;202,A.21,B.80,C.E6.F1,B:G7N03DJNAMFLD3633F2NRJDY037AG12D13YG7JNDNNMD1Y0R2AR36L21GLM3N1L1MYADDGJRGGG7GLLMD60ANAMG6D", "hasFlower": 0, "hasSeason": 0}, {"id": "190", "q": "000,3,6,A,D,G.18.20,2,4,6,A,C,E,G.40,2,5,7,9,B,<PERSON>,G.60,2,4,8,C,<PERSON>,G.76,A.80,G.95,7,9,B.A0,3,D,G.B8.C0,2,4,6,A,C,E,G.D8.E1,3,5,B,D,F.F7,9.G0,3,D,G;100,G.18.22,6,A,E.40,8,G.52,E.68.98.B3,8,D.C1,6,A,F.D8.E1,F.F7,9;248.D1,8,F:88UJ8VHQ330F0HVQUCJLFRIVFC38QUTU3IAAII1GLLUTGCA0RQV3LTI0GQAV1101V00HKTGG3CHKFAQTAGKL8TFIF8U0KL", "hasFlower": 0, "hasSeason": 0}, {"id": "191", "q": "000,2,4,6,8,A,C,E,G.20,4,7,9,C,G.32,E.44,6,8,A,C.50,2,E,G.64,6,A,C.70,8,G.83,5,B,D.90,7,9,G.A3,5,B,D.B0,7,9,G.C2,4,C,E.D6,8,A.E0,2,4,C,E,G.F7,9.G0,2,E,G;106,A.38.42,E<PERSON>54,C.88.B4,C.C2,E.D7,9.G2,E:BW2C56QYX0XEBUEWL33OWTU99WQY90E98CEQHQBNCY3T0289UMTQMENEXT6O5HEBR0SSN9QLNEY3044XCR0U", "hasFlower": 0, "hasSeason": 0}, {"id": "192", "q": "000,3,5,9,B,E.21,4,6,8,A,D.40,2,5,7,9,C,E.60,2,4,6,8,A,C,E.80,2,4,6,8,A,C,E.A0,2,5,9,C,E.B7.C2,4,A,C<PERSON>D0,6,8,E<PERSON>E3,B<PERSON>F0,5,7,9,E.G3,B.H1,5,7,9,D;100,3,B,E.45,9.61,7,D.73,B.80,E<PERSON>92,C<PERSON>C3,B.D7.F4,6,8,A;2F6,8:534H5N5CPC3B55BZ3AP6CPN434BV4N0PAZB0360AHPA0B5PC380P3XBXBV448AP3BC40XNNA4C30N03B6HHB6XHH", "hasFlower": 0, "hasSeason": 0}, {"id": "193", "q": "000,2,4,6,8,A,C,E,G.20,3,8,D,G<PERSON>35,B.40,2,7,9,E,<PERSON><PERSON>54,C.60,2,6,8,A,<PERSON>,G<PERSON>74,C.81,7,9,F.94,C.A0,2,6,8,A,E,G.B4,C.C0,6,8,A,<PERSON>.D2,E.E0,5,7,9,B,G.F3,D.G0,6,8,A,G;104,7,9,C.23,D.78.A2,8,E.E7,9.F3,D;204,C:0HOFG0XXHVO9H9704OF9OX0V0947O90VJ4HG7GGHVVXVJ9F4J0OG94JX9HG4F9OO9JFHJ0HVJJHOFFXFVOH7", "hasFlower": 0, "hasSeason": 0}, {"id": "194", "q": "000,4,6,8,A,E.12,C.20,5,7,9,E.32,C.40,4,6,8,A,<PERSON><PERSON>52,<PERSON><PERSON>64,7,A.70,2,C,E.84,7,A.90,E.A2,4,6,8,A,C.C0,2,4,6,8,A,C,E.E0,2,5,7,9,C,E.G0,2,4,6,8,A,C,E;112,C.27.41,5,9,D.53,B.70,4,A,<PERSON>.A4,<PERSON><PERSON>C3,B.D1,5,9,D.F7.G2,C:1ARU551VR1HR5VBNVVR41BR1NU1NVRN4ABV1ANUNHUAU5NVHNU4RUURVAB4UB5UA554HAB1B54A1BVA55B1NAVNB", "hasFlower": 0, "hasSeason": 0}, {"id": "195", "q": "000,2,4,7,A,C,E.20,2,4,A,C,E.36,8.40,2,4,A,C,E<PERSON>56,8.61,3,B,D.75,7,9.94,6,8,A.A0,E.B3,5,7,9,B.C0,E.D2,4,6,8,A,C.E0,E.F2,4,A,C.G6,8;101,3,B,D.21,3,B,D.37.40,2,5,9,C,E.61,D.86,8.A0,E<PERSON>B4,A<PERSON>C0,6,8,E.D2,<PERSON><PERSON>F3,B.G6,8:S4EQ4AQJJ3WLASLTWWJASPS3P3QAAEQTWAET343N3TQWJT6TLWWELQA3JTEAT3EAN6SLSELJQW4PQT344WAP3STSWE", "hasFlower": 0, "hasSeason": 0}, {"id": "196", "q": "000,2,4,6,8,A,C,E.20,2,5,7,9,C,E.40,2,4,6,8,A,C,E.60,2,5,9,C,E.77.80,2,4,A,C,E.96,8.A0,2,4,A,C,E.B6,8.C1,3,B,D.D5,7,9.E0,2,C,E.F5,7,9.G0,3,B,E;102,5,9,C.10,7,E.25,9.30,E.52,C.A3,5,9,B.B7.C2,C.E0,E.F5,9.G0,E:2U22WLDUHONNMR2ZBMZJ7WPR77XJ8JWUI7HBKBXKEWIOZXWMDOOUKLMUNH2JZNHW2EMRMLRDWLJWLXDH8JPHBKUOLO", "hasFlower": 0, "hasSeason": 0}, {"id": "197", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.41,3,5,7,9,B.60,2,4,6,8,A,C.80,4,6,8,C.A0,2,4,6,8,A,C.C2,4,8,A.E1,3,5,7,9,B.G0,3,5,7,9,C;101,3,5,7,9,B.22,5,7,A.43,9.51,B.70,5,7,C.94,8.A0,2,A,<PERSON>.D3,9.E1,6,B.F3,9.G0,5,7,C:UJNNNJSG21UTVJUPGVG1J1PG2VNTGJ2JVUUUM1VJGUTTSNJMPGG2MSNTMMMGNGUVVSU2VVMUPTMTSMJPMNTJPTV2ST", "hasFlower": 0, "hasSeason": 0}, {"id": "198", "q": "000,2,4,6,8,A,C,E.20,3,5,7,9,B,E.40,2,4,6,8,A,<PERSON>,E.63,5,7,9,B.70,E.83,5,7,9,B.90,<PERSON><PERSON>A2,5,9,C.B0,7,E.C2,4,A,C.D6,8.E0,2,4,A,C,E.F6,8.G0,2,4,A,C,E.H7.I2,C;106,8.13,B.37.40,E.57.65,9.83,6,8,B.A2,C.B7.D5,9.E0,7,E.G7:VWYP43YARGQV3MAYEMJGSMGP3RSWARER6WJW3SILEY03P4M0GQJYQIVJEQWP34SQ3Q6MWQ4Q44JS6V3SJLYVGAG6MV", "hasFlower": 0, "hasSeason": 0}, {"id": "199", "q": "000,3,5,9,B,E.21,3,B,D.36,8.40,2,4,A,C,E.57.60,2,5,9,C,E.77.80,4,A,E.97.A0,2,4,A,C,E.B7.C0,2,4,A,C,E.E1,3,6,8,B,D.G0,2,5,9,C,E;103,5,9,B.22,C.40,E.67.70,E.A0,2,7,C,E.B4,A.C1,D.E3,B.G0,2,C,E;240,E:TTXXL59XSTOF3KTL99UAXWKSY9L0FTX0TRKK0LT0YFUVAXYUVY0UTOYVXA5XW3HFYLTC0YY9YXTLXCYAHV9R", "hasFlower": 0, "hasSeason": 0}, {"id": "200", "q": "000,2,4,6,8,A,C,E.20,5,7,9,E.33,B.40,7,E<PERSON>52,5,9,C.67.71,3,5,9,B,D.87.91,3,B,D.A5,9.B7.C0,2,5,9,C,E.D7.E0,3,B,E.F5,7,9.G0,E;100,2,5,7,9,C,E.27.30,3,B,E.56,8.71,D.83,B.B5,9.C1,D.E0,3,7,B,E;202,5,7,9,C.56,8.71,D.E7:9ZFO9X7OXI19FIOOK199AKM11MZXFA1XKIA90OK1M1ZOBM9XKFB09KFZBFFX09OX9OKFII1FK0XKZI10KZ0OAOF7B1", "hasFlower": 0, "hasSeason": 0}]