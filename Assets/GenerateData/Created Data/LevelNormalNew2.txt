[{"id": "1", "q": "000,2,4,6,8,A.21,3,5,7,9.40,2,8,A.54,6.60,2,8,A.74,6.80,A.92,8.A0,4,6,A.C0,2,5,8,A;100,3,5,7,A.21,3,5,7,9.41,9.53,5,7.61,9.91,9.B0,5,A.C2,8:299GO2YV21LVJPOVCLP94TT9M5Y4GCFCA2C4U4YJ5MPYS0S1VSA1PJF0J1SU", "hasFlower": 0, "hasSeason": 0}, {"id": "2", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,3,5,7,9,C.60,2,4,6,8,A,C.80,2,5,7,A,C.A0,2,4,8,A,C.B6.C0,2,4,8,A,C;103,6,9.25,7.45,7.61,6,B.91,B.B5,7:O13TS9GAO1JOM2JF7FA34T4MAF21QGQAET9MOJQE27Q193JF9GS237M7SSTG", "hasFlower": 0, "hasSeason": 0}, {"id": "3", "q": "000,3,5,7,9,C.21,3,5,7,9,B.43,5,7,9.51,B.63,5,7,9.70,C.82,4,6,8,A.90,C.A3,5,7,9.C0,2,4,6,8,A,C.E0,3,5,7,9,C;105,7.22,4,8,A.44,6,8.51,B.A5,7.D4,8.E6:2J1YQ8SRP6VQALLBEGAXICK14A07965ZRHYMTZGBSIXHP68SKMA60594TJVECS72", "hasFlower": 0, "hasSeason": 0}, {"id": "4", "q": "000,3,6,9,C.20,5,7,C.32,A.44,6,8.60,2,5,7,A,C.86.92,4,8,A.A0,6,C.C2,5,7,A;100,3,6,9,C.26.62,5,7,A.92,A.A0,C.B6.C2,A;200,3,6,9,C.26.65,7.B6.C2,A:K2W5AHIYHWAK2YHGJYKPKAFPU5LU7DD5D0LJQWN7FJNHW7IDYIJAG0Q72I25", "hasFlower": 0, "hasSeason": 0}, {"id": "5", "q": "000,2,4,6,8,A.23,7.35.40,2,8,A.54,6.60,2,8,A.74,6.80,2,8,A.A1,4,6,9.C2,4,6,8.E1,3,5,7,9;102,5,8.35.40,A.52,5,8.73,7.91,9.A5.C4,6.E1,3,5,7,9;205.52,5,8.A5.E2,8:WE44FMHBS470E5J399551B5F3VMJDVMBD6N9FMG194HGHNTT6VH3W7VBFX0X0770S3", "hasFlower": 0, "hasSeason": 0}, {"id": "6", "q": "000,3,6,9,C.22,4,8,A.30,C.43,9.50,5,7,C.70,5,7,C.83,9.90,C.A2,5,7,A.C0,4,6,8,C;100,3,6,9,C.22,A.30,C.43,9.50,6,C.75,7.80,C.A2,5,7,A;200,C.43,9.50,C.80,C;350,C.80,C:I54U5XW6UDL1MH06WNB5IWCLHV4B6EV61FU3HMA2VA2374XUQBDT0VHWTBE45NQSSCF7", "hasFlower": 0, "hasSeason": 0}, {"id": "7", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.41,3,9,B.56.60,3,9,C.75,7.80,2,A,C.94,8.A0,2,6,A,C.B4,8.C0,2,A,C;100,2,4,8,A,C.16.20,C.41,B.53,9.90,C.A4,6,8.B0,C.C2,A:S2SO1LKRUVL1N6Z2XVJU5QJUANAKN5Q2XU6SXBQNQBWK31VRXSOWB5B2V51ZZKZ3", "hasFlower": 0, "hasSeason": 0}, {"id": "8", "q": "000,2,5,8,A.20,2,4,6,8,A.40,2,5,8,A.60,2,4,6,8,A.83,7.90,5,A.B0,2,5,8,A.D0,2,4,6,8,A;100,A.22,8.40,A.52,8.65.73,7.95.B0,A<PERSON>D2,8;200,A.22,8.52,8.65.95.D2,8:598O5YWHSHQYY4QLDQ09566HU5YWWTLQ4S0SUOOCSD6UH0UWC9608O84D9C8CDT4", "hasFlower": 0, "hasSeason": 0}, {"id": "9", "q": "000,3,5,7,A.22,4,6,8.30,A.43,5,7.50,A.63,7.70,5,A.90,3,7,A.B0,3,5,7,A.D2,4,6,8.E0,A;104,6.24,6.40,A.53,7.93,7.A0,A<PERSON>C3,7;215.93,7.A0,A<PERSON>C3,7;315.93,7.C3,7:C33RD7G1TY1EBB2NIG9T21HWOG30YDO7V7SV0LIOWCHOAC1AGH9RH53EL5NSC7", "hasFlower": 0, "hasSeason": 0}, {"id": "10", "q": "000,2,4,6,8,A,C.20,2,4,8,A,C.36.41,3,9,B.55,7.60,2,A,C.75,7.81,3,9,B.95,7.A0,2,A,C.B4,8.C0,2,6,A,C;101,5,7,B.20,2,A,C.36.52,A.65,7.82,A.95,7.A0,C.B2,A:AB02K0LBR70DGXVHMMI2LEIY4D7VWHKJG6FHWXFGH2B9BKTG41JAPTEKY1620PR9", "hasFlower": 0, "hasSeason": 0}, {"id": "11", "q": "000,3,5,7,A.20,2,5,8,A.40,2,5,8,A.60,2,4,6,8,A.80,3,7,A.95.A1,3,7,9.B5.C0,2,8,A;103,5,7.20,5,A.32,8.40,5,A.61,9.80,A.95.A2,8.B5;225.31,9.45.95.B5;345.95:V41ZMJZVAA94MESV69BW512JHPLF9ZSPYLMBEH6FEPJVJM4Y4P9ZA6E22W5A62", "hasFlower": 0, "hasSeason": 0}, {"id": "12", "q": "000,2,4,6,8,A.21,3,5,7,9.41,3,7,9.55.61,3,7,9.75.81,3,7,9.A0,2,4,6,8,A<PERSON>C0,2,4,6,8,A<PERSON>E1,3,7,9;121,9.33,7.61,9.75.91,9.C3,7.D1,9;261,9.91,9;361,9.91,9:PAYRI8671271GKSZYC4TZPD2DM2B560ABH4HIFGNR0Q9JUJMTCFNU2SXQ9X8K5", "hasFlower": 0, "hasSeason": 0}, {"id": "13", "q": "000,2,4,6,8,A,C.20,3,5,7,9,C.40,5,7,C.52,A.60,4,6,8,C.72,A.85,7.90,C.A2,4,6,8,A.B0,C.C2,5,7,A;101,4,6,8,B.40,C.52,5,7,A.76.90,C.A3,9.B1,B.C6:JPGJCYF7UHVY0HCIR6EM4LQTRDBERQPV1LN97C06Z1BFMANCADR9WT4ZUWIG", "hasFlower": 0, "hasSeason": 0}, {"id": "14", "q": "000,2,4,8,A,C.16.20,2,4,8,A,C.40,2,4,8,A,C.56.61,3,9,B.75,7.80,2,A,C.94,8.A0,2,A,C.B4,6,8.C0,2,A,C;103,9.10,6,C.23,9.41,3,9,B.80,C.93,9.B3,9.C0,C:3PG0VZXEK7M9HE9POTTFN00JYJGYFMR7590OEEGTOK9WR6T5NO6DVDHP3WPXGZ", "hasFlower": 0, "hasSeason": 0}, {"id": "15", "q": "000,2,5,7,A,C.20,2,4,8,A,C.36.41,B.53,6,9.60,C.72,5,7,A.80,C.92,5,7,A.B0,2,4,8,A,C.C6;105,7.12,A.20,4,8,C.36.41,B.53,9.60,C.72,5,7,A.92,A.B0,2,4,8,A,C:6Q5KCVTX3V7IOZ7FMA435FXWQY26VQHSCT3OWNC49NUIFASUKYFHQ7ZV7923ZCZM", "hasFlower": 0, "hasSeason": 0}, {"id": "16", "q": "000,3,5,7,9,C.20,2,A,C.34,6,8.51,6,B.63,9.75,7.81,B.96.A2,4,8,A.B0,C.C2,5,7,A;100,5,7,C.22,A.46.75,7.96.A3,9.C2,6,A;200,6,C.75,7.A3,9;300,C.76;400,C:84YNNEXRS40I2EJL9CLA8RA5YY12319T3NC4XFFDXSNSDJT4Y5P0IVZPSXZV", "hasFlower": 0, "hasSeason": 0}, {"id": "17", "q": "000,2,4,6,8,A.20,2,5,8,A.40,2,4,6,8,A.61,3,5,7,9.80,3,7,A.95.A1,9.B3,7.C5.D0,2,8,A<PERSON>E4,6;103,7.20,A.32,8.61,3,7,9.83,7.D0,A;203,7.20,A.61,3,7,9.83,7.D0,A:0X2WKA03CFFTKEVZOM7S7IFJPLNYYQDWTMSVLWZHHNXCMEIJFG2RMWAGRODYP3QY", "hasFlower": 0, "hasSeason": 0}, {"id": "18", "q": "000,2,5,7,A,C.20,3,9,C.36.40,2,A,C.55,7.60,C.82,5,7,A.90,C.A2,4,6,8,A.B0,C.C2,4,6,8,A;100,2,A,C.40,2,A,C.90,2,A,C.A4,8.B2,6,A;201,B.42,A.92,A;301,B;401,B:FZE219GXKSACL8JOB5T5RQYWZCARK2L7PW8YAADMHDF3HDXMS9P3OSJGE71DSBQT", "hasFlower": 0, "hasSeason": 0}, {"id": "19", "q": "000,2,8,A.14,6.20,A.32,4,6,8.40,A<PERSON>53,5,7.60,A.72,4,6,8.80,A.93,7.A5.B0,2,8,A.C4,6.D0,<PERSON><PERSON>E3,7;101,9.24,6.30,A.43,5,7.50,A.63,5,7.70,A.83,7.B1,5,9;225.30,A.44,6.60,A.73,7:F3PCUSCU2FU7VMD23G3SSMDNIEP2EN2P7FED4YSYP4VMNGEUF7VDMICI7GG4Y34INYVC", "hasFlower": 0, "hasSeason": 0}, {"id": "20", "q": "001,3,5,7,9,B.21,3,5,7,9,B.40,3,5,7,9,C.60,2,4,6,8,A,C.80,2,4,8,A,C.96.A0,2,A,C.B4,6,8.C0,2,A,C.D4,6,8.E0,C;124,8.45,7.60,6,C.74,8.80,C.96.B2,5,7,A.D4,8.E0,C:IY3OW8Q8XQV4VLVNZ58G4S8ZUYYUXQHOHLXUIN3SNUHGSIZIOL34W3NLHZQXSYW5W4OV", "hasFlower": 0, "hasSeason": 0}, {"id": "21", "q": "000,2,4,6,8,A,C.20,2,5,7,A,C.42,4,6,8,A.50,C.62,4,8,A.80,2,4,6,8,A,C.A2,4,6,8,A.B0,C.C2,5,7,A;100,3,5,7,9,C.20,6,C.42,5,7,A.50,C.63,9.80,2,5,7,A,C.A4,8.B0,6,C.C2,A:IMCXVCDK48N3U4G5MI03Q8FR5FNYFAV0K23ZGT5XHDPUHPA5XGGMRTJVZJXQFT2U3IUITVYM", "hasFlower": 0, "hasSeason": 0}, {"id": "22", "q": "000,2,4,6,8,A.20,2,5,8,A.40,2,4,6,8,A.62,8.70,4,6,A.82,8.A1,3,5,7,9.C0,2,4,6,8,A.E0,2,4,6,8,A;102,4,6,8.10,A.22,5,8.40,2,5,8,A.72,5,8.A1,4,6,9.C1,5,9.E0,3,7,A:DE393FJBA6H4STW01AMG0TUEQ5I9ZNHRKPRFYQSGIZG6QT4KDJL45M7GUBL4AAWTPQ7N1Y", "hasFlower": 0, "hasSeason": 0}, {"id": "23", "q": "000,2,4,6,8,A,C.22,4,6,8,A.40,2,4,8,A,C.56.60,2,A,C.75,7.81,B.95,7.A0,2,A,C.B4,8.C1,B.D3,5,7,9.E0,C;101,4,8,B.22,4,6,8,A.40,2,A,C.56.60,2,A,C.81,B.D4,6,8:H88I9S6E5OEG3SIHBREZF5FF3LTCZLBY65FHCLI968HRTUI2T82PRPOUGT6E522RYL", "hasFlower": 0, "hasSeason": 0}, {"id": "24", "q": "000,2,4,6,8,A,C.20,2,6,A,C.34,8.40,6,C.52,A.60,4,8,C.80,2,A,C.94,6,8.A1,B.B3,6,9.C1,B;102,4,8,A.21,6,B.34,8.40,C.52,A.64,8.80,2,A,C.A6.B1,3,9,B;203,9.34,8.52,A.82,A.A6:9L6TYNH8CKKGYDOJB4YTR8EGEJHJ33RHL3IA9B894XNCLIAQ9JKKB8DSX3BLYSCO6ICQHI", "hasFlower": 0, "hasSeason": 0}, {"id": "25", "q": "000,2,5,8,A.23,5,7.40,4,6,A.52,8.60,4,6,A.82,4,6,8.90,A.A3,5,7.C0,2,4,6,8,A;100,2,5,8,A.24,6.40,5,A.52,8.60,4,6,A.82,4,6,8.90,A.A4,6;202,**********,A.83,7.A5:2QOPQIW2JEJISBJZM2N9R2Z4HWEOB0ISIYM0KCRBRBW8AKLPRHN9ZYCSWFZFL4JS8A", "hasFlower": 0, "hasSeason": 0}, {"id": "26", "q": "000,2,5,7,A,C.20,2,5,7,A,C.40,2,4,6,8,A,C.62,5,7,A.80,3,5,7,9,C.A0,2,4,8,A,C.B6.C0,2,4,8,A,C;106.12,A.26.30,C.43,5,7,9.65,7.93,9.A0,C.B2,6,A;212,A.30,C.46.A0,C:FNM4Q6G570RQKHM4C9RKYD9A6UZCM419N7C1U0C9OXGUWVMVU5YHFIPXBPA0B4IOD0WZ", "hasFlower": 0, "hasSeason": 0}, {"id": "27", "q": "001,B.13,9.20,6,C<PERSON>32,A.40,5,7,C<PERSON>52,A.60,4,8,C.82,4,8,A.90,C<PERSON>A2,5,7,A.B0,C.C3,9;120,6,C.32,A.50,2,A,C.82,4,8,A.A2,A.B0,C;220,C.52,A.82,4,8,A.B0,C;352,A.82,A;452,A.82,A:O8H35FP4DXZ4Y2PTIM2HPQ3QOUSJD1SYMEB8A7UPA8X5BE7NI6GLZ6JNW8TLF9SGSW91", "hasFlower": 0, "hasSeason": 0}, {"id": "28", "q": "001,3,5,7,9,B.20,3,6,9,C.40,2,A,C<PERSON>54,6,8.60,C.73,5,7,9.80,C.92,A.A0,5,7,C.B3,9.C0,5,7,C;106.13,9.20,C.42,A.50,6,C.64,8.80,C.92,A.A5,7.B0,C<PERSON>C5,7;220,C.64,8.B0,5,7,C:T37IVZ4RAAHRADS3KM7IDZDP31U6J4WESVHSEW8D3AMV85UL8RZLNVRNJWP6TZSW158K", "hasFlower": 0, "hasSeason": 0}, {"id": "29", "q": "000,2,4,6,8,A,C.20,4,6,8,C<PERSON>32,A<PERSON>44,6,8.51,B.65,7.70,2,A,C.85,7.90,2,A,C.A4,6,8.B1,B<PERSON>C3,6,9.D0,C<PERSON>E2,4,8,A;101,5,7,B.24,8.32,A.44,8.51,6,B.90,5,7,C.C3,9.E3,9;201,B.24,8.32,A.C3,9.E3,9:4NGAXOYE877KQ2RZH32VZTMW6UU04W3P7KZRGP8VZI3OG827RATGM9IN906RBTS3HIYQXI2ETSB8", "hasFlower": 0, "hasSeason": 0}, {"id": "30", "q": "001,4,6,8,B.20,3,5,7,9,C.41,4,8,B.60,2,4,8,A,C.80,2,4,8,A,C.A1,3,5,7,9,B.C1,3,5,7,9,B;101,4,8,B.16.24,8.41,4,8,B.61,3,9,B.80,2,4,8,A,C.A2,4,6,8,A.C1,3,6,9,B:KP9L6OFHL917B1CBG6CN0HP6TPFALRPMKB0HIG8RQQ7JNLIC2TJ82JD7EMCDROJ76REHAB", "hasFlower": 0, "hasSeason": 0}, {"id": "31", "q": "002,4,6,8,A.10,C.23,5,7,9.30,C<PERSON>43,6,9.51,B.63,5,7,9.70,C.82,4,6,8,A.A0,3,5,7,9,C.C1,4,6,8,B.E0,3,5,7,9,C.G0,2,4,6,8,A,C;102,A.16.56.64,8.70,6,C.96.A0,4,8,C.C5,7.E5,7.G6:IR1R8KGZDELLL5EKISBRD8ZKG21GJTQQJ1CSCTXCXGRC189SDAOAXOL89BMXK5D2S59900M5", "hasFlower": 0, "hasSeason": 0}, {"id": "32", "q": "000,3,5,7,9,C.21,3,5,7,9,B.40,2,4,6,8,A,C.61,3,6,9,B.80,2,5,7,A,C.A0,3,5,7,9,C.C0,2,5,7,A,C.E0,4,6,8,C;105,7.13,9.25,7.32,A.53,9.66.71,B.86.90,C.A3,9.C0,2,5,7,A,C.E6:IPX6A3K1EYB1Z82HRFYBKHXEKGIFO42SPR6DOXXKMJQSDWY93440LY84WGZ9WL1W1EJ0MEQA", "hasFlower": 0, "hasSeason": 0}, {"id": "33", "q": "000,3,5,7,A.20,A.32,5,8.40,A<PERSON>54,6.61,9.73,5,7.80,A.92,4,6,8.B0,2,8,A.C5.D0,A.E2,4,6,8;100,4,6,A.30,2,5,8,A.55.61,9.73,7.92,4,6,8.B0,2,8,A.C5.E3,7;231,9.45.61,9.73,7.B1,9;331,9.61,9:HDSR8QAXUX16KPDAXKUPZEUZOFQ40F8RA4E0FRB5ZOAZCUR35B160XYYMOS2N3N0OJKCJFM2KH", "hasFlower": 0, "hasSeason": 0}, {"id": "34", "q": "000,2,4,6,8,A,C.22,5,7,A.42,4,6,8,A.50,C.63,5,7,9.70,C.82,5,7,A.A0,2,4,6,8,A,C.C3,6,9.E2,4,6,8,A;101,5,7,B.22,A.45,7.53,9.65,7.82,6,A.A2,4,8,A.B6.C3,9.E6;246.54,8.82,A.A2,4,8,A.B6:R8HV3MXX5RLNN7E9163R91M39COHH9ME13PPOZ05ZECCVLZR77O86VE606ZVO857M1CXXNP5H8NP", "hasFlower": 0, "hasSeason": 0}, {"id": "35", "q": "000,2,4,6,8,A,C.20,2,A,C.36.40,2,4,8,A,C.60,2,5,7,A,C.81,4,6,8,B.A0,2,5,7,A,C.C1,3,5,7,9,B.E0,2,6,A,C.G0,2,4,8,A,C;100,2,6,A,C.21,B.40,C.61,B.76.81,B.95,7.A1,B.C4,8.E2,A.G1,4,8,B:QTMW65Y0V3R2DIQ6O3IK399SSD828O41W84X42382M1TSMWS95BORI1I0OX6XY49VYKWBVQY16XMQV", "hasFlower": 0, "hasSeason": 0}, {"id": "36", "q": "000,3,6,9,C.20,2,5,7,A,C.40,4,8,C.56.61,3,9,B.75,7.80,3,9,C.95,7.A0,2,A,C.B5,7.C1,3,9,B;100,3,6,9,C.20,C.44,8.56.62,A.74,6,8.80,C.95,7.A0,2,A,C.C2,A;206.44,8.56.75,7.90,6,C:AXRG7GE4PKP67UT64GNANBBMTI7ZKNQBXPIKUBKGXE7QAINIXRQMEMZUTR66P4ZMAEQTZ4RU", "hasFlower": 0, "hasSeason": 0}, {"id": "37", "q": "000,2,4,6,8,A,C.21,4,6,8,B.40,3,5,7,9,C.60,3,5,7,9,C.80,4,8,C.A0,2,5,7,A,C.C0,2,5,7,A,C.E3,5,7,9;100,3,5,7,9,C.24,6,8.50,4,8,C.66.70,4,8,C.A0,2,5,7,A,C.D5,7:EB2O0UD025NELAAWH56PDLUS6HN3SP2SB2ER63DUW3WZ0ZOHEWANBNSRU50B53ZLD6HLAZ", "hasFlower": 0, "hasSeason": 0}, {"id": "38", "q": "000,4,6,8,C.20,3,6,9,C.40,3,6,9,C.61,3,5,7,9,B.81,3,6,9,B.A0,3,6,9,C.C0,3,5,7,9,C.E0,3,5,7,9,C;104,8.10,C.26.61,3,5,7,9,B.81,B.93,9.A0,C.C6.D4,8.E0,C;210,C.26.62,A.E0,C:AVNSNDLM17BM3IKSYAF3RY96T9Q67EPQ85AJ4X5DTTPJY24LVAKBR2FUUIWP1PXT8YGG44EW", "hasFlower": 0, "hasSeason": 0}, {"id": "39", "q": "000,2,4,6,8,A.20,3,5,7,A.40,3,5,7,A.60,2,4,6,8,A.80,2,4,6,8,A.A2,4,6,8.B0,A<PERSON>C3,7.D0,5,A<PERSON>E2,8;102,5,8.10,A.33,7.50,A.62,5,8.81,9.95.C3,7.D0,A<PERSON>E2,8;202,8.33,7.81,9.C3,7:KOWENHC9EWM8CMUV5N15V9WNUOCBEAW9KVVDK5MAHH8G5EKUBUGMBG18D911AHG8NADDBC", "hasFlower": 0, "hasSeason": 0}, {"id": "40", "q": "000,2,4,6,8,A,C.21,3,5,7,9,B.40,2,4,8,A,C.60,2,4,6,8,A,C.80,3,5,7,9,C.A0,3,5,7,9,C.C0,2,5,7,A,C.E0,2,4,8,A,C.F6.G0,2,4,8,A,C;105,7.26.44,8.52,A.73,9.80,5,7,C.A3,9.C2,6,A.F6:JXLCA284740CECYJT676BO890PTEM1GLU4DOIGP0ZA9RQ2UFF0UQ7FSX7WWDZUTC4BHHRTYSF1IM", "hasFlower": 0, "hasSeason": 0}, {"id": "41", "q": "000,2,4,6,8,A,C.20,2,5,7,A,C.40,2,5,7,A,C.60,3,9,C.75,7.80,2,A,C.94,6,8.A0,C<PERSON>B3,9.C0,6,C<PERSON>D2,A<PERSON>E4,6,8.F0,2,A,C.G6;101,4,8,B.16.31,B.45,7.63,9.76.81,B.94,6,8.B3,9.D2,A.G6;263,9.76.81,B.B3,9:CW6BRC59GN657PIW4T4RYX9YVFP4VFTG9BV51J4KIXFC6T8F18R26RTNAVUCBBI9JG75UKAPPIG211", "hasFlower": 0, "hasSeason": 0}, {"id": "42", "q": "000,2,4,6,8,A,C,E.21,3,6,8,B,D.40,2,5,9,C,E.57.60,2,4,A,C,E.77.80,2,4,A,C,E.96,8.A0,2,C,E.B5,9.C1,3,7,B,D.E0,3,6,8,B,E;103,7,B.22,C.40,E.57.70,2,C,E.97.A0,E.C1,D.E3,B:0HJTGAEIZ33N6TFGAFU6JJHNM9DDI9XY6EPKTQUN6ASTMQQZJXPMPUSI0KYE133I1QNPEUAM", "hasFlower": 0, "hasSeason": 0}, {"id": "43", "q": "000,2,5,7,A,C.20,2,4,8,A,C.36.40,2,A,C<PERSON>54,6,8.61,B<PERSON>73,9.91,5,7,B.A3,9.B0,C.C6.D0,2,4,8,A,C.E6;100,2,5,7,A,C.20,3,9,C.36.41,B.56.61,B.73,9.91,B.C0,6,C.D3,9.E6;202,5,7,A.10,C.51,6,B.E6:1YFBMX93JXY2JOKKGEF4MBEFHD2B1KIB9I3UXJKJ32HE6E9IWYOYFRU4W16LO91GDXLDRI2GDGO3", "hasFlower": 0, "hasSeason": 0}, {"id": "44", "q": "000,2,4,6,8,A,C.25,7.30,2,A,C.44,8.50,2,A,C.64,8.70,2,6,A,C.84,8.90,C<PERSON>A2,4,8,A.B0,C.C2,5,7,A.E4,6,8;105,7.25,7.30,C.51,B.63,9.71,6,B.84,8.A1,B.D5,7;215,7.30,C.51,B.A1,B.D5,7:UG7NF0RCQGJR748PU5BX9L34AM9WVDCLV15HTJM0I46V1XS8BAQPDN3FIVT64ZIIJZHW5S5J", "hasFlower": 0, "hasSeason": 0}, {"id": "45", "q": "000,2,5,7,A,C.20,3,5,7,9,C.40,2,4,8,A,C.56.60,2,A,C.75,7.80,2,A,C.95,7.A0,2,A,C.B4,8.C0,2,6,A,C.D4,8.E1,6,B;105,7.10,C.23,9.30,C.44,8.51,B.66.70,C.92,6,A.B1,4,8,B.D4,8.E6:VRE0U50BWNRME5GUFA1AH7UMQABQANFDG38QM5W77RY3GHYF5U9RMHNN9YGVQ97FYKHDK198", "hasFlower": 0, "hasSeason": 0}, {"id": "46", "q": "002,4,8,A.16.20,2,4,8,A,C.36.40,3,9,C.55,7.60,2,A,C.76.80,2,4,8,A,C.96.A0,2,A,C.B4,8.C0,6,C.D3,9.E0,6,C<PERSON>F2,4,8,A.G6;102,A.14,6,8.20,C.33,6,9.50,C.66.70,2,A,C.84,8.90,C.A2,A.B0,C.D6.E3,9.G6:G5TGL4R4VQ008T9HAOCJVHHVHRO7LTA6P5066YM9RKRYK89PO0JTJCC5A1VA7KC9561JO84477QMK8", "hasFlower": 0, "hasSeason": 0}, {"id": "47", "q": "001,4,6,8,B.20,2,4,6,8,A,C.40,3,5,7,9,C.60,2,4,6,8,A,C.80,2,4,6,8,A,C.A0,2,4,6,8,A,C.C0,2,4,6,8,A,C.E0,3,5,7,9,C.G0,2,4,8,A,C;101,5,7,B.22,A.43,9.73,6,9.A3,6,9.C4,8.G4,8:XDW45K2OI5ICIICOGRRGMD6DJMRRW47E77CMKXE428W6J7KG6M8YJ8OX62YGXEEK5YWDJ4YO528C", "hasFlower": 0, "hasSeason": 0}, {"id": "48", "q": "000,2,5,7,A,C.21,3,5,7,9,B.41,3,5,7,9,B.61,3,6,9,B.80,2,4,6,8,A,C.A1,3,5,7,9,B.C0,4,6,8,C.D2,<PERSON><PERSON>E5,7.F1,3,9,B.G5,7;105,7.11,B.23,9.36.42,4,8,A.82,4,6,8,A.A5,7.D2,A.E6.F3,9;233,9.83,5,7,9.D2,A:71DAL54761RWWZHM0S4WLRSDQZ165A762RMQK336WQK0M0E5AE445LPPE2QHHZK1RPSSL3Z2P32MH70KEA", "hasFlower": 0, "hasSeason": 0}, {"id": "49", "q": "000,2,6,A,C.14,8.20,C.32,4,6,8,A.40,C.53,5,7,9.60,C.72,4,6,8,A.80,C.92,5,7,A.B0,2,4,6,8,A,C.D0,5,7,C.E3,9;100,C.20,C.33,5,7,9.54,8.60,C.73,5,7,9.80,C.95,7.A2,A.B4,8.D0,6,C.E3,9;220,C.44,8.76.95,7.A2,A.B4,8.D0,C:RCW8ZF8X3SWJP3DLAHKPOCXTQ1W068DRTO1221MSAISXCJ6SZMTQTJ0HIKHIFF9JWMMR699IDHLRC6X221FKD89K", "hasFlower": 0, "hasSeason": 0}, {"id": "50", "q": "000,3,6,8,B,E.21,3,6,8,B,D.41,3,B,D<PERSON>55,9.62,7,C.70,4,A,E<PERSON>82,7,C.95,9.A1,3,B,D.C0,3,5,9,B,E.E0,5,7,9,E;117.23,B.31,D.43,B.71,4,7,A,D.95,9.A3,B.C3,5,9,B.D0,E.E7;295,9.A3,B.C4,A.D0,E:9A5YNQB794HFEVAX40Y8TYGNS6RXRAEVE60B6N9I6BH37GTEDK1N90SIKQ3KHD380H5FK5A53B1Y", "hasFlower": 0, "hasSeason": 0}, {"id": "51", "q": "000,2,4,6,8,A,C.22,4,6,8,A.40,3,5,7,9,C.60,2,4,6,8,A,C.80,2,4,6,8,A,C.A0,2,4,6,8,A,C.C0,2,5,7,A,C.E2,4,6,8,A.G0,2,4,8,A,C;103,9.40,3,5,7,9,C.61,B.75,7.A5,7.B1,B.D5,7:MQLOVH9QAELQMKR0IHI5HRBOBRB6OKYA5V9I694ET0ELNQYN9H651B5Y1OR4MV460MIE0TYV4L", "hasFlower": 0, "hasSeason": 0}, {"id": "52", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.41,3,5,7,9,B.60,2,6,A,<PERSON><PERSON>74,8.81,6,B.94,8.A0,6,C<PERSON>B2,<PERSON><PERSON>C0,4,6,8,C<PERSON>D2,<PERSON><PERSON>E4,6,8.F0,2,A,C.G4,6,8;102,5,7,A.20,3,9,C.35,7.66.A6.C0,4,8,C.E2,4,8,A.F0,C.G4,8:4G5MANKQXPG4XU7P77HYKM286U865GX6G5FPIXIQNFARAFUK2388QMAH3RNK5I32RMN2U37QP6RFYI", "hasFlower": 0, "hasSeason": 0}, {"id": "53", "q": "000,3,6,8,B,E.20,4,6,8,A,E.41,5,7,9,D.60,4,6,8,A,E.72,C.80,4,7,A,E.A0,5,7,9,E.C0,3,5,7,9,B,E.E0,2,4,7,A,C,E;103,7,B.10,E.25,9.37.41,5,9,D.57.60,E<PERSON>73,B.80,E.A5,7,9.B0,E<PERSON>C6,8.D0,3,B,E.E7:ZI6GE0LNEE0T6MC3TT49LF90NZR9GZYNROR6TCLLMFAIYZAJR96JOJ4Y0AIOAO3M34JYICF4FCGGN3EM", "hasFlower": 0, "hasSeason": 0}, {"id": "54", "q": "000,2,4,6,8,A,C<PERSON>21,3,9,B.35,7.41,B<PERSON>55,7.60,C<PERSON>72,5,7,A.80,C.92,6,A.B0,5,7,C.D1,4,6,8,B.F1,3,9,B.G5,7;102,4,8,A.22,A.36.56.60,C.72,6,A.92,6,A.B0,6,C.D6.F2,A.G6;212,A.46.66.B6.D6.F2,A:YVKIRHU00KYGVKOO0YCA4PRIEM0VFDO4FQUODE1GU1YFHH4KGQFMXG1AU1A55PEDREAV4HRDXC", "hasFlower": 0, "hasSeason": 0}, {"id": "55", "q": "002,4,7,A,C.20,2,4,7,A,C,E.41,3,5,7,9,B,D.60,2,4,6,8,A,C,E.80,2,4,6,8,A,C,E.A0,2,5,7,9,C,E.C0,2,4,6,8,A,C,E.E1,3,B,<PERSON>;114,A.32,C.45,9.51,D.66,8.73,B.90,2,6,8,C,E.B2,5,9,C:X70B8L2GAF4EBJWLV037TTLVGU1SU39UOTR4AJBCD6GRI9CIZWR8R12QLSAWGXZA0DUWE6BT0FOQ", "hasFlower": 0, "hasSeason": 0}, {"id": "56", "q": "002,4,6,8,A.10,C.23,5,7,9.30,C.46.50,2,A,C.65,7.70,2,A,C.84,6,8.90,<PERSON><PERSON>A2,6,A.B0,C.C2,4,8,A.D6.E0,2,4,8,A,C;102,4,8,A.10,C.24,8.36.50,2,A,C.65,7.A0,2,A,C.D4,8.E0,C;210,C.24,8.51,B.A1,B.D4,8.E0,C:2X0GOKWZ33XJ0AACWER5E3EHOT0FREFKNJ5MB7AGNBGML7MYV53W2OATCROVLZMZ67BZH56URUGBY7W0", "hasFlower": 0, "hasSeason": 0}, {"id": "57", "q": "000,3,5,7,9,B,E.22,5,7,9,C.30,E<PERSON>44,6,8,A<PERSON>51,D<PERSON>63,5,7,9,B.70,E.82,6,8,C<PERSON>90,E<PERSON>A2,5,9,C<PERSON>B0,7,<PERSON><PERSON>C4,<PERSON><PERSON>D0,6,8,<PERSON><PERSON>E3,B<PERSON>F5,7,9.G0,2,C,E;100,6,8,E.26,8.30,E.44,6,8,A.64,6,8,A.81,6,8,D.A1,5,9,D.B7.D0,6,8,E.F6,8.G1,D:IWWKFVK0DMUVGGPFA29677AG1AS0FGD690MM7KS2YP2ITH69106YUWF1YPDUKVTSDH92U1TAYHWOOVITO7IHMPSO", "hasFlower": 0, "hasSeason": 0}, {"id": "58", "q": "000,4,6,8,A,E.12,C.20,4,6,8,A,E.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.81,3,5,9,B,D.97.A0,3,B,E.B5,9.C1,3,7,B,D.E0,2,4,6,8,A,C,E;105,7,9.26,8.40,2,7,C,E.60,2,4,7,A,C,E.81,D.97.A0,3,B,E.C1,7,D.D3,B.E7:BZS0P564UCGAG0SU60BC4JBXDQ55GJCRWH3HJLQJ1X4D0WHS51LX1RH3QAP1XLGAB4RZ6DRL3CWAPDUW36SQUP", "hasFlower": 0, "hasSeason": 0}, {"id": "59", "q": "000,2,4,6,8,A,C.20,5,7,C.33,9.40,5,7,C<PERSON>52,A.60,6,<PERSON><PERSON>72,<PERSON><PERSON>80,5,7,C.92,A<PERSON>A4,8.B0,6,<PERSON><PERSON>C3,9.D0,5,7,C<PERSON>E2,A;103,5,7,9.33,6,9.40,C.60,6,C.72,A.85,7.B0,C<PERSON>C3,6,9.E2,A;205,7.33,9.40,C.60,C.72,<PERSON><PERSON>B0,C<PERSON>C3,9;333,9.40,C.B0,C.C3,9:HWJY3GFKHZ71PHMYYAC2EC60KH816P309ZCWZMIL9GI13NF5SA8RZBBTESRM9RG1ARNKB8KYA738WJ5W9LGB2CMT", "hasFlower": 0, "hasSeason": 0}, {"id": "60", "q": "000,4,6,8,C.12,A.20,6,C.33,9.41,5,7,B.53,9.60,6,C<PERSON>73,9.81,5,7,B.A1,3,5,7,9,B.C0,2,4,6,8,A,C.E0,3,6,9,C.G0,2,4,6,8,A,C;104,8.11,B.36.42,A.56.60,C.76.A1,5,7,B.C4,6,8.E6.F0,C.G3,9;204,8.11,B.46.60,C.A5,7.C5,7:XTKGP17RI3HVIU7HTUHSOOFJVL7U1IGGBF364S3H1K2437J1SX4EVERR6S0FF00V26TTR24M0G6MPBID2PDPLU", "hasFlower": 0, "hasSeason": 0}, {"id": "61", "q": "000,3,5,7,9,C.20,2,5,7,A,C.40,3,5,7,9,C.62,4,6,8,A.70,C.83,6,9.A0,2,4,8,A,C.C0,2,4,6,8,A,C.E0,4,6,8,C;104,8.10,C.26.40,3,5,7,9,C.64,8.83,9.A0,C.B3,9.C1,5,7,B.E0,C;226.40,3,5,7,9,C.64,8.B3,9:MJHLTM9Z3HC4FTCK6Z945S2TXY9ZPT5ZFALYCBHCKBI2LYR6AP44SBBSPP2L92V1IVX17RJHF57VY5F3VS", "hasFlower": 0, "hasSeason": 0}, {"id": "62", "q": "000,2,4,6,8,A,C.21,3,6,9,B.41,4,8,B.56.60,2,4,8,A,C.80,2,4,8,A,C.A0,2,4,8,A,C.B6.C0,2,4,8,A,C.E0,3,5,7,9,C.G0,2,4,8,A,C;105,7.12,A.44,8.51,B.63,9.80,4,8,C.A2,A.C1,3,9,B.E0,4,8,C.G2,4,8,A:B9DCHDLJ0QGENW0MKM0CMF72PEIJT9KJBV4MHH07D7EXJHXV7EP4N329PTIC16G53195D5FLWQCPGUG6U5", "hasFlower": 0, "hasSeason": 0}, {"id": "63", "q": "000,2,5,9,C,E.20,4,6,8,A,E.40,2,5,9,C,E.57.60,3,5,9,B,E.77.80,2,5,9,C,E.A0,3,5,7,9,B,E.C0,3,5,9,B,E.E1,3,5,9,B,D;101,D.15,9.20,E.41,5,9,D.67.81,D.A5,9.D3,B.E5,9;201,D.15,9.81,D.D3,B.E5,9:G5UY8E9W71SGN76OSAGQ5MME7Q1MFK88OVAX9UBNVKYM1G8X3WNA9Y6ASVK5W3K71XBN5BWVFQ9YSXQB", "hasFlower": 0, "hasSeason": 0}, {"id": "64", "q": "000,2,4,6,8,A,C.20,2,4,6,8,A,C.40,3,5,7,9,C.60,2,6,A,C.82,6,A.90,C.A2,4,6,8,A.B0,C.C2,5,7,A<PERSON>D0,C<PERSON>E3,5,7,9;101,3,6,9,B.20,3,5,7,9,C.40,3,9,C.56.60,C.76.90,C.A4,6,8.B0,<PERSON><PERSON>C5,7.D0,<PERSON><PERSON>E3,6,9:JDTCKBCCKV2QRFOX81X1GTUZGE2ADVIPKW4B6Q6MAA4NQ49CRB8JU1MOISH4XZO0PS09ANFX1EHWBQKO", "hasFlower": 0, "hasSeason": 0}, {"id": "65", "q": "001,3,5,7,9,B,D.21,3,5,7,9,B,D.40,6,8,E.52,4,A,C.60,6,8,E.74,A.80,6,8,E.92,C.A0,4,7,A,E.C0,3,5,9,B,E.D7.E3,5,9,B;101,4,A,D.27.46,8.60,6,8,E.86,8.90,E.C3,B.D7.E3,5,9,B;201,D.67.86,8.C3,B.E4,A;301,D.67:L5DAEXJG50OKL4KOPB8QCZPEHA5QAOBAD6Q8JXIXIXID5CZZDK6LJ6SPGZPSBK70H4OIB69E74JLC14C19EQ", "hasFlower": 0, "hasSeason": 0}, {"id": "66", "q": "001,3,9,B.15,7.20,2,A,C.34,6,8.40,C<PERSON>54,6,8.60,C.74,6,8.80,C.94,8.A0,6,C<PERSON>B3,9.C0,6,<PERSON><PERSON>D2,<PERSON><PERSON>E0,5,7,C;101,B.15,7.20,C.35,7.50,4,8,C.74,8.94,8.A0,C.C0,6,C.E0,5,7,C;220,C.35,7.64,8.94,8.D0,C:JCH3UCU1KGXS5CJO37GLS024O337J7UC5JX5TSOK70K42Y29TN44VOUXH0YXL0G9S52TVHKTG1HN", "hasFlower": 0, "hasSeason": 0}, {"id": "67", "q": "001,3,5,7,9,B.20,2,5,7,A,C.40,3,5,7,9,C.62,4,8,A.76.80,3,9,C.96.A1,3,9,B.C1,3,5,7,9,B.E3,5,7,9.F0,C<PERSON>G5,7;101,3,5,7,9,B.21,6,B.53,9.76.80,3,9,C.96.A2,A.C3,5,7,9.F6;216.53,9.83,6,9.A2,A.C3,6,9.F6:XZSNOO4FJJZO1QC1IEJ8Z68UMEMQ8225QUUX18OMLLXKDQBDFFL4SNNFB2AJ20ML4U1XI600DANK54D0CZ", "hasFlower": 0, "hasSeason": 0}, {"id": "68", "q": "001,3,5,7,9,B.21,3,5,7,9,B.40,2,5,7,A,C.61,5,7,B.80,2,A,C.95,7.A0,2,A,C.B5,7.C0,2,A,C.D4,6,8.E2,A;101,3,6,9,B.21,5,7,B.40,6,C.61,5,7,B.80,2,A,C.95,7.A0,2,A,C.B6.C0,C.D2,5,7,A:OW1ONDAWIQFR4MILFKKORVHAOTK5HNTDD10WIQWL0Q2KQCHLJ1V0DF1VIJAJ5CL0M2AJR2FV24RH", "hasFlower": 0, "hasSeason": 0}, {"id": "69", "q": "000,2,4,6,8,A,C.21,3,6,9,B.41,3,5,7,9,B.60,3,5,7,9,C.80,2,4,6,8,A,C.A0,2,4,6,8,A,C.C0,2,4,8,A,C.D6.E0,2,A,C;106.12,A.26.32,A.53,6,9.75,7.81,B.94,8.A6.B1,B.D0,6,C.E2,A;212,A.26.81,B.D6.E2,A:S9EJ13KXJYKC52V094ED5JHC410B2P1ZZ1TISP99TJB03I4V3STXBYCKHBXPCXTPHS032IYKU2D4IUYH", "hasFlower": 0, "hasSeason": 0}, {"id": "70", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.42,5,7,9,C.50,E.63,6,8,B.70,E.82,6,8,C.A0,2,4,7,A,C,E.C0,5,7,9,E.D2,C.E0,5,7,9,E;106,8.13,B.21,5,9,D.50,6,8,E.70,7,E.92,7,C.A0,E.D1,5,9,D:HHFMM0KZ44CDQDVF6IE7LZ9MLGU4ANV6GNZLR1GGDEOUQ26RJA4HAKH07I882DY6NOYN1LAM9JCZ", "hasFlower": 0, "hasSeason": 0}, {"id": "71", "q": "002,5,7,A.10,C.22,4,8,A.30,6,C<PERSON>43,9.50,C.63,5,7,9.80,2,5,7,A,C.A2,4,8,A.B0,C.C2,4,8,A.D6.E0,3,9,C<PERSON>F5,7.G0,3,9,C;102,5,7,A.22,4,8,A.30,C.43,9.82,5,7,A.A3,9.D3,9.E0,C.F4,8;206.12,A.33,9.86.D3,9:0WWPR7T66N9EDHTL3D0B7FLSPZCOBGGOLQEO9B0BNUS70UFNZNCRYU7VSWOZE3YFLAAW8Q6S8HU6FVZE", "hasFlower": 0, "hasSeason": 0}, {"id": "72", "q": "001,3,5,7,9,B.21,4,8,B.40,2,A,C.60,2,5,7,A,C.80,2,6,A,C.A0,2,5,7,A,C.C0,2,A,C.E1,3,5,7,9,B.G0,2,5,7,A,C;101,6,B.14,8.21,B.50,2,A,C.65,7.71,B.86.91,B.C0,2,A,C.E5,7.F1,B.G6;211,4,8,B.50,C.76.C0,C.E5,7.F1,B.G6:0QQ8PBI7EZTP83WMG8Q86VM5HV9S7S49926IOA1MJ3454J76S0P02TVD5JEN0AJSV5HG26PMRN4TZWDRB9OTQ712", "hasFlower": 0, "hasSeason": 0}, {"id": "73", "q": "000,2,4,6,8,A,C.20,3,5,7,9,C.40,3,6,9,C.60,3,6,9,C.81,3,5,7,9,B.A2,6,A<PERSON>B0,C<PERSON>C3,6,9.D0,C.E2,4,8,A.F0,6,C.G2,A;100,2,4,8,A,C.23,5,7,9.30,C.46.53,9.81,3,6,9,B.C3,9.E0,3,9,C.F6.G2,A;200,C.14,8.36.53,9.82,A.C3,9:OX3IINNY4226QX9XOIJ0REU4MZVXZ7NKPY096YZVPJ76Y4ATET76RREJJM4PTA7PM2QEIZRKU29QAO3TMOQUU9AN", "hasFlower": 0, "hasSeason": 0}, {"id": "74", "q": "001,3,5,7,9,B,D.20,3,5,7,9,B,E.40,2,5,9,C,E.60,2,4,A,C,E.76,8.80,3,B,E.95,7,9.A0,2,C,<PERSON><PERSON>B4,A<PERSON>C1,6,8,D.D3,B.E0,5,7,9,E.F3,B.G0,5,7,9,E;123,6,8,B.30,E.42,C.62,C.76,8.95,9.A1,D.C6,8.E0,5,7,9,E.F3,B.G0,E:WK6VCVGET5I2N5I2DET5RTZ7SJ737D5RLLAQSEO2CSWW33A3HOK1LSQ6CA1X1ZQDEAN2HDIGIRK7KR1QJXTWCL", "hasFlower": 0, "hasSeason": 0}, {"id": "75", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,2,4,6,8,A,C,E.60,2,4,6,8,A,C,E.81,3,6,8,B,D.A0,2,4,7,A,C,E.C0,2,4,6,8,A,C,E.E1,3,5,7,9,B,D.G1,3,5,7,9,B,D;100,E.22,C.37.42,C.61,3,B,D.82,7,C.A2,C.C1,D.E7.F2,C:O6PXVN4H195FB6HBFTSLJKMNPLOUAP7JVEDMAE9KHYS35DYO4A3STRXL47EOM3PBR66UURBS77A3HCERUMKL1C4K", "hasFlower": 0, "hasSeason": 0}, {"id": "76", "q": "003,5,7,9.10,C.23,6,9.31,B<PERSON>45,7.53,9.60,6,C<PERSON>72,A<PERSON>80,6,<PERSON><PERSON>92,A<PERSON>A0,5,7,<PERSON><PERSON>B2,<PERSON><PERSON>C4,6,8.D0,2,A,<PERSON><PERSON>E4,6,8.F0,<PERSON><PERSON>G3,5,7,9;103,6,9.10,C.23,6,9.45,7.53,9.60,C.81,B.B5,7.C2,A<PERSON>D4,6,8.E0,<PERSON><PERSON>F4,6,8;206.10,C.23,9.45,7.60,C.D5,7:8FRJLL8R5IWINBA1U3P2BD3D6ZK96GJPG3BWZIVAUARJRLAXP5GUY8BY15KJ6KGYVKPFUI6XN52XFZF3ZX89LY", "hasFlower": 0, "hasSeason": 0}, {"id": "77", "q": "000,3,9,C.15,7.21,3,9,B.35,7.40,3,9,C<PERSON>56.61,3,9,B.75,7.80,3,9,C.A0,2,5,7,A,C.C1,3,6,9,B.E1,5,7,B.F3,9.G5,7;115,7.22,A.35,7.40,C.56.75,7.A2,5,7,A.C2,6,<PERSON><PERSON>E5,7.F3,9.G5,7;216.35,7.56.B2,A.C6.E5,7.G6;335,7.E5,7:GM4FLOOHE2JNYQRLLXSB16E2YAMVJG96HSB0Z6F0DEKKGHI2L3ZQU6E3V7UUXKG02RR4NDJA1HJ7FR90IFKU", "hasFlower": 0, "hasSeason": 0}, {"id": "78", "q": "000,2,4,6,8,A,C.20,2,4,8,A,C.41,3,6,9,B.60,2,4,8,A,C.80,2,6,A,C.A1,3,5,7,9,B.C0,2,4,6,8,A,C.E1,3,5,7,9,B.G0,3,5,7,9,C;102,4,8,A.22,4,8,A.41,6,B.53,9.60,C.81,6,B.A3,5,7,9.C2,5,7,A.E6.F4,8.G6:8I3R8CDEBF0SXA1JU65NVQWNEYCFZKC587VVR51B0DPS0JL1NFPU1EDT7WQOQYO8FCDOIZ63ETQKLV50XONA", "hasFlower": 0, "hasSeason": 0}, {"id": "79", "q": "000,3,5,7,9,C.22,4,8,A.30,6,C.42,A<PERSON>50,4,6,8,C<PERSON>62,A<PERSON>70,4,6,8,C.82,A.90,5,7,C<PERSON>A2,A.B0,4,6,8,C<PERSON>C2,A<PERSON>D0,5,7,<PERSON><PERSON>E2,<PERSON><PERSON>F4,8.G2,6,A;103,5,7,9.22,A.30,6,C.50,2,5,7,A,C.72,5,7,A.91,B.A5,7.B0,3,9,C.C5,7.D0,C.E2,A.G2,A:FLRUI9FUAUQODTOFLPQZH2AA0VURSDSZK9FPKDVN344A6MDON6Q37E9NMI04LMI6INKG9H4QEE0MK06LOTGHH7E2", "hasFlower": 0, "hasSeason": 0}, {"id": "80", "q": "000,2,4,6,8,A,C,E.20,2,4,A,C,E.36,8.40,E<PERSON>52,5,9,<PERSON><PERSON>60,<PERSON><PERSON>74,7,A.82,<PERSON><PERSON>96,8.A1,4,A,D.B6,8.C0,2,C,E.D5,9.E0,3,7,B,E.F5,9.G0,2,7,C,E;100,3,6,8,B,E.21,4,A,D.40,E.60,E<PERSON>74,A.82,7,C.A4,A.B7.C1,D.E5,9.F7.G1,D:SM6PZ50QS7H97Q4X23XUNFTPT9NEYE80S6U9OWH6723MSL65WQJH5X2UOLHMGY8972QFMW334NNZJZX5ZUGW", "hasFlower": 0, "hasSeason": 0}, {"id": "81", "q": "000,2,4,6,8,A,C,E.20,2,4,6,8,A,C,E.40,2,4,7,A,C,E.60,2,5,7,9,C,E.80,3,5,7,9,B,E.A0,3,5,7,9,B,E.C0,2,5,7,9,C,E.E0,2,4,7,A,C,E.G0,2,4,6,8,A,C,E;101,3,B,D.17.25,9.52,C.85,9.A3,B.C2,C.G2,4,7,A,C:VGS7OKB5EK13NNSA3XIHOG3PWA2AH67AVSEHGP6U6XI3W7B2WUJSIPW12N1I7VONG52JVJHXE9XEP6OBJ5159B", "hasFlower": 0, "hasSeason": 0}, {"id": "82", "q": "000,2,6,A,C.22,4,6,8,A.40,2,4,8,A,C.56.62,4,8,A.80,3,5,7,9,C.A1,3,5,7,9,B.C0,2,4,6,8,A,C.E1,3,5,7,9,B.G1,5,7,B;100,6,C.24,8.32,A.53,5,7,9.74,8.94,8.A1,6,B.B4,8.C0,C.D2,5,7,A.G5,7;224,8.55,7.A1,5,7,B.D2,A:TYWUV6Q2E8K8PQ59SXWUSLSYY45XQYBBET0F45SMKVGUF9UV5LT0E6TMW299Q020EPBGF8WGXFX4VB6G2K84KP6P", "hasFlower": 0, "hasSeason": 0}, {"id": "83", "q": "000,2,4,6,8,A,C,E.20,2,5,7,9,C,E.40,3,5,7,9,B,E.60,2,5,7,9,C,E.80,3,5,7,9,B,E.A1,3,6,8,B,D.C0,2,4,A,C,E.D7.E1,3,5,9,B,D;100,3,B,E.15,7,9.22,C.37.44,A.50,6,8,E.77.83,B.96,8.A2,C.C1,D.E1,4,A,D:7BZT6DOKOQ9L37ZLVV26LJRHQ0BVYN7KJU1RT53UUM5KZCUID9M2WV59CP0DZ1HFPJ9NFPIWKY07LJ5033PD", "hasFlower": 0, "hasSeason": 0}, {"id": "84", "q": "000,3,5,7,9,C.24,6,8.31,B.43,5,7,9.50,C<PERSON>66.72,A<PERSON>84,6,8.92,A<PERSON>A5,7.B1,<PERSON><PERSON>C3,5,7,9.E1,3,6,9,B.G2,4,8,A;100,6,C.26.31,B.46.72,6,A.84,8.96.B1,5,7,B.C3,9.D6.E1,B.G2,A;************,8.A6.B1,B<PERSON>C3,9.D6;384,8.C3,9.D6;484,8:J54OQWYXY27XOG11LUBTT17HFXDLGGHQ3MZL3APSPO4YZDPGM5F7BAOIYULMKES17NPEBBJDMUIWU2KNHXDH", "hasFlower": 0, "hasSeason": 0}, {"id": "85", "q": "000,2,4,6,8,A,C.20,2,6,A,C.34,8.40,2,A,C<PERSON>55,7.60,C<PERSON>73,6,9.80,C.92,4,8,A.A0,6,C.B4,8.C0,2,6,A,<PERSON><PERSON>D4,8.E0,2,6,A,C.G1,3,5,7,9,B;102,5,7,A.32,A.50,6,C.73,9.94,8.B6.C0,4,8,C.E2,6,A.G1,3,9,B;206.32,A.73,9.94,8.E2,A:0W4IE32FZKEAZ7FCKAFO3A4HIHB0XV1WKFBY79EO4PBIVP2J7NWBCO7ZP9VK3H3AC9Q0PQCEI0JW429O2HNV1YXZ", "hasFlower": 0, "hasSeason": 0}, {"id": "86", "q": "000,3,5,7,9,C.20,2,5,7,A,C.40,3,5,7,9,C.60,2,4,6,8,A,C.80,2,4,8,A,C.A1,4,6,8,B.C0,2,4,6,8,A,C.E0,2,4,6,8,A,C.G1,4,8,B;100,C.21,5,7,B.44,8.56.62,A.70,4,8,C.91,4,8,B.B6.D4,8.F1,B.G4,8:AHAP3ERROXSO6XDWHIM6726PY6Y434001I0WSSPD0EBR87IBOBSBO48AMA32N7KPK72X3IXH2N14EERH", "hasFlower": 0, "hasSeason": 0}, {"id": "87", "q": "001,3,5,9,B,D.17.20,3,5,9,B,E.37.40,3,5,9,B,E.61,3,5,7,9,B,D.80,2,5,7,9,C,E.A0,2,4,6,8,A,C,E.C0,4,6,8,A,E.E2,4,6,8,A,C.F0,E.G4,6,8,A;101,3,5,9,B,D.17.30,4,7,A,E.63,6,8,B.80,E.A2,5,7,9,C.C7.E4,7,A.G7:WJQHPLZJW6OOLTTEVVNDZ0AXJ0SWXGY56G6TZ5EQ1N3TXEC1K1XHY1C58KDA3OQ5EPAPSNDWJQZHHPN860ODCA0C", "hasFlower": 0, "hasSeason": 0}, {"id": "88", "q": "000,2,4,6,8,A,C,E.20,2,4,7,A,C,E.40,2,4,6,8,A,C,E.63,5,7,9,B.70,E.82,4,6,8,A,C.90,E.A2,5,7,9,C.C0,2,4,6,8,A,C,E.E0,2,4,6,8,A,C,E.G1,3,5,7,9,B,D;102,C.20,E.37.42,C.56,8.74,6,8,A.A6,8.C6,8.D2,C.E7.F5,9.G2,C:X1GU5W16CQ417R0XBFDTCC3A5U0XNAWVD2NF0MXVF2Q32FGQYMYGEBWP4YVZ163G52YL50VZS3EECRTRPSLE6QW67R", "hasFlower": 0, "hasSeason": 0}, {"id": "89", "q": "000,2,4,6,8,A,C.20,4,6,8,C.41,3,5,7,9,B.60,3,5,7,9,C.80,2,4,6,8,A,C.A0,3,5,7,9,C.C0,4,6,8,C.D2,A.E0,5,7,C.G1,3,5,7,9,B;102,5,7,A.24,8.43,9.60,6,C.85,7.A6.B0,C.E0,C.G1,4,6,8,B;202,5,7,A.60,6,C.B0,C.G4,8:LGV3JAPR9OAZHTG0G3PAEJAJOR6ZRTVSF8T9B5SONL2703VPE36H5MVKI75R5BEG6JT8F6X27XWPONWE878KIKKM", "hasFlower": 0, "hasSeason": 0}, {"id": "90", "q": "000,2,4,7,A,C,E.20,3,5,7,9,B,E.41,3,5,7,9,B,D.61,3,6,8,B,D.80,4,6,8,A,E.92,C.A0,4,7,A,E.C0,4,7,A,E.E0,2,4,6,8,A,C,E;100,2,4,7,A,C,E.20,3,7,B,E.35,9.47.52,C.66,8.80,4,6,8,A,E.A4,A.B0,7,E.D0,7,E:BTM3N9X2Y4YGM0XBPCCC9N0XH1CMK17KH2Y9V7E4793TGVYM3WXP0OB4DKGLVBE7HLO0AG4VELADHN232NWLEK", "hasFlower": 0, "hasSeason": 0}, {"id": "91", "q": "000,2,5,7,A,C.25,7.40,2,4,6,8,A,C.60,3,5,7,9,C.80,2,A,C.A0,5,7,C<PERSON>B3,9.C0,5,7,C<PERSON>D2,<PERSON><PERSON>E4,6,8.G1,3,5,7,9,B;100,C.35,7.42,A.50,5,7,C.81,B.A5,7.B0,<PERSON><PERSON>C5,7.D2,A<PERSON>E5,7.G1,3,9,B;235,7.42,A.55,7.B0,C<PERSON>C5,7.D2,<PERSON><PERSON>E5,7.G1,B:BBT8BLTO7EIZ3IXI5PLR1O2FV2TDX7ERQOOL9PK8X1GZTEVZVDV8I2QPD81XQ3E53YY1GGRY2FBQG5PLY9K3Z5RD", "hasFlower": 0, "hasSeason": 0}, {"id": "92", "q": "000,2,4,6,8,A,C.20,C.34,8.40,2,6,A,C.54,8.60,6,C.81,3,5,7,9,B.A1,5,7,B.C0,2,4,6,8,A,C.E1,4,8,B.G0,2,6,A,C;100,3,6,9,C.34,8.60,6,C.81,3,5,7,9,B.A1,B.B5,7.C1,3,9,B.E4,8.F1,B;260,6,C.86.A1,B.B6.C2,A:5YAF6SN069XQC8UAJTE89INESHZ24ZYRT9NQ6ECR8U6CZJHXU425TSG4QEFUH45IIRL5G09ILHCYSNZ8YRTQ", "hasFlower": 0, "hasSeason": 0}, {"id": "93", "q": "000,3,5,7,9,B,E.20,2,4,6,8,A,C,E.40,2,4,7,A,C,E.62,5,7,9,C.70,E.82,4,7,A,C.A0,2,6,8,C,E.B4,A.C0,2,7,C,E.D4,A.E1,6,8,D;104,7,A.21,6,8,D.57.62,5,9,C.70,E.82,4,A,C.97.C0,2,C,E.D7.E1,D;207.21,6,8,D.83,B.C0,2,C,E:JO9FL2I778BWM04JFGTFCSX76U7UL8SSAMFE1Z90AWU0IAI9MC1ZZMALZ4PUG64OGS8E6T01XG812BWI9PW56EE4L5", "hasFlower": 0, "hasSeason": 0}, {"id": "94", "q": "000,3,5,9,B,E.17.20,4,A,E.36,8.40,2,C,E<PERSON>54,7,A.60,2,C,E.74,6,8,A.81,D.93,6,8,B.A0,E.B2,4,6,8,A,<PERSON><PERSON>C0,E.D2,5,9,C.E0,7,E.F4,A.G1,6,8,D;100,3,B,E.17.24,A.30,6,8,E.51,3,B,D.67.74,A.81,D.93,B.A0,6,8,E.B3,B.C1,<PERSON>.E0,E.F4,7,A:BP7HYIULN6KK59QCEZDLN56AC3EV4M0WN8WPAPFI3KQDIQNDJ90DMEBWI7ZKEAPZ6YU4HFJXYA16BJVYMSJZWQXMS81B", "hasFlower": 0, "hasSeason": 0}, {"id": "95", "q": "000,3,5,7,9,C.20,2,5,7,A,C.40,2,4,6,8,A,C.60,2,4,8,A,C.76.83,9.96.A0,2,4,8,A,C.C0,2,6,A,C.D4,8.E0,C.F2,5,7,A.G0,C;100,3,9,C.16.22,A.35,7.40,2,A,C.63,9.83,9.A2,4,8,A.C0,C.D4,8.F2,5,7,A;216.22,A.35,7.F2,5,7,A:IHUPHWXOL8XBKTBBWLIGOPYAVFDZC0TU9GW7X9YCY9ZBI8NKFAXCA0Q0WDIZGVKVP8HAUYEHNQZ79GD0DCPUE8VK", "hasFlower": 0, "hasSeason": 0}, {"id": "96", "q": "000,4,6,8,C.20,2,4,6,8,A,C.41,3,5,7,9,B.60,2,5,7,A,C.82,4,6,8,A.A1,3,5,7,9,B.C1,3,6,9,B.E1,3,9,B.F5,7.G0,C;105,7.10,C.24,6,8.42,5,7,A.75,7.83,9.A1,5,7,B.B3,9.D2,A.F6.G0,C;205,7.25,7.42,A.75,7.A5,7.D2,A:MFRUOSHFRNKYBHEBNAAVLSVEP8MEROI19559MNHKU91BRAFLIYVPGAPHBVEQ6MSGFOITLLS6UP69NKI6UQO8KT", "hasFlower": 0, "hasSeason": 0}, {"id": "97", "q": "000,2,4,6,8,A,C,E.22,5,7,9,C.40,4,7,A,E.52,C.60,4,6,8,A,E.72,C.80,4,6,8,A,E.A0,2,4,6,8,A,C,E.C0,2,4,6,8,A,C,E.E1,4,6,8,A,D.G2,5,9,C;103,B.22,C.44,A.52,7,C.70,4,6,8,A,E.94,6,8,A.A0,E.B7.C2,4,A,C.D6,8.E4,A:4W3X4O8S1EVCQUP832TXBQI6GDIHLGDCFTC9QS9D6CK3P5N3LBKPPJBVE5OKX8HQN6A12ALXADA1WKJ4LFB77648U1", "hasFlower": 0, "hasSeason": 0}, {"id": "98", "q": "000,2,4,6,8,A,C,E.20,2,6,8,C,E.40,3,5,9,B,E.61,3,5,7,9,B,D.81,3,5,7,9,B,D.A0,6,8,E.B3,B.C1,5,9,D.D7.E1,3,5,9,B,D;100,2,4,7,A,C,E.20,2,6,8,C,E.40,3,5,9,B,E.61,D.73,B.A0,6,8,E.B3,B.C1,D.D6,8.E3,B:V6TQ3O4EQK5HSG6OKGRQKGNXJBO7MFVZZXPFC439LO6XCBDSPNYQMN7CLHR9BP7I6YD478CGEPTJB4FN5F8XIK", "hasFlower": 0, "hasSeason": 0}, {"id": "99", "q": "000,2,4,6,8,A,C,E.20,2,5,7,9,C,E.40,2,4,7,A,C,E.61,3,5,7,9,B,D.80,4,7,A,E.A0,2,4,7,A,C,E.C0,3,5,7,9,B,E.E1,3,5,7,9,B,D.G1,3,5,9,B,D;101,3,7,B,D.25,9.30,E.43,7,B.51,D.64,A.A4,A.C3,5,9,B.D7.E4,A.G2,4,A,C:4XOB9DVIKFBD9D85OPAEVNYOJJ4EDMPURKI2MS61UEPQE8G8NRFG0211MXVYJHWA6S5YH8JS0YXXPR14HO4QHSWVRM", "hasFlower": 0, "hasSeason": 0}, {"id": "100", "q": "000,2,5,9,C,E.17.20,3,5,9,B,E.37.42,4,A,C.56,8.60,2,C,E.75,9.80,2,C,E.95,9.A1,D.B3,5,7,9,B.C0,E.D3,5,7,9,B.E1,D;101,D.16,8.24,A.42,4,A,C.61,D.75,9.80,E.A1,D.B4,A.C6,8.D4,A<PERSON>E1,D;224,A.44,A.B4,A.C6,8.D4,A:15BD6OE47UC993KRBDHHOY80W173TD5R1AXB8S6WS4RRSUMCTZUXZWI0MN6FFAQBMINKM7FAD16EWY7QSUAF", "hasFlower": 0, "hasSeason": 0}]