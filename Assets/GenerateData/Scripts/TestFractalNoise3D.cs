using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities.Extensions;
using Sirenix.OdinInspector;
using UnityEngine;

namespace GenerateData.Scripts
{
    public class TestFractalNoise3D : BhMonoBehavior
    {
        public int width = 10;  // <PERSON><PERSON><PERSON> thước theo chiều X
        public int height = 10; // <PERSON><PERSON>ch thước theo chiều Y
        public int depth = 10;  // <PERSON><PERSON>ch thước theo chiều Z
        public float scale = 0.1f;  // Đ<PERSON> chi tiết của noise
        public int octaves = 4;  // Số tầng của FBM (quyết định độ chi tiết)
        public float persistence = 0.5f;  // Mức độ giảm dần biên độ ở mỗi tầng
        public float lacunarity = 2.0f;  // Mức độ thay đổi tần số ở mỗi tầng
        
        public GameObject tilePrefab; // Tile mẫu

        
        /*void Start()
        {
            GenerateFractalNoise();
        }*/

        [Button]
        void GenerateFractalNoise()
        {
            transform.DestroyChildrenImmediate();
            
            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    for (int z = 0; z < depth; z++)
                    {
                        float value = 0.0f;
                        float amplitude = 1.0f;
                        float frequency = scale;

                        // FBM (Fractal Brownian Motion) tổng hợp nhiều tầng noise
                        for (int i = 0; i < octaves; i++)
                        {
                            value += Mathf.PerlinNoise((x + frequency) * scale, (y + frequency) * scale) * amplitude;
                            frequency *= lacunarity;
                            amplitude *= persistence;
                        }

                        // Điều chỉnh giá trị noise theo ngưỡng để tạo hình dạng đẹp
                        if (value > 0.5f)
                        {
                            Vector3 position = new Vector3(x, y, z);
                            // Tạo đối tượng hoặc tile tại vị trí
                            Debug.Log($"Tile at {position} with value {value}");
                            
                            Instantiate(tilePrefab, position, Quaternion.identity, transform);
                        }
                    }
                }
            }
        }
    }
}