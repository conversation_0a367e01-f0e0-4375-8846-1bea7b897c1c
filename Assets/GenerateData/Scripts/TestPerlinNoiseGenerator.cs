using System.Linq;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Extensions;
using LibNoise.Filter;
using LibNoise.Primitive;
using Sirenix.OdinInspector;
using TMPro;
using UnityEngine;

namespace GenerateData.Scripts
{
    public class TestPerlinNoiseGenerator : BhMonoBehavior
    {
        public int width = 10;  // Số cột
        public int height = 10; // Số hàng
        public float scale = 1.0f; // Tỷ lệ độ chi tiết của Noise

        public float threshold = .5f;

        public GameObject tilePrefab; // Tile mẫu

        public Transform container;

        public bool isUsingLib;

        /*void Start()
        {
            GenerateLevel();
        }*/

        [Button]
        void TestCalculateValue(float x, float y = 6)
        {
            float noiseValue = Mathf.PerlinNoise(Mathf.Abs((float)x / width * scale), (float) y / height * scale);
            BhDebug.Log("Noise value: " + noiseValue);
        }

        static float SDFCircle(float x, float y, float cx, float cy, float r) {
            return Mathf.Sqrt((x - cx) * (x - cx) + (y - cy) * (y - cy)) - r;
        }


        [Button]
        void GenerateLevel()
        {
            BhDebug.Log("Start generating level");
            
            container.DestroyChildrenImmediate();

            var halfWidth = width / 2f;
            
            var halfHeight = height / 2f;
            
            for (float x = - halfWidth; x < halfWidth; x++)
            {
                for (float y = - halfHeight; y < halfHeight; y++)
                {


                    
                    /*// Tạo giá trị Perlin Noise cho mỗi ô
                    float noiseValue = Mathf.PerlinNoise((float)x / width * scale, (float) y / height * scale);

                    if (isUsingLib)
                    {
                        var noiseGenerator = new Billow();
                        
                        noiseValue = noiseGenerator.GetValue((float)x / width * scale, (float) y / height * scale);
                    }*/
                    
                    /*float periodicX = Mathf.Sin(x * scale) * 0.5f + 0.5f;
                    float periodicY = Mathf.Cos(y * scale) * 0.5f + 0.5f;
                    float noiseValue = Mathf.PerlinNoise(periodicX, periodicY);*/
                    
                    
                    /*float noise1 = Mathf.PerlinNoise((float)x / width * scale, (float) y / height * scale);
                    float noise2 = Mathf.PerlinNoise((width - x) * scale, (float) y / height * scale);
                    float noiseValue = (noise1 + noise2) / 2;*/
                    
                    //float noiseCal = Mathf.PerlinNoise((float)x / width * scale, (float) y / height * scale);
                    float noiseCal = Mathf.PerlinNoise(x * scale, y * scale);
                    float shapeMask = SDFCircle(x, y, halfWidth, halfHeight, Mathf.Sqrt(Mathf.Pow(halfWidth, 2) + Mathf.Pow(halfHeight, 2)));
                    float finalValue = noiseCal * shapeMask;
                    
                    //float noiseValue = Mathf.PerlinNoise(Mathf.Abs((float)x / width * scale), (float) y / height * scale);
                    float noiseValue = finalValue;
                    

                    
                    
                    BhDebug.Log("Noise value: " + noiseValue);

                    // Nếu giá trị noise lớn hơn ngưỡng (0.5), tạo một khối tại vị trí đó
                    if (noiseValue >= threshold)
                    {
                        Vector3 position = new Vector3(x, y, 0);
                        var item = Instantiate(tilePrefab, position, Quaternion.identity, container);
                        var children = item.GetComponentsInChildren<TMP_Text>();

                        var index = children.FirstOrDefault(t => t.gameObject.name == "Index")!.text = $"{(int)Mathf.Abs(x)} - {(int)y}";
                        var value = children.FirstOrDefault(t => t.gameObject.name == "Value")!.text =
                            noiseValue.ToString("F5");

                    }
                }
            }
        }
    }
}