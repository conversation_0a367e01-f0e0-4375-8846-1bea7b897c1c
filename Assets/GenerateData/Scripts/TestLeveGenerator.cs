using System;
using System.Collections.Generic;
using System.Linq;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Extensions;
using Game.Extensions;
using LibNoise.Primitive;
using Sirenix.OdinInspector;
using TMPro;
using UnityEngine;

namespace GenerateData.Scripts
{

#if UNITY_EDITOR
    
    public class TestLeveGenerator : SerializedMonoBehaviour
    {
        [SerializeField]
        private SymmetryType type;
        
        [SerializeField]
        private GameObject prefab;

        [SerializeField]
        private Transform containerRoot;

        [SerializeField]
        private Transform containerGenerator;

        [SerializeField]
        [TableMatrix(SquareCells = true)]
        private int[,] matrix;
        
        [SerializeField]
        private List<int> symmetryColumns;
        
        [SerializeField]
        private bool isToRight;



        [Header("Noise Generator")]
        public int row;
        
        public int col;

        public float threshold = .1f;

        public float scale = 1f;

        public SymmetryType symmetryType;
        
        [Button]
        void ConvertNumberToChar(int number)
        {
            BhDebug.Log("char " + number.ToChar());   
        }

        [Button]
        void ConvertCharToNumber(char character)
        {
            BhDebug.Log("number " + character.ToNumber());
        }

        [Button]
        void CreateSizeMatrix(int row, int column)
        {
            matrix = new int[row, column];
        }

        [Button]
        void GenerateLevel()
        {
            containerRoot.DestroyChildrenImmediate();
            
            /*int[,] matrix =
            {
                { 1, 2, 3 },
                { 4, 5, 6 }
            };*/
            
            BhDebug.Log("Row matrix " + matrix.GetLength(0).ToString());
            BhDebug.Log("Column matrix " + matrix.GetLength(1).ToString());

            for (int i = 0; i < matrix.GetLength(0); i++)
            {
                for (int j = 0; j < matrix.GetLength(1); j++)
                {
                    var position = new Vector3(j, -i, 0f);
                    GameObject go = Instantiate(prefab,position, Quaternion.identity, containerRoot);
                    go.transform.localPosition = position;
                    
                    var children = go.GetComponentsInChildren<TMP_Text>();
                    
                    var index = children.FirstOrDefault(t => t.gameObject.name == "Index")!.text = $"{(int)Mathf.Abs(i)} - {(int)j}";
                    var value = children.FirstOrDefault(t => t.gameObject.name == "Value")!.text =
                        matrix[i, j].ToString();
                }
            }
            
            
            int[,] result = LevelGenerateUtils.GetVerticalSymmetricMatrix(matrix, symmetryColumns, isToRight);
            
            containerGenerator.DestroyChildrenImmediate();

            for (int i = 0; i < result.GetLength(0); i++)
            {
                for (int j = 0; j < result.GetLength(1); j++)
                {
                    var position = new Vector3(j, -i, 0f);
                    GameObject go = Instantiate(prefab, position, Quaternion.identity, containerGenerator);
                    go.transform.localPosition = position;
                    
                    var children = go.GetComponentsInChildren<TMP_Text>();
                    
                    var index = children.FirstOrDefault(t => t.gameObject.name == "Index")!.text = $"{(int)Mathf.Abs(i)} - {(int)j}";
                    var value = children.FirstOrDefault(t => t.gameObject.name == "Value")!.text =
                        result[i, j].ToString();
                }
            }
        }


        [Button]
        void GenerateShape()
        {
            containerRoot.DestroyChildrenImmediate();
            
            BhDebug.Log("Row matrix " + matrix.GetLength(0).ToString());
            BhDebug.Log("Column matrix " + matrix.GetLength(1).ToString());

            for (int i = 0; i < matrix.GetLength(0); i++)
            {
                for (int j = 0; j < matrix.GetLength(1); j++)
                {
                    var position = new Vector3(j, -i, 0f);
                    GameObject go = Instantiate(prefab,position, Quaternion.identity, containerRoot);
                    go.transform.localPosition = position;
                    
                    var children = go.GetComponentsInChildren<TMP_Text>();
                    
                    var index = children.FirstOrDefault(t => t.gameObject.name == "Index")!.text = $"{(int)Mathf.Abs(i)} - {(int)j}";
                    var value = children.FirstOrDefault(t => t.gameObject.name == "Value")!.text =
                        matrix[i, j].ToString();
                }
            }

            var rowIn = matrix.GetLength(0) / 2;
            var colIn = matrix.GetLength(1) / 2;

            int[,] result = GetShape(matrix, IsInCircle);
            //int[,] result = GetShape(matrix, IsInCircle, new Vector2Int(rowIn, colIn), IsOutCircle);
            
            containerGenerator.DestroyChildrenImmediate();

            for (int i = 0; i < result.GetLength(0); i++)
            {
                for (int j = 0; j < result.GetLength(1); j++)
                {

                    if (result[i, j] != 0)
                    {
                        var position = new Vector3(j, -i, 0f);
                        GameObject go = Instantiate(prefab, position, Quaternion.identity, containerGenerator);
                        go.transform.localPosition = position;
                        
                    }
                    
                    /*
                    var children = go.GetComponentsInChildren<TMP_Text>();
                    
                    var index = children.FirstOrDefault(t => t.gameObject.name == "Index")!.text = $"{(int)Mathf.Abs(i)} - {(int)j}";
                    var value = children.FirstOrDefault(t => t.gameObject.name == "Value")!.text =
                        result[i, j].ToString();*/
                }
            }
        }


        [Button]
        void GenerateNoiseLevel()
        {
            var calculateRow = Mathf.FloorToInt((float)row / 2);
            var calculateCol = Mathf.FloorToInt((float)col / 2);
            
            BhDebug.Log($"Generating noise level {calculateRow}x{calculateCol}");
            
            var matrix = new float[calculateRow, calculateCol];

            var temp = new int[calculateRow, calculateCol];

            var noise = new SimplexPerlin();

            for (int i = 0; i < calculateRow; i++)
            {
                for (int j = 0; j < calculateCol; j++)
                {
                    matrix[i, j] = noise.GetValue(i * scale, j * scale);

                    if (matrix[i, j] >= threshold)
                    {
                        temp[i, j] = 1;
                    }
                    else
                    {
                        temp[i, j] = 0;
                    }
                }
            }

            //var result = LevelGenerate.GetVerticalSymmetricMatrix(temp, symmetryColumns, isToRight);
            //var result = LevelGenerate.GetSymmetryMatrix(temp, symmetryType);
            var result = LevelGenerateUtils.GetSymmetryMatrixMaxSize(temp, symmetryType);
            
            containerRoot.DestroyChildrenImmediate();

            for (int i = 0; i < result.GetLength(0); i++)
            {
                for (int j = 0; j < result.GetLength(1); j++)
                {
                    if (result[i, j]!= 0)
                    {
                        var position = new Vector3(j, -i, 0f);
                        GameObject go = Instantiate(prefab, position, Quaternion.identity, containerRoot);
                        go.transform.localPosition = position;
                        
                    }
                }
            }
            
            /*BhDebug.LogError("================================================");
            PrintMatrix(result);*/


            temp = CloneMatrix(result);

            var cellBoard = FindSquaresSymmetric(temp);
            
            /*BhDebug.LogError("================================================");
            PrintMatrix(cellBoard);*/
            
            containerGenerator.DestroyChildrenImmediate();

            for (int i = 0; i < cellBoard.GetLength(0); i++)
            {
                for (int j = 0; j < cellBoard.GetLength(1); j++)
                {
                    if (cellBoard[i, j]!= 0)
                    {
                        var position = new Vector3(j, -i, 0f);
                        GameObject go = Instantiate(prefab, position, Quaternion.identity, containerGenerator);
                        go.transform.localPosition = position;
                        
                    }
                }
            }
        }


        int[,] GetShape(int[,] originalMatrix, Func<Vector2, Vector2, bool> shapeFunction)
        {
            var rows = originalMatrix.GetLength(0);
            var columns = originalMatrix.GetLength(1);
            
            var result = new int[rows, columns];

            var size = new Vector2(rows, columns);

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < columns; j++)
                {
                    BhDebug.Log("Point " + new Vector2(i, j));
                    result[i, j] = shapeFunction(size, new Vector2(i + .5f, j + .5f)) ? 1 : 0;
                }
            }
            
            return result;
        }
        
        int[,] GetShape(int[,] originalMatrix, Func<Vector2, Vector2, bool> shapeFunctionIn, Vector2Int offsetSize, Func<Vector2, Vector2, bool> shapeFunctionOut)
        {
            var rows = originalMatrix.GetLength(0);
            var columns = originalMatrix.GetLength(1);
            
            var result = new int[rows, columns];

            var size = new Vector2(rows, columns);

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < columns; j++)
                {
                    result[i, j] = shapeFunctionIn(size, new Vector2(i + .5f, j + .5f)) &&
                                   shapeFunctionOut(size - offsetSize, new Vector2(i + .5f, j + .5f))
                        ? 1
                        : 0;
                }
            }
            
            return result;
        }

        bool IsInCircle(Vector2 size, Vector2 point)
        {
            var center = new Vector2(size.x / 2, size.y / 2);
            
            var radius = Mathf.Min(size.x, size.y) / 2;
            
            return Vector2.Distance(center, point) <= radius;
        }

        bool IsOutCircle(Vector2 size, Vector2 point)
        {
            var center = new Vector2(size.x / 2, size.y / 2);
            
            var radius = Mathf.Min(size.x, size.y) / 2;

            return Vector2.Distance(center, point) > radius;
        }
        
        
        
        
        
        
        
        
        
        
        [Button]
        void FindSquare()
        {
            int[,] matrix = {
                { 1, 1, 0, 1, 1 },
                { 1, 1, 0, 1, 1 },
                { 0, 0, 1, 1, 0 },
                { 1, 1, 1, 1, 0 }
            };

            int[,] regions = FindSquaresMatrix(matrix);
            PrintMatrix(regions);
        }

        static List<List<(int, int)>> FindSquares(int[,] matrix)
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            bool[,] visited = new bool[rows, cols];
            List<List<(int, int)>> result = new List<List<(int, int)>>();

            for (int i = 0; i < rows - 1; i++)
            {
                for (int j = 0; j < cols - 1; j++)
                {
                    // Kiểm tra nếu có thể tạo thành hình vuông 2x2 và chưa được đánh dấu
                    if (matrix[i, j] == 1 && matrix[i, j + 1] == 1 &&
                        matrix[i + 1, j] == 1 && matrix[i + 1, j + 1] == 1 &&
                        !visited[i, j] && !visited[i, j + 1] && 
                        !visited[i + 1, j] && !visited[i + 1, j + 1])
                    {
                        // Đánh dấu các ô đã sử dụng
                        visited[i, j] = visited[i, j + 1] = true;
                        visited[i + 1, j] = visited[i + 1, j + 1] = true;

                        // Lưu lại vùng tìm được
                        result.Add(new List<(int, int)>
                        {
                            (i, j), (i, j + 1),
                            (i + 1, j), (i + 1, j + 1)
                        });
                    }
                }
            }

            return result;
        }
        
        
        static int[,] FindSquaresMatrix(int[,] matrix)
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            int[,] result = new int[rows, cols]; // Ma trận lưu số vùng
            bool[,] visited = new bool[rows, cols];
            int regionId = 1; // ID vùng bắt đầu từ 1

            for (int i = 0; i < rows - 1; i++)
            {
                for (int j = 0; j < cols - 1; j++)
                {
                    // Kiểm tra nếu có thể tạo thành hình vuông 2x2 và chưa được đánh dấu
                    if (matrix[i, j] == 1 && matrix[i, j + 1] == 1 &&
                        matrix[i + 1, j] == 1 && matrix[i + 1, j + 1] == 1 &&
                        !visited[i, j] && !visited[i, j + 1] &&
                        !visited[i + 1, j] && !visited[i + 1, j + 1])
                    {
                        // Đánh dấu vùng với ID mới
                        result[i, j] = result[i, j + 1] = regionId;
                        result[i + 1, j] = result[i + 1, j + 1] = regionId;

                        // Đánh dấu đã sử dụng
                        visited[i, j] = visited[i, j + 1] = true;
                        visited[i + 1, j] = visited[i + 1, j + 1] = true;

                        regionId++; // Tăng ID vùng tiếp theo
                    }
                }
            }

            return result;
        }
        
        static int[,] FindSquaresTopLeft(int[,] matrix)
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            int[,] result = new int[rows, cols]; // Ma trận lưu số vùng
            bool[,] visited = new bool[rows, cols];
            int regionId = 1; // ID vùng bắt đầu từ 1

            for (int i = 0; i < rows - 1; i++)
            {
                for (int j = 0; j < cols - 1; j++)
                {
                    // Kiểm tra nếu có thể tạo thành hình vuông 2x2 và chưa được đánh dấu
                    if (matrix[i, j] == 1 &&
                        !visited[i, j] && !visited[i, j + 1] &&
                        !visited[i + 1, j] && !visited[i + 1, j + 1])
                    {
                        // Đánh dấu vùng với ID mới
                        result[i, j] = result[i, j + 1] = regionId;
                        result[i + 1, j] = result[i + 1, j + 1] = regionId;

                        // Đánh dấu đã sử dụng
                        visited[i, j] = visited[i, j + 1] = true;
                        visited[i + 1, j] = visited[i + 1, j + 1] = true;

                        regionId++; // Tăng ID vùng tiếp theo
                    }
                }
            }

            return result;
        }
        
        static int[,] FindSquaresSymmetric(int[,] matrix)
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            int[,] result = new int[rows, cols]; // Ma trận lưu số vùng
            bool[,] visited = new bool[rows, cols];
            int regionId = 1; // ID vùng bắt đầu từ 1

            for (int i = 0; i < rows - 1; i++)
            {
                for (int j = 0; j < cols / 2; j++)
                {
                    var mirrorJ = cols - 2 - j;
                    // Kiểm tra nếu có thể tạo thành hình vuông 2x2 và chưa được đánh dấu
                    if (matrix[i, j] == 1 && !visited[i, j] && !visited[i, j + 1] && !visited[i + 1, j] && !visited[i + 1, j + 1]
                        && 
                        matrix[i, mirrorJ + 1] == 1 && !visited[i, mirrorJ] && !visited[i, mirrorJ + 1] && !visited[i + 1, mirrorJ] && !visited[i + 1, mirrorJ + 1])
                    {
                        // Đánh dấu vùng với ID mới
                        result[i, j] = result[i, j + 1] = regionId;
                        result[i + 1, j] = result[i + 1, j + 1] = regionId;

                        // Đánh dấu đã sử dụng
                        visited[i, j] = visited[i, j + 1] = true;
                        visited[i + 1, j] = visited[i + 1, j + 1] = true;
                        
                         result[i, mirrorJ] = result[i, mirrorJ + 1] = regionId;
                         result[i + 1, mirrorJ] = result[i + 1, mirrorJ + 1] = regionId;
                         visited[i, mirrorJ] = visited[i, mirrorJ + 1] = true;
                         visited[i + 1, mirrorJ] = visited[i + 1, mirrorJ + 1] = true;

                        regionId++; // Tăng ID vùng tiếp theo
                    }
                }
            }

            return result;
        }

        
        
        static void PrintMatrix(int[,] matrix)
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            for (int i = 0; i < rows; i++)
            {
                var s = "";
                for (int j = 0; j < cols; j++)
                {
                   s += matrix[i, j] + " ";
                }
                BhDebug.Log(s);
            }
        }
    
        
        
        public static T[,] CloneMatrix<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] clone = new T[rows, cols];

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    clone[i, j] = matrix[i, j];
                }
            }

            return clone;
        }


    }
#endif

}