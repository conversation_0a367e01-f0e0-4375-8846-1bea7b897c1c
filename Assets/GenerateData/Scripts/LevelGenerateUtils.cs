using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Extensions;
using MahjongGame.Scripts.Gameplay.Data;
using UnityEngine;
using Random = UnityEngine.Random;

namespace GenerateData.Scripts
{
    public static class LevelGenerateUtils
    {

        public static int[,] GetSymmetryMatrix(int[,] originalMatrix, SymmetryType type = SymmetryType.Both)
        {
            if (type == 0)
            {
                return originalMatrix;
            }
            else if (type == SymmetryType.Both)
            {
                var originalCol = originalMatrix.GetLength(1);

                var isToRight = Random.value > .5f;

                List<int> symmetricColumn = new List<int>();

                if (isToRight)
                {
                    symmetricColumn.Clear();

                    var columnCount = Random.Range(0, originalCol - 1);

                    for (int i = 0; i < columnCount; i++)
                    {
                        symmetricColumn.Add(originalCol - 1 - i);
                    }
                }
                else
                {
                    symmetricColumn.Clear();

                    var columnCount = Random.Range(0, originalCol - 1);

                    for (int i = 0; i < columnCount; i++)
                    {
                        symmetricColumn.Add(i);
                    }
                }
                
                symmetricColumn.Sort();
                
                var temp = GetHorizontalSymmetricMatrix(originalMatrix, symmetricColumn, isToRight);
                
                //Vertical
                var currentRow = temp.GetLength(0);
                
                var isToBottom = Random.value > .5f;

                List<int> symmetricRow = new List<int>();

                if (isToBottom)
                {
                    symmetricRow.Clear();

                    var rowCount = Random.Range(0, currentRow - 1);

                    for (int i = 0; i < rowCount; i++)
                    {
                        symmetricRow.Add(currentRow - 1 - i);
                    }
                }
                else
                {
                    symmetricRow.Clear();

                    var rowCount = Random.Range(0, currentRow - 1);

                    for (int i = 0; i < rowCount; i++)
                    {
                        symmetricRow.Add(i);
                    }
                }
                
                symmetricRow.Sort();
                
                var result = GetVerticalSymmetricMatrix(temp, symmetricRow, isToBottom);
                
                return result;
            }
            else if ((type & SymmetryType.Horizontal) != 0)
            {
                //Horizontal
                var originalCol = originalMatrix.GetLength(1);

                var isToRight = Random.value > .5f;

                List<int> symmetricColumn = new List<int>();

                if (isToRight)
                {
                    symmetricColumn.Clear();

                    var columnCount = Random.Range(0, originalCol - 1);
                    
                    for (int i = 0; i < columnCount; i++)
                    {
                        symmetricColumn.Add(originalCol - 1 - i);
                    }
                }
                else
                {
                    symmetricColumn.Clear();

                    var columnCount = Random.Range(0, originalCol - 1);

                    for (int i = 0; i < columnCount; i++)
                    {
                        symmetricColumn.Add(i);
                    }
                }
                
                symmetricColumn.Sort();

                var temp = GetHorizontalSymmetricMatrix(originalMatrix, symmetricColumn, isToRight);
                
                return temp;
            }
            else
            {
                //Vertical
                var originalRow = originalMatrix.GetLength(0);
                
                var isToBottom = Random.value > .5f;

                List<int> symmetricRow = new List<int>();

                if (isToBottom)
                {
                    symmetricRow.Clear();

                    var rowCount = Random.Range(0, originalRow - 1);

                    for (int i = 0; i < rowCount; i++)
                    {
                        symmetricRow.Add(originalRow - 1 - i);
                    }
                }
                else
                {
                    symmetricRow.Clear();

                    var rowCount = Random.Range(0, originalRow - 1);

                    for (int i = 0; i < rowCount; i++)
                    {
                        symmetricRow.Add(i);
                    }
                }
                
                symmetricRow.Sort();
                
                var result = GetVerticalSymmetricMatrix(originalMatrix, symmetricRow, isToBottom);
                
                return result;
            }

            return null;
        }
        
        public static int[,] GetSymmetryMatrixMaxSize(int[,] originalMatrix, SymmetryType type = SymmetryType.Both)
        {
            if (type == 0)
            {
                return originalMatrix;
            }
            else if (type == SymmetryType.Both)
            {
                var isToRight = Random.value > .5f;

                List<int> symmetricColumn = new List<int>();
                
                var temp = GetHorizontalSymmetricMatrix(originalMatrix, symmetricColumn, isToRight);
                
                var isToBottom = Random.value > .5f;

                List<int> symmetricRow = new List<int>();

                
                var result = GetVerticalSymmetricMatrix(temp, symmetricRow, isToBottom);
                
                return result;
            }
            else if ((type & SymmetryType.Horizontal) != 0)
            {
                //Horizontal
                var originalCol = originalMatrix.GetLength(1);

                var isToRight = Random.value > .5f;

                List<int> symmetricColumn = new List<int>();

                if (isToRight)
                {
                    symmetricColumn.Clear();

                    var columnCount = originalCol % 2;
                    
                    for (int i = 0; i < columnCount; i++)
                    {
                        symmetricColumn.Add(originalCol - 1 - i);
                    }
                }
                else
                {
                    symmetricColumn.Clear();

                    var columnCount = originalCol % 2;

                    for (int i = 0; i < columnCount; i++)
                    {
                        symmetricColumn.Add(i);
                    }
                }
                
                symmetricColumn.Sort();

                var temp = GetHorizontalSymmetricMatrix(originalMatrix, symmetricColumn, isToRight);
                
                return temp;
            }
            else
            {
                //Vertical
                var originalRow = originalMatrix.GetLength(0);
                
                var isToBottom = Random.value > .5f;

                List<int> symmetricRow = new List<int>();

                if (isToBottom)
                {
                    symmetricRow.Clear();

                    var rowCount = originalRow % 2;

                    for (int i = 0; i < rowCount; i++)
                    {
                        symmetricRow.Add(originalRow - 1 - i);
                    }
                }
                else
                {
                    symmetricRow.Clear();

                    var rowCount = originalRow % 2;

                    for (int i = 0; i < rowCount; i++)
                    {
                        symmetricRow.Add(i);
                    }
                }
                
                symmetricRow.Sort();
                
                var result = GetVerticalSymmetricMatrix(originalMatrix, symmetricRow, isToBottom);
                
                return result;
            }

            return null;
        }
        
        
        public static int[,] GetSymmetryMatrixMaxSize(int[,] originalMatrix, SymmetryType type = SymmetryType.Both, bool isToRight = false, bool isToBottom = false)
        {
            if (type == 0)
            {
                return originalMatrix;
            }
            else if (type == SymmetryType.Both)
            {
                List<int> symmetricColumn = new List<int>();
                
                var temp = GetHorizontalSymmetricMatrix(originalMatrix, symmetricColumn, isToRight);
                
                List<int> symmetricRow = new List<int>();

                
                var result = GetVerticalSymmetricMatrix(temp, symmetricRow, isToBottom);
                
                return result;
            }
            else if ((type & SymmetryType.Horizontal) != 0)
            {
                //Horizontal
                var originalCol = originalMatrix.GetLength(1);

                List<int> symmetricColumn = new List<int>();

                if (isToRight)
                {
                    symmetricColumn.Clear();

                    var columnCount = originalCol % 2;
                    
                    for (int i = 0; i < columnCount; i++)
                    {
                        symmetricColumn.Add(originalCol - 1 - i);
                    }
                }
                else
                {
                    symmetricColumn.Clear();

                    var columnCount = originalCol % 2;

                    for (int i = 0; i < columnCount; i++)
                    {
                        symmetricColumn.Add(i);
                    }
                }
                
                symmetricColumn.Sort();

                var temp = GetHorizontalSymmetricMatrix(originalMatrix, symmetricColumn, isToRight);
                
                return temp;
            }
            else
            {
                //Vertical
                var originalRow = originalMatrix.GetLength(0);

                List<int> symmetricRow = new List<int>();

                if (isToBottom)
                {
                    symmetricRow.Clear();

                    var rowCount = originalRow % 2;

                    for (int i = 0; i < rowCount; i++)
                    {
                        symmetricRow.Add(originalRow - 1 - i);
                    }
                }
                else
                {
                    symmetricRow.Clear();

                    var rowCount = originalRow % 2;

                    for (int i = 0; i < rowCount; i++)
                    {
                        symmetricRow.Add(i);
                    }
                }
                
                symmetricRow.Sort();
                
                var result = GetVerticalSymmetricMatrix(originalMatrix, symmetricRow, isToBottom);
                
                return result;
            }
        }


        public static int[,] GetHorizontalSymmetricMatrix(int[,] originalMatrix, List<int> symmetryColumns, bool isToRight)
        {
            int rows = originalMatrix.GetLength(0);
            int cols = originalMatrix.GetLength(1);
            
            if (symmetryColumns.Count == cols)
            {
                return originalMatrix;
            } 

            if (!symmetryColumns.IsConsecutiveAscending())
            {
                throw new ArgumentException("Symmetry columns must be consecutive ascending.");
                return null;
            }

            var symmetricColumnCount = symmetryColumns.Count;
            
            // Calculate number col
            int newCols = symmetryColumns.Count;
            
            if (isToRight)
            {
                if (symmetryColumns.Count > 0)
                {
                    newCols += symmetryColumns[0] * 2;
                }
                else
                {
                    newCols = cols * 2;
                }
            }
            else
            {
                if (symmetricColumnCount > 0)
                {
                    newCols += (cols - symmetryColumns.GetLast() - 1) * 2;
                }
                else
                {
                    newCols = cols * 2;
                }
            }
            
            int[,] result = new int[rows, newCols];
            
            if (isToRight)
            {
                if (symmetricColumnCount > 0)
                {
                    // Copy symmetric columns
                    for (int i = 0; i < rows; i++)
                    {
                        for (int j = 0; j <= symmetryColumns.GetLast(); j++)
                        {
                            result[i, j] = originalMatrix[i, j];
                        }
                    }

                    for (int i = 0; i < rows; i++)
                    {
                        for (int j = 0; j < symmetryColumns[0]; j++)
                        {
                            result[i, newCols - 1 - j] = originalMatrix[i, j];
                        }
                    }
                }
                else
                {
                    // Copy symmetric columns
                    for (int i = 0; i < rows; i++)
                    {
                        for (int j = 0; j < cols; j++)
                        {
                            result[i, j] = originalMatrix[i, j];
                            result[i, newCols - 1 - j] = originalMatrix[i, j];
                        }
                    }
                }
            }
            else
            {
                var offset = newCols - cols;
                
                if (symmetricColumnCount > 0)
                {
                    // Copy
                    for (int i = 0; i < rows; i++)
                    {
                        for (int j = cols - 1; j >= symmetryColumns[0]; j--)
                        {
                            result[i, j + offset] = originalMatrix[i, j];
                        }
                    }
                
                    // Symmetry
                    for (int i = 0; i < rows; i++)
                    {
                        for (int j = 0; j < offset; j++)
                        {
                            result[i, j] = originalMatrix[i, cols - 1 - j];
                        }
                    }
                }
                else
                {
                    // Copy
                    for (int i = 0; i < rows; i++)
                    {
                        for (int j = 0; j < cols; j++)
                        {
                            result[i, j + offset] = originalMatrix[i, j];
                            result[i, j] = originalMatrix[i, cols - 1 - j];
                        }
                    }
                }

            }

            return result;
        }


        public static int[,] GetVerticalSymmetricMatrix(int[,] originalMatrix, List<int> symmetryRows, bool isToBottom)
        {
            int rows = originalMatrix.GetLength(0);
            int cols = originalMatrix.GetLength(1);
            
            if (symmetryRows.Count == rows)
            {
                return originalMatrix;
            } 

            if (!symmetryRows.IsConsecutiveAscending())
            {
                throw new ArgumentException("Symmetry rows must be consecutive ascending.");
                return null;
            }
            
            var symmetricRowCount = symmetryRows.Count;
            
            // Calculate number rows
            int newRows = symmetryRows.Count;
            
            if (isToBottom)
            {
                if (symmetricRowCount > 0)
                {
                    newRows += symmetryRows[0] * 2;
                }
                else
                {
                    newRows = rows * 2;
                }
            }
            else
            {
                if (symmetricRowCount > 0)
                {
                    newRows += (rows - symmetryRows.GetLast() - 1) * 2;
                }
                else
                {
                    newRows = rows * 2;
                }
            }
            
            int[,] result = new int[newRows, cols];

            if (isToBottom)
            {
                if (symmetricRowCount > 0)
                {
                    // Copy symmetric rows
                    for (int j = 0; j < cols; j++)
                    {
                        for (int i = 0; i <= symmetryRows.GetLast(); i++)
                        {
                            result[i, j] = originalMatrix[i, j];
                        }
                    }

                    for (int j = 0; j < cols; j++)
                    {
                        for (int i = 0; i < symmetryRows[0]; i++)
                        {
                            result[newRows - 1 - i, j] = originalMatrix[i, j];
                        }
                    }
                }
                else
                {
                    // Copy symmetric rows
                    for (int j = 0; j < cols; j++)
                    {
                        for (int i = 0; i < rows; i++)
                        {
                            result[i, j] = originalMatrix[i, j];
                            result[newRows - 1 - i, j] = originalMatrix[i, j];
                        }
                    }
                }
            }
            else
            {
                var offset = newRows - rows;

                if (symmetricRowCount > 0)
                {
                    // Copy
                    for (int j = 0; j < cols; j++)
                    {
                        for (int i = rows - 1; i >= symmetryRows[0]; i--)
                        {
                            result[i + offset, j] = originalMatrix[i, j];
                        }
                    }
                    
                    // Symmetry
                    for (int j = 0; j < cols; j++)
                    {
                        for (int i = 0; i < offset; i++)
                        {
                            result[i, j] = originalMatrix[rows - 1 - i, j];
                        }
                    }
                }
                else
                {
                    // Copy
                    for (int j = 0; j < cols; j++)
                    {
                        for (int i = 0; i < rows; i++)
                        {
                            result[i + offset, j] = originalMatrix[i, j];
                            result[i, j] = originalMatrix[rows - 1 - i, j];
                        }
                    }
                }
                

            }

            return result;
        }



        static bool IsConsecutiveAscending(this List<int> numbers)
        {
            if (numbers == null)
                return false;

            if (numbers.Count < 2)
                return true;

            for (int i = 1; i < numbers.Count; i++)
            {
                if (numbers[i] != numbers[i - 1] + 1)
                    return false;
            }

            return true;
        }
        
        
        public static List<List<(int, int)>> FindSquares(this int[,] matrix)
                 {
                     int rows = matrix.GetLength(0);
                     int cols = matrix.GetLength(1);
                     bool[,] visited = new bool[rows, cols];
                     List<List<(int, int)>> result = new List<List<(int, int)>>();
         
                     for (int i = 0; i < rows - 1; i++)
                     {
                         for (int j = 0; j < cols - 1; j++)
                         {
                             // Kiểm tra nếu có thể tạo thành hình vuông 2x2 và chưa được đánh dấu
                             if (matrix[i, j] == 1 && matrix[i, j + 1] == 1 &&
                                 matrix[i + 1, j] == 1 && matrix[i + 1, j + 1] == 1 &&
                                 !visited[i, j] && !visited[i, j + 1] && 
                                 !visited[i + 1, j] && !visited[i + 1, j + 1])
                             {
                                 // Đánh dấu các ô đã sử dụng
                                 visited[i, j] = visited[i, j + 1] = true;
                                 visited[i + 1, j] = visited[i + 1, j + 1] = true;
         
                                 // Lưu lại vùng tìm được
                                 result.Add(new List<(int, int)>
                                 {
                                     (i, j), (i, j + 1),
                                     (i + 1, j), (i + 1, j + 1)
                                 });
                             }
                         }
                     }
         
                     return result;
                 }
        
        
        public static int[,] FindSquaresMatrix(this int[,] matrix)
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            int[,] result = new int[rows, cols]; // Ma trận lưu số vùng
            bool[,] visited = new bool[rows, cols];
            int regionId = 1; // ID vùng bắt đầu từ 1

            for (int i = 0; i < rows - 1; i++)
            {
                for (int j = 0; j < cols - 1; j++)
                {
                    // Kiểm tra nếu có thể tạo thành hình vuông 2x2 và chưa được đánh dấu
                    if (matrix[i, j] == 1 && matrix[i, j + 1] == 1 &&
                        matrix[i + 1, j] == 1 && matrix[i + 1, j + 1] == 1 &&
                        !visited[i, j] && !visited[i, j + 1] &&
                        !visited[i + 1, j] && !visited[i + 1, j + 1])
                    {
                        // Đánh dấu vùng với ID mới
                        result[i, j] = result[i, j + 1] = regionId;
                        result[i + 1, j] = result[i + 1, j + 1] = regionId;

                        // Đánh dấu đã sử dụng
                        visited[i, j] = visited[i, j + 1] = true;
                        visited[i + 1, j] = visited[i + 1, j + 1] = true;

                        regionId++; // Tăng ID vùng tiếp theo
                    }
                }
            }

            return result;
        }
        
        public static int[,] FindSquaresTopLeft(this int[,] matrix)
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            int[,] result = new int[rows, cols]; // Ma trận lưu số vùng
            bool[,] visited = new bool[rows, cols];
            int regionId = 1; // ID vùng bắt đầu từ 1

            for (int i = 0; i < rows - 1; i++)
            {
                for (int j = 0; j < cols - 1; j++)
                {
                    // Kiểm tra nếu có thể tạo thành hình vuông 2x2 và chưa được đánh dấu
                    if (matrix[i, j] == 1 &&
                        !visited[i, j] && !visited[i, j + 1] &&
                        !visited[i + 1, j] && !visited[i + 1, j + 1])
                    {
                        // Đánh dấu vùng với ID mới
                        result[i, j] = result[i, j + 1] = regionId;
                        result[i + 1, j] = result[i + 1, j + 1] = regionId;

                        // Đánh dấu đã sử dụng
                        visited[i, j] = visited[i, j + 1] = true;
                        visited[i + 1, j] = visited[i + 1, j + 1] = true;

                        regionId++; // Tăng ID vùng tiếp theo
                    }
                }
            }

            return result;
        }
        
        public static int[,] FindSquaresSymmetric(this int[,] matrix)
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            int[,] result = new int[rows, cols]; // Ma trận lưu số vùng
            bool[,] visited = new bool[rows, cols];
            int regionId = 1; // ID vùng bắt đầu từ 1

            for (int i = 0; i < rows - 1; i++)
            {
                for (int j = 0; j < cols / 2; j++)
                {
                    var mirrorJ = cols - 2 - j;
                    // Kiểm tra nếu có thể tạo thành hình vuông 2x2 và chưa được đánh dấu
                    if (matrix[i, j] == 1 && !visited[i, j] && !visited[i, j + 1] && !visited[i + 1, j] && !visited[i + 1, j + 1]
                        && 
                        matrix[i, mirrorJ + 1] == 1 && !visited[i, mirrorJ] && !visited[i, mirrorJ + 1] && !visited[i + 1, mirrorJ] && !visited[i + 1, mirrorJ + 1])
                    {
                        // Đánh dấu vùng với ID mới
                        result[i, j] = result[i, j + 1] = regionId;
                        result[i + 1, j] = result[i + 1, j + 1] = regionId;

                        // Đánh dấu đã sử dụng
                        visited[i, j] = visited[i, j + 1] = true;
                        visited[i + 1, j] = visited[i + 1, j + 1] = true;
                        
                        result[i, mirrorJ] = result[i, mirrorJ + 1] = regionId;
                        result[i + 1, mirrorJ] = result[i + 1, mirrorJ + 1] = regionId;
                        visited[i, mirrorJ] = visited[i, mirrorJ + 1] = true;
                        visited[i + 1, mirrorJ] = visited[i + 1, mirrorJ + 1] = true;

                        regionId++; // Tăng ID vùng tiếp theo
                    }
                }
            }

            return result;
        }
        
        
        public static List<List<(int, int, int)>> FindSquares3D(this int[,,] matrix)
        {
            int depth = matrix.GetLength(0);
            int rows = matrix.GetLength(1);
            int cols = matrix.GetLength(2);
    
            bool[,,] visited = new bool[depth, rows, cols]; // Đánh dấu ô đã kiểm tra
            List<List<(int, int, int)>> result = new List<List<(int, int, int)>>(); // Kết quả

            for (int d = 0; d < depth; d++) // Duyệt từng layer (depth)
            {
                for (int i = 0; i < rows - 1; i++)
                {
                    for (int j = 0; j < cols - 1; j++)
                    {
                        // Kiểm tra vùng 2x2 hợp lệ
                        bool isValidSquare =
                            matrix[d, i, j] == 1 && matrix[d, i, j + 1] == 1 &&
                            matrix[d, i + 1, j] == 1 && matrix[d, i + 1, j + 1] == 1 &&
                            !visited[d, i, j] && !visited[d, i, j + 1] &&
                            !visited[d, i + 1, j] && !visited[d, i + 1, j + 1];

                        // Nếu đang ở layer trên, yêu cầu layer dưới cũng phải có vùng 2x2
                        if (d > 0)
                        {
                            isValidSquare &= result.Any(region =>
                                region.Contains((d - 1, i, j)) &&
                                region.Contains((d - 1, i, j + 1)) &&
                                region.Contains((d - 1, i + 1, j)) &&
                                region.Contains((d - 1, i + 1, j + 1)));
                        }

                        if (isValidSquare)
                        {
                            // Đánh dấu ô đã sử dụng
                            visited[d, i, j] = visited[d, i, j + 1] = true;
                            visited[d, i + 1, j] = visited[d, i + 1, j + 1] = true;

                            // Lưu lại vùng 2x2
                            result.Add(new List<(int, int, int)>
                            {
                                (d, i, j), (d, i, j + 1),
                                (d, i + 1, j), (d, i + 1, j + 1)
                            });
                        }
                    }
                }
            }

            return result;
        }
        
        
        public static int[,,] FindSquaresMatrix3D(this int[,,] matrix)
        {
            int depth = matrix.GetLength(0);
            int rows = matrix.GetLength(1);
            int cols = matrix.GetLength(2);
    
            int[,,] result = new int[depth, rows, cols]; // Kết quả đánh dấu ID vùng 2x2
            bool[,,] visited = new bool[depth, rows, cols]; // Đánh dấu đã kiểm tra
            int regionId = 1; // ID vùng, bắt đầu từ 1

            for (int d = 0; d < depth; d++) // Duyệt qua từng layer (depth)
            {
                for (int i = 0; i < rows - 1; i++)
                {
                    for (int j = 0; j < cols - 1; j++)
                    {
                        // Kiểm tra nếu tạo thành hình vuông 2x2 hợp lệ ở layer hiện tại
                        bool isValidSquare =
                            matrix[d, i, j] == 1 && matrix[d, i, j + 1] == 1 &&
                            matrix[d, i + 1, j] == 1 && matrix[d, i + 1, j + 1] == 1 &&
                            !visited[d, i, j] && !visited[d, i, j + 1] &&
                            !visited[d, i + 1, j] && !visited[d, i + 1, j + 1];

                        // Nếu ở layer cao hơn, kiểm tra xem layer dưới có vùng tương ứng không
                        if (d > 0)
                        {
                            isValidSquare &= result[d - 1, i, j] > 0 &&
                                             result[d - 1, i, j + 1] > 0 &&
                                             result[d - 1, i + 1, j] > 0 &&
                                             result[d - 1, i + 1, j + 1] > 0;
                        }

                        if (isValidSquare)
                        {
                            // Gán ID vùng
                            result[d, i, j] = result[d, i, j + 1] = regionId;
                            result[d, i + 1, j] = result[d, i + 1, j + 1] = regionId;

                            // Đánh dấu đã sử dụng
                            visited[d, i, j] = visited[d, i, j + 1] = true;
                            visited[d, i + 1, j] = visited[d, i + 1, j + 1] = true;

                            regionId++; // Tăng ID vùng tiếp theo
                        }
                    }
                }
            }

            return result;
        }

        public static int[,,] FindSquaresSymmetric3D(this int[,,] matrix, out int count)
        {
            count = 0;
            int depth = matrix.GetLength(0);
            int rows = matrix.GetLength(1);
            int cols = matrix.GetLength(2);

            int[,,] result = new int[depth, rows, cols]; // Kết quả đánh dấu ID vùng 2x2
            bool[,,] visited = new bool[depth, rows, cols]; // Đánh dấu đã kiểm tra
            int regionId = 1; // ID vùng, bắt đầu từ 1

            for (int d = 0; d < depth; d++) // Duyệt từng layer (depth)
            {
                for (int i = 0; i < rows - 1; i++)
                {
                    for (int j = 0; j < cols / 2; j++)
                    {
                        int mirrorJ = cols - 2 - j; // Vị trí đối xứng

                        // Kiểm tra có thể tạo hình vuông 2x2 đối xứng
                        bool isValidSquare =
                            matrix[d, i, j] == 1 && 
                            //matrix[d, i, j + 1] == 1 &&
                            //matrix[d, i + 1, j] == 1 && 
                            //matrix[d, i + 1, j + 1] == 1 &&
                            //matrix[d, i, mirrorJ] == 1 && 
                            matrix[d, i, mirrorJ + 1] == 1 &&
                            //matrix[d, i + 1, mirrorJ] == 1 && 
                            //matrix[d, i + 1, mirrorJ + 1] == 1 &&
                            !visited[d, i, j] && !visited[d, i, j + 1] &&
                            !visited[d, i + 1, j] && !visited[d, i + 1, j + 1] &&
                            !visited[d, i, mirrorJ] && !visited[d, i, mirrorJ + 1] &&
                            !visited[d, i + 1, mirrorJ] && !visited[d, i + 1, mirrorJ + 1];

                        // Nếu đang ở layer trên, yêu cầu layer dưới cũng phải có vùng 2x2
                        if (d > 0)
                        {
                            isValidSquare &= result[d - 1, i, j] > 0 &&
                                             result[d - 1, i, j + 1] > 0 &&
                                             result[d - 1, i + 1, j] > 0 &&
                                             result[d - 1, i + 1, j + 1] > 0 &&
                                             result[d - 1, i, mirrorJ] > 0 &&
                                             result[d - 1, i, mirrorJ + 1] > 0 &&
                                             result[d - 1, i + 1, mirrorJ] > 0 &&
                                             result[d - 1, i + 1, mirrorJ + 1] > 0;
                        }

                        if (isValidSquare)
                        {
                            // Đánh dấu vùng với ID mới
                            result[d, i, j] = result[d, i, j + 1] = 1;
                            result[d, i + 1, j] = result[d, i + 1, j + 1] = 1;

                            result[d, i, mirrorJ] = result[d, i, mirrorJ + 1] = 1;
                            result[d, i + 1, mirrorJ] = result[d, i + 1, mirrorJ + 1] = 1;

                            // Đánh dấu đã sử dụng
                            visited[d, i, j] = visited[d, i, j + 1] = true;
                            visited[d, i + 1, j] = visited[d, i + 1, j + 1] = true;
                            visited[d, i, mirrorJ] = visited[d, i, mirrorJ + 1] = true;
                            visited[d, i + 1, mirrorJ] = visited[d, i + 1, mirrorJ + 1] = true;

                            regionId++; // Tăng ID vùng tiếp theo

                            if (Mathf.Abs(j - mirrorJ) == 0)
                            {
                                count++;
                            }
                            else
                            {
                                count += 2;
                            }
                        }
                    }
                }
            }

            return result;
        }
        
        
        public static List<List<(int, int, int)>> FindSquaresSymmetric3DRange(this int[,,] matrix, out int count)
        {
            count = 0;
            int depth = matrix.GetLength(0);
            int rows = matrix.GetLength(1);
            int cols = matrix.GetLength(2);
            
            List<List<(int, int, int)>> resultRange = new List<List<(int, int, int)>>();

            int[,,] result = new int[depth, rows, cols]; // Kết quả đánh dấu ID vùng 2x2
            bool[,,] visited = new bool[depth, rows, cols]; // Đánh dấu đã kiểm tra
            int regionId = 1; // ID vùng, bắt đầu từ 1

            for (int d = 0; d < depth; d++) // Duyệt từng layer (depth)
            {
                for (int i = 0; i < rows - 1; i++)
                {
                    for (int j = 0; j < cols / 2; j++)
                    {
                        int mirrorJ = cols - 2 - j; // Vị trí đối xứng

                        // Kiểm tra có thể tạo hình vuông 2x2 đối xứng
                        bool isValidSquare =
                            matrix[d, i, j] > 0 && 
                            //matrix[d, i, j + 1] == 1 &&
                            //matrix[d, i + 1, j] == 1 && 
                            //matrix[d, i + 1, j + 1] == 1 &&
                            //matrix[d, i, mirrorJ] == 1 && 
                            matrix[d, i, mirrorJ + 1] > 0 &&
                            //matrix[d, i + 1, mirrorJ] == 1 && 
                            //matrix[d, i + 1, mirrorJ + 1] == 1 &&
                            !visited[d, i, j] && !visited[d, i, j + 1] &&
                            !visited[d, i + 1, j] && !visited[d, i + 1, j + 1] &&
                            !visited[d, i, mirrorJ] && !visited[d, i, mirrorJ + 1] &&
                            !visited[d, i + 1, mirrorJ] && !visited[d, i + 1, mirrorJ + 1];

                        // Nếu đang ở layer trên, yêu cầu layer dưới cũng phải có vùng 2x2
                        if (d > 0)
                        {
                            isValidSquare &= result[d - 1, i, j] > 0 &&
                                             result[d - 1, i, j + 1] > 0 &&
                                             result[d - 1, i + 1, j] > 0 &&
                                             result[d - 1, i + 1, j + 1] > 0 &&
                                             result[d - 1, i, mirrorJ] > 0 &&
                                             result[d - 1, i, mirrorJ + 1] > 0 &&
                                             result[d - 1, i + 1, mirrorJ] > 0 &&
                                             result[d - 1, i + 1, mirrorJ + 1] > 0;
                        }

                        if (isValidSquare)
                        {
                            // Đánh dấu vùng với ID mới
                            result[d, i, j] = result[d, i, j + 1] = regionId;
                            result[d, i + 1, j] = result[d, i + 1, j + 1] = regionId;

                            result[d, i, mirrorJ] = result[d, i, mirrorJ + 1] = regionId;
                            result[d, i + 1, mirrorJ] = result[d, i + 1, mirrorJ + 1] = regionId;

                            // Đánh dấu đã sử dụng
                            visited[d, i, j] = visited[d, i, j + 1] = true;
                            visited[d, i + 1, j] = visited[d, i + 1, j + 1] = true;
                            visited[d, i, mirrorJ] = visited[d, i, mirrorJ + 1] = true;
                            visited[d, i + 1, mirrorJ] = visited[d, i + 1, mirrorJ + 1] = true;

                            regionId++; // Tăng ID vùng tiếp theo

                            if (Mathf.Abs(j - mirrorJ) == 0)
                            {
                                count++;
                                
                                resultRange.Add(new List<(int, int, int)>()
                                {
                                    (d, i, j), (d, i, j + 1),
                                    (d, i + 1, j), (d, i + 1, j + 1)
                                });
                                
                            }
                            else
                            {
                                count += 2;

                                resultRange.Add(new List<(int, int, int)>()
                                {
                                    (d, i, j), (d, i, j + 1),
                                    (d, i + 1, j), (d, i + 1, j + 1)
                                });

                                resultRange.Add(new List<(int, int, int)>()
                                {
                                    (d, i, mirrorJ), (d, i, mirrorJ + 1),
                                    (d, i + 1, mirrorJ), (d, i + 1, mirrorJ + 1)
                                });
                            }
                        }
                    }
                }
            }

            return resultRange;
        }

        public static (List<List<(int, int, int)>> regions, int count) FindSquares3DAndCount(this int[,,] matrix)
        {
            int depth = matrix.GetLength(0);
            int rows = matrix.GetLength(1);
            int cols = matrix.GetLength(2);

            bool[,,] visited = new bool[depth, rows, cols]; // Đánh dấu ô đã kiểm tra
            List<List<(int, int, int)>> result = new List<List<(int, int, int)>>(); // Kết quả
            int regionCount = 0; // Đếm số lượng vùng

            for (int d = 0; d < depth; d++) // Duyệt từng layer (depth)
            {
                for (int i = 0; i < rows - 1; i++)
                {
                    for (int j = 0; j < cols - 1; j++)
                    {
                        // Kiểm tra vùng 2x2 hợp lệ
                        bool isValidSquare =
                            matrix[d, i, j] == 1 && matrix[d, i, j + 1] == 1 &&
                            matrix[d, i + 1, j] == 1 && matrix[d, i + 1, j + 1] == 1 &&
                            !visited[d, i, j] && !visited[d, i, j + 1] &&
                            !visited[d, i + 1, j] && !visited[d, i + 1, j + 1];

                        // Nếu đang ở layer trên, yêu cầu layer dưới cũng phải có vùng 2x2
                        if (d > 0)
                        {
                            isValidSquare &= result.Any(region =>
                                region.Contains((d - 1, i, j)) &&
                                region.Contains((d - 1, i, j + 1)) &&
                                region.Contains((d - 1, i + 1, j)) &&
                                region.Contains((d - 1, i + 1, j + 1)));
                        }

                        if (isValidSquare)
                        {
                            // Đánh dấu ô đã sử dụng
                            visited[d, i, j] = visited[d, i, j + 1] = true;
                            visited[d, i + 1, j] = visited[d, i + 1, j + 1] = true;

                            // Lưu lại vùng 2x2
                            result.Add(new List<(int, int, int)>
                            {
                                (d, i, j), (d, i, j + 1),
                                (d, i + 1, j), (d, i + 1, j + 1)
                            });

                            regionCount++; // Tăng số lượng vùng
                        }
                    }
                }
            }

            return (result, regionCount);
        }
        
        
        
        
        
        
        public static LevelDataRaw CreateLevelDataRaw(this int[,,] matrix, List<char> itemTypeList, string id, bool hasFlower, bool hasSeason)
        {
            int depth = matrix.GetLength(0);
            int rows = matrix.GetLength(1);
            int cols = matrix.GetLength(2);
            
            List<List<(int, int, int)>> resultRange = new List<List<(int, int, int)>>();

            List<(int, int, int)> tileData = new List<(int, int, int)>();

            int[,,] result = new int[depth, rows, cols]; // Kết quả đánh dấu ID vùng 2x2
            bool[,,] visited = new bool[depth, rows, cols]; // Đánh dấu đã kiểm tra
            int regionId = 1; // ID vùng, bắt đầu từ 1

            for (int d = 0; d < depth; d++) // Duyệt từng layer (depth)
            {
                for (int i = 0; i < rows - 1; i++)
                {
                    for (int j = 0; j < cols / 2; j++)
                    {
                        int mirrorJ = cols - 2 - j; // Vị trí đối xứng

                        // Kiểm tra có thể tạo hình vuông 2x2 đối xứng
                        bool isValidSquare =
                            matrix[d, i, j] > 0 && 
                            //matrix[d, i, j + 1] == 1 &&
                            //matrix[d, i + 1, j] == 1 && 
                            //matrix[d, i + 1, j + 1] == 1 &&
                            //matrix[d, i, mirrorJ] == 1 && 
                            matrix[d, i, mirrorJ + 1] > 0 &&
                            //matrix[d, i + 1, mirrorJ] == 1 && 
                            //matrix[d, i + 1, mirrorJ + 1] == 1 &&
                            !visited[d, i, j] && !visited[d, i, j + 1] &&
                            !visited[d, i + 1, j] && !visited[d, i + 1, j + 1] &&
                            !visited[d, i, mirrorJ] && !visited[d, i, mirrorJ + 1] &&
                            !visited[d, i + 1, mirrorJ] && !visited[d, i + 1, mirrorJ + 1];

                        // Nếu đang ở layer trên, yêu cầu layer dưới cũng phải có vùng 2x2
                        if (d > 0)
                        {
                            isValidSquare &= result[d - 1, i, j] > 0 &&
                                             result[d - 1, i, j + 1] > 0 &&
                                             result[d - 1, i + 1, j] > 0 &&
                                             result[d - 1, i + 1, j + 1] > 0 &&
                                             result[d - 1, i, mirrorJ] > 0 &&
                                             result[d - 1, i, mirrorJ + 1] > 0 &&
                                             result[d - 1, i + 1, mirrorJ] > 0 &&
                                             result[d - 1, i + 1, mirrorJ + 1] > 0;
                        }

                        if (isValidSquare)
                        {
                            // Đánh dấu vùng với ID mới
                            result[d, i, j] = result[d, i, j + 1] = regionId;
                            result[d, i + 1, j] = result[d, i + 1, j + 1] = regionId;

                            result[d, i, mirrorJ] = result[d, i, mirrorJ + 1] = regionId;
                            result[d, i + 1, mirrorJ] = result[d, i + 1, mirrorJ + 1] = regionId;

                            // Đánh dấu đã sử dụng
                            visited[d, i, j] = visited[d, i, j + 1] = true;
                            visited[d, i + 1, j] = visited[d, i + 1, j + 1] = true;
                            visited[d, i, mirrorJ] = visited[d, i, mirrorJ + 1] = true;
                            visited[d, i + 1, mirrorJ] = visited[d, i + 1, mirrorJ + 1] = true;

                            regionId++; // Tăng ID vùng tiếp theo

                            if (Mathf.Abs(j - mirrorJ) == 0)
                            {
                                
                                resultRange.Add(new List<(int, int, int)>()
                                {
                                    (d, i, j), (d, i, j + 1),
                                    (d, i + 1, j), (d, i + 1, j + 1)
                                });
                                
                                tileData.Add((d,i,j));
                                
                            }
                            else
                            {
                                resultRange.Add(new List<(int, int, int)>()
                                {
                                    (d, i, j), (d, i, j + 1),
                                    (d, i + 1, j), (d, i + 1, j + 1)
                                });
                                
                                tileData.Add((d,i,j));


                                resultRange.Add(new List<(int, int, int)>()
                                {
                                    (d, i, mirrorJ), (d, i, mirrorJ + 1),
                                    (d, i + 1, mirrorJ), (d, i + 1, mirrorJ + 1)
                                });
                                
                                tileData.Add((d,i,mirrorJ));

                            }
                        }
                    }
                }
            }
            
            
            tileData = tileData.OrderBy(t => t.Item1)
                .ThenBy(t => t.Item2)
                .ThenBy(t => t.Item3)
                .ToList();


            var q = string.Join(";", tileData
                .GroupBy(t => t.Item1.ToChar()) // Nhóm theo d
                .Select(g => $"{g.Key}" + string.Join(".", g
                    .GroupBy(t => t.Item2.ToChar()) // Nhóm tiếp theo i trong từng nhóm d
                    .Select(ig => $"{ig.Key}" + string.Join(",", ig.Select(t => t.Item3.ToChar()))))));

            //BhDebug.Log("value: " + q);

            var sb = new StringBuilder();

            foreach (var c in itemTypeList)
            {
                sb.Append(c.ToString());
            }

            var qResult = q + ":" + sb.ToString();

            var levelData = new LevelDataRaw()
            {
                id = id,
                q = qResult,
                hasFlower = hasFlower ? 1 : 0,
                hasSeason = hasSeason ? 1 : 0,
            };

            return levelData;
        }
        
        
        
        public static void PrintMatrix(this int[,] matrix)
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            for (int i = 0; i < rows; i++)
            {
                var s = "";
                for (int j = 0; j < cols; j++)
                {
                    s += matrix[i, j] + " ";
                }
                BhDebug.Log(s);
            }
        }
        
        public static void PrintMatrix(this int[,,] matrix)
        {
            int depth = matrix.GetLength(0);
            int rows = matrix.GetLength(1);
            int cols = matrix.GetLength(2);
            for (int d = 0; d < depth; d++)
            {
                BhDebug.Log($"Layer {d}:");
                var s = "";
                for (int i = 0; i < rows; i++)
                {
                    for (int j = 0; j < cols; j++)
                    {
                        s += matrix[d, i, j] + " ";
                    }
                    s += "\n";
                }
                BhDebug.Log(s);
            }
        }
    
        
        
        public static T[,] CloneMatrix<T>(this T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] clone = new T[rows, cols];

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    clone[i, j] = matrix[i, j];
                }
            }

            return clone;
        }
    }


    [Flags]
    public enum SymmetryType
    {
        Horizontal = 1 << 0,
        Vertical = 1 << 1,
        Both = Horizontal | Vertical,
    }
}