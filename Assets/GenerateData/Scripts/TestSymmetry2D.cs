using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities.Extensions;
using DG.Tweening;
using Sirenix.OdinInspector;
using UnityEngine;

namespace GenerateData.Scripts
{
    public class TestSymmetry2D : BhMonoBehavior
    {
        public bool isSymmetrical = true;  // Tùy chọn bật/tắt đối xứng
        public Vector2 symmetryAxis = Vector2.right;  // Trục đối xứng, mặc định là theo trục X (right)

        public GameObject prefab;


        public Transform containerRef;

        // Start is called before the first frame update

        // Phương thức tạo đối xứng của vật thể 2D
        [Button]
        void CreateSymmetry()
        {
            transform.DestroyChildren();

            for (int i = 0; i < containerRef.childCount; i++)
            {
                var child = containerRef.GetChild(i);
                
                // Tính toán vị trí đối xứng dựa trên trục đối xứng
                Vector3 symmetricPosition = child.position;

                // <PERSON><PERSON><PERSON> ngược vị trí theo trục đối xứng
                symmetricPosition.x = symmetryAxis.x != 0 ? -child.position.x : child.position.x;
                symmetricPosition.y = symmetryAxis.y != 0 ? -child.position.y : child.position.y;
        
                // Tạo đối tượng đối xứng ở vị trí mới
                Instantiate(prefab, symmetricPosition, child.rotation, transform);
            }
            
        }
    }
}