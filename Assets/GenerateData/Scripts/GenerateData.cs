using System;
using b100SDK.Scripts.Base;
using Newtonsoft.Json;
using UnityEngine;

namespace GenerateData.Scripts
{
    [Serializable]
    public class GenerateData
    {
        public void VerifyData()
        {

        }
    }
    
    public static class GenerateDataBase
    {
        private static string _dataKey = "GenerateData";

        public static void SaveData()
        {
            var dataString = JsonConvert.SerializeObject(GameManager.Instance.data);
            PlayerPrefs.SetString(_dataKey, dataString);
            PlayerPrefs.Save();
        }

        public static GenerateData LoadData()
        {
            if (!PlayerPrefs.HasKey(_dataKey))
                return null;
        
            var dataString = PlayerPrefs.GetString(_dataKey);
            return JsonConvert.DeserializeObject<GenerateData>(dataString);
        
        }
    }
}