using System;
using System.Collections.Generic;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities;
using Sirenix.OdinInspector;
using UnityEngine;

namespace GenerateData.Scripts
{
    public class TestSeparateLayer : BhMonoBehavior
    {
        [SerializeField]
        [GUIColor(nameof(GetGuiColor))]
        [InfoBox("Tile count must be greater than 0 and is pow of 2")]
        private uint tileCount = 60;

        [SerializeField, Range(1, 10)]
        [InfoBox("Layer count must be greater than 0 and less than 10")]
        private int layerCount = 2;
        
        [SerializeField]
        private SeparateData separateData;

        private bool _isTrueTileCount;
        
        


        [Serializable]
        class SeparateData
        {
            public int minTilePerLayer = 2;
            
        } 


        [Button]
        void Separate()
        {
            
        }

        Color GetGuiColor()
        {
            return _isTrueTileCount ? new Color(.1f, 1f, 0f) : Color.red;
        }


        private void OnValidate()
        {
            _isTrueTileCount = tileCount % 2 == 0 && tileCount > 0;
        }
    }
}