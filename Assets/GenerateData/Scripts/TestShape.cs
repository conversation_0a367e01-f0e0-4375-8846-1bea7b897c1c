using System;
using System.Collections.Generic;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities.Extensions;
using Sirenix.OdinInspector;
using UnityEngine;

namespace GenerateData.Scripts
{
    public class TestShape : BhMonoBehavior
    {
        public GameObject tilePrefab; // Prefab của tile để sinh ra hình

        public Transform container;


        [Button]
        void CreateRectangle()
        {
            GenerateShape(0, 0, 5, 3, (x, y) => true);
        }

        [Button]
        void CreateSquare()
        {
            GenerateShape(0, 0, 5, 5, (x, y) => x == y);
        }

        [Button]
        void CreateTriangle()
        {
            GenerateShape(0, 0, 5, 5, (x, y) => x >= y);
        }
        
        
        [Button]
        void CreateHexagon()
        {
            GenerateShape(0, 0, 5, 5, (x, y) => x + y <= 5);
        }
        
        
        [Button]
        void CreateCircle()
        {
            GenerateShape(0, 0, 10, 10, (x, y) => (x - 5) * (x - 5) + (y - 5) * (y - 5) <= 25);
        }

        public void GenerateShape(int startX, int startY, int maxWidth, int maxHeight, Func<int, int, bool> shapeFunction)
        {
            container.DestroyChildrenImmediate();
                  
            List<GameObject> tiles = new List<GameObject>();

            for (int x = 0; x < maxWidth; x++)
            {
                for (int y = 0; y < maxHeight; y++)
                {
                    if (shapeFunction(x, y)) // Kiểm tra xem điểm này có thuộc hình không
                    {
                        Vector3 position = new Vector3(startX + x, startY + y, 0);
                        GameObject tile = Instantiate(tilePrefab, position, Quaternion.identity, container);
                        tiles.Add(tile);
                    }
                }
            }
        }
    }
}