using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using b100SDK.Scripts.DesignPatterns;
using b100SDK.Scripts.UI.Panel;
using b100SDK.Scripts.Utilities;
using GenerateData.Scripts.Configs;
using MahjongGame.Scripts.Gameplay;
using MahjongGame.Scripts.Gameplay.Controller;
using MahjongGame.Scripts.Gameplay.Data;
using Newtonsoft.Json;
using Sirenix.OdinInspector;
using UnityEngine;

namespace GenerateData.Scripts
{
    public class LevelEditor : Singleton<LevelEditor>
    {
        [SerializeField]
        private LevelCreateConfig levelCreateConfig;
        
        [SerializeField]
        private LevelGenerator levelGenerator;

        [SerializeField]
        private LevelGeneratorNew levelGeneratorNew;
        
        [SerializeField]
        private List<LevelDataRaw> _levels = new List<LevelDataRaw>();

        [SerializeField]
        private LevelDataRaw _currentLevel;

        public LevelDataRaw CurrentLevel => _currentLevel;
        
        private string _filePath;

        private CancellationTokenSource _cancellationTokenSource;

        private string _currentId;

        public string FilePath
        {
            get => _filePath;
            set
            {
                _filePath = value;
                UpdateFilePath();
            }
        }





        public void LoadLevel(string id)
        {
            var level = _levels.FirstOrDefault(x => x.id == id);
            if (level != null)
            {
                _currentId = id;
                _currentLevel = level;
                
                BhDebug.Log("Load level with q:" + _currentLevel.q);
                
                GameController.Instance.InitGame();
            }
            else
            {
                Debug.LogError("Level " + id + " is not exist");
                PopupNotification.Show("Level " + id + " is not found");
            }
        }
        
        
        public void SaveLevel(string id)
        {
            _currentLevel.id = id;
            
            var level = _levels.FirstOrDefault(x => x.id == id);
            
            if (level != null)
            {
                PopupConfirmAction.Show($"Level with id {id} already exists. Do you want to overwrite it?", () =>
                {
                    // Overwrite the level
                    level.q = _currentLevel.q;
                    level.hasSeason = _currentLevel.hasSeason;
                    level.hasFlower = _currentLevel.hasFlower;
                    
                    SaveFile();
                });
            }
            else
            {
                _levels.Add(_currentLevel);
                
                SaveFile();
            }
            
            UpdateFilePath();
        }


        [Button]
        async void ReplaceAllLevel()
        {
            for (int i = 0; i < _levels.Count; i++)
            {
                var levelId = _levels[i].id;
                LoadLevel(levelId);
                Evm.OnShuffle.Dispatch();
                
                ReplaceLevel();
                
                await Task.Delay(2000); // wait for 1 second to avoid UI freeze
            }
        }
        
        [Button]
        public void ReplaceLevel()
        {
            _currentLevel.id = _currentId;

            var levelReplay = DataExtensions.GetLevelDataRaw(GameController.Instance.GameplayController.LevelData.items,
                _currentId, _currentLevel.hasFlower == 1, _currentLevel.hasSeason == 1);
            
            var level = _levels.FirstOrDefault(x => x.id == _currentId);

            _currentLevel = levelReplay;
            
            if (level != null)
            {
                /*PopupConfirmAction.Show($"Level with id {id} already exists. Do you want to overwrite it?", () =>
                {
                    // Overwrite the level
                    level.q = _currentLevel.q;
                    level.hasSeason = _currentLevel.hasSeason;
                    level.hasFlower = _currentLevel.hasFlower;
                    
                    SaveFile();
                });*/
                
                // Overwrite the level
                BhDebug.Log($"Overwriting level with id {_currentId}");
                level.q = _currentLevel.q;
                level.hasSeason = _currentLevel.hasSeason;
                level.hasFlower = _currentLevel.hasFlower;
                    
                SaveFile();
            }
            UpdateFilePath();
        }

        public void DeleteLevel(string id)
        {
            var level = _levels.FirstOrDefault(x => x.id == id);

            var index = _levels.FindIndex(x => x.id == id);
            
            if (level!= null)
            {
                PopupConfirmAction.Show($"Are you sure you want to delete level with id {id}?", () =>
                {
                    // Delete the level
                    BhDebug.Log($"Remove leve {id} with q: " + level.q);
                    
                    _levels.RemoveAt(index);
                    
                    SaveFile();
                    
                    UpdateFilePath();
                });
            }
            
        }

        public void SwapLevel(string id1, string id2)
        {
            var level1 = _levels.FirstOrDefault(x => x.id == id1);
            var level2 = _levels.FirstOrDefault(x => x.id == id2);
            
            var indexLevel1 = _levels.FindIndex(x => x.id == id1);
            var indexLevel2 = _levels.FindIndex(x => x.id == id2);
            
            if (level1!= null && level2!= null)
            {
                PopupConfirmAction.Show($"Are you sure you want to swap level {id1} to {id2}?", () =>
                {
                    // Swap the levels
                    var tempLevel = new LevelDataRaw()
                    {
                        id = level1.id,
                        q = level1.q,
                        hasSeason = level1.hasSeason,
                        hasFlower = level1.hasFlower
                    };
                    
                    _levels[indexLevel1].q = level2.q;
                    _levels[indexLevel1].hasSeason = level2.hasSeason;
                    _levels[indexLevel1].hasFlower = level2.hasFlower;
                    
                    _levels[indexLevel2].q = tempLevel.q;
                    _levels[indexLevel2].hasSeason = tempLevel.hasSeason;
                    _levels[indexLevel2].hasFlower = tempLevel.hasFlower;
                    
                    SaveFile();
                    UpdateFilePath();
                });
            }
            else
            {
                Debug.LogError("One or both levels are not found");
                PopupNotification.Show("One or both levels are not found");
            }
        }


        public async void CreateLevel(string tileCount, bool hasFlower, bool hasSeason, string layerCount, string typeCount, string offsetRowCol, string offsetMultiplier, string offsetTileCount)
        {
            PopupWait.Show();
            _cancellationTokenSource = new CancellationTokenSource();
            _currentLevel = await levelGenerator.CreateLevel(tileCount, hasFlower, hasSeason, layerCount, typeCount, offsetRowCol,
                offsetMultiplier, offsetTileCount, _cancellationTokenSource);
            
            var levelInfo = levelCreateConfig.GetLevelGenerateInfo(1);
            
            if (_currentLevel == null || string.IsNullOrEmpty(_currentLevel.q))
            {
                if (PopupWait.Instance)
                {
                    PopupWait.Instance.Close();
                }
                return;
            }
            
            ShuffleCountCalculate.Evaluate(_currentLevel.GetLevelData(), levelInfo);

            if (PopupWait.Instance)
            {
                PopupWait.Instance.Close();
            }
            if (_currentLevel != null)
            {
                GameController.Instance.InitGame();
            }
        }

        public void CancelCreate()
        {
            if (_cancellationTokenSource != null)
            {
                _cancellationTokenSource.Cancel();
            }
        }



        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        async void CreateLevelByConfig()
        {
            PopupWait.Show();
            _cancellationTokenSource = new CancellationTokenSource();
            
            var result = new List<LevelDataRaw>();
            Evm.OnUpdateLevelCreatedCount.Dispatch(0);
            
            var levelCount = levelCreateConfig.GetTotalLevel();

            for (int i = 0; i < levelCount; i++)
            {
                var correctLevel = i + 1;
                var levelInfo = levelCreateConfig.GetLevelGenerateInfo(correctLevel);
                
                /*BhDebug.Log("================================================");
                BhDebug.Log("=====================Create level " + correctLevel + " with tile count: " + levelInfo);*/
                
                _currentLevel = await levelGenerator.CreateLevel(levelInfo.tileCount.ToString(), false, false, levelInfo.isRandomLayer ? "" : levelInfo.layerCount.ToString(), "", "",
                    "", "", _cancellationTokenSource);

                while (_currentLevel == null)
                {
                    await Task.Delay(1);
                    levelInfo = levelCreateConfig.GetLevelGenerateInfo(correctLevel);
                    _currentLevel = await levelGenerator.CreateLevel(levelInfo.tileCount.ToString(), false, false, levelInfo.isRandomLayer ? "" : levelInfo.layerCount.ToString(), "", "",
                        "", "", _cancellationTokenSource);
                }
                
                var isPassEvaluate = false;

                var level = _currentLevel.GetLevelData();
                isPassEvaluate = ShuffleCountCalculate.Evaluate(level, levelInfo);
                
                while (!isPassEvaluate)
                {
                    levelInfo = levelCreateConfig.GetLevelGenerateInfo(correctLevel);
                    _currentLevel = await levelGenerator.CreateLevel(levelInfo.tileCount.ToString(), false, false, levelInfo.isRandomLayer ? "" : levelInfo.layerCount.ToString(), "", "",
                        "", "", _cancellationTokenSource);
                    if (_currentLevel == null || string.IsNullOrEmpty(_currentLevel.q))
                    {
                        continue;
                    }
                    level = _currentLevel.GetLevelData();
                    isPassEvaluate = ShuffleCountCalculate.Evaluate(level, levelInfo);
                }
                
                _currentLevel.id = correctLevel.ToString();
                
                result.Add(_currentLevel);
                Evm.OnUpdateLevelCreatedCount.Dispatch(result.Count);
                
                if (_currentLevel != null)
                {
                    GameController.Instance.InitGame();
                }
                
                //BhDebug.Log("================================================");
            }

            var path = levelCreateConfig.savePath;
            var textData = JsonConvert.SerializeObject(result.ToArray());
            File.WriteAllText(path, textData);
            Debug.Log("Level saved successfully to: " + path);
            
            if (PopupWait.Instance)
            {
                PopupWait.Instance.Close();
            }
            PopupNotification.Show("Level saved successfully");
        }      
        
        
        [PropertySpace(20f)]
        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        async void CreateLevelByConfigNew()
        {
            PopupWait.Show();
            _cancellationTokenSource = new CancellationTokenSource();
            
            var result = new List<LevelDataRaw>();
            Evm.OnUpdateLevelCreatedCount.Dispatch(0);
            
            var levelCount = levelCreateConfig.GetTotalLevel();

            for (int i = 0; i < levelCount; i++)
            {
                var correctLevel = i + 1;
                var levelInfo = levelCreateConfig.GetLevelGenerateInfo(correctLevel);
                
                /*BhDebug.Log("================================================");
                BhDebug.Log("=====================Create level " + correctLevel + " with tile count: " + levelInfo);*/

                _currentLevel = await levelGeneratorNew.CreateLevel(levelInfo.tileCount, levelInfo.layerCount,
                    levelInfo.rowCount, levelInfo.colCount, _cancellationTokenSource);

                while (_currentLevel == null)
                {
                    await Task.Delay(1);
                    levelInfo = levelCreateConfig.GetLevelGenerateInfo(correctLevel);
                    _currentLevel = await levelGeneratorNew.CreateLevel(levelInfo.tileCount, levelInfo.layerCount,
                        levelInfo.rowCount, levelInfo.colCount, _cancellationTokenSource);
                }
                
                var isPassEvaluate = false;

                var level = _currentLevel.GetLevelData();
                isPassEvaluate = ShuffleCountCalculate.Evaluate(level, levelInfo);
                
                while (!isPassEvaluate)
                {
                    levelInfo = levelCreateConfig.GetLevelGenerateInfo(correctLevel);
                    _currentLevel = await levelGeneratorNew.CreateLevel(levelInfo.tileCount, levelInfo.layerCount,
                        levelInfo.rowCount, levelInfo.colCount, _cancellationTokenSource);
                    
                    level = _currentLevel.GetLevelData();
                    isPassEvaluate = ShuffleCountCalculate.Evaluate(level, levelInfo);
                }
                
                _currentLevel.id = correctLevel.ToString();
                
                result.Add(_currentLevel);
                Evm.OnUpdateLevelCreatedCount.Dispatch(result.Count);
                
                if (_currentLevel != null)
                {
                    GameController.Instance.InitGame();
                }
                
                //BhDebug.Log("================================================");
            }

            var path = levelCreateConfig.savePath;
            var textData = JsonConvert.SerializeObject(result.ToArray());
            File.WriteAllText(path, textData);
            Debug.Log("Level saved successfully to: " + path);
            
            if (PopupWait.Instance)
            {
                PopupWait.Instance.Close();
            }
            PopupNotification.Show("Level saved successfully");
        }
        
        
        public LevelData GetDataFromDataRaw()
        {
            var levelData = LevelEditor.Instance.CurrentLevel.GetLevelData();

            var size =
                $"{levelData.boardGame.GetLength(0)}x{levelData.boardGame.GetLength(1) / 2}x{levelData.boardGame.GetLength(2) / 2}";
            var tileCount = levelData.items.Count;

            var typeCount = levelData.items.Select(x => x.LogicType).Distinct().Count();

            var str = size + $" - {tileCount} - {typeCount}";
            
            Evm.OnUpdateSize.Dispatch(str);
            
            
            
            //Calculate

            var result = ShuffleCountCalculate.Calculate(levelData);

            var avgStr = $"{result.Item1} - {result.Item2}";
            
            Evm.OnUpdateShuffleAvg.Dispatch(avgStr);
            

            return LevelEditor.Instance.CurrentLevel.GetLevelData();
        }



        void SaveFile()
        {
            if (string.IsNullOrEmpty(_filePath))
            {
                Debug.LogError("The file path is null or empty");
                PopupNotification.Show("Please check our file path");
            }
            else
            {
                var textData = JsonConvert.SerializeObject(_levels.ToArray());
                
                File.WriteAllText(_filePath, textData);
                
                Debug.Log("Level saved successfully to: " + _filePath);
                PopupNotification.Show("Level saved successfully");
            }
        }
        

        void UpdateFilePath()
        {
            var stringData = File.ReadAllText(_filePath);

            if (string.IsNullOrEmpty(stringData))
            {
                Debug.LogWarning("The data in file path is null or empty");
                return;
            }
            
            var data = JsonConvert.DeserializeObject<LevelDataRaw[]>(stringData);
            
            if (data!= null)
            {
                _levels = data.ToList();
            }
            
            Evm.OnUpdateLevelCount.Dispatch(_levels.Count);
        }
    }
}