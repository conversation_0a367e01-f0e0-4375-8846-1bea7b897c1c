using System;
using System.Collections.Generic;
using System.Linq;
using b100SDK.Scripts.Utilities;
using Sirenix.OdinInspector;
using UnityEngine;
using Random = UnityEngine.Random;

namespace GenerateData.Scripts.Configs
{
    [CreateAssetMenu(fileName = "LevelCreateConfig", menuName = "Configs/LevelCreateConfig")]
    public class LevelCreateConfig : ScriptableObject
    {
        [SerializeField]
        private List<LevelRange> levelRanges;

        public string savePath;

        [Button]
        public LevelGenerateInfo GetLevelGenerateInfo(int level)
        {
            var levelGenerateInfo = new LevelGenerateInfo();

            var startLevel = 0;
            var totalLevels = 0;
            LevelRange targetLevelRange = null;
            
            for (var index = 0; index < levelRanges.Count; index++)
            {
                /*
                if (index >= levelRanges.Count)
                {
                    targetLevelRange = levelRanges.Last();
                    break;
                }
                */
                
                
                
                var range = levelRanges[index];
                if (level <= totalLevels)
                {
                    var correctIndex = index - 1;
                    if (correctIndex < 0)
                    {
                        correctIndex = 0;
                    }
                    targetLevelRange = levelRanges[correctIndex];
                    break;
                }

                startLevel = totalLevels;
                totalLevels += range.GetLevelCount();
            }
            
            if (targetLevelRange == null)
            {
                targetLevelRange = levelRanges.Last();
            }

            /*BhDebug.Log("Start level: " + startLevel);
            BhDebug.Log("Totals level: " + totalLevels);*/
            
            
            if (targetLevelRange == null)
            {
                Debug.LogError("Not found level range");
                return null;
            }
            
            levelGenerateInfo.level = level;
            
            var levelInRange = level - startLevel;
            
            var t = (float) levelInRange / targetLevelRange.GetLevelCount();
            
            levelGenerateInfo.tileCount = targetLevelRange.GetTileCount(t);

            levelGenerateInfo.tileCount = Mathf.RoundToInt(levelGenerateInfo.tileCount / 2f) * 2;
            
            levelGenerateInfo.isRandomLayer = targetLevelRange.IsRandomLayer();
            levelGenerateInfo.layerCount = targetLevelRange.GetLayerCount();
            
            levelGenerateInfo.rowCount = targetLevelRange.GetRowCount();
            levelGenerateInfo.colCount = targetLevelRange.GetColCount();

            var offset = (level - totalLevels);
            var stuckSpacing = targetLevelRange.GetStuckSpacing();
            
            //BhDebug.Log($"Level: {level}, Offset: {offset}, StuckSpacing: {stuckSpacing}, IsStuck: {offset % stuckSpacing == 0}");
            
            
            levelGenerateInfo.isStuck = (level - totalLevels) % targetLevelRange.GetStuckSpacing() == 0;
            
            levelGenerateInfo.minStuckRate = targetLevelRange.GetMinStuckRate();
            levelGenerateInfo.maxStuckRate = targetLevelRange.GetMaxStuckRate();
            
            if (Random.Range(0f, 1f) <= targetLevelRange.GetRandomRateStuck())
            {
                levelGenerateInfo.isStuck = true;
            }
            
            levelGenerateInfo.minRateClearLevelHasStuck = targetLevelRange.GetMinRateClearLevelHasStuck();
            levelGenerateInfo.maxRateClearLevelHasStuck = targetLevelRange.GetMaxRateClearLevelHasStuck();
            
            levelGenerateInfo.minShuffleCountToClear = targetLevelRange.GetMinShuffleCountToClear();
            levelGenerateInfo.maxShuffleCountToClear = targetLevelRange.GetMaxShuffleCountToClear();
            
            levelGenerateInfo.maxFillRateLayer = targetLevelRange.GetMaxFillRateLayer();
            levelGenerateInfo.maxOffsetTilePerLayer = targetLevelRange.GetMaxOffsetTilePerLayer();
            
            return levelGenerateInfo;
        }


        public int GetTotalLevel()
        {
            return levelRanges.Sum(x => x.GetLevelCount());
        }
    }

    [Serializable]
    public class LevelRange
    {
        [Title("Level config")]
        [SerializeField]
        private int levelCount;
        
        [SerializeField]
        private int minTileCount;
        
        [SerializeField]
        private int maxTileCount;

        [SerializeField]
        private float randomRateTile = .1f;
        
        [SerializeField]
        private AnimationCurve tileCountCurve;

        [Space]
        [SerializeField]
        private float randomLayerRate = .3f;

        [SerializeField]
        private int minLayer = 2;
        
        [SerializeField]
        private int maxLayer = 5;
        
        [Space]
        [SerializeField]
        private int minRow = 6;
        
        [SerializeField]
        private int maxRow = 12;

        [Space]
        [SerializeField]
        private int minCol = 5;
        
        [SerializeField]
        private int maxCol = 8;

        
        [Title("Stuck")]
        [SerializeField]
        private int stuckSpacing;
        
        [Header("Tỉ lệ random stuck sau khi đã quyết định level có bị stuck hay ko")]
        [SerializeField]
        private float randomRateStuck = .1f;
        
        [Space]
        [Header("Kiểm soát việc check xem có tính là stuck hay không")]
        [SerializeField]
        [Tooltip("Nếu tỉ lệ stuck thấp hơn giá trị này thì sẽ không được coi là stuck")]
        private float minStuckRate = 0f;
        
        [SerializeField]
        [Tooltip("Nếu muốn level được coi là stuck thì tỉ lệ stuck bắt buộc phải cao hơn giá trị này")]
        private float maxStuckRate = .99f;
        
        [Space]
        [Header("Kiểm soát việc check xem có clear level khi stuck hay không")]
        [SerializeField]
        private float minRateClearLevelHasStuck = .4f;
        
        [SerializeField]
        private float maxRateClearLevelHasStuck = .95f;
        
        [Space]
        [Header("Kiểm soát việc duùng bao lần shuffle để clear được")]
        [SerializeField]
        private float minShuffleCountToClear = .01f;

        [SerializeField]
        private float maxShuffleCountToClear = 1.2f;

        [Title("Layout")]
        [SerializeField]
        private float maxFillRateLayer = .9f;

        [SerializeField]
        private float maxOffsetTilePerLayer = .6f;
        
        
        public int GetLevelCount()
        {
            return levelCount;
        }
        
        public int GetTileCount(float t)
        {
            //BhDebug.Log("T: " + t);
            float curveValue = tileCountCurve.Evaluate(t);
            int baseTileCount = Mathf.RoundToInt(Mathf.Lerp(minTileCount, maxTileCount, curveValue));
            int randomOffset = Mathf.RoundToInt(baseTileCount * randomRateTile * Random.Range(-1f, 1f));
            return Mathf.Clamp(baseTileCount + randomOffset, minTileCount, maxTileCount);
        }
        
        public int GetLayerCount()
        {
            return Random.Range(minLayer, maxLayer + 1);
        }
        
        public int GetRowCount()
        {
            return Random.Range(minRow, maxRow + 1);
        }
        
        public int GetColCount()
        {
            return Random.Range(minCol, maxCol + 1);
        }
        
        public bool IsRandomLayer()
        {
            return Random.Range(0f, 1f) <= randomLayerRate;
        }
        
        public int GetStuckSpacing()
        {
            return stuckSpacing;
        }
        
        public float GetStuckRate()
        {
            return Random.Range(minStuckRate, maxStuckRate);
        }
        
        public float GetRandomRateStuck()
        {
            return randomRateStuck;
        }
        
        public float GetMinStuckRate()
        {
            return minStuckRate;
        }
        
        public float GetMaxStuckRate()
        {
            return maxStuckRate;
        }
        
        public float GetMinRateClearLevelHasStuck()
        {
            return minRateClearLevelHasStuck;
        }
        
        public float GetMaxRateClearLevelHasStuck()
        {
            return maxRateClearLevelHasStuck;
        }
        
        public float GetRateClearLevelHasStuck()
        {
            return Random.Range(minRateClearLevelHasStuck, maxRateClearLevelHasStuck);
        }
        
        public float GetShuffleCountToClear()
        {
            return Random.Range(minShuffleCountToClear, maxShuffleCountToClear);
        }
        
        public float GetMinShuffleCountToClear()
        {
            return minShuffleCountToClear;
        }
        
        public float GetMaxShuffleCountToClear()
        {
            return maxShuffleCountToClear;
        }
        
        public float GetMaxFillRateLayer()
        {
            return maxFillRateLayer;
        }
        
        public float GetMaxOffsetTilePerLayer()
        {
            return maxOffsetTilePerLayer;
        }
    }

    [Serializable]
    public class LevelGenerateInfo
    {
        public int level;
        public int tileCount;
        public bool isRandomLayer;
        public int layerCount;
        public int rowCount;
        public int colCount;
        public bool isStuck;
        
        public float minStuckRate;
        public float maxStuckRate;
        
        public float minRateClearLevelHasStuck;
        public float maxRateClearLevelHasStuck;
        
        public float minShuffleCountToClear;
        public float maxShuffleCountToClear;
        
        public float maxFillRateLayer;
        public float maxOffsetTilePerLayer;

        public override string ToString()
        {
            return $"Level: {level}, TileCount: {tileCount}, IsStuck: {isStuck}, MinStuckRate: {minStuckRate}, MaxStuckRate: {maxStuckRate}, " +
                   $"MinRateClearLevelHasStuck: {minRateClearLevelHasStuck}, MaxRateClearLevelHasStuck: {maxRateClearLevelHasStuck}, " +
                   $"MinShuffleCountToClear: {minShuffleCountToClear}, MaxShuffleCountToClear: {maxShuffleCountToClear}, " +
                   $"MaxFillRateLayer: {maxFillRateLayer}, MaxOffsetTilePerLayer: {maxOffsetTilePerLayer}";
        }
    }
}