using System;
using System.Collections;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.UI.Components.Button;
using MahjongGame.Scripts.Gameplay;
using SimpleFileBrowser;
using Sirenix.OdinInspector;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace GenerateData.Scripts.UI
{
    public class UIPanel : BhMonoBehavior
    {
        [Title("Left Panel")]
		public TMP_InputField offsetRowCol;

		public TMP_InputField offsetMultiplier;
		
		public TMP_InputField offsetTileCount;

		public TMP_InputField tileCount;

		public TMP_InputField layerCount;

		public TMP_InputField typeCount;

		public Toggle hasFlower;

		public Toggle hasSeason;
		
        [Title("Right Panel")]
        public TMP_InputField boardInfo;

        public TMP_InputField shuffleAvg;

        public BhButton createLevelButton;

        public BhButton initButton;

        public BhButton shuffleButton;

        public BhButton autoPlayButton;
        
        [Header("Folder")]
        [SerializeField]
        private BhButton selectFolderSave;

        [SerializeField]
        private TMP_Text filePath;

        [SerializeField]
        private TMP_Text levelCountText;

        
        [Header("Level")]
        [SerializeField]
        private TMP_InputField levelInput;
        
        [SerializeField]
        private BhButton saveButton;
        
        [SerializeField]
        private BhButton deleteButton;
        
        [SerializeField]
        private BhButton loadButton;
        
        [Header("Swap")]
        [SerializeField]
        private TMP_InputField swapId1;
        
        [SerializeField]
        private TMP_InputField swapId2;
        
        [SerializeField]
        private BhButton swapButton;


        private void OnEnable()
        {
	        createLevelButton.onClick.AddListener(CreateLevel);
	        initButton.onClick.AddListener(InitLevel);
            selectFolderSave.onClick.AddListener(SelectFilePath);
            saveButton.onClick.AddListener(SaveLevel);
            deleteButton.onClick.AddListener(DeleteLevel);
            loadButton.onClick.AddListener(LoadLevel);
            swapButton.onClick.AddListener(SwapLevel);
            
            shuffleButton.onClick.AddListener(Shuffle);
            autoPlayButton.onClick.AddListener(AutoPlay);

            Evm.OnUpdateSize.AddListener(UpdateInfo);
            Evm.OnUpdateShuffleAvg.AddListener(UpdateShuffleAvg);

            Evm.OnUpdateLevelCount.AddListener(UpdateLevelCount);
        }

        private void OnDisable()
        {
	        createLevelButton.onClick.RemoveListener(CreateLevel);
	        initButton.onClick.RemoveListener(InitLevel);
            selectFolderSave.onClick.RemoveListener(SelectFilePath);
            saveButton.onClick.RemoveListener(SaveLevel);
            deleteButton.onClick.RemoveListener(DeleteLevel);
            loadButton.onClick.RemoveListener(LoadLevel);
            swapButton.onClick.RemoveListener(SwapLevel);
            
            shuffleButton.onClick.RemoveListener(Shuffle);
            autoPlayButton.onClick.RemoveListener(AutoPlay);
            
            Evm.OnUpdateSize.RemoveListener(UpdateInfo);
            Evm.OnUpdateShuffleAvg.RemoveListener(UpdateShuffleAvg);
            
            Evm.OnUpdateLevelCount.RemoveListener(UpdateLevelCount);
        }


        #region Folder

        
        void SelectFilePath()
		{
			// Set filters (optional)
			// It is sufficient to set the filters just once (instead of each time before showing the file browser dialog), 
			// if all the dialogs will be using the same filters
			FileBrowser.SetFilters( true, new FileBrowser.Filter( "Images", ".jpg", ".png" ), new FileBrowser.Filter( "Text Files", ".txt") );

			// Set default filter that is selected when the dialog is shown (optional)
			// Returns true if the default filter is set successfully
			// In this case, set Images filter as the default filter
			FileBrowser.SetDefaultFilter( ".txt" );

			// Add a new quick link to the browser (optional) (returns true if quick link is added successfully)
			// It is sufficient to add a quick link just once
			// Name: Users
			// Path: C:\Users
			// Icon: default (folder icon)
			FileBrowser.AddQuickLink( "Users", "C:\\Users", null );
			
			// !!! Uncomment any of the examples below to show the file browser !!!

			// Example 1: Show a save file dialog using callback approach
			// onSuccess event: not registered (which means this dialog is pretty useless)
			// onCancel event: not registered
			// Save file/folder: file, Allow multiple selection: false
			// Initial path: "C:\", Initial filename: "Screenshot.png"
			// Title: "Save As", Submit button text: "Save"
			// FileBrowser.ShowSaveDialog( null, null, FileBrowser.PickMode.Files, false, "C:\\", "Screenshot.png", "Save As", "Save" );

			// Example 2: Show a select folder dialog using callback approach
			// onSuccess event: print the selected folder's path
			// onCancel event: print "Canceled"
			// Load file/folder: folder, Allow multiple selection: false
			// Initial path: default (Documents), Initial filename: empty
			// Title: "Select Folder", Submit button text: "Select"
			// FileBrowser.ShowLoadDialog( ( paths ) => { Debug.Log( "Selected: " + paths[0] ); },
			//						   () => { Debug.Log( "Canceled" ); },
			//						   FileBrowser.PickMode.Folders, false, null, null, "Select Folder", "Select" );

			// Example 3: Show a select file dialog using coroutine approach
			 StartCoroutine( ShowLoadDialogCoroutine() );
		}
        
		IEnumerator ShowLoadDialogCoroutine()
		{
			// Show a load file dialog and wait for a response from user
			// Load file/folder: file, Allow multiple selection: true
			// Initial path: default (Documents), Initial filename: empty
			// Title: "Load File", Submit button text: "Load"
			yield return FileBrowser.WaitForLoadDialog( FileBrowser.PickMode.Files, true, null, null, "Select Files", "Load" );

			// Dialog is closed
			// Print whether the user has selected some files or cancelled the operation (FileBrowser.Success)
			Debug.Log( FileBrowser.Success );

			if( FileBrowser.Success )
				OnFilesSelected( FileBrowser.Result ); // FileBrowser.Result is null, if FileBrowser.Success is false
		}
        
		void OnFilesSelected( string[] filePaths )
		{
			// Print paths of the selected files
			for( int i = 0; i < filePaths.Length; i++ )
				Debug.Log( filePaths[i] );

			// Get the file path of the first selected file
			string filePath = filePaths[0];

			this.filePath.text = filePath;
			/*// Read the bytes of the first file via FileBrowserHelpers
			// Contrary to File.ReadAllBytes, this function works on Android 10+, as well
			byte[] bytes = FileBrowserHelpers.ReadBytesFromFile( filePath );

			// Or, copy the first file to persistentDataPath
			string destinationPath = Path.Combine( Application.persistentDataPath, FileBrowserHelpers.GetFilename( filePath ) );
			FileBrowserHelpers.CopyFile( filePath, destinationPath );*/

			LevelEditor.Instance.FilePath = filePath;
		}
		
		
		

        #endregion



        #region Level

        void LoadLevel()
        {
	        LevelEditor.Instance.LoadLevel(levelInput.text);
        }

        void SaveLevel()
        {
	        LevelEditor.Instance.SaveLevel(levelInput.text);
        }
        
        void DeleteLevel()
        {
	        LevelEditor.Instance.DeleteLevel(levelInput.text);
        }

        #endregion


        #region Swap

        
        void SwapLevel()
        {
            LevelEditor.Instance.SwapLevel(swapId1.text, swapId2.text);
        }

        #endregion






        #region CreateLevel

        public void CreateLevel()
        {
	        LevelEditor.Instance.CreateLevel(tileCount.text, hasFlower.isOn, hasSeason.isOn, layerCount.text, typeCount.text,
		        offsetRowCol.text, offsetMultiplier.text, offsetTileCount.text);
        }
        

        #endregion


        void InitLevel()
        {
	        if (GameController.Instance)
	        {
		        GameController.Instance.InitGame();
	        }
        }






        void UpdateInfo(string info)
        {
	        boardInfo.text = info;
        }
        
        void UpdateShuffleAvg(string avg)
        {
            shuffleAvg.text = avg;
        }

        void Shuffle()
        {
	        Evm.Shuffle.Dispatch();
        }

        void AutoPlay()
        {
	        Evm.AutoTest.Dispatch();
        }

        void UpdateLevelCount(int levelCount)
        {
	        levelCountText.text = "Level Count: " + levelCount;
        }
    }
}