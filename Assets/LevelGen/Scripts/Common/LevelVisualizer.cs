using b100SDK.Scripts.Utilities.Extensions;
using UnityEngine;

namespace LevelGen.Scripts.Common
{
    public class LevelVisualizer : MonoBehaviour
    {
        [Header("Grid Settings")]
        public int maxLayers = 5;
        
        [Header("Tile Settings")]
        public GameObject tilePrefab;
        public float tileSpacing = 1.1f;
        public float layerHeight = 2f;

        public void SpawnLevel(int[,,] matrix, bool needClear = true)
        {
            if (needClear)
            {
                transform.DestroyChildren();
            }
            
            for (int d = 0; d < matrix.GetLength(0); d++)
            {
                var row = matrix.GetLength(1);
                var col = matrix.GetLength(2);
                var matrix2D =  new int[row,col];
                
                for (int i = 0; i < row; i++)
                {
                    for (int j = 0; j < col; j++)
                    {
                        matrix2D[i, j] = matrix[d, i, j];
                    }
                }
                
                SpawnLayer(matrix2D, d);
            }
        }

        public void SpawnLayer(int[,] matrix, int layerIndex, bool needClear = false)
        {
            if (needClear)
            {
                transform.DestroyChildren();
            }
            
            var zOffset = layerIndex * layerHeight;
            var gridWidth = matrix.GetLength(0);
            var gridHeight = matrix.GetLength(1);
            
            for (int i = 0; i < matrix.GetLength(0); i++)
            {
                for (int j = 0; j < matrix.GetLength(1); j++)
                {
                    if (matrix[i, j] != 0) // Chỉ hiển thị nếu giá trị khác 0
                    {
                        Vector3 position = new Vector3(
                            (j - gridHeight * 0.5f) * tileSpacing,
                            zOffset,
                            (i - gridWidth * 0.5f) * tileSpacing
                        );
                        
                        GameObject go = Instantiate(tilePrefab, position, Quaternion.identity, transform);
                        go.transform.localPosition = position;
                        
                        Renderer renderer = go.GetComponent<Renderer>();
                        if (renderer != null)
                        {
                            Color layerColor = Color.HSVToRGB((float)layerIndex / maxLayers, 0.3f, 1f);
                            renderer.material.color = layerColor;
                        }
                    }
                }
            }
        }
    }
}