using System.Collections.Generic;
using System.Linq;
using b100SDK.Scripts.Utilities.Extensions;
using b100SDK.Scripts.Utilities.Tool;
using Sirenix.OdinInspector;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Common
{
    [CreateAssetMenu(fileName = "Filter Config Group", menuName = "LevelGen/Filter Config Group")]
    public class FilterConfigGroup : ScriptableObject
    {
        [SerializeField]
        private List<FilterConfigBase> filterConfigs = new List<FilterConfigBase>();
        
        
        
        public List<FilterConfigBase> GetFilterConfigs()
        {
            return filterConfigs;
        }
        
        public FilterConfigBase GetFilterConfig(string filterName)
        {
            return filterConfigs.Find(config => config.GetFilterName() == filterName);
        }
        
        public FilterConfigBase GetFilterConfig(int index)
        {
            var correctIndex = index % filterConfigs.Count;
            return filterConfigs[correctIndex];
        }
        
        public FilterConfigBase GetRandomFilterConfig()
        {
            return filterConfigs.GetRandom();
        }
        
        
        
        
        [PropertySpace(20)]
        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        void CreateAllConfigs()
        {
            #if UNITY_EDITOR
            var filterTypes = typeof(FilterConfigBase).Assembly.GetTypes()
                .Where(t => !t.IsAbstract && t.IsSubclassOf(typeof(FilterConfigBase)));

            foreach (var type in filterTypes)
            {
                var config = ScriptableObject.CreateInstance(type) as FilterConfigBase;
                if (config != null)
                {
                    string assetPath = $"{path}/{type.Name}.asset";
                    config.SetFilterName();
                    UnityEditor.AssetDatabase.CreateAsset(config, assetPath);
                }
            }
            
            UnityEditor.AssetDatabase.SaveAssets();
            UnityEditor.AssetDatabase.Refresh();
            #endif
        }

        
        
        [PropertySpace(20)]
        [PropertyOrder(10)]
        [FolderPath]
        [SerializeField]
        private string path;
        
        [PropertyOrder(10)]
        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        void GetAllConfigs()
        {
            filterConfigs = ConfigTool.GetConfigs<FilterConfigBase>(path);
        }
    }
}