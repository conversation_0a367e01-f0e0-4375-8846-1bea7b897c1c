using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;
using Sirenix.OdinInspector;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Butterfly Filter với random generation ranges
    /// </summary>
    [CreateAssetMenu(fileName = "ButterflyFilter", menuName = "LevelGen/Filters/Butterfly Filter")]
    public class ButterflyFilterConfig : FilterConfigBase
    {
        [Header("Butterfly Parameters")]
        [Range(0.3f, 1.5f)] public float wingSpanScale = 0.8f;
        [Range(0.2f, 1f)] public float wingHeightScale = 0.6f;
        [Range(0.02f, 0.2f)] public float bodyWidthScale = 0.08f;
        [Range(0.3f, 1.2f)] public float bodyLengthScale = 0.7f;
        public bool fillInside = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.5f;
        [Range(0f, 360f)] public float rotation = 0f;

        [Header("Random Generation Settings")]
        [SerializeField] private bool enableRandomGeneration = true;

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges", ShowLabel = true)]
        [Header("Wing & Body Ranges")]
        [SerializeField] private Vector2 wingSpanScaleRange = new Vector2(0.5f, 1.2f);
        [SerializeField] private Vector2 wingHeightScaleRange = new Vector2(0.4f, 0.8f);
        [SerializeField] private Vector2 bodyWidthScaleRange = new Vector2(0.04f, 0.15f);
        [SerializeField] private Vector2 bodyLengthScaleRange = new Vector2(0.5f, 1f);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Position & Rotation Ranges")]
        [SerializeField] private Vector2 centerRange = new Vector2(0.3f, 0.7f);
        [SerializeField] private Vector2 rotationRange = new Vector2(0f, 360f);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Probability Settings")]
        [Range(0f, 1f)]
        [SerializeField] private float fillInsideChance = 0.8f;
        [Range(0f, 1f)]
        [SerializeField] private float useRandomCenterChance = 0.3f;

        public override string GetFilterName()
        {
            return "Butterfly Filter";
        }

        public override IFilter CreateFilter()
        {
            return new ButterflyFilter(wingSpanScale, wingHeightScale, bodyWidthScale, bodyLengthScale,
                fillInside, centerX, centerY, rotation);
        }

        protected override IFilter CreateRandomFilterInternal()
        {
            if (!enableRandomGeneration)
            {
                return CreateFilter();
            }

            // Random dimensions
            float randomWingSpanScale = RandomInRange(wingSpanScaleRange);
            float randomWingHeightScale = RandomInRange(wingHeightScaleRange);
            float randomBodyWidthScale = RandomInRange(bodyWidthScaleRange);
            float randomBodyLengthScale = RandomInRange(bodyLengthScaleRange);

            // Random fill inside
            bool randomFillInside = RandomBool(fillInsideChance);

            // Random center position
            Vector2 centerPos = RandomBool(useRandomCenterChance) ?
                RandomCenter(centerRange) : MidCenter();

            // Random rotation
            float randomRotation = RandomInRange(rotationRange);

            return new ButterflyFilter(
                randomWingSpanScale,
                randomWingHeightScale,
                randomBodyWidthScale,
                randomBodyLengthScale,
                randomFillInside,
                centerPos.x,
                centerPos.y,
                randomRotation
            );
        }

        #region Utility Methods

        private float RandomInRange(Vector2 range)
        {
            return UnityEngine.Random.Range(range.x, range.y);
        }

        private Vector2 RandomCenter(Vector2 range)
        {
            return new Vector2(
                UnityEngine.Random.Range(range.x, range.y),
                UnityEngine.Random.Range(range.x, range.y)
            );
        }

        #endregion

        #region Editor Utilities

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Reset to Default Ranges", ButtonSizes.Medium)]
        private void ResetToDefaultRanges()
        {
            wingSpanScaleRange = new Vector2(0.5f, 1.2f);
            wingHeightScaleRange = new Vector2(0.4f, 0.8f);
            bodyWidthScaleRange = new Vector2(0.04f, 0.15f);
            bodyLengthScaleRange = new Vector2(0.5f, 1f);
            centerRange = new Vector2(0.3f, 0.7f);
            rotationRange = new Vector2(0f, 360f);
            fillInsideChance = 0.8f;
            useRandomCenterChance = 0.3f;
        }

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Test Random Generation", ButtonSizes.Medium)]
        private void TestRandomGeneration()
        {
            var randomFilter = CreateRandomFilterInternal();
            Debug.Log($"Generated Random Butterfly Filter - Test successful!");
        }

        #endregion
    }
}
