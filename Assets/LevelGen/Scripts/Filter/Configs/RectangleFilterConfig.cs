using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;
using Sirenix.OdinInspector;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Rectangle Filter với random generation ranges
    /// </summary>
    [CreateAssetMenu(fileName = "RectangleFilter", menuName = "LevelGen/Filters/Rectangle Filter")]
    public class RectangleFilterConfigBase : FilterConfigBase
    {
        [Header("Rectangle Parameters")]
        [Range(0.1f, 2f)] public float widthScale = 0.6f;
        [Range(0.1f, 2f)] public float heightScale = 0.4f;
        public bool fillInside = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.5f;
        public bool roundedCorners = false;
        [Range(0.05f, 0.5f)] public float cornerRadiusScale = 0.1f;

        [Header("Random Generation Settings")]
        [SerializeField] private bool enableRandomGeneration = true;

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges", ShowLabel = true)]
        [Header("Size Ranges")]
        [SerializeField] private Vector2 widthScaleRange = new Vector2(0.3f, 1.5f);
        [SerializeField] private Vector2 heightScaleRange = new Vector2(0.3f, 1.5f);
        [SerializeField] private Vector2 cornerRadiusRange = new Vector2(0.05f, 0.5f);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Position Ranges")]
        [SerializeField] private Vector2 centerRange = new Vector2(0.3f, 0.7f);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Probability Settings")]
        [Range(0f, 1f)]
        [SerializeField] private float fillInsideChance = 0.8f;
        [Range(0f, 1f)]
        [SerializeField] private float roundedCornersChance = 0.5f;
        [Range(0f, 1f)]
        [SerializeField] private float useRandomCenterChance = 0.3f;
        [Range(0f, 1f)]
        [SerializeField] private float useSquareShapeChance = 0.3f;

        public override string GetFilterName()
        {
            return "Rectangle Filter";
        }

        public override IFilter CreateFilter()
        {
            return new RectangleFilter(widthScale, heightScale, fillInside, centerX, centerY, roundedCorners, cornerRadiusScale);
        }

        protected override IFilter CreateRandomFilterInternal()
        {
            if (!enableRandomGeneration)
            {
                return CreateFilter();
            }

            // Random size (có thể square hoặc rectangle)
            float randomWidthScale, randomHeightScale;
            if (RandomBool(useSquareShapeChance))
            {
                // Square shape - same width and height
                float scale = RandomInRange(widthScaleRange);
                randomWidthScale = scale;
                randomHeightScale = scale;
            }
            else
            {
                // Rectangle shape - different width and height
                randomWidthScale = RandomInRange(widthScaleRange);
                randomHeightScale = RandomInRange(heightScaleRange);
            }

            // Random fill inside
            bool randomFillInside = RandomBool(fillInsideChance);

            // Random center position
            Vector2 centerPos = RandomBool(useRandomCenterChance) ?
                RandomCenter(centerRange) : MidCenter();

            // Random rounded corners
            bool randomRoundedCorners = RandomBool(roundedCornersChance);
            float randomCornerRadius = randomRoundedCorners ? RandomInRange(cornerRadiusRange) : 0f;

            return new RectangleFilter(
                randomWidthScale,
                randomHeightScale,
                randomFillInside,
                centerPos.x,
                centerPos.y,
                randomRoundedCorners,
                randomCornerRadius
            );
        }

        #region Utility Methods

        private float RandomInRange(Vector2 range)
        {
            return UnityEngine.Random.Range(range.x, range.y);
        }

        private Vector2 RandomCenter(Vector2 range)
        {
            return new Vector2(
                UnityEngine.Random.Range(range.x, range.y),
                UnityEngine.Random.Range(range.x, range.y)
            );
        }

        #endregion

        #region Editor Utilities

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Reset to Default Ranges", ButtonSizes.Medium)]
        private void ResetToDefaultRanges()
        {
            widthScaleRange = new Vector2(0.3f, 1.5f);
            heightScaleRange = new Vector2(0.3f, 1.5f);
            cornerRadiusRange = new Vector2(0.05f, 0.5f);
            centerRange = new Vector2(0.3f, 0.7f);
            fillInsideChance = 0.8f;
            roundedCornersChance = 0.5f;
            useRandomCenterChance = 0.3f;
            useSquareShapeChance = 0.3f;
        }

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Test Random Generation", ButtonSizes.Medium)]
        private void TestRandomGeneration()
        {
            var randomFilter = CreateRandomFilterInternal();
            Debug.Log($"Generated Random Rectangle Filter - Test successful!");
        }

        #endregion
    }
}
