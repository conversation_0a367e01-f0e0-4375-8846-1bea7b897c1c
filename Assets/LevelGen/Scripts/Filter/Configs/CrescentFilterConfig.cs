using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;
using Sirenix.OdinInspector;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Crescent Filter với random generation ranges
    /// </summary>
    [CreateAssetMenu(fileName = "CrescentFilter", menuName = "LevelGen/Filters/Crescent Filter")]
    public class CrescentFilterConfigBase : FilterConfigBase
    {
        [Header("Crescent Parameters")]
        [Range(0.2f, 1f)] public float outerRadiusScale = 0.4f;
        [Range(0.1f, 0.8f)] public float innerRadiusScale = 0.3f;
        [Range(0.05f, 0.5f)] public float offsetScale = 0.15f;
        public bool fillInside = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.5f;
        [Range(0f, 360f)] public float rotation = 0f;

        [Header("Random Generation Settings")]
        [SerializeField] private bool enableRandomGeneration = true;

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges", ShowLabel = true)]
        [Header("Crescent Parameter Ranges")]
        [SerializeField] private Vector2 outerRadiusScaleRange = new Vector2(0.3f, 0.7f);
        [SerializeField] private Vector2 innerRadiusScaleRange = new Vector2(0.15f, 0.5f);
        [SerializeField] private Vector2 offsetScaleRange = new Vector2(0.1f, 0.3f);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Position & Rotation Ranges")]
        [SerializeField] private Vector2 centerRange = new Vector2(0.3f, 0.7f);
        [SerializeField] private Vector2 rotationRange = new Vector2(0f, 360f);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Probability Settings")]
        [Range(0f, 1f)]
        [SerializeField] private float fillInsideChance = 0.8f;
        [Range(0f, 1f)]
        [SerializeField] private float useRandomCenterChance = 0.3f;

        public override string GetFilterName()
        {
            return "Crescent Filter";
        }

        public override IFilter CreateFilter()
        {
            return new CrescentFilter(outerRadiusScale, innerRadiusScale, offsetScale, fillInside, centerX, centerY, rotation);
        }

        protected override IFilter CreateRandomFilterInternal()
        {
            if (!enableRandomGeneration)
            {
                return CreateFilter();
            }

            // Random crescent parameters
            float randomOuterRadiusScale = RandomInRange(outerRadiusScaleRange);
            float randomInnerRadiusScale = RandomInRange(innerRadiusScaleRange);
            float randomOffsetScale = RandomInRange(offsetScaleRange);

            // Random fill inside
            bool randomFillInside = RandomBool(fillInsideChance);

            // Random center position
            Vector2 centerPos = RandomBool(useRandomCenterChance) ?
                RandomCenter(centerRange) : MidCenter();

            // Random rotation
            float randomRotation = RandomInRange(rotationRange);

            return new CrescentFilter(
                randomOuterRadiusScale,
                randomInnerRadiusScale,
                randomOffsetScale,
                randomFillInside,
                centerPos.x,
                centerPos.y,
                randomRotation
            );
        }

        #region Utility Methods

        private float RandomInRange(Vector2 range)
        {
            return UnityEngine.Random.Range(range.x, range.y);
        }

        private Vector2 RandomCenter(Vector2 range)
        {
            return new Vector2(
                UnityEngine.Random.Range(range.x, range.y),
                UnityEngine.Random.Range(range.x, range.y)
            );
        }

        #endregion

        #region Editor Utilities

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Reset to Default Ranges", ButtonSizes.Medium)]
        private void ResetToDefaultRanges()
        {
            outerRadiusScaleRange = new Vector2(0.3f, 0.7f);
            innerRadiusScaleRange = new Vector2(0.15f, 0.5f);
            offsetScaleRange = new Vector2(0.1f, 0.3f);
            centerRange = new Vector2(0.3f, 0.7f);
            rotationRange = new Vector2(0f, 360f);
            fillInsideChance = 0.8f;
            useRandomCenterChance = 0.3f;
        }

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Test Random Generation", ButtonSizes.Medium)]
        private void TestRandomGeneration()
        {
            var randomFilter = CreateRandomFilterInternal();
            Debug.Log($"Generated Random Crescent Filter - Test successful!");
        }

        #endregion
    }
}
