using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;
using Sirenix.OdinInspector;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Wave Filter với random generation ranges
    /// </summary>
    [CreateAssetMenu(fileName = "WaveFilter", menuName = "LevelGen/Filters/Wave Filter")]
    public class WaveFilterConfig : FilterConfigBase
    {
        [Header("Wave Parameters")]
        [Range(0.5f, 10f)] public float frequency = 2f;
        [Range(0.05f, 0.5f)] public float amplitudeScale = 0.1f;
        [Range(0.01f, 0.2f)] public float thicknessScale = 0.05f;
        public WaveFilter.WaveDirection direction = WaveFilter.WaveDirection.Horizontal;
        [Range(0f, 360f)] public float phase = 0f;
        public WaveFilter.WaveType waveType = WaveFilter.WaveType.Sine;

        [Header("Random Generation Settings")]
        [SerializeField] private bool enableRandomGeneration = true;

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges", ShowLabel = true)]
        [Header("Wave Parameter Ranges")]
        [SerializeField] private Vector2 frequencyRange = new Vector2(1f, 6f);
        [SerializeField] private Vector2 amplitudeScaleRange = new Vector2(0.05f, 0.3f);
        [SerializeField] private Vector2 thicknessScaleRange = new Vector2(0.02f, 0.1f);
        [SerializeField] private Vector2 phaseRange = new Vector2(0f, 360f);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Enum Selection Settings")]
        [SerializeField] private bool randomizeDirection = true;
        [SerializeField] private bool randomizeWaveType = true;

        [ShowIf("enableRandomGeneration")]
        [ShowIf("@!randomizeDirection")]
        [BoxGroup("Random Ranges")]
        [SerializeField] private WaveFilter.WaveDirection[] preferredDirections =
        {
            WaveFilter.WaveDirection.Horizontal,
            WaveFilter.WaveDirection.Vertical
        };

        [ShowIf("enableRandomGeneration")]
        [ShowIf("@!randomizeWaveType")]
        [BoxGroup("Random Ranges")]
        [SerializeField] private WaveFilter.WaveType[] preferredWaveTypes =
        {
            WaveFilter.WaveType.Sine,
            WaveFilter.WaveType.Cosine
        };

        public override string GetFilterName()
        {
            return "Wave Filter";
        }

        public override IFilter CreateFilter()
        {
            return new WaveFilter(frequency, amplitudeScale, thicknessScale, direction, phase, waveType);
        }

        protected override IFilter CreateRandomFilterInternal()
        {
            if (!enableRandomGeneration)
            {
                return CreateFilter();
            }

            // Random wave parameters
            float randomFrequency = RandomInRange(frequencyRange);
            float randomAmplitudeScale = RandomInRange(amplitudeScaleRange);
            float randomThicknessScale = RandomInRange(thicknessScaleRange);
            float randomPhase = RandomInRange(phaseRange);

            // Random direction
            WaveFilter.WaveDirection randomDirection;
            if (randomizeDirection)
            {
                var allDirections = System.Enum.GetValues(typeof(WaveFilter.WaveDirection));
                randomDirection = (WaveFilter.WaveDirection)allDirections.GetValue(
                    UnityEngine.Random.Range(0, allDirections.Length));
            }
            else
            {
                randomDirection = preferredDirections[UnityEngine.Random.Range(0, preferredDirections.Length)];
            }

            // Random wave type
            WaveFilter.WaveType randomWaveType;
            if (randomizeWaveType)
            {
                var allWaveTypes = System.Enum.GetValues(typeof(WaveFilter.WaveType));
                randomWaveType = (WaveFilter.WaveType)allWaveTypes.GetValue(
                    UnityEngine.Random.Range(0, allWaveTypes.Length));
            }
            else
            {
                randomWaveType = preferredWaveTypes[UnityEngine.Random.Range(0, preferredWaveTypes.Length)];
            }

            return new WaveFilter(
                randomFrequency,
                randomAmplitudeScale,
                randomThicknessScale,
                randomDirection,
                randomPhase,
                randomWaveType
            );
        }

        #region Utility Methods

        private float RandomInRange(Vector2 range)
        {
            return UnityEngine.Random.Range(range.x, range.y);
        }

        #endregion

        #region Editor Utilities

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Reset to Default Ranges", ButtonSizes.Medium)]
        private void ResetToDefaultRanges()
        {
            frequencyRange = new Vector2(1f, 6f);
            amplitudeScaleRange = new Vector2(0.05f, 0.3f);
            thicknessScaleRange = new Vector2(0.02f, 0.1f);
            phaseRange = new Vector2(0f, 360f);
            randomizeDirection = true;
            randomizeWaveType = true;
            preferredDirections = new[] { WaveFilter.WaveDirection.Horizontal, WaveFilter.WaveDirection.Vertical };
            preferredWaveTypes = new[] { WaveFilter.WaveType.Sine, WaveFilter.WaveType.Cosine };
        }

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Test Random Generation", ButtonSizes.Medium)]
        private void TestRandomGeneration()
        {
            var randomFilter = CreateRandomFilterInternal();
            Debug.Log($"Generated Random Wave Filter - Test successful!");
        }

        #endregion
    }
}
