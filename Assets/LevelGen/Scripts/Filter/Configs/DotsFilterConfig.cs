using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;
using Sirenix.OdinInspector;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Dots Filter với random generation ranges
    /// </summary>
    [CreateAssetMenu(fileName = "DotsFilter", menuName = "LevelGen/Filters/Dots Filter")]
    public class DotsFilterConfig : FilterConfigBase
    {
        [Header("Dots Parameters")]
        [Range(1, 20)] public int spacingX = 6;
        [Range(1, 20)] public int spacingY = 6;
        [Range(1, 10)] public int dotRadius = 2;
        [Range(-10, 10)] public int offsetX = 0;
        [Range(-10, 10)] public int offsetY = 0;
        public bool fillDots = true;

        [Header("Random Generation Settings")]
        [SerializeField] private bool enableRandomGeneration = true;

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges", ShowLabel = true)]
        [Header("Dots Parameter Ranges")]
        [SerializeField] private Vector2Int spacingRange = new Vector2Int(4, 15);
        [SerializeField] private Vector2Int dotRadiusRange = new Vector2Int(1, 6);
        [SerializeField] private Vector2Int offsetXRange = new Vector2Int(-5, 6);
        [SerializeField] private Vector2Int offsetYRange = new Vector2Int(-5, 6);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Probability Settings")]
        [Range(0f, 1f)]
        [SerializeField] private float fillDotsChance = 0.8f;
        [Range(0f, 1f)]
        [SerializeField] private float useSymmetricSpacingChance = 0.7f;
        [Range(0f, 1f)]
        [SerializeField] private float useSymmetricOffsetChance = 0.5f;

        public override string GetFilterName()
        {
            return "Dots Filter";
        }

        public override IFilter CreateFilter()
        {
            return new DotsFilter(spacingX, dotRadius, fillDots, offsetX, offsetY);
        }

        protected override IFilter CreateRandomFilterInternal()
        {
            if (!enableRandomGeneration)
            {
                return CreateFilter();
            }

            // Random spacing (có thể symmetric hoặc khác nhau)
            int randomSpacingX, randomSpacingY;
            if (RandomBool(useSymmetricSpacingChance))
            {
                // Symmetric spacing
                int spacing = RandomInRange(spacingRange);
                randomSpacingX = spacing;
                randomSpacingY = spacing;
            }
            else
            {
                // Different spacing for X and Y
                randomSpacingX = RandomInRange(spacingRange);
                randomSpacingY = RandomInRange(spacingRange);
            }

            // Random dot radius
            int randomDotRadius = RandomInRange(dotRadiusRange);

            // Random offset (có thể symmetric hoặc khác nhau)
            int randomOffsetX, randomOffsetY;
            if (RandomBool(useSymmetricOffsetChance))
            {
                // Symmetric offset
                int offset = RandomInRange(offsetXRange);
                randomOffsetX = offset;
                randomOffsetY = offset;
            }
            else
            {
                // Different offset for X and Y
                randomOffsetX = RandomInRange(offsetXRange);
                randomOffsetY = RandomInRange(offsetYRange);
            }

            // Random fill dots
            bool randomFillDots = RandomBool(fillDotsChance);

            return new DotsFilter(
                randomSpacingX,
                randomDotRadius,
                randomFillDots,
                randomOffsetX,
                randomOffsetY
            );
        }

        #region Utility Methods

        private int RandomInRange(Vector2Int range)
        {
            return UnityEngine.Random.Range(range.x, range.y);
        }

        #endregion

        #region Editor Utilities

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Reset to Default Ranges", ButtonSizes.Medium)]
        private void ResetToDefaultRanges()
        {
            spacingRange = new Vector2Int(4, 15);
            dotRadiusRange = new Vector2Int(1, 6);
            offsetXRange = new Vector2Int(-5, 6);
            offsetYRange = new Vector2Int(-5, 6);
            fillDotsChance = 0.8f;
            useSymmetricSpacingChance = 0.7f;
            useSymmetricOffsetChance = 0.5f;
        }

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Test Random Generation", ButtonSizes.Medium)]
        private void TestRandomGeneration()
        {
            var randomFilter = CreateRandomFilterInternal();
            Debug.Log($"Generated Random Dots Filter - Test successful!");
        }

        #endregion
    }
}
