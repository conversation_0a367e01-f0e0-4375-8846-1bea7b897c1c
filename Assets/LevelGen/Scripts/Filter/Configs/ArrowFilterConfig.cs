using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;
using Sirenix.OdinInspector;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Arrow Filter với random generation ranges
    /// </summary>
    [CreateAssetMenu(fileName = "ArrowFilter", menuName = "LevelGen/Filters/Arrow Filter")]
    public class ArrowFilterConfig : FilterConfigBase
    {
        [Header("Arrow Parameters")]
        [Range(0.2f, 1.5f)] public float lengthScale = 0.7f;
        [Range(0.1f, 0.8f)] public float headWidthScale = 0.4f;
        [Range(0.05f, 0.4f)] public float shaftWidthScale = 0.15f;
        [Range(0.1f, 0.6f)] public float headLengthScale = 0.3f;
        public bool fillInside = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.5f;
        [Range(0f, 360f)] public float rotation = 0f;

        [Header("Random Generation Settings")]
        [SerializeField] private bool enableRandomGeneration = true;

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges", ShowLabel = true)]
        [Header("Length & Size Ranges")]
        [SerializeField] private Vector2 lengthScaleRange = new Vector2(0.4f, 1.2f);
        [SerializeField] private Vector2 headWidthScaleRange = new Vector2(0.2f, 0.6f);
        [SerializeField] private Vector2 shaftWidthScaleRange = new Vector2(0.08f, 0.3f);
        [SerializeField] private Vector2 headLengthScaleRange = new Vector2(0.15f, 0.5f);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Position & Rotation Ranges")]
        [SerializeField] private Vector2 centerRange = new Vector2(0.3f, 0.7f);
        [SerializeField] private Vector2 rotationRange = new Vector2(0f, 360f);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Probability Settings")]
        [Range(0f, 1f)]
        [SerializeField] private float fillInsideChance = 0.8f;
        [Range(0f, 1f)]
        [SerializeField] private float useRandomCenterChance = 0.3f;

        public override string GetFilterName()
        {
            return "Arrow Filter";
        }

        public override IFilter CreateFilter()
        {
            return new ArrowFilter(lengthScale, headWidthScale, shaftWidthScale, headLengthScale,
                fillInside, centerX, centerY, rotation);
        }

        protected override IFilter CreateRandomFilterInternal()
        {
            if (!enableRandomGeneration)
            {
                return CreateFilter();
            }

            // Random dimensions
            float randomLengthScale = RandomInRange(lengthScaleRange);
            float randomHeadWidthScale = RandomInRange(headWidthScaleRange);
            float randomShaftWidthScale = RandomInRange(shaftWidthScaleRange);
            float randomHeadLengthScale = RandomInRange(headLengthScaleRange);

            // Random fill inside
            bool randomFillInside = RandomBool(fillInsideChance);

            // Random center position (có thể dùng center cố định hoặc random)
            Vector2 centerPos = RandomBool(useRandomCenterChance) ?
                RandomCenter(centerRange) : MidCenter();

            // Random rotation
            float randomRotation = RandomInRange(rotationRange);

            return new ArrowFilter(
                randomLengthScale,
                randomHeadWidthScale,
                randomShaftWidthScale,
                randomHeadLengthScale,
                randomFillInside,
                centerPos.x,
                centerPos.y,
                randomRotation
            );
        }

        #region Utility Methods

        /// <summary>
        /// Random float trong range
        /// </summary>
        private float RandomInRange(Vector2 range)
        {
            return UnityEngine.Random.Range(range.x, range.y);
        }

        /// <summary>
        /// Random center position trong range
        /// </summary>
        private Vector2 RandomCenter(Vector2 range)
        {
            return new Vector2(
                UnityEngine.Random.Range(range.x, range.y),
                UnityEngine.Random.Range(range.x, range.y)
            );
        }

        #endregion

        #region Editor Utilities

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Reset to Default Ranges", ButtonSizes.Medium)]
        private void ResetToDefaultRanges()
        {
            lengthScaleRange = new Vector2(0.4f, 1.2f);
            headWidthScaleRange = new Vector2(0.2f, 0.6f);
            shaftWidthScaleRange = new Vector2(0.08f, 0.3f);
            headLengthScaleRange = new Vector2(0.15f, 0.5f);
            centerRange = new Vector2(0.3f, 0.7f);
            rotationRange = new Vector2(0f, 360f);
            fillInsideChance = 0.8f;
            useRandomCenterChance = 0.3f;
        }

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Test Random Generation", ButtonSizes.Medium)]
        private void TestRandomGeneration()
        {
            var randomFilter = CreateRandomFilterInternal();
            Debug.Log($"Generated Random Arrow Filter - Test successful!");
        }

        #endregion
    }
}
