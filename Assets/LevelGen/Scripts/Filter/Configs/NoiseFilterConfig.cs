using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;
using Sirenix.OdinInspector;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Noise Filter với random generation ranges
    /// </summary>
    [CreateAssetMenu(fileName = "NoiseFilter", menuName = "LevelGen/Filters/Noise Filter")]
    public class NoiseFilterConfigBase : FilterConfigBase
    {
        [Header("Noise Parameters")]
        [Range(0.01f, 0.5f)] public float scale = 0.1f;
        [Range(0f, 1f)] public float threshold = 0.5f;
        public bool fillAboveThreshold = true;
        [Range(1, 6)] public int octaves = 1;
        [Range(0.1f, 1f)] public float persistence = 0.5f;
        [Range(1f, 4f)] public float lacunarity = 2f;
        public Vector2 offset = Vector2.zero;
        public int seed = 0;

        [Header("Random Generation Settings")]
        [SerializeField] private bool enableRandomGeneration = true;

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges", ShowLabel = true)]
        [Header("Noise Parameter Ranges")]
        [SerializeField] private Vector2 scaleRange = new Vector2(0.05f, 0.3f);
        [SerializeField] private Vector2 thresholdRange = new Vector2(0.3f, 0.7f);
        [SerializeField] private Vector2Int octavesRange = new Vector2Int(1, 4);
        [SerializeField] private Vector2 persistenceRange = new Vector2(0.3f, 0.8f);
        [SerializeField] private Vector2 lacunarityRange = new Vector2(1.5f, 3f);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Offset & Seed Ranges")]
        [SerializeField] private Vector2 offsetRange = new Vector2(-100f, 100f);
        [SerializeField] private Vector2Int seedRange = new Vector2Int(1, 1000);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Probability Settings")]
        [Range(0f, 1f)]
        [SerializeField] private float fillAboveThresholdChance = 0.5f;

        public override string GetFilterName()
        {
            return "Noise Filter";
        }

        public override IFilter CreateFilter()
        {
            return new NoiseFilter(scale, threshold, fillAboveThreshold, octaves, persistence, lacunarity, offset, seed);
        }

        protected override IFilter CreateRandomFilterInternal()
        {
            if (!enableRandomGeneration)
            {
                return CreateFilter();
            }

            return new NoiseFilter(
                RandomInRange(scaleRange),
                RandomInRange(thresholdRange),
                RandomBool(fillAboveThresholdChance),
                RandomInRange(octavesRange),
                RandomInRange(persistenceRange),
                RandomInRange(lacunarityRange),
                new Vector2(
                    RandomInRange(offsetRange),
                    RandomInRange(offsetRange)
                ),
                RandomInRange(seedRange)
            );
        }

        #region Utility Methods

        private float RandomInRange(Vector2 range)
        {
            return UnityEngine.Random.Range(range.x, range.y);
        }

        private int RandomInRange(Vector2Int range)
        {
            return UnityEngine.Random.Range(range.x, range.y);
        }

        #endregion

        #region Editor Utilities

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Reset to Default Ranges", ButtonSizes.Medium)]
        private void ResetToDefaultRanges()
        {
            scaleRange = new Vector2(0.05f, 0.3f);
            thresholdRange = new Vector2(0.3f, 0.7f);
            octavesRange = new Vector2Int(1, 4);
            persistenceRange = new Vector2(0.3f, 0.8f);
            lacunarityRange = new Vector2(1.5f, 3f);
            offsetRange = new Vector2(-100f, 100f);
            seedRange = new Vector2Int(1, 1000);
            fillAboveThresholdChance = 0.5f;
        }

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Test Random Generation", ButtonSizes.Medium)]
        private void TestRandomGeneration()
        {
            var randomFilter = CreateRandomFilterInternal();
            Debug.Log($"Generated Random Noise Filter - Test successful!");
        }

        #endregion
    }
}
