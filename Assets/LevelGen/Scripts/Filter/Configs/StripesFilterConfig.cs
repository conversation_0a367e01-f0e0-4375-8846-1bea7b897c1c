using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;
using Sirenix.OdinInspector;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Stripes Filter với random generation ranges
    /// </summary>
    [CreateAssetMenu(fileName = "StripesFilter", menuName = "LevelGen/Filters/Stripes Filter")]
    public class StripesFilterConfig : FilterConfigBase
    {
        [Header("Stripes Parameters")]
        [Range(1, 20)] public int stripeWidth = 4;
        [Range(1, 20)] public int gapWidth = 4;
        [Range(0f, 360f)] public float rotation = 0f;
        public bool fillStripes = true;
        [Range(-20, 20)] public int offsetX = 0;
        [Range(-20, 20)] public int offsetY = 0;

        [Header("Random Generation Settings")]
        [SerializeField] private bool enableRandomGeneration = true;

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges", ShowLabel = true)]
        [Header("Stripes Parameter Ranges")]
        [SerializeField] private Vector2Int stripeWidthRange = new Vector2Int(2, 12);
        [SerializeField] private Vector2Int gapWidthRange = new Vector2Int(2, 12);
        [SerializeField] private Vector2 rotationRange = new Vector2(0f, 360f);
        [SerializeField] private Vector2Int offsetXRange = new Vector2Int(-10, 11);
        [SerializeField] private Vector2Int offsetYRange = new Vector2Int(-10, 11);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Probability Settings")]
        [Range(0f, 1f)]
        [SerializeField] private float fillStripesChance = 0.7f;
        [Range(0f, 1f)]
        [SerializeField] private float useSymmetricWidthChance = 0.5f;
        [Range(0f, 1f)]
        [SerializeField] private float useSymmetricOffsetChance = 0.4f;
        [Range(0f, 1f)]
        [SerializeField] private float useCardinalRotationChance = 0.6f; // 0°, 45°, 90°, 135°

        public override string GetFilterName()
        {
            return "Stripes Filter";
        }

        public override IFilter CreateFilter()
        {
            return new StripesFilter(stripeWidth, gapWidth, rotation, fillStripes, offsetX, offsetY);
        }

        protected override IFilter CreateRandomFilterInternal()
        {
            if (!enableRandomGeneration)
            {
                return CreateFilter();
            }

            // Random stripe and gap widths (có thể symmetric hoặc khác nhau)
            int randomStripeWidth, randomGapWidth;
            if (RandomBool(useSymmetricWidthChance))
            {
                // Symmetric width
                int width = RandomInRange(stripeWidthRange);
                randomStripeWidth = width;
                randomGapWidth = width;
            }
            else
            {
                // Different widths
                randomStripeWidth = RandomInRange(stripeWidthRange);
                randomGapWidth = RandomInRange(gapWidthRange);
            }

            // Random rotation (có thể cardinal directions hoặc random)
            float randomRotation;
            if (RandomBool(useCardinalRotationChance))
            {
                // Cardinal directions: 0°, 45°, 90°, 135°
                float[] cardinalAngles = { 0f, 45f, 90f, 135f };
                randomRotation = cardinalAngles[UnityEngine.Random.Range(0, cardinalAngles.Length)];
            }
            else
            {
                // Any rotation
                randomRotation = RandomInRange(rotationRange);
            }

            // Random fill stripes
            bool randomFillStripes = RandomBool(fillStripesChance);

            // Random offset (có thể symmetric hoặc khác nhau)
            int randomOffsetX, randomOffsetY;
            if (RandomBool(useSymmetricOffsetChance))
            {
                // Symmetric offset
                int offset = RandomInRange(offsetXRange);
                randomOffsetX = offset;
                randomOffsetY = offset;
            }
            else
            {
                // Different offsets
                randomOffsetX = RandomInRange(offsetXRange);
                randomOffsetY = RandomInRange(offsetYRange);
            }

            return new StripesFilter(
                randomStripeWidth,
                randomGapWidth,
                randomRotation,
                randomFillStripes,
                randomOffsetX,
                randomOffsetY
            );
        }

        #region Utility Methods

        private int RandomInRange(Vector2Int range)
        {
            return UnityEngine.Random.Range(range.x, range.y);
        }

        private float RandomInRange(Vector2 range)
        {
            return UnityEngine.Random.Range(range.x, range.y);
        }

        #endregion

        #region Editor Utilities

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Reset to Default Ranges", ButtonSizes.Medium)]
        private void ResetToDefaultRanges()
        {
            stripeWidthRange = new Vector2Int(2, 12);
            gapWidthRange = new Vector2Int(2, 12);
            rotationRange = new Vector2(0f, 360f);
            offsetXRange = new Vector2Int(-10, 11);
            offsetYRange = new Vector2Int(-10, 11);
            fillStripesChance = 0.7f;
            useSymmetricWidthChance = 0.5f;
            useSymmetricOffsetChance = 0.4f;
            useCardinalRotationChance = 0.6f;
        }

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Test Random Generation", ButtonSizes.Medium)]
        private void TestRandomGeneration()
        {
            var randomFilter = CreateRandomFilterInternal();
            Debug.Log($"Generated Random Stripes Filter - Test successful!");
        }

        #endregion
    }
}
