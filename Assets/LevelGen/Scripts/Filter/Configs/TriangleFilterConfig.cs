using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;
using Sirenix.OdinInspector;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Triangle Filter với random generation ranges
    /// </summary>
    [CreateAssetMenu(fileName = "TriangleFilter", menuName = "LevelGen/Filters/Triangle Filter")]
    public class TriangleFilterConfigBase : FilterConfigBase
    {
        [Header("Triangle Parameters")]
        [Range(0.1f, 2f)] public float sizeScale = 0.6f;
        public bool fillInside = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.5f;
        [Range(0f, 360f)] public float rotation = 0f;
        public TriangleFilter.TriangleType triangleType = TriangleFilter.TriangleType.Equilateral;

        [Header("Random Generation Settings")]
        [SerializeField] private bool enableRandomGeneration = true;

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges", ShowLabel = true)]
        [Header("Triangle Parameter Ranges")]
        [SerializeField] private Vector2 sizeScaleRange = new Vector2(0.3f, 1.2f);
        [SerializeField] private Vector2 rotationRange = new Vector2(0f, 360f);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Position Ranges")]
        [SerializeField] private Vector2 centerRange = new Vector2(0.3f, 0.7f);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Triangle Type Settings")]
        [SerializeField] private bool randomizeTriangleType = true;

        [ShowIf("enableRandomGeneration")]
        [ShowIf("@!randomizeTriangleType")]
        [BoxGroup("Random Ranges")]
        [SerializeField] private TriangleFilter.TriangleType[] preferredTriangleTypes =
        {
            TriangleFilter.TriangleType.Equilateral,
            TriangleFilter.TriangleType.Right
        };

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Probability Settings")]
        [Range(0f, 1f)]
        [SerializeField] private float fillInsideChance = 0.8f;
        [Range(0f, 1f)]
        [SerializeField] private float useRandomCenterChance = 0.3f;

        public override string GetFilterName()
        {
            return "Triangle Filter";
        }

        public override IFilter CreateFilter()
        {
            return new TriangleFilter(sizeScale, fillInside, centerX, centerY, rotation, triangleType);
        }

        protected override IFilter CreateRandomFilterInternal()
        {
            if (!enableRandomGeneration)
            {
                return CreateFilter();
            }

            // Random triangle parameters
            float randomSizeScale = RandomInRange(sizeScaleRange);
            float randomRotation = RandomInRange(rotationRange);

            // Random fill inside
            bool randomFillInside = RandomBool(fillInsideChance);

            // Random center position
            Vector2 centerPos = RandomBool(useRandomCenterChance) ?
                RandomCenter(centerRange) : MidCenter();

            // Random triangle type
            TriangleFilter.TriangleType randomTriangleType;
            if (randomizeTriangleType)
            {
                var allTriangleTypes = System.Enum.GetValues(typeof(TriangleFilter.TriangleType));
                randomTriangleType = (TriangleFilter.TriangleType)allTriangleTypes.GetValue(
                    UnityEngine.Random.Range(0, allTriangleTypes.Length));
            }
            else
            {
                randomTriangleType = preferredTriangleTypes[
                    UnityEngine.Random.Range(0, preferredTriangleTypes.Length)];
            }

            return new TriangleFilter(
                randomSizeScale,
                randomFillInside,
                centerPos.x,
                centerPos.y,
                randomRotation,
                randomTriangleType
            );
        }

        #region Utility Methods

        private float RandomInRange(Vector2 range)
        {
            return UnityEngine.Random.Range(range.x, range.y);
        }

        private Vector2 RandomCenter(Vector2 range)
        {
            return new Vector2(
                UnityEngine.Random.Range(range.x, range.y),
                UnityEngine.Random.Range(range.x, range.y)
            );
        }

        #endregion

        #region Editor Utilities

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Reset to Default Ranges", ButtonSizes.Medium)]
        private void ResetToDefaultRanges()
        {
            sizeScaleRange = new Vector2(0.3f, 1.2f);
            rotationRange = new Vector2(0f, 360f);
            centerRange = new Vector2(0.3f, 0.7f);
            randomizeTriangleType = true;
            preferredTriangleTypes = new[]
            {
                TriangleFilter.TriangleType.Equilateral,
                TriangleFilter.TriangleType.Right
            };
            fillInsideChance = 0.8f;
            useRandomCenterChance = 0.3f;
        }

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Test Random Generation", ButtonSizes.Medium)]
        private void TestRandomGeneration()
        {
            var randomFilter = CreateRandomFilterInternal();
            Debug.Log($"Generated Random Triangle Filter - Test successful!");
        }

        #endregion
    }
}
