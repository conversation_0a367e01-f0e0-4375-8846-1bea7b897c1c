using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;
using Sirenix.OdinInspector;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Smoothing Filter với random generation ranges
    /// </summary>
    [CreateAssetMenu(fileName = "SmoothingFilter", menuName = "LevelGen/Filters/Smoothing Filter")]
    public class SmoothingFilterConfigBase : FilterConfigBase
    {
        [Header("Smoothing Parameters")]
        public SmoothingFilter.SmoothingType smoothingType = SmoothingFilter.SmoothingType.Gaussian;
        [Range(1, 5)] public int iterations = 1;
        [Range(0f, 1f)] public float threshold = 0.5f;

        [Header("Random Generation Settings")]
        [SerializeField] private bool enableRandomGeneration = true;

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges", ShowLabel = true)]
        [Header("Smoothing Parameter Ranges")]
        [SerializeField] private Vector2Int iterationsRange = new Vector2Int(1, 4);
        [SerializeField] private Vector2 thresholdRange = new Vector2(0.3f, 0.7f);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Smoothing Type Settings")]
        [SerializeField] private bool randomizeSmoothingType = true;

        [ShowIf("enableRandomGeneration")]
        [ShowIf("@!randomizeSmoothingType")]
        [BoxGroup("Random Ranges")]
        [SerializeField] private SmoothingFilter.SmoothingType[] preferredSmoothingTypes =
        {
            SmoothingFilter.SmoothingType.Gaussian,
            SmoothingFilter.SmoothingType.Median
        };

        public override string GetFilterName()
        {
            return "Smoothing Filter";
        }

        public override IFilter CreateFilter()
        {
            return new SmoothingFilter(smoothingType, iterations, threshold);
        }

        protected override IFilter CreateRandomFilterInternal()
        {
            if (!enableRandomGeneration)
            {
                return CreateFilter();
            }

            // Random iterations and threshold
            int randomIterations = RandomInRange(iterationsRange);
            float randomThreshold = RandomInRange(thresholdRange);

            // Random smoothing type
            SmoothingFilter.SmoothingType randomSmoothingType;
            if (randomizeSmoothingType)
            {
                var allSmoothingTypes = System.Enum.GetValues(typeof(SmoothingFilter.SmoothingType));
                randomSmoothingType = (SmoothingFilter.SmoothingType)allSmoothingTypes.GetValue(
                    UnityEngine.Random.Range(0, allSmoothingTypes.Length));
            }
            else
            {
                randomSmoothingType = preferredSmoothingTypes[
                    UnityEngine.Random.Range(0, preferredSmoothingTypes.Length)];
            }

            return new SmoothingFilter(
                randomSmoothingType,
                randomIterations,
                randomThreshold
            );
        }

        #region Utility Methods

        private int RandomInRange(Vector2Int range)
        {
            return UnityEngine.Random.Range(range.x, range.y);
        }

        private float RandomInRange(Vector2 range)
        {
            return UnityEngine.Random.Range(range.x, range.y);
        }

        #endregion

        #region Editor Utilities

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Reset to Default Ranges", ButtonSizes.Medium)]
        private void ResetToDefaultRanges()
        {
            iterationsRange = new Vector2Int(1, 4);
            thresholdRange = new Vector2(0.3f, 0.7f);
            randomizeSmoothingType = true;
            preferredSmoothingTypes = new[]
            {
                SmoothingFilter.SmoothingType.Gaussian,
                SmoothingFilter.SmoothingType.Median
            };
        }

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Test Random Generation", ButtonSizes.Medium)]
        private void TestRandomGeneration()
        {
            var randomFilter = CreateRandomFilterInternal();
            Debug.Log($"Generated Random Smoothing Filter - Test successful!");
        }

        #endregion
    }
}
