using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;
using Sirenix.OdinInspector;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Polygon Filter
    /// </summary>
    [CreateAssetMenu(fileName = "PolygonFilter", menuName = "LevelGen/Filters/Polygon Filter")]
    public class PolygonFilterConfigBase : FilterConfigBase
    {
        [Header("Polygon Parameters")]
        [Range(3, 12)] public int sides = 6;
        [Range(0.1f, 2f)] public float radiusScale = 0.4f;
        public bool fillInside = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.5f;
        [Range(0f, 360f)] public float rotation = 0f;

        public override string GetFilterName()
        {
            return "Polygon Filter";
        }

        public override IFilter CreateFilter()
        {
            return new PolygonFilter(sides, radiusScale, fillInside, centerX, centerY, rotation);
        }
        
        [Header("Random Generation Settings")]
        [SerializeField] private bool enableRandomGeneration = true;

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges", ShowLabel = true)]
        [Header("Polygon Parameter Ranges")]
        [SerializeField] private Vector2Int sidesRange = new Vector2Int(3, 10);
        [SerializeField] private Vector2 radiusScaleRange = new Vector2(0.3f, 0.8f);
        [SerializeField] private Vector2 rotationRange = new Vector2(0f, 360f);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Position Ranges")]
        [SerializeField] private Vector2 centerRange = new Vector2(0.3f, 0.7f);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Probability Settings")]
        [Range(0f, 1f)]
        [SerializeField] private float fillInsideChance = 0.8f;
        [Range(0f, 1f)]
        [SerializeField] private float useRandomCenterChance = 0.3f;

        protected override IFilter CreateRandomFilterInternal()
        {
            if (!enableRandomGeneration)
            {
                return CreateFilter();
            }

            // Random polygon parameters
            int randomSides = RandomInRange(sidesRange);
            float randomRadiusScale = RandomInRange(radiusScaleRange);
            float randomRotation = RandomInRange(rotationRange);

            // Random fill inside
            bool randomFillInside = RandomBool(fillInsideChance);

            // Random center position
            Vector2 centerPos = RandomBool(useRandomCenterChance) ?
                RandomCenter(centerRange) : MidCenter();

            return new PolygonFilter(
                randomSides,
                randomRadiusScale,
                randomFillInside,
                centerPos.x,
                centerPos.y,
                randomRotation
            );
        }

        #region Utility Methods

        private int RandomInRange(Vector2Int range)
        {
            return UnityEngine.Random.Range(range.x, range.y);
        }

        private float RandomInRange(Vector2 range)
        {
            return UnityEngine.Random.Range(range.x, range.y);
        }

        private Vector2 RandomCenter(Vector2 range)
        {
            return new Vector2(
                UnityEngine.Random.Range(range.x, range.y),
                UnityEngine.Random.Range(range.x, range.y)
            );
        }

        #endregion

        #region Editor Utilities

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Reset to Default Ranges", ButtonSizes.Medium)]
        private void ResetToDefaultRanges()
        {
            sidesRange = new Vector2Int(3, 10);
            radiusScaleRange = new Vector2(0.3f, 0.8f);
            rotationRange = new Vector2(0f, 360f);
            centerRange = new Vector2(0.3f, 0.7f);
            fillInsideChance = 0.8f;
            useRandomCenterChance = 0.3f;
        }

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Test Random Generation", ButtonSizes.Medium)]
        private void TestRandomGeneration()
        {
            var randomFilter = CreateRandomFilterInternal();
            Debug.Log($"Generated Random Polygon Filter - Test successful!");
        }

        #endregion
    }
}
