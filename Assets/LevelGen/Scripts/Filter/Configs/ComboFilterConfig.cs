using System.Collections.Generic;
using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;
using Sirenix.OdinInspector;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Combo Filter với random generation ranges
    /// </summary>
    [CreateAssetMenu(fileName = "ComboFilter", menuName = "LevelGen/Filters/Combo Filter")]
    public class ComboFilterConfig : FilterConfigBase
    {
        [Header("Combo Parameters")]
        public ComboFilter.ComboMode mode = ComboFilter.ComboMode.Sequential;

        [Header("Filter Configs")]
        [InfoBox("Add filter configs that will be combined")]
        [SerializeField] private List<FilterConfigBase> filterConfigs = new List<FilterConfigBase>();

        [Header("Random Generation Settings")]
        [SerializeField] private bool enableRandomGeneration = true;

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges", ShowLabel = true)]
        [Header("Combo Parameter Ranges")]
        [SerializeField] private Vector2Int filterCountRange = new Vector2Int(2, 4);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Combo Mode Settings")]
        [SerializeField] private bool randomizeComboMode = true;

        [ShowIf("enableRandomGeneration")]
        [ShowIf("@!randomizeComboMode")]
        [BoxGroup("Random Ranges")]
        [SerializeField] private ComboFilter.ComboMode[] preferredComboModes =
        {
            ComboFilter.ComboMode.Union,
            ComboFilter.ComboMode.Intersection
        };

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Filter Type Preferences")]
        [SerializeField] private bool includeShapeFilters = true;
        [SerializeField] private bool includePatternFilters = true;
        [SerializeField] private bool includeProcessingFilters = false; // Usually too complex for random combos

        public override string GetFilterName()
        {
            return "Combo Filter";
        }

        public override IFilter CreateFilter()
        {
            var filters = new List<IFilter>();

            foreach (var config in filterConfigs)
            {
                if (config != null)
                {
                    filters.Add(config.CreateFilter());
                }
            }

            return new ComboFilter(filters.ToArray(), mode);
        }

        protected override IFilter CreateRandomFilterInternal()
        {
            if (!enableRandomGeneration)
            {
                return CreateFilter();
            }

            // Random filter count
            int randomFilterCount = RandomInRange(filterCountRange);

            // Random combo mode
            ComboFilter.ComboMode randomComboMode;
            if (randomizeComboMode)
            {
                var allComboModes = System.Enum.GetValues(typeof(ComboFilter.ComboMode));
                randomComboMode = (ComboFilter.ComboMode)allComboModes.GetValue(
                    UnityEngine.Random.Range(0, allComboModes.Length));
            }
            else
            {
                randomComboMode = preferredComboModes[
                    UnityEngine.Random.Range(0, preferredComboModes.Length)];
            }

            // Create random filters for the combo
            var filters = new List<IFilter>();
            for (int i = 0; i < randomFilterCount; i++)
            {
                IFilter randomFilter = CreateRandomSubFilter();
                if (randomFilter != null)
                {
                    filters.Add(randomFilter);
                }
            }

            // Ensure we have at least one filter
            if (filters.Count == 0)
            {
                filters.Add(new CircleFilter(0.3f, 0.3f, true, 0.5f, 0.5f));
            }

            return new ComboFilter(filters.ToArray(), randomComboMode);
        }

        /// <summary>
        /// Create a random sub-filter for the combo
        /// </summary>
        private IFilter CreateRandomSubFilter()
        {
            var availableTypes = new List<System.Type>();

            if (includeShapeFilters)
            {
                availableTypes.AddRange(new[]
                {
                    typeof(CircleFilter),
                    typeof(RectangleFilter),
                    typeof(TriangleFilter),
                    typeof(DiamondFilter)
                });
            }

            if (includePatternFilters)
            {
                availableTypes.AddRange(new[]
                {
                    typeof(NoiseFilter),
                    typeof(DotsFilter),
                    typeof(StripesFilter)
                });
            }

            if (includeProcessingFilters)
            {
                availableTypes.AddRange(new[]
                {
                    typeof(SmoothingFilter)
                });
            }

            if (availableTypes.Count == 0)
                return null;

            var randomType = availableTypes[UnityEngine.Random.Range(0, availableTypes.Count)];

            // Create simple instances of each filter type
            if (randomType == typeof(CircleFilter))
                return new CircleFilter(
                    UnityEngine.Random.Range(0.2f, 0.6f),
                    UnityEngine.Random.Range(0.2f, 0.6f),
                    true, 0.5f, 0.5f);

            if (randomType == typeof(RectangleFilter))
                return new RectangleFilter(
                    UnityEngine.Random.Range(0.3f, 0.8f),
                    UnityEngine.Random.Range(0.3f, 0.8f),
                    true, 0.5f, 0.5f, false, 0f);

            if (randomType == typeof(TriangleFilter))
                return new TriangleFilter(
                    UnityEngine.Random.Range(0.4f, 0.8f),
                    true, 0.5f, 0.5f, 0f,
                    TriangleFilter.TriangleType.Equilateral);

            if (randomType == typeof(DiamondFilter))
                return new DiamondFilter(
                    UnityEngine.Random.Range(0.4f, 0.8f),
                    true, 0.5f, 0.5f);

            if (randomType == typeof(NoiseFilter))
                return new NoiseFilter(
                    UnityEngine.Random.Range(0.1f, 0.3f),
                    UnityEngine.Random.Range(0.4f, 0.6f),
                    true, 1, 0.5f, 2f, Vector2.zero, 0);

            if (randomType == typeof(DotsFilter))
                return new DotsFilter(
                    UnityEngine.Random.Range(4, 8),
                    UnityEngine.Random.Range(1, 3),
                    true, 0, 0);

            if (randomType == typeof(StripesFilter))
                return new StripesFilter(
                    UnityEngine.Random.Range(3, 8),
                    UnityEngine.Random.Range(3, 8),
                    UnityEngine.Random.Range(0f, 180f),
                    true, 0, 0);

            if (randomType == typeof(SmoothingFilter))
                return new SmoothingFilter(
                    SmoothingFilter.SmoothingType.Gaussian,
                    1, 0.5f);

            return null;
        }

        #region Utility Methods

        private int RandomInRange(Vector2Int range)
        {
            return UnityEngine.Random.Range(range.x, range.y);
        }

        #endregion

        #region Editor Utilities

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Reset to Default Ranges", ButtonSizes.Medium)]
        private void ResetToDefaultRanges()
        {
            filterCountRange = new Vector2Int(2, 4);
            randomizeComboMode = true;
            preferredComboModes = new[]
            {
                ComboFilter.ComboMode.Union,
                ComboFilter.ComboMode.Intersection
            };
            includeShapeFilters = true;
            includePatternFilters = true;
            includeProcessingFilters = false;
        }

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Test Random Generation", ButtonSizes.Medium)]
        private void TestRandomGeneration()
        {
            var randomFilter = CreateRandomFilterInternal();
            Debug.Log($"Generated Random Combo Filter - Test successful!");
        }

        #endregion

        [Button("Add Filter Config")]
        public void AddFilterConfig(FilterConfigBase config)
        {
            if (config != null && !filterConfigs.Contains(config))
            {
                filterConfigs.Add(config);
            }
        }

        [Button("Remove Filter Config")]
        public void RemoveFilterConfig(FilterConfigBase config)
        {
            filterConfigs.Remove(config);
        }

        [Button("Clear All Filters")]
        public void ClearAllFilters()
        {
            filterConfigs.Clear();
        }

        public List<FilterConfigBase> GetFilterConfigs()
        {
            return new List<FilterConfigBase>(filterConfigs);
        }
    }
}
