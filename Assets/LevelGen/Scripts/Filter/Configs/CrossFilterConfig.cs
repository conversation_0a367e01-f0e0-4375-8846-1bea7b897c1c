using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;
using Sirenix.OdinInspector;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Cross Filter với random generation ranges
    /// </summary>
    [CreateAssetMenu(fileName = "CrossFilter", menuName = "LevelGen/Filters/Cross Filter")]
    public class CrossFilterConfigBase : FilterConfigBase
    {
        [Header("Cross Parameters")]
        [Range(0.05f, 0.5f)] public float horizontalWidthScale = 0.1f;
        [Range(0.05f, 0.5f)] public float verticalWidthScale = 0.1f;
        [Range(0.2f, 2f)] public float horizontalLengthScale = 0.8f;
        [Range(0.2f, 2f)] public float verticalLengthScale = 0.8f;
        public bool fillInside = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.5f;
        [Range(0f, 360f)] public float rotation = 0f;

        [Header("Random Generation Settings")]
        [SerializeField] private bool enableRandomGeneration = true;

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges", ShowLabel = true)]
        [Header("Cross Parameter Ranges")]
        [SerializeField] private Vector2 horizontalWidthScaleRange = new Vector2(0.05f, 0.3f);
        [SerializeField] private Vector2 verticalWidthScaleRange = new Vector2(0.05f, 0.3f);
        [SerializeField] private Vector2 horizontalLengthScaleRange = new Vector2(0.4f, 1.5f);
        [SerializeField] private Vector2 verticalLengthScaleRange = new Vector2(0.4f, 1.5f);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Position & Rotation Ranges")]
        [SerializeField] private Vector2 centerRange = new Vector2(0.3f, 0.7f);
        [SerializeField] private Vector2 rotationRange = new Vector2(0f, 360f);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Probability Settings")]
        [Range(0f, 1f)]
        [SerializeField] private float fillInsideChance = 0.8f;
        [Range(0f, 1f)]
        [SerializeField] private float useRandomCenterChance = 0.3f;
        [Range(0f, 1f)]
        [SerializeField] private float useSymmetricWidthChance = 0.6f;
        [Range(0f, 1f)]
        [SerializeField] private float useSymmetricLengthChance = 0.7f;

        public override string GetFilterName()
        {
            return "Cross Filter";
        }

        public override IFilter CreateFilter()
        {
            return new CrossFilter(horizontalWidthScale, verticalWidthScale, horizontalLengthScale, verticalLengthScale,
                fillInside, centerX, centerY, rotation);
        }

        protected override IFilter CreateRandomFilterInternal()
        {
            if (!enableRandomGeneration)
            {
                return CreateFilter();
            }

            // Random width scales (có thể symmetric hoặc khác nhau)
            float randomHorizontalWidthScale, randomVerticalWidthScale;
            if (RandomBool(useSymmetricWidthChance))
            {
                // Symmetric width
                float width = RandomInRange(horizontalWidthScaleRange);
                randomHorizontalWidthScale = width;
                randomVerticalWidthScale = width;
            }
            else
            {
                // Different widths
                randomHorizontalWidthScale = RandomInRange(horizontalWidthScaleRange);
                randomVerticalWidthScale = RandomInRange(verticalWidthScaleRange);
            }

            // Random length scales (có thể symmetric hoặc khác nhau)
            float randomHorizontalLengthScale, randomVerticalLengthScale;
            if (RandomBool(useSymmetricLengthChance))
            {
                // Symmetric length
                float length = RandomInRange(horizontalLengthScaleRange);
                randomHorizontalLengthScale = length;
                randomVerticalLengthScale = length;
            }
            else
            {
                // Different lengths
                randomHorizontalLengthScale = RandomInRange(horizontalLengthScaleRange);
                randomVerticalLengthScale = RandomInRange(verticalLengthScaleRange);
            }

            // Random fill inside
            bool randomFillInside = RandomBool(fillInsideChance);

            // Random center position
            Vector2 centerPos = RandomBool(useRandomCenterChance) ?
                RandomCenter(centerRange) : MidCenter();

            // Random rotation
            float randomRotation = RandomInRange(rotationRange);

            return new CrossFilter(
                randomHorizontalWidthScale,
                randomVerticalWidthScale,
                randomHorizontalLengthScale,
                randomVerticalLengthScale,
                randomFillInside,
                centerPos.x,
                centerPos.y,
                randomRotation
            );
        }

        #region Utility Methods

        private float RandomInRange(Vector2 range)
        {
            return UnityEngine.Random.Range(range.x, range.y);
        }

        private Vector2 RandomCenter(Vector2 range)
        {
            return new Vector2(
                UnityEngine.Random.Range(range.x, range.y),
                UnityEngine.Random.Range(range.x, range.y)
            );
        }

        #endregion

        #region Editor Utilities

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Reset to Default Ranges", ButtonSizes.Medium)]
        private void ResetToDefaultRanges()
        {
            horizontalWidthScaleRange = new Vector2(0.05f, 0.3f);
            verticalWidthScaleRange = new Vector2(0.05f, 0.3f);
            horizontalLengthScaleRange = new Vector2(0.4f, 1.5f);
            verticalLengthScaleRange = new Vector2(0.4f, 1.5f);
            centerRange = new Vector2(0.3f, 0.7f);
            rotationRange = new Vector2(0f, 360f);
            fillInsideChance = 0.8f;
            useRandomCenterChance = 0.3f;
            useSymmetricWidthChance = 0.6f;
            useSymmetricLengthChance = 0.7f;
        }

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Test Random Generation", ButtonSizes.Medium)]
        private void TestRandomGeneration()
        {
            var randomFilter = CreateRandomFilterInternal();
            Debug.Log($"Generated Random Cross Filter - Test successful!");
        }

        #endregion
    }
}
