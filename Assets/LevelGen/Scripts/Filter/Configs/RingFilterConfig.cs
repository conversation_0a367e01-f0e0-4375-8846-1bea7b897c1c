using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;
using Sirenix.OdinInspector;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Ring Filter với random generation ranges
    /// </summary>
    [CreateAssetMenu(fileName = "RingFilter", menuName = "LevelGen/Filters/Ring Filter")]
    public class RingFilterConfig : FilterConfigBase
    {
        [Header("Ring Parameters")]
        [Range(0.1f, 1f)] public float outerRadiusScale = 0.4f;
        [Range(0.05f, 0.8f)] public float innerRadiusScale = 0.2f;
        public bool fillInside = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.5f;

        [Header("Random Generation Settings")]
        [SerializeField] private bool enableRandomGeneration = true;

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges", ShowLabel = true)]
        [Header("Ring Parameter Ranges")]
        [SerializeField] private Vector2 outerRadiusScaleRange = new Vector2(0.2f, 0.8f);
        [SerializeField] private Vector2 innerRadiusScaleRange = new Vector2(0.1f, 0.5f);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Position Ranges")]
        [SerializeField] private Vector2 centerRange = new Vector2(0.3f, 0.7f);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Probability Settings")]
        [Range(0f, 1f)]
        [SerializeField] private float fillInsideChance = 0.8f;
        [Range(0f, 1f)]
        [SerializeField] private float useRandomCenterChance = 0.3f;
        [Range(0f, 1f)]
        [SerializeField] private float ensureValidRingChance = 0.9f; // Ensure inner < outer

        public override string GetFilterName()
        {
            return "Ring Filter";
        }

        public override IFilter CreateFilter()
        {
            return new RingFilter(outerRadiusScale, innerRadiusScale, fillInside, centerX, centerY);
        }

        protected override IFilter CreateRandomFilterInternal()
        {
            if (!enableRandomGeneration)
            {
                return CreateFilter();
            }

            // Random ring parameters with validation
            float randomOuterRadiusScale = RandomInRange(outerRadiusScaleRange);
            float randomInnerRadiusScale;

            if (RandomBool(ensureValidRingChance))
            {
                // Ensure inner radius is smaller than outer radius
                float maxInner = Mathf.Min(innerRadiusScaleRange.y, randomOuterRadiusScale * 0.8f);
                randomInnerRadiusScale = UnityEngine.Random.Range(innerRadiusScaleRange.x, maxInner);
            }
            else
            {
                // Allow any inner radius (might create interesting effects)
                randomInnerRadiusScale = RandomInRange(innerRadiusScaleRange);
            }

            // Random fill inside
            bool randomFillInside = RandomBool(fillInsideChance);

            // Random center position
            Vector2 centerPos = RandomBool(useRandomCenterChance) ?
                RandomCenter(centerRange) : MidCenter();

            return new RingFilter(
                randomOuterRadiusScale,
                randomInnerRadiusScale,
                randomFillInside,
                centerPos.x,
                centerPos.y
            );
        }

        #region Utility Methods

        private float RandomInRange(Vector2 range)
        {
            return UnityEngine.Random.Range(range.x, range.y);
        }

        private Vector2 RandomCenter(Vector2 range)
        {
            return new Vector2(
                UnityEngine.Random.Range(range.x, range.y),
                UnityEngine.Random.Range(range.x, range.y)
            );
        }

        #endregion

        #region Editor Utilities

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Reset to Default Ranges", ButtonSizes.Medium)]
        private void ResetToDefaultRanges()
        {
            outerRadiusScaleRange = new Vector2(0.2f, 0.8f);
            innerRadiusScaleRange = new Vector2(0.1f, 0.5f);
            centerRange = new Vector2(0.3f, 0.7f);
            fillInsideChance = 0.8f;
            useRandomCenterChance = 0.3f;
            ensureValidRingChance = 0.9f;
        }

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Test Random Generation", ButtonSizes.Medium)]
        private void TestRandomGeneration()
        {
            var randomFilter = CreateRandomFilterInternal();
            Debug.Log($"Generated Random Ring Filter - Test successful!");
        }

        #endregion
    }
}
