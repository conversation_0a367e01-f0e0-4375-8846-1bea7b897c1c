using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;
using Sirenix.OdinInspector;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Connectivity Filter với random generation ranges
    /// </summary>
    [CreateAssetMenu(fileName = "ConnectivityFilter", menuName = "LevelGen/Filters/Connectivity Filter")]
    public class ConnectivityFilterConfigBase : FilterConfigBase
    {
        [Header("Connectivity Parameters")]
        [Range(1, 50)] public int minComponentSize = 5;
        public bool keepLargestComponent = true;
        public bool connectComponents = false;
        public ConnectivityFilter.ConnectivityType connectivityType = ConnectivityFilter.ConnectivityType.FourConnected;

        [Header("Random Generation Settings")]
        [SerializeField] private bool enableRandomGeneration = true;

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges", ShowLabel = true)]
        [Header("Component Size Range")]
        [SerializeField] private Vector2Int minComponentSizeRange = new Vector2Int(3, 15);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Probability Settings")]
        [Range(0f, 1f)]
        [SerializeField] private float keepLargestComponentChance = 0.6f;
        [Range(0f, 1f)]
        [SerializeField] private float connectComponentsChance = 0.3f;

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Connectivity Type Settings")]
        [SerializeField] private bool randomizeConnectivityType = true;

        [ShowIf("enableRandomGeneration")]
        [ShowIf("@!randomizeConnectivityType")]
        [BoxGroup("Random Ranges")]
        [SerializeField] private ConnectivityFilter.ConnectivityType[] preferredConnectivityTypes =
        {
            ConnectivityFilter.ConnectivityType.FourConnected,
            ConnectivityFilter.ConnectivityType.EightConnected
        };

        public override string GetFilterName()
        {
            return "Connectivity Filter";
        }

        public override IFilter CreateFilter()
        {
            return new ConnectivityFilter(minComponentSize, keepLargestComponent, connectComponents, connectivityType);
        }

        protected override IFilter CreateRandomFilterInternal()
        {
            if (!enableRandomGeneration)
            {
                return CreateFilter();
            }

            // Random component size
            int randomMinComponentSize = RandomInRange(minComponentSizeRange);

            // Random boolean settings
            bool randomKeepLargestComponent = RandomBool(keepLargestComponentChance);
            bool randomConnectComponents = RandomBool(connectComponentsChance);

            // Random connectivity type
            ConnectivityFilter.ConnectivityType randomConnectivityType;
            if (randomizeConnectivityType)
            {
                var allConnectivityTypes = System.Enum.GetValues(typeof(ConnectivityFilter.ConnectivityType));
                randomConnectivityType = (ConnectivityFilter.ConnectivityType)allConnectivityTypes.GetValue(
                    UnityEngine.Random.Range(0, allConnectivityTypes.Length));
            }
            else
            {
                randomConnectivityType = preferredConnectivityTypes[
                    UnityEngine.Random.Range(0, preferredConnectivityTypes.Length)];
            }

            return new ConnectivityFilter(
                randomMinComponentSize,
                randomKeepLargestComponent,
                randomConnectComponents,
                randomConnectivityType
            );
        }

        #region Utility Methods

        private int RandomInRange(Vector2Int range)
        {
            return UnityEngine.Random.Range(range.x, range.y);
        }

        #endregion

        #region Editor Utilities

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Reset to Default Ranges", ButtonSizes.Medium)]
        private void ResetToDefaultRanges()
        {
            minComponentSizeRange = new Vector2Int(3, 15);
            keepLargestComponentChance = 0.6f;
            connectComponentsChance = 0.3f;
            randomizeConnectivityType = true;
            preferredConnectivityTypes = new[]
            {
                ConnectivityFilter.ConnectivityType.FourConnected,
                ConnectivityFilter.ConnectivityType.EightConnected
            };
        }

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Test Random Generation", ButtonSizes.Medium)]
        private void TestRandomGeneration()
        {
            var randomFilter = CreateRandomFilterInternal();
            Debug.Log($"Generated Random Connectivity Filter - Test successful!");
        }

        #endregion
    }
}
