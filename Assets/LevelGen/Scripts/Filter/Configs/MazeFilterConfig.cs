using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Maze Filter
    /// </summary>
    [CreateAssetMenu(fileName = "MazeFilter", menuName = "LevelGen/Filters/Maze Filter")]
    public class MazeFilterConfig : FilterConfigBase
    {
        [Header("Maze Parameters")]
        [Range(1, 5)] public int wallThickness = 1;
        [Range(1, 5)] public int pathWidth = 2;
        public int seed = 0;
        public bool ensureConnectivity = true;

        public override string GetFilterName()
        {
            return "Maze Filter";
        }

        public override IFilter CreateFilter()
        {
            return new MazeFilter(wallThickness, pathWidth, seed, ensureConnectivity);
        }
        
        protected override IFilter CreateRandomFilterInternal()
        {
            return new MazeFilter(
                UnityEngine.Random.Range(1, 4), // wallThickness
                UnityEngine.Random.Range(1, 4), // pathWidth
                UnityEngine.Random.Range(1, 10000), // seed
                RandomBool(0.8f) // ensureConnectivity (80% chance)
            );
        }
    }
}
