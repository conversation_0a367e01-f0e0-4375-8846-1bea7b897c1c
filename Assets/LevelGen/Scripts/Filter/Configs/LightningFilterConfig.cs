using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Lightning Filter
    /// </summary>
    [CreateAssetMenu(fileName = "LightningFilter", menuName = "LevelGen/Filters/Lightning Filter")]
    public class LightningFilterConfig : FilterConfigBase
    {
        [Header("Lightning Parameters")]
        [Range(0.1f, 1f)] public float lengthScale = 0.6f;
        [Range(0.01f, 0.1f)] public float thicknessScale = 0.03f;
        [Range(3, 20)] public int segments = 8;
        [Range(0f, 1f)] public float zigzagIntensity = 0.3f;
        public bool fillInside = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.5f;
        [Range(0f, 360f)] public float rotation = 0f;
        [Range(0, 1000)] public int seed = 0;

        public override string GetFilterName()
        {
            return "Lightning Filter";
        }

        public override IFilter CreateFilter()
        {
            return new LightningFilter(lengthScale, thicknessScale, segments, zigzagIntensity, 
                fillInside, centerX, centerY, rotation, seed);
        }
        
        protected override IFilter CreateRandomFilterInternal()
        {
            return new LightningFilter(
                UnityEngine.Random.Range(0.3f, 0.8f), // lengthScale
                UnityEngine.Random.Range(0.02f, 0.06f), // thicknessScale
                UnityEngine.Random.Range(5, 15), // segments
                UnityEngine.Random.Range(0.1f, 0.6f), // zigzagIntensity
                RandomBool(0.7f), // fillInside (70% chance)
                MidCenter().x, // centerX
                MidCenter().y, // centerY
                RandomRotation(), // rotation
                UnityEngine.Random.Range(1, 1000) // seed
            );
        }
    }
}
