using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;
using Sirenix.OdinInspector;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Checkerboard Filter với random generation ranges
    /// </summary>
    [CreateAssetMenu(fileName = "CheckerboardFilter", menuName = "LevelGen/Filters/Checkerboard Filter")]
    public class CheckerboardFilterConfig : FilterConfigBase
    {
        [Header("Checkerboard Parameters")]
        [Range(1, 20)] public int squareSize = 4;
        public bool invertPattern = false;
        [Range(-10, 10)] public int offsetX = 0;
        [Range(-10, 10)] public int offsetY = 0;

        [Header("Random Generation Settings")]
        [SerializeField] private bool enableRandomGeneration = true;

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges", ShowLabel = true)]
        [Header("Checkerboard Parameter Ranges")]
        [SerializeField] private Vector2Int squareSizeRange = new Vector2Int(2, 12);
        [SerializeField] private Vector2Int offsetXRange = new Vector2Int(-5, 6);
        [SerializeField] private Vector2Int offsetYRange = new Vector2Int(-5, 6);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Probability Settings")]
        [Range(0f, 1f)]
        [SerializeField] private float invertPatternChance = 0.5f;
        [Range(0f, 1f)]
        [SerializeField] private float useSymmetricOffsetChance = 0.6f;

        public override string GetFilterName()
        {
            return "Checkerboard Filter";
        }

        public override IFilter CreateFilter()
        {
            return new CheckerboardFilter(squareSize, invertPattern, offsetX, offsetY);
        }

        protected override IFilter CreateRandomFilterInternal()
        {
            if (!enableRandomGeneration)
            {
                return CreateFilter();
            }

            // Random square size
            int randomSquareSize = RandomInRange(squareSizeRange);

            // Random invert pattern
            bool randomInvertPattern = RandomBool(invertPatternChance);

            // Random offset (có thể symmetric hoặc khác nhau)
            int randomOffsetX, randomOffsetY;
            if (RandomBool(useSymmetricOffsetChance))
            {
                // Symmetric offset
                int offset = RandomInRange(offsetXRange);
                randomOffsetX = offset;
                randomOffsetY = offset;
            }
            else
            {
                // Different offsets
                randomOffsetX = RandomInRange(offsetXRange);
                randomOffsetY = RandomInRange(offsetYRange);
            }

            return new CheckerboardFilter(
                randomSquareSize,
                randomInvertPattern,
                randomOffsetX,
                randomOffsetY
            );
        }

        #region Utility Methods

        private int RandomInRange(Vector2Int range)
        {
            return UnityEngine.Random.Range(range.x, range.y);
        }

        #endregion

        #region Editor Utilities

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Reset to Default Ranges", ButtonSizes.Medium)]
        private void ResetToDefaultRanges()
        {
            squareSizeRange = new Vector2Int(2, 12);
            offsetXRange = new Vector2Int(-5, 6);
            offsetYRange = new Vector2Int(-5, 6);
            invertPatternChance = 0.5f;
            useSymmetricOffsetChance = 0.6f;
        }

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Test Random Generation", ButtonSizes.Medium)]
        private void TestRandomGeneration()
        {
            var randomFilter = CreateRandomFilterInternal();
            Debug.Log($"Generated Random Checkerboard Filter - Test successful!");
        }

        #endregion
    }
}
