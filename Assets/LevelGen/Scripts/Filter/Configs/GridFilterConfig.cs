using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;
using Sirenix.OdinInspector;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Grid Filter với random generation ranges
    /// </summary>
    [CreateAssetMenu(fileName = "GridFilter", menuName = "LevelGen/Filters/Grid Filter")]
    public class GridFilterConfig : FilterConfigBase
    {
        [Header("Grid Parameters")]
        [Range(1, 20)] public int cellWidth = 4;
        [Range(1, 20)] public int cellHeight = 4;
        [Range(1, 5)] public int lineThickness = 1;
        public bool fillCells = false;
        [Range(-10, 10)] public int offsetX = 0;
        [Range(-10, 10)] public int offsetY = 0;

        [Header("Random Generation Settings")]
        [SerializeField] private bool enableRandomGeneration = true;

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges", ShowLabel = true)]
        [Header("Grid Parameter Ranges")]
        [SerializeField] private Vector2Int cellWidthRange = new Vector2Int(3, 12);
        [SerializeField] private Vector2Int cellHeightRange = new Vector2Int(3, 12);
        [SerializeField] private Vector2Int lineThicknessRange = new Vector2Int(1, 3);
        [SerializeField] private Vector2Int offsetXRange = new Vector2Int(-5, 6);
        [SerializeField] private Vector2Int offsetYRange = new Vector2Int(-5, 6);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Probability Settings")]
        [Range(0f, 1f)]
        [SerializeField] private float fillCellsChance = 0.3f;
        [Range(0f, 1f)]
        [SerializeField] private float useSquareCellsChance = 0.7f;
        [Range(0f, 1f)]
        [SerializeField] private float useSymmetricOffsetChance = 0.5f;

        public override string GetFilterName()
        {
            return "Grid Filter";
        }

        public override IFilter CreateFilter()
        {
            return new GridFilter(cellWidth, lineThickness, !fillCells, offsetX, offsetY);
        }

        protected override IFilter CreateRandomFilterInternal()
        {
            if (!enableRandomGeneration)
            {
                return CreateFilter();
            }

            // Random cell size (có thể square hoặc rectangular)
            int randomCellWidth, randomCellHeight;
            if (RandomBool(useSquareCellsChance))
            {
                // Square cells - same width and height
                int cellSize = RandomInRange(cellWidthRange);
                randomCellWidth = cellSize;
                randomCellHeight = cellSize;
            }
            else
            {
                // Rectangular cells - different width and height
                randomCellWidth = RandomInRange(cellWidthRange);
                randomCellHeight = RandomInRange(cellHeightRange);
            }

            // Random line thickness
            int randomLineThickness = RandomInRange(lineThicknessRange);

            // Random fill cells
            bool randomFillCells = RandomBool(fillCellsChance);

            // Random offset (có thể symmetric hoặc khác nhau)
            int randomOffsetX, randomOffsetY;
            if (RandomBool(useSymmetricOffsetChance))
            {
                // Symmetric offset
                int offset = RandomInRange(offsetXRange);
                randomOffsetX = offset;
                randomOffsetY = offset;
            }
            else
            {
                // Different offsets
                randomOffsetX = RandomInRange(offsetXRange);
                randomOffsetY = RandomInRange(offsetYRange);
            }

            return new GridFilter(
                randomCellWidth,
                randomLineThickness,
                !randomFillCells, // GridFilter uses fillLines, which is opposite of fillCells
                randomOffsetX,
                randomOffsetY
            );
        }

        #region Utility Methods

        private int RandomInRange(Vector2Int range)
        {
            return UnityEngine.Random.Range(range.x, range.y);
        }

        #endregion

        #region Editor Utilities

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Reset to Default Ranges", ButtonSizes.Medium)]
        private void ResetToDefaultRanges()
        {
            cellWidthRange = new Vector2Int(3, 12);
            cellHeightRange = new Vector2Int(3, 12);
            lineThicknessRange = new Vector2Int(1, 3);
            offsetXRange = new Vector2Int(-5, 6);
            offsetYRange = new Vector2Int(-5, 6);
            fillCellsChance = 0.3f;
            useSquareCellsChance = 0.7f;
            useSymmetricOffsetChance = 0.5f;
        }

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Test Random Generation", ButtonSizes.Medium)]
        private void TestRandomGeneration()
        {
            var randomFilter = CreateRandomFilterInternal();
            Debug.Log($"Generated Random Grid Filter - Test successful!");
        }

        #endregion
    }
}
