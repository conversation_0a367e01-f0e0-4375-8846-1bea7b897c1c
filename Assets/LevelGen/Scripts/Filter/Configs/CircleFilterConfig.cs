using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;
using Sirenix.OdinInspector;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Circle Filter với random generation ranges
    /// </summary>
    [CreateAssetMenu(fileName = "CircleFilter", menuName = "LevelGen/Filters/Circle Filter")]
    public class CircleFilterConfigBase : FilterConfigBase
    {
        [Header("Circle Parameters")]
        [Range(0.1f, 2f)] public float scaleX = 0.4f;
        [Range(0.1f, 2f)] public float scaleY = 0.4f;
        public bool fillInside = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.5f;

        [Header("Random Generation Settings")]
        [SerializeField] private bool enableRandomGeneration = true;

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges", ShowLabel = true)]
        [Header("Scale Ranges")]
        [SerializeField] private Vector2 scaleXRange = new Vector2(0.2f, 1.5f);
        [SerializeField] private Vector2 scaleYRange = new Vector2(0.2f, 1.5f);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Position Ranges")]
        [SerializeField] private Vector2 centerRange = new Vector2(0.3f, 0.7f);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Probability Settings")]
        [Range(0f, 1f)]
        [SerializeField] private float fillInsideChance = 0.8f;
        [Range(0f, 1f)]
        [SerializeField] private float useRandomCenterChance = 0.3f;
        [Range(0f, 1f)]
        [SerializeField] private float useCircleShapeChance = 0.6f;

        public override string GetFilterName()
        {
            return "Circle Filter";
        }

        public override IFilter CreateFilter()
        {
            return new CircleFilter(scaleX, scaleY, fillInside, centerX, centerY);
        }

        protected override IFilter CreateRandomFilterInternal()
        {
            if (!enableRandomGeneration)
            {
                return CreateFilter();
            }

            // Random scale (có thể circle hoặc oval)
            float randomScaleX, randomScaleY;
            if (RandomBool(useCircleShapeChance))
            {
                // Circle shape - same X and Y scale
                float scale = RandomInRange(scaleXRange);
                randomScaleX = scale;
                randomScaleY = scale;
            }
            else
            {
                // Oval shape - different X and Y scale
                randomScaleX = RandomInRange(scaleXRange);
                randomScaleY = RandomInRange(scaleYRange);
            }

            // Random fill inside
            bool randomFillInside = RandomBool(fillInsideChance);

            // Random center position
            Vector2 centerPos = RandomBool(useRandomCenterChance) ?
                RandomCenter(centerRange) : MidCenter();

            return new CircleFilter(
                randomScaleX,
                randomScaleY,
                randomFillInside,
                centerPos.x,
                centerPos.y
            );
        }

        #region Utility Methods

        private float RandomInRange(Vector2 range)
        {
            return UnityEngine.Random.Range(range.x, range.y);
        }

        private Vector2 RandomCenter(Vector2 range)
        {
            return new Vector2(
                UnityEngine.Random.Range(range.x, range.y),
                UnityEngine.Random.Range(range.x, range.y)
            );
        }

        #endregion

        #region Editor Utilities

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Reset to Default Ranges", ButtonSizes.Medium)]
        private void ResetToDefaultRanges()
        {
            scaleXRange = new Vector2(0.2f, 1.5f);
            scaleYRange = new Vector2(0.2f, 1.5f);
            centerRange = new Vector2(0.3f, 0.7f);
            fillInsideChance = 0.8f;
            useRandomCenterChance = 0.3f;
            useCircleShapeChance = 0.6f;
        }

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Test Random Generation", ButtonSizes.Medium)]
        private void TestRandomGeneration()
        {
            var randomFilter = CreateRandomFilterInternal();
            Debug.Log($"Generated Random Circle Filter - Test successful!");
        }

        #endregion
    }
}
