using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;
using Sirenix.OdinInspector;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Heart Filter với random generation ranges
    /// </summary>
    [CreateAssetMenu(fileName = "HeartFilter", menuName = "LevelGen/Filters/Heart Filter")]
    public class HeartFilterConfigBase : FilterConfigBase
    {
        [Header("Heart Parameters")]
        [Range(0.1f, 1f)] public float sizeScale = 0.3f;
        public bool fillInside = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.6f;

        [Header("Random Generation Settings")]
        [SerializeField] private bool enableRandomGeneration = true;

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges", ShowLabel = true)]
        [Header("Heart Parameter Ranges")]
        [SerializeField] private Vector2 sizeScaleRange = new Vector2(0.2f, 0.6f);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Position Ranges")]
        [SerializeField] private Vector2 centerXRange = new Vector2(0.3f, 0.7f);
        [SerializeField] private Vector2 centerYRange = new Vector2(0.4f, 0.8f); // Heart looks better when positioned higher

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Probability Settings")]
        [Range(0f, 1f)]
        [SerializeField] private float fillInsideChance = 0.8f;
        [Range(0f, 1f)]
        [SerializeField] private float useRandomCenterChance = 0.3f;

        public override string GetFilterName()
        {
            return "Heart Filter";
        }

        public override IFilter CreateFilter()
        {
            return new HeartFilter(sizeScale, fillInside, centerX, centerY);
        }

        protected override IFilter CreateRandomFilterInternal()
        {
            if (!enableRandomGeneration)
            {
                return CreateFilter();
            }

            // Random size
            float randomSizeScale = RandomInRange(sizeScaleRange);

            // Random fill inside
            bool randomFillInside = RandomBool(fillInsideChance);

            // Random center position
            Vector2 centerPos;
            if (RandomBool(useRandomCenterChance))
            {
                centerPos = new Vector2(
                    RandomInRange(centerXRange),
                    RandomInRange(centerYRange)
                );
            }
            else
            {
                centerPos = new Vector2(0.5f, 0.6f); // Default heart position
            }

            return new HeartFilter(
                randomSizeScale,
                randomFillInside,
                centerPos.x,
                centerPos.y
            );
        }

        #region Utility Methods

        private float RandomInRange(Vector2 range)
        {
            return UnityEngine.Random.Range(range.x, range.y);
        }

        #endregion

        #region Editor Utilities

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Reset to Default Ranges", ButtonSizes.Medium)]
        private void ResetToDefaultRanges()
        {
            sizeScaleRange = new Vector2(0.2f, 0.6f);
            centerXRange = new Vector2(0.3f, 0.7f);
            centerYRange = new Vector2(0.4f, 0.8f);
            fillInsideChance = 0.8f;
            useRandomCenterChance = 0.3f;
        }

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Test Random Generation", ButtonSizes.Medium)]
        private void TestRandomGeneration()
        {
            var randomFilter = CreateRandomFilterInternal();
            Debug.Log($"Generated Random Heart Filter - Test successful!");
        }

        #endregion
    }
}
