using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;
using Sirenix.OdinInspector;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Diamond Filter với random generation ranges
    /// </summary>
    [CreateAssetMenu(fileName = "DiamondFilter", menuName = "LevelGen/Filters/Diamond Filter")]
    public class DiamondFilterConfigBase : FilterConfigBase
    {
        [Header("Diamond Parameters")]
        [Range(0.1f, 2f)] public float sizeScale = 0.8f;
        public bool fillInside = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.5f;

        [Header("Random Generation Settings")]
        [SerializeField] private bool enableRandomGeneration = true;

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges", ShowLabel = true)]
        [Header("Size Range")]
        [SerializeField] private Vector2 sizeScaleRange = new Vector2(0.4f, 1.4f);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Position Ranges")]
        [SerializeField] private Vector2 centerRange = new Vector2(0.3f, 0.7f);

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Header("Probability Settings")]
        [Range(0f, 1f)]
        [SerializeField] private float fillInsideChance = 0.8f;
        [Range(0f, 1f)]
        [SerializeField] private float useRandomCenterChance = 0.3f;

        public override string GetFilterName()
        {
            return "Diamond Filter";
        }

        public override IFilter CreateFilter()
        {
            return new DiamondFilter(sizeScale, fillInside, centerX, centerY);
        }

        protected override IFilter CreateRandomFilterInternal()
        {
            if (!enableRandomGeneration)
            {
                return CreateFilter();
            }

            // Random size
            float randomSizeScale = RandomInRange(sizeScaleRange);

            // Random fill inside
            bool randomFillInside = RandomBool(fillInsideChance);

            // Random center position
            Vector2 centerPos = RandomBool(useRandomCenterChance) ?
                RandomCenter(centerRange) : MidCenter();

            return new DiamondFilter(
                randomSizeScale,
                randomFillInside,
                centerPos.x,
                centerPos.y
            );
        }

        #region Utility Methods

        private float RandomInRange(Vector2 range)
        {
            return UnityEngine.Random.Range(range.x, range.y);
        }

        private Vector2 RandomCenter(Vector2 range)
        {
            return new Vector2(
                UnityEngine.Random.Range(range.x, range.y),
                UnityEngine.Random.Range(range.x, range.y)
            );
        }

        #endregion

        #region Editor Utilities

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Reset to Default Ranges", ButtonSizes.Medium)]
        private void ResetToDefaultRanges()
        {
            sizeScaleRange = new Vector2(0.4f, 1.4f);
            centerRange = new Vector2(0.3f, 0.7f);
            fillInsideChance = 0.8f;
            useRandomCenterChance = 0.3f;
        }

        [ShowIf("enableRandomGeneration")]
        [BoxGroup("Random Ranges")]
        [Button("Test Random Generation", ButtonSizes.Medium)]
        private void TestRandomGeneration()
        {
            var randomFilter = CreateRandomFilterInternal();
            Debug.Log($"Generated Random Diamond Filter - Test successful!");
        }

        #endregion
    }
}
