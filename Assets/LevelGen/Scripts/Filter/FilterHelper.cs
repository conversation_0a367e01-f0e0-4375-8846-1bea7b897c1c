using System.Collections.Generic;
using System.Linq;
using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter
{
    public static class FilterHelper
    {
        public static T[,] ApplyFilter<T>(this T[,] matrix, IFilter filter) where T : struct
        {
            return filter.Filter(matrix);
        }
        
        public static T[,] ApplyFilter<T>(this T[,] matrix, List<IFilter> filters) where T : struct
        {
            foreach (var filter in filters)
            {
                matrix = filter.Filter(matrix);
            }

            return matrix;
        }
        
        public static T[,] ApplyFilter<T>(this T[,] matrix, params IFilter[] filters) where T : struct
        {
            foreach (var filter in filters)
            {
                matrix = filter.Filter(matrix);
            }

            return matrix;
        }

        /// <summary>
        /// Áp dụng filter với probability
        /// </summary>
        public static T[,] ApplyFilterWithProbability<T>(this T[,] matrix, IFilter filter, float probability) where T : struct
        {
            if (Random.value <= probability)
            {
                return filter.Filter(matrix);
            }
            return matrix;
        }

        /// <summary>
        /// <PERSON><PERSON><PERSON> hợ<PERSON> kết qu<PERSON> của nhiều filter bằng cách lấy union
        /// </summary>
        public static T[,] CombineFiltersUnion<T>(this T[,] matrix, params IFilter[] filters) where T : struct
        {
            if (filters.Length == 0) return matrix;

            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            // Apply each filter and combine results
            List<T[,]> filteredResults = new List<T[,]>();
            foreach (var filter in filters)
            {
                if (filter != null)
                {
                    filteredResults.Add(filter.Filter(matrix));
                }
            }

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    bool hasValue = false;
                    foreach (var filteredMatrix in filteredResults)
                    {
                        if (!IsDefaultValue(filteredMatrix[i, j]))
                        {
                            result[i, j] = filteredMatrix[i, j];
                            hasValue = true;
                            break;
                        }
                    }

                    if (!hasValue)
                    {
                        result[i, j] = default(T);
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// Kết hợp kết quả của nhiều filter bằng cách lấy intersection
        /// </summary>
        public static T[,] CombineFiltersIntersection<T>(this T[,] matrix, params IFilter[] filters) where T : struct
        {
            if (filters.Length == 0) return matrix;

            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            // Apply each filter and combine results
            List<T[,]> filteredResults = new List<T[,]>();
            foreach (var filter in filters)
            {
                if (filter != null)
                {
                    filteredResults.Add(filter.Filter(matrix));
                }
            }

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    bool allHaveValue = true;
                    T valueToUse = default(T);

                    foreach (var filteredMatrix in filteredResults)
                    {
                        if (IsDefaultValue(filteredMatrix[i, j]))
                        {
                            allHaveValue = false;
                            break;
                        }
                        else
                        {
                            valueToUse = filteredMatrix[i, j];
                        }
                    }

                    result[i, j] = allHaveValue ? valueToUse : default(T);
                }
            }

            return result;
        }

        /// <summary>
        /// Tạo filter composite từ nhiều filter với weight
        /// </summary>
        public static T[,] ApplyWeightedFilters<T>(this T[,] matrix, (IFilter filter, float weight)[] weightedFilters) where T : struct
        {
            if (weightedFilters.Length == 0) return matrix;

            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            // Normalize weights
            float totalWeight = weightedFilters.Sum(wf => wf.weight);
            if (totalWeight <= 0) return matrix;

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    float weightedSum = 0f;
                    float actualTotalWeight = 0f;

                    foreach (var (filter, weight) in weightedFilters)
                    {
                        if (filter != null && weight > 0)
                        {
                            var filtered = filter.Filter(matrix);
                            float value = ConvertToFloat(filtered[i, j]);
                            weightedSum += value * weight;
                            actualTotalWeight += weight;
                        }
                    }

                    if (actualTotalWeight > 0)
                    {
                        float averageValue = weightedSum / actualTotalWeight;
                        result[i, j] = ConvertFromFloat<T>(averageValue);
                    }
                    else
                    {
                        result[i, j] = matrix[i, j];
                    }
                }
            }

            return result;
        }

        private static bool IsDefaultValue<T>(T value) where T : struct
        {
            return value.Equals(default(T));
        }

        private static float ConvertToFloat<T>(T value) where T : struct
        {
            if (value is int intValue)
                return intValue;
            if (value is float floatValue)
                return floatValue;
            if (value is double doubleValue)
                return (float)doubleValue;
            if (value is byte byteValue)
                return byteValue / 255f;

            return 0f;
        }

        private static T ConvertFromFloat<T>(float value) where T : struct
        {
            if (typeof(T) == typeof(int))
                return (T)(object)Mathf.RoundToInt(value);
            if (typeof(T) == typeof(float))
                return (T)(object)value;
            if (typeof(T) == typeof(double))
                return (T)(object)(double)value;
            if (typeof(T) == typeof(byte))
                return (T)(object)(byte)Mathf.Clamp(value * 255f, 0, 255);

            return default(T);
        }
    }
}