using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter
{
    /// <summary>
    /// Filter kết hợp nhiều filter khác
    /// </summary>
    public class ComboFilter : IFilter
    {
        private readonly IFilter[] filters;
        private readonly ComboMode mode;

        public enum ComboMode
        {
            Sequential,     // Áp dụng tuần tự
            Union,          // Kết hợp bằng OR
            Intersection,   // Kết hợp bằng AND
            Weighted        // Kết hợp có trọng số
        }

        public ComboFilter(IFilter[] filters, ComboMode mode = ComboMode.Sequential)
        {
            this.filters = filters ?? new IFilter[0];
            this.mode = mode;
        }

        public T[,] Filter<T>(T[,] matrix) where T : struct
        {
            if (filters.Length == 0)
                return matrix;

            switch (mode)
            {
                case ComboMode.Sequential:
                    return ApplySequential(matrix);
                
                case ComboMode.Union:
                    return ApplyUnion(matrix);
                
                case ComboMode.Intersection:
                    return ApplyIntersection(matrix);
                
                case ComboMode.Weighted:
                    return ApplyWeighted(matrix);
                
                default:
                    return ApplySequential(matrix);
            }
        }

        private T[,] ApplySequential<T>(T[,] matrix) where T : struct
        {
            T[,] result = matrix;
            foreach (var filter in filters)
            {
                if (filter != null)
                {
                    result = filter.Filter(result);
                }
            }
            return result;
        }

        private T[,] ApplyUnion<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            // Apply each filter and combine with OR logic
            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    bool hasValue = false;
                    T value = default(T);

                    foreach (var filter in filters)
                    {
                        if (filter != null)
                        {
                            var filterResult = filter.Filter(matrix);
                            if (!filterResult[i, j].Equals(default(T)))
                            {
                                hasValue = true;
                                value = filterResult[i, j];
                                break; // Use first non-default value
                            }
                        }
                    }

                    result[i, j] = hasValue ? value : default(T);
                }
            }

            return result;
        }

        private T[,] ApplyIntersection<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            // Apply each filter and combine with AND logic
            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    bool allHaveValue = true;
                    T value = matrix[i, j];

                    foreach (var filter in filters)
                    {
                        if (filter != null)
                        {
                            var filterResult = filter.Filter(matrix);
                            if (filterResult[i, j].Equals(default(T)))
                            {
                                allHaveValue = false;
                                break;
                            }
                        }
                    }

                    result[i, j] = allHaveValue ? value : default(T);
                }
            }

            return result;
        }

        private T[,] ApplyWeighted<T>(T[,] matrix) where T : struct
        {
            // For simplicity, just apply sequential for weighted mode
            // In a more advanced implementation, you could blend the results
            return ApplySequential(matrix);
        }
    }
}
