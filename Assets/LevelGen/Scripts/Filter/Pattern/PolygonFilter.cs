using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Pattern
{
    /// <summary>
    /// Filter tạo hình đa giác với số cạnh tùy ý
    /// </summary>
    public class PolygonFilter : IFilter
    {
        private readonly int sides;
        private readonly float radius;
        private readonly bool fillInside;
        private readonly float centerX;
        private readonly float centerY;
        private readonly float rotation;

        public PolygonFilter(int sides = 6, float radiusScale = 0.4f, bool fillInside = true,
            float centerX = 0.5f, float centerY = 0.5f, float rotation = 0f)
        {
            this.sides = Mathf.Max(3, sides);
            this.radius = radiusScale;
            this.fillInside = fillInside;
            this.centerX = centerX;
            this.centerY = centerY;
            this.rotation = rotation;
        }

        public T[,] Filter<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            float centerRow = (rows - 1) * centerY;
            float centerCol = (cols - 1) * centerX;

            // Tính radius dựa trên kích thước matrix và scale, đảm bảo minimum size
            float minSize = Mathf.Min(rows, cols);
            float polygonRadius = Mathf.Max(2f, minSize * radius * 0.5f);

            // VERTICAL SYMMETRY: Calculate polygon vertices with symmetric rotation
            Vector2[] vertices = new Vector2[sides];

            // For even-sided polygons, ensure one vertex is on Y-axis for symmetry
            float symmetricRotation = rotation;
            if (sides % 2 == 0)
            {
                // For even sides, align to maintain Y-axis symmetry
                symmetricRotation = Mathf.Round(rotation / (360f / sides)) * (360f / sides);
            }

            for (int i = 0; i < sides; i++)
            {
                float angle = (2 * Mathf.PI * i / sides) + (symmetricRotation * Mathf.Deg2Rad);
                vertices[i] = new Vector2(
                    centerCol + polygonRadius * Mathf.Cos(angle),
                    centerRow + polygonRadius * Mathf.Sin(angle)
                );
            }

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    Vector2 point = new Vector2(j, i);
                    bool isInside = IsPointInPolygon(point, vertices);

                    if ((fillInside && isInside) || (!fillInside && !isInside))
                    {
                        result[i, j] = matrix[i, j];
                    }
                    else
                    {
                        result[i, j] = default(T);
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// Kiểm tra xem điểm có nằm trong đa giác không (Ray casting algorithm)
        /// </summary>
        private bool IsPointInPolygon(Vector2 point, Vector2[] vertices)
        {
            int intersections = 0;
            int vertexCount = vertices.Length;

            for (int i = 0; i < vertexCount; i++)
            {
                Vector2 vertex1 = vertices[i];
                Vector2 vertex2 = vertices[(i + 1) % vertexCount];

                // Check if ray from point to the right intersects with edge
                if (((vertex1.y > point.y) != (vertex2.y > point.y)) &&
                    (point.x < (vertex2.x - vertex1.x) * (point.y - vertex1.y) / (vertex2.y - vertex1.y) + vertex1.x))
                {
                    intersections++;
                }
            }

            return (intersections % 2) == 1;
        }
    }
}
