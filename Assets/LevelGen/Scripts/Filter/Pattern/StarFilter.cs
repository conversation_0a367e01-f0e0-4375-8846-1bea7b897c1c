using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Pattern
{
    /// <summary>
    /// Filter tạo hình ngôi sao
    /// </summary>
    public class StarFilter : IFilter
    {
        private readonly int points;
        private readonly float outerRadiusScale;
        private readonly float innerRadiusScale;
        private readonly bool fillInside;
        private readonly float centerX;
        private readonly float centerY;
        private readonly float rotation;

        public StarFilter(int points = 5, float outerRadiusScale = 0.4f, float innerRadiusScale = 0.2f,
            bool fillInside = true, float centerX = 0.5f, float centerY = 0.5f, float rotation = 0f)
        {
            this.points = Mathf.Max(3, points);
            this.outerRadiusScale = outerRadiusScale;
            this.innerRadiusScale = innerRadiusScale;
            this.fillInside = fillInside;
            this.centerX = centerX;
            this.centerY = centerY;
            this.rotation = rotation;
        }

        public T[,] Filter<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            float centerRow = centerY * (rows - 1);
            float centerCol = centerX * (cols - 1);

            // Với matrix siêu nhỏ, tính radius phù hợp
            float minSize = Mathf.Min(rows, cols);
            float maxRadius = Mathf.Max(1.2f, minSize * outerRadiusScale * 0.4f);
            float minRadius = Mathf.Max(0.6f, Mathf.Min(maxRadius * 0.4f, minSize * innerRadiusScale * 0.3f));

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    float x = j - centerCol;
                    float y = i - centerRow;

                    float distance = Mathf.Sqrt(x * x + y * y);
                    float angle = Mathf.Atan2(y, x) + rotation * Mathf.Deg2Rad;

                    // Normalize angle to [0, 2π]
                    while (angle < 0) angle += 2 * Mathf.PI;
                    while (angle >= 2 * Mathf.PI) angle -= 2 * Mathf.PI;

                    // Calculate star radius at this angle
                    float starRadius = GetStarRadiusAtAngle(angle, maxRadius, minRadius);

                    bool isInside = distance <= starRadius;

                    if ((fillInside && isInside) || (!fillInside && !isInside))
                    {
                        result[i, j] = matrix[i, j];
                    }
                    else
                    {
                        result[i, j] = default(T);
                    }
                }
            }

            return result;
        }

        private float GetStarRadiusAtAngle(float angle, float outerR, float innerR)
        {
            float anglePerPoint = 2 * Mathf.PI / points;
            float halfAnglePerPoint = anglePerPoint * 0.5f;
            
            // Find which segment we're in
            float segmentAngle = angle % anglePerPoint;
            
            // Distance from center of segment
            float distFromCenter = Mathf.Abs(segmentAngle - halfAnglePerPoint);
            
            // Interpolate between inner and outer radius
            float t = distFromCenter / halfAnglePerPoint;
            return Mathf.Lerp(outerR, innerR, t);
        }
    }
}
