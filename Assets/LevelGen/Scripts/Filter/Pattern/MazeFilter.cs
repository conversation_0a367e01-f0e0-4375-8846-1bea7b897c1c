using System.Collections.Generic;
using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Pattern
{
    /// <summary>
    /// Filter tạo pattern mê cung
    /// </summary>
    public class MazeFilter : IFilter
    {
        private readonly int wallThickness;
        private readonly int pathWidth;
        private readonly int seed;
        private readonly bool ensureConnectivity;

        public MazeFilter(int wallThickness = 1, int pathWidth = 2, int seed = 0, bool ensureConnectivity = true)
        {
            this.wallThickness = Mathf.Max(1, wallThickness);
            this.pathWidth = Mathf.Max(1, pathWidth);
            this.seed = seed;
            this.ensureConnectivity = ensureConnectivity;
        }

        public T[,] Filter<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            // Tạo maze pattern
            bool[,] mazePattern = GenerateMazePattern(rows, cols);

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    if (mazePattern[i, j])
                    {
                        result[i, j] = matrix[i, j];
                    }
                    else
                    {
                        result[i, j] = default(T);
                    }
                }
            }

            return result;
        }

        private bool[,] GenerateMazePattern(int rows, int cols)
        {
            Random.State oldState = Random.state;
            if (seed != 0)
                Random.InitState(seed);

            bool[,] maze = new bool[rows, cols];
            int cellSize = wallThickness + pathWidth;

            // Tạo grid pattern cơ bản
            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    int cellI = i / cellSize;
                    int cellJ = j / cellSize;
                    int localI = i % cellSize;
                    int localJ = j % cellSize;

                    // Tạo đường đi
                    bool isPath = false;

                    // Đường đi ngang
                    if (localI < pathWidth && (cellJ % 2 == 0 || Random.value > 0.3f))
                    {
                        isPath = true;
                    }
                    // Đường đi dọc
                    else if (localJ < pathWidth && (cellI % 2 == 0 || Random.value > 0.3f))
                    {
                        isPath = true;
                    }
                    // Ngã tư
                    else if (localI < pathWidth && localJ < pathWidth && Random.value > 0.5f)
                    {
                        isPath = true;
                    }

                    maze[i, j] = isPath;
                }
            }

            // Đảm bảo connectivity nếu cần
            if (ensureConnectivity)
            {
                EnsureMazeConnectivity(maze, rows, cols);
            }

            Random.state = oldState;
            return maze;
        }

        private void EnsureMazeConnectivity(bool[,] maze, int rows, int cols)
        {
            // Tìm tất cả các vùng connected
            bool[,] visited = new bool[rows, cols];
            List<List<Vector2Int>> components = new List<List<Vector2Int>>();

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    if (maze[i, j] && !visited[i, j])
                    {
                        List<Vector2Int> component = new List<Vector2Int>();
                        FloodFill(maze, visited, i, j, rows, cols, component);
                        if (component.Count > 0)
                            components.Add(component);
                    }
                }
            }

            // Kết nối các component
            for (int i = 1; i < components.Count; i++)
            {
                ConnectComponents(maze, components[0], components[i], rows, cols);
            }
        }

        private void FloodFill(bool[,] maze, bool[,] visited, int startI, int startJ, int rows, int cols, List<Vector2Int> component)
        {
            Stack<Vector2Int> stack = new Stack<Vector2Int>();
            stack.Push(new Vector2Int(startI, startJ));

            int[] di = { -1, 1, 0, 0 };
            int[] dj = { 0, 0, -1, 1 };

            while (stack.Count > 0)
            {
                Vector2Int current = stack.Pop();
                int i = current.x;
                int j = current.y;

                if (i < 0 || i >= rows || j < 0 || j >= cols || visited[i, j] || !maze[i, j])
                    continue;

                visited[i, j] = true;
                component.Add(current);

                for (int d = 0; d < 4; d++)
                {
                    stack.Push(new Vector2Int(i + di[d], j + dj[d]));
                }
            }
        }

        private void ConnectComponents(bool[,] maze, List<Vector2Int> comp1, List<Vector2Int> comp2, int rows, int cols)
        {
            Vector2Int closest1 = comp1[0];
            Vector2Int closest2 = comp2[0];
            float minDistance = float.MaxValue;

            // Tìm 2 điểm gần nhất
            foreach (var p1 in comp1)
            {
                foreach (var p2 in comp2)
                {
                    float distance = Vector2Int.Distance(p1, p2);
                    if (distance < minDistance)
                    {
                        minDistance = distance;
                        closest1 = p1;
                        closest2 = p2;
                    }
                }
            }

            // Tạo đường kết nối
            CreatePath(maze, closest1, closest2, rows, cols);
        }

        private void CreatePath(bool[,] maze, Vector2Int start, Vector2Int end, int rows, int cols)
        {
            int currentI = start.x;
            int currentJ = start.y;

            // Di chuyển theo đường thẳng
            while (currentI != end.x || currentJ != end.y)
            {
                if (currentI >= 0 && currentI < rows && currentJ >= 0 && currentJ < cols)
                {
                    maze[currentI, currentJ] = true;
                }

                if (currentI < end.x) currentI++;
                else if (currentI > end.x) currentI--;
                else if (currentJ < end.y) currentJ++;
                else if (currentJ > end.y) currentJ--;
            }

            if (end.x >= 0 && end.x < rows && end.y >= 0 && end.y < cols)
            {
                maze[end.x, end.y] = true;
            }
        }
    }
}
