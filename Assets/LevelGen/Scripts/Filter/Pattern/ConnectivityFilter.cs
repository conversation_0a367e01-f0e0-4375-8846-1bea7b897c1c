using System.Collections.Generic;
using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Pattern
{
    /// <summary>
    /// Filter đảm bảo connectivity và loại bỏ các vùng isolated
    /// </summary>
    public class ConnectivityFilter : IFilter
    {
        private readonly int minComponentSize;
        private readonly bool keepLargestComponent;
        private readonly bool connectComponents;
        private readonly ConnectivityType connectivityType;

        public enum ConnectivityType
        {
            FourConnected,  // Chỉ kết nối 4 hướng (lên, xuống, trái, phải)
            EightConnected  // Kết nối 8 hướng (bao gồm cả đường chéo)
        }

        public ConnectivityFilter(int minComponentSize = 5, bool keepLargestComponent = true, 
            bool connectComponents = false, ConnectivityType connectivityType = ConnectivityType.FourConnected)
        {
            this.minComponentSize = Mathf.Max(1, minComponentSize);
            this.keepLargestComponent = keepLargestComponent;
            this.connectComponents = connectComponents;
            this.connectivityType = connectivityType;
        }

        public T[,] Filter<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            // Copy original matrix
            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    result[i, j] = matrix[i, j];
                }
            }

            // Find all connected components
            List<List<Vector2Int>> components = FindConnectedComponents(result);

            if (components.Count == 0)
                return result;

            // Process components based on settings
            if (keepLargestComponent)
            {
                KeepLargestComponentOnly(result, components);
            }
            else
            {
                RemoveSmallComponents(result, components);
            }

            // Connect components if requested
            if (connectComponents && components.Count > 1)
            {
                ConnectAllComponents(result, components);
            }

            return result;
        }

        private List<List<Vector2Int>> FindConnectedComponents<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            bool[,] visited = new bool[rows, cols];
            List<List<Vector2Int>> components = new List<List<Vector2Int>>();

            int[] di, dj;
            GetConnectivityDirections(out di, out dj);

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    if (!visited[i, j] && !IsDefaultValue(matrix[i, j]))
                    {
                        List<Vector2Int> component = new List<Vector2Int>();
                        FloodFill(matrix, visited, i, j, rows, cols, component, di, dj);
                        
                        if (component.Count > 0)
                            components.Add(component);
                    }
                }
            }

            return components;
        }

        private void GetConnectivityDirections(out int[] di, out int[] dj)
        {
            if (connectivityType == ConnectivityType.FourConnected)
            {
                di = new int[] { -1, 1, 0, 0 };
                dj = new int[] { 0, 0, -1, 1 };
            }
            else // EightConnected
            {
                di = new int[] { -1, -1, -1, 0, 0, 1, 1, 1 };
                dj = new int[] { -1, 0, 1, -1, 1, -1, 0, 1 };
            }
        }

        private void FloodFill<T>(T[,] matrix, bool[,] visited, int startI, int startJ, 
            int rows, int cols, List<Vector2Int> component, int[] di, int[] dj) where T : struct
        {
            Stack<Vector2Int> stack = new Stack<Vector2Int>();
            stack.Push(new Vector2Int(startI, startJ));

            while (stack.Count > 0)
            {
                Vector2Int current = stack.Pop();
                int i = current.x;
                int j = current.y;

                if (i < 0 || i >= rows || j < 0 || j >= cols || visited[i, j] || IsDefaultValue(matrix[i, j]))
                    continue;

                visited[i, j] = true;
                component.Add(current);

                for (int d = 0; d < di.Length; d++)
                {
                    stack.Push(new Vector2Int(i + di[d], j + dj[d]));
                }
            }
        }

        private void KeepLargestComponentOnly<T>(T[,] matrix, List<List<Vector2Int>> components) where T : struct
        {
            if (components.Count <= 1)
                return;

            // Find largest component
            List<Vector2Int> largestComponent = components[0];
            foreach (var component in components)
            {
                if (component.Count > largestComponent.Count)
                    largestComponent = component;
            }

            // Clear all cells
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    matrix[i, j] = default(T);
                }
            }

            // Restore only largest component
            foreach (var pos in largestComponent)
            {
                matrix[pos.x, pos.y] = GetNonDefaultValue<T>();
            }
        }

        private void RemoveSmallComponents<T>(T[,] matrix, List<List<Vector2Int>> components) where T : struct
        {
            foreach (var component in components)
            {
                if (component.Count < minComponentSize)
                {
                    // Remove this component
                    foreach (var pos in component)
                    {
                        matrix[pos.x, pos.y] = default(T);
                    }
                }
            }
        }

        private void ConnectAllComponents<T>(T[,] matrix, List<List<Vector2Int>> components) where T : struct
        {
            if (components.Count <= 1)
                return;

            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);

            // Connect each component to the first one
            for (int i = 1; i < components.Count; i++)
            {
                ConnectTwoComponents(matrix, components[0], components[i], rows, cols);
            }
        }

        private void ConnectTwoComponents<T>(T[,] matrix, List<Vector2Int> comp1, List<Vector2Int> comp2, int rows, int cols) where T : struct
        {
            Vector2Int closest1 = comp1[0];
            Vector2Int closest2 = comp2[0];
            float minDistance = float.MaxValue;

            // Find closest points between components
            foreach (var p1 in comp1)
            {
                foreach (var p2 in comp2)
                {
                    float distance = Vector2Int.Distance(p1, p2);
                    if (distance < minDistance)
                    {
                        minDistance = distance;
                        closest1 = p1;
                        closest2 = p2;
                    }
                }
            }

            // Create path between closest points
            CreatePath(matrix, closest1, closest2, rows, cols);
        }

        private void CreatePath<T>(T[,] matrix, Vector2Int start, Vector2Int end, int rows, int cols) where T : struct
        {
            int currentI = start.x;
            int currentJ = start.y;
            T nonDefaultValue = GetNonDefaultValue<T>();

            // Move horizontally first, then vertically
            while (currentJ != end.y)
            {
                if (currentI >= 0 && currentI < rows && currentJ >= 0 && currentJ < cols)
                {
                    matrix[currentI, currentJ] = nonDefaultValue;
                }

                if (currentJ < end.y) currentJ++;
                else currentJ--;
            }

            while (currentI != end.x)
            {
                if (currentI >= 0 && currentI < rows && currentJ >= 0 && currentJ < cols)
                {
                    matrix[currentI, currentJ] = nonDefaultValue;
                }

                if (currentI < end.x) currentI++;
                else currentI--;
            }

            // Ensure end point is set
            if (end.x >= 0 && end.x < rows && end.y >= 0 && end.y < cols)
            {
                matrix[end.x, end.y] = nonDefaultValue;
            }
        }

        private bool IsDefaultValue<T>(T value) where T : struct
        {
            return value.Equals(default(T));
        }

        private T GetNonDefaultValue<T>() where T : struct
        {
            if (typeof(T) == typeof(int))
                return (T)(object)1;
            if (typeof(T) == typeof(float))
                return (T)(object)1f;
            if (typeof(T) == typeof(double))
                return (T)(object)1.0;
            if (typeof(T) == typeof(byte))
                return (T)(object)(byte)255;
            
            return default(T);
        }
    }
}
