using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Pattern
{
    /// <summary>
    /// Filter tạo pattern bàn cờ
    /// </summary>
    public class CheckerboardFilter : IFilter
    {
        private readonly int squareSize;
        private readonly bool invertPattern;
        private readonly int offsetX;
        private readonly int offsetY;

        public CheckerboardFilter(int squareSize = 4, bool invertPattern = false, int offsetX = 0, int offsetY = 0)
        {
            this.squareSize = Mathf.Max(1, squareSize);
            this.invertPattern = invertPattern;
            this.offsetX = offsetX;
            this.offsetY = offsetY;
        }

        public T[,] Filter<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    int adjustedI = i + offsetY;
                    int adjustedJ = j + offsetX;

                    int squareI = adjustedI / squareSize;
                    int squareJ = adjustedJ / squareSize;

                    // VERTICAL SYMMETRY: Checkerboard pattern is naturally symmetric about Y-axis
                    // The (squareI + squareJ) % 2 calculation ensures symmetry
                    bool isWhiteSquare = (squareI + squareJ) % 2 == 0;

                    if (invertPattern)
                        isWhiteSquare = !isWhiteSquare;

                    if (isWhiteSquare)
                    {
                        result[i, j] = matrix[i, j];
                    }
                    else
                    {
                        result[i, j] = default(T);
                    }
                }
            }

            return result;
        }
    }
}
