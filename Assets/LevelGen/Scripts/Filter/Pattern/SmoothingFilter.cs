using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Pattern
{
    /// <summary>
    /// Filter làm mịn edges và loại bỏ noise
    /// </summary>
    public class SmoothingFilter : IFilter
    {
        private readonly int iterations;
        private readonly SmoothingType smoothingType;
        private readonly float threshold;

        public enum SmoothingType
        {
            Gaussian,
            Median,
            MorphologicalOpening,
            MorphologicalClosing,
            EdgePreserving
        }

        public SmoothingFilter(SmoothingType smoothingType = SmoothingType.Gaussian, int iterations = 1, float threshold = 0.5f)
        {
            this.smoothingType = smoothingType;
            this.iterations = Mathf.Max(1, iterations);
            this.threshold = Mathf.Clamp01(threshold);
        }

        public T[,] Filter<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            // Copy original matrix
            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    result[i, j] = matrix[i, j];
                }
            }

            // Apply smoothing iterations
            for (int iter = 0; iter < iterations; iter++)
            {
                result = ApplySmoothing(result, smoothingType);
            }

            return result;
        }

        private T[,] ApplySmoothing<T>(T[,] matrix, SmoothingType type) where T : struct
        {
            switch (type)
            {
                case SmoothingType.Gaussian:
                    return ApplyGaussianSmoothing(matrix);
                case SmoothingType.Median:
                    return ApplyMedianFilter(matrix);
                case SmoothingType.MorphologicalOpening:
                    return ApplyMorphologicalOpening(matrix);
                case SmoothingType.MorphologicalClosing:
                    return ApplyMorphologicalClosing(matrix);
                case SmoothingType.EdgePreserving:
                    return ApplyEdgePreservingSmoothing(matrix);
                default:
                    return ApplyGaussianSmoothing(matrix);
            }
        }

        private T[,] ApplyGaussianSmoothing<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            // Gaussian kernel 3x3
            float[,] kernel = {
                { 1f/16f, 2f/16f, 1f/16f },
                { 2f/16f, 4f/16f, 2f/16f },
                { 1f/16f, 2f/16f, 1f/16f }
            };

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    float sum = 0f;
                    float weightSum = 0f;

                    for (int ki = -1; ki <= 1; ki++)
                    {
                        for (int kj = -1; kj <= 1; kj++)
                        {
                            int ni = i + ki;
                            int nj = j + kj;

                            if (ni >= 0 && ni < rows && nj >= 0 && nj < cols)
                            {
                                float value = ConvertToFloat(matrix[ni, nj]);
                                float weight = kernel[ki + 1, kj + 1];
                                sum += value * weight;
                                weightSum += weight;
                            }
                        }
                    }

                    float smoothedValue = weightSum > 0 ? sum / weightSum : ConvertToFloat(matrix[i, j]);
                    result[i, j] = ConvertFromFloat<T>(smoothedValue);
                }
            }

            return result;
        }

        private T[,] ApplyMedianFilter<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    float[] neighbors = new float[9];
                    int count = 0;

                    for (int ki = -1; ki <= 1; ki++)
                    {
                        for (int kj = -1; kj <= 1; kj++)
                        {
                            int ni = i + ki;
                            int nj = j + kj;

                            if (ni >= 0 && ni < rows && nj >= 0 && nj < cols)
                            {
                                neighbors[count++] = ConvertToFloat(matrix[ni, nj]);
                            }
                        }
                    }

                    // Sort and find median
                    System.Array.Sort(neighbors, 0, count);
                    float median = count > 0 ? neighbors[count / 2] : ConvertToFloat(matrix[i, j]);
                    result[i, j] = ConvertFromFloat<T>(median);
                }
            }

            return result;
        }

        private T[,] ApplyMorphologicalOpening<T>(T[,] matrix) where T : struct
        {
            // Opening = Erosion followed by Dilation
            var eroded = ApplyErosion(matrix);
            return ApplyDilation(eroded);
        }

        private T[,] ApplyMorphologicalClosing<T>(T[,] matrix) where T : struct
        {
            // Closing = Dilation followed by Erosion
            var dilated = ApplyDilation(matrix);
            return ApplyErosion(dilated);
        }

        private T[,] ApplyErosion<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    float minValue = float.MaxValue;

                    for (int ki = -1; ki <= 1; ki++)
                    {
                        for (int kj = -1; kj <= 1; kj++)
                        {
                            int ni = i + ki;
                            int nj = j + kj;

                            if (ni >= 0 && ni < rows && nj >= 0 && nj < cols)
                            {
                                float value = ConvertToFloat(matrix[ni, nj]);
                                minValue = Mathf.Min(minValue, value);
                            }
                        }
                    }

                    result[i, j] = ConvertFromFloat<T>(minValue == float.MaxValue ? ConvertToFloat(matrix[i, j]) : minValue);
                }
            }

            return result;
        }

        private T[,] ApplyDilation<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    float maxValue = float.MinValue;

                    for (int ki = -1; ki <= 1; ki++)
                    {
                        for (int kj = -1; kj <= 1; kj++)
                        {
                            int ni = i + ki;
                            int nj = j + kj;

                            if (ni >= 0 && ni < rows && nj >= 0 && nj < cols)
                            {
                                float value = ConvertToFloat(matrix[ni, nj]);
                                maxValue = Mathf.Max(maxValue, value);
                            }
                        }
                    }

                    result[i, j] = ConvertFromFloat<T>(maxValue == float.MinValue ? ConvertToFloat(matrix[i, j]) : maxValue);
                }
            }

            return result;
        }

        private T[,] ApplyEdgePreservingSmoothing<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    float centerValue = ConvertToFloat(matrix[i, j]);
                    float sum = centerValue;
                    float weightSum = 1f;

                    for (int ki = -1; ki <= 1; ki++)
                    {
                        for (int kj = -1; kj <= 1; kj++)
                        {
                            if (ki == 0 && kj == 0) continue;

                            int ni = i + ki;
                            int nj = j + kj;

                            if (ni >= 0 && ni < rows && nj >= 0 && nj < cols)
                            {
                                float neighborValue = ConvertToFloat(matrix[ni, nj]);
                                float difference = Mathf.Abs(centerValue - neighborValue);
                                
                                // Only smooth if difference is small (preserve edges)
                                if (difference < threshold)
                                {
                                    float weight = 1f - difference / threshold;
                                    sum += neighborValue * weight;
                                    weightSum += weight;
                                }
                            }
                        }
                    }

                    result[i, j] = ConvertFromFloat<T>(sum / weightSum);
                }
            }

            return result;
        }

        private float ConvertToFloat<T>(T value) where T : struct
        {
            if (value is int intValue)
                return intValue;
            if (value is float floatValue)
                return floatValue;
            if (value is double doubleValue)
                return (float)doubleValue;
            if (value is byte byteValue)
                return byteValue / 255f;
            
            return 0f;
        }

        private T ConvertFromFloat<T>(float value) where T : struct
        {
            if (typeof(T) == typeof(int))
                return (T)(object)Mathf.RoundToInt(value);
            if (typeof(T) == typeof(float))
                return (T)(object)value;
            if (typeof(T) == typeof(double))
                return (T)(object)(double)value;
            if (typeof(T) == typeof(byte))
                return (T)(object)(byte)Mathf.Clamp(value * 255f, 0, 255);
            
            return default(T);
        }
    }
}
