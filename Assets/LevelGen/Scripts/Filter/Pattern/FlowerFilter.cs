using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Pattern
{
    /// <summary>
    /// Filter tạo hình hoa với nhi<PERSON>u c<PERSON>h hoa
    /// </summary>
    public class FlowerFilter : IFilter
    {
        private readonly int petalCount;
        private readonly float petalSize;
        private readonly float centerSize;
        private readonly bool fillInside;
        private readonly float centerX;
        private readonly float centerY;
        private readonly float rotation;

        public FlowerFilter(int petalCount = 6, float petalSizeScale = 0.3f, float centerSizeScale = 0.1f,
            bool fillInside = true, float centerX = 0.5f, float centerY = 0.5f, float rotation = 0f)
        {
            this.petalCount = Mathf.Max(3, petalCount);
            this.petalSize = petalSizeScale;
            this.centerSize = centerSizeScale;
            this.fillInside = fillInside;
            this.centerX = centerX;
            this.centerY = centerY;
            this.rotation = rotation;
        }

        public T[,] Filter<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            float centerRow = (rows - 1) * centerY;
            float centerCol = (cols - 1) * centerX;

            // Tính radius dựa trên kích thước matrix và scale
            float minSize = Mathf.Min(rows, cols);
            float maxRadius = minSize * petalSize * 0.5f;
            float centerRadius = minSize * centerSize * 0.5f;

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    float x = j - centerCol;
                    float y = i - centerRow;
                    
                    float distance = Mathf.Sqrt(x * x + y * y);
                    float angle = Mathf.Atan2(y, x) + rotation * Mathf.Deg2Rad;
                    
                    // Normalize angle to [0, 2π]
                    while (angle < 0) angle += 2 * Mathf.PI;
                    while (angle >= 2 * Mathf.PI) angle -= 2 * Mathf.PI;

                    bool isInside = false;

                    // Check if in center
                    if (distance <= centerRadius)
                    {
                        isInside = true;
                    }
                    else
                    {
                        // Check if in any petal
                        float petalAngle = 2 * Mathf.PI / petalCount;
                        
                        for (int p = 0; p < petalCount; p++)
                        {
                            float petalCenterAngle = p * petalAngle;
                            float angleDiff = Mathf.Abs(angle - petalCenterAngle);
                            
                            // Handle wrap-around
                            if (angleDiff > Mathf.PI)
                                angleDiff = 2 * Mathf.PI - angleDiff;
                            
                            // Petal shape: ellipse extending from center
                            if (angleDiff <= petalAngle * 0.5f)
                            {
                                float petalRadius = GetPetalRadius(angleDiff, petalAngle * 0.5f, maxRadius, centerRadius);
                                if (distance <= petalRadius)
                                {
                                    isInside = true;
                                    break;
                                }
                            }
                        }
                    }

                    if ((fillInside && isInside) || (!fillInside && !isInside))
                    {
                        result[i, j] = matrix[i, j];
                    }
                    else
                    {
                        result[i, j] = default(T);
                    }
                }
            }

            return result;
        }

        private float GetPetalRadius(float angleDiff, float maxAngleDiff, float maxRadius, float centerRadius)
        {
            // Create petal shape using cosine function
            float normalizedAngle = angleDiff / maxAngleDiff; // 0 to 1
            float petalShape = Mathf.Cos(normalizedAngle * Mathf.PI * 0.5f); // Smooth falloff
            
            return centerRadius + (maxRadius - centerRadius) * petalShape;
        }
    }
}
