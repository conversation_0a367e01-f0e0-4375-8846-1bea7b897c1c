using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Pattern
{
    /// <summary>
    /// Filter tạo hình tam giác
    /// </summary>
    public class TriangleFilter : IFilter
    {
        private readonly float sizeScale;
        private readonly bool fillInside;
        private readonly float centerX;
        private readonly float centerY;
        private readonly float rotation;
        private readonly TriangleType triangleType;

        public enum TriangleType
        {
            Equilateral,    // Tam giác đều
            Right,          // Tam giác vuông
            Isosceles       // Tam giác cân
        }

        public TriangleFilter(float sizeScale = 0.6f, bool fillInside = true, float centerX = 0.5f, float centerY = 0.5f, 
            float rotation = 0f, TriangleType triangleType = TriangleType.Equilateral)
        {
            this.sizeScale = sizeScale;
            this.fillInside = fillInside;
            this.centerX = centerX;
            this.centerY = centerY;
            this.rotation = rotation;
            this.triangleType = triangleType;
        }

        public T[,] Filter<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            float centerRow = (rows - 1) * centerY;
            float centerCol = (cols - 1) * centerX;
            float minSize = Mathf.Min(rows, cols);
            float triangleSize = Mathf.Max(2f, minSize * sizeScale * 0.5f);

            // Calculate triangle vertices based on type
            Vector2[] vertices = GetTriangleVertices(centerCol, centerRow, triangleSize);

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    Vector2 point = new Vector2(j, i);
                    bool isInside = IsPointInTriangle(point, vertices);

                    if ((fillInside && isInside) || (!fillInside && !isInside))
                    {
                        result[i, j] = matrix[i, j];
                    }
                    else
                    {
                        result[i, j] = default(T);
                    }
                }
            }

            return result;
        }

        private Vector2[] GetTriangleVertices(float centerX, float centerY, float size)
        {
            Vector2[] vertices = new Vector2[3];
            float rotRad = rotation * Mathf.Deg2Rad;

            switch (triangleType)
            {
                case TriangleType.Equilateral:
                    // VERTICAL SYMMETRY: Tam giác đều đối xứng qua trục Y
                    vertices[0] = new Vector2(0, -size * 0.866f);      // Top (center)
                    vertices[1] = new Vector2(-size * 0.5f, size * 0.433f);  // Bottom left
                    vertices[2] = new Vector2(size * 0.5f, size * 0.433f);   // Bottom right
                    break;

                case TriangleType.Right:
                    // VERTICAL SYMMETRY: Tam giác vuông cân đối xứng qua trục Y
                    vertices[0] = new Vector2(0, -size * 0.5f);         // Top (center)
                    vertices[1] = new Vector2(-size * 0.5f, size * 0.5f);    // Bottom left
                    vertices[2] = new Vector2(size * 0.5f, size * 0.5f);     // Bottom right
                    break;

                case TriangleType.Isosceles:
                    // VERTICAL SYMMETRY: Tam giác cân đối xứng qua trục Y
                    vertices[0] = new Vector2(0, -size * 0.7f);         // Top (center)
                    vertices[1] = new Vector2(-size * 0.6f, size * 0.4f);   // Bottom left
                    vertices[2] = new Vector2(size * 0.6f, size * 0.4f);    // Bottom right
                    break;
            }

            // VERTICAL SYMMETRY: Only apply rotation that maintains symmetry
            // Restrict rotation to multiples of 180 degrees to maintain Y-axis symmetry
            float symmetricRotation = Mathf.Round(rotation / 180f) * 180f;
            float symmetricRotRad = symmetricRotation * Mathf.Deg2Rad;

            // Apply rotation and translation
            for (int i = 0; i < 3; i++)
            {
                float x = vertices[i].x;
                float y = vertices[i].y;

                // Rotate with symmetric rotation only
                float rotatedX = x * Mathf.Cos(symmetricRotRad) - y * Mathf.Sin(symmetricRotRad);
                float rotatedY = x * Mathf.Sin(symmetricRotRad) + y * Mathf.Cos(symmetricRotRad);

                // Translate to center
                vertices[i] = new Vector2(centerX + rotatedX, centerY + rotatedY);
            }

            return vertices;
        }

        private bool IsPointInTriangle(Vector2 point, Vector2[] vertices)
        {
            Vector2 v0 = vertices[2] - vertices[0];
            Vector2 v1 = vertices[1] - vertices[0];
            Vector2 v2 = point - vertices[0];

            float dot00 = Vector2.Dot(v0, v0);
            float dot01 = Vector2.Dot(v0, v1);
            float dot02 = Vector2.Dot(v0, v2);
            float dot11 = Vector2.Dot(v1, v1);
            float dot12 = Vector2.Dot(v1, v2);

            float invDenom = 1 / (dot00 * dot11 - dot01 * dot01);
            float u = (dot11 * dot02 - dot01 * dot12) * invDenom;
            float v = (dot00 * dot12 - dot01 * dot02) * invDenom;

            return (u >= 0) && (v >= 0) && (u + v <= 1);
        }
    }
}
