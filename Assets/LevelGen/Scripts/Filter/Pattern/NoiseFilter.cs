using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Pattern
{
    /// <summary>
    /// Filter tạo pattern noise (Perlin noise)
    /// </summary>
    public class NoiseFilter : IFilter
    {
        private readonly float scale;
        private readonly float threshold;
        private readonly bool fillAboveThreshold;
        private readonly int octaves;
        private readonly float persistence;
        private readonly float lacunarity;
        private readonly Vector2 offset;
        private readonly int seed;

        public NoiseFilter(float scale = 0.1f, float threshold = 0.5f, bool fillAboveThreshold = true,
            int octaves = 1, float persistence = 0.5f, float lacunarity = 2f, Vector2 offset = default, int seed = 0)
        {
            this.scale = Mathf.Max(0.001f, scale);
            this.threshold = Mathf.Clamp01(threshold);
            this.fillAboveThreshold = fillAboveThreshold;
            this.octaves = Mathf.Max(1, octaves);
            this.persistence = persistence;
            this.lacunarity = lacunarity;
            this.offset = offset;
            this.seed = seed;
        }

        public T[,] Filter<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    float noiseValue = GetNoiseValue(j, i);
                    bool isAboveThreshold = noiseValue > threshold;

                    if ((fillAboveThreshold && isAboveThreshold) || (!fillAboveThreshold && !isAboveThreshold))
                    {
                        result[i, j] = matrix[i, j];
                    }
                    else
                    {
                        result[i, j] = default(T);
                    }
                }
            }

            return result;
        }

        private float GetNoiseValue(int x, int y)
        {
            float value = 0f;
            float amplitude = 1f;
            float frequency = scale;
            float maxValue = 0f;

            for (int i = 0; i < octaves; i++)
            {
                // VERTICAL SYMMETRY: Perlin noise is naturally symmetric if we use the same coordinates
                // The noise function itself doesn't need modification for symmetry
                float sampleX = (x + offset.x + seed * 1000) * frequency;
                float sampleY = (y + offset.y + seed * 1000) * frequency;

                float noiseValue = Mathf.PerlinNoise(sampleX, sampleY);
                value += noiseValue * amplitude;

                maxValue += amplitude;
                amplitude *= persistence;
                frequency *= lacunarity;
            }

            return value / maxValue; // Normalize to [0, 1]
        }
    }
}
