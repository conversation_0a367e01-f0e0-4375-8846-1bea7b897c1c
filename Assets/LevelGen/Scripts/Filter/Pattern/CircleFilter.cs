using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Pattern
{
    /// <summary>
    /// Filter tạo hình tròn hoặc oval
    /// </summary>
    public class CircleFilter : IFilter
    {
        private readonly float scaleX;
        private readonly float scaleY;
        private readonly bool fillInside;
        private readonly float centerX;
        private readonly float centerY;

        public CircleFilter(float scale = 0.4f, bool fillInside = true)
            : this(scale, scale, fillInside, 0.5f, 0.5f)
        {
        }

        public CircleFilter(float scaleX, float scaleY, bool fillInside = true, float centerX = 0.5f, float centerY = 0.5f)
        {
            this.scaleX = scaleX;
            this.scaleY = scaleY;
            this.fillInside = fillInside;
            this.centerX = centerX;
            this.centerY = centerY;
        }

        public T[,] Filter<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            // Với matrix rất nhỏ (6x12), cần t<PERSON>h toán đặc biệt
            float centerRow = centerY * (rows - 1);
            float centerCol = centerX * (cols - 1);

            // Tính radius phù hợp với matrix siêu nhỏ
            float radiusRow = Mathf.Max(0.8f, rows * scaleY * 0.45f);
            float radiusCol = Mathf.Max(0.8f, cols * scaleX * 0.45f);

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    // VERTICAL SYMMETRY: Ellipse formula using dx^2 + dy^2 is naturally symmetric about Y-axis
                    float dx = (j - centerCol) / radiusCol;
                    float dy = (i - centerRow) / radiusRow;
                    float distanceSquared = dx * dx + dy * dy;

                    bool isInside = distanceSquared <= 1.0f;

                    if ((fillInside && isInside) || (!fillInside && !isInside))
                    {
                        result[i, j] = matrix[i, j];
                    }
                    else
                    {
                        result[i, j] = default(T);
                    }
                }
            }

            return result;
        }
    }
}
