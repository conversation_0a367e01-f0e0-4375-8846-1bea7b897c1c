using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Pattern
{
    /// <summary>
    /// Filter tạo pattern xoắn ốc
    /// </summary>
    public class SpiralFilter : IFilter
    {
        private readonly float spiralTightness;
        private readonly float thickness;
        private readonly bool clockwise;
        private readonly float centerX;
        private readonly float centerY;
        private readonly int turns;

        public SpiralFilter(float spiralTightness = 0.1f, float thicknessScale = 0.05f, bool clockwise = true,
            float centerX = 0.5f, float centerY = 0.5f, int turns = 3)
        {
            this.spiralTightness = spiralTightness;
            this.thickness = thicknessScale;
            this.clockwise = clockwise;
            this.centerX = centerX;
            this.centerY = centerY;
            this.turns = turns;
        }

        public T[,] Filter<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            float centerRow = (rows - 1) * centerY;
            float centerCol = (cols - 1) * centerX;

            // T<PERSON>h kích thước dựa trên matrix size và scale
            float minSize = Mathf.Min(rows, cols);
            float maxRadius = minSize * 0.5f;
            float thicknessPixels = minSize * thickness;

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    float x = j - centerCol;
                    float y = i - centerRow;
                    
                    float distance = Mathf.Sqrt(x * x + y * y);
                    float angle = Mathf.Atan2(y, x);
                    
                    if (!clockwise)
                        angle = -angle;
                    
                    // Normalize angle to [0, 2π]
                    while (angle < 0) angle += 2 * Mathf.PI;
                    
                    // Calculate spiral radius at this angle
                    float spiralRadius = spiralTightness * angle * maxRadius / (2 * Mathf.PI * turns);
                    
                    // Check if point is on the spiral
                    bool isOnSpiral = Mathf.Abs(distance - spiralRadius) <= thicknessPixels && distance <= maxRadius;

                    if (isOnSpiral)
                    {
                        result[i, j] = matrix[i, j];
                    }
                    else
                    {
                        result[i, j] = default(T);
                    }
                }
            }

            return result;
        }
    }
}
