using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Pattern
{
    /// <summary>
    /// Filter tạo hình tia chớp
    /// </summary>
    public class LightningFilter : IFilter
    {
        private readonly float lengthScale;
        private readonly float thicknessScale;
        private readonly int segments;
        private readonly float zigzagIntensity;
        private readonly bool fillInside;
        private readonly float centerX;
        private readonly float centerY;
        private readonly float rotation;
        private readonly int seed;

        public LightningFilter(float lengthScale = 0.8f, float thicknessScale = 0.05f, int segments = 6, 
            float zigzagIntensity = 0.3f, bool fillInside = true, float centerX = 0.5f, float centerY = 0.5f, 
            float rotation = 0f, int seed = 0)
        {
            this.lengthScale = lengthScale;
            this.thicknessScale = thicknessScale;
            this.segments = Mathf.Max(3, segments);
            this.zigzagIntensity = zigzagIntensity;
            this.fillInside = fillInside;
            this.centerX = centerX;
            this.centerY = centerY;
            this.rotation = rotation;
            this.seed = seed;
        }

        public T[,] Filter<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            float centerRow = (rows - 1) * centerY;
            float centerCol = (cols - 1) * centerX;
            float minSize = Mathf.Min(rows, cols);
            float thickness = minSize * thicknessScale;

            // Generate lightning path
            Vector2[] lightningPath = GenerateLightningPath(centerCol, centerRow, minSize);

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    Vector2 point = new Vector2(j, i);
                    bool isInside = IsNearLightningPath(point, lightningPath, thickness);

                    if ((fillInside && isInside) || (!fillInside && !isInside))
                    {
                        result[i, j] = matrix[i, j];
                    }
                    else
                    {
                        result[i, j] = default(T);
                    }
                }
            }

            return result;
        }

        private Vector2[] GenerateLightningPath(float centerX, float centerY, float minSize)
        {
            Random.State oldState = Random.state;
            if (seed != 0)
                Random.InitState(seed);

            Vector2[] path = new Vector2[segments + 1];
            float totalLength = minSize * lengthScale;
            float segmentLength = totalLength / segments;

            // Start point
            float startX = centerX - totalLength * 0.5f;
            float startY = centerY;
            
            // Apply rotation to start direction
            float rotRad = rotation * Mathf.Deg2Rad;
            Vector2 direction = new Vector2(Mathf.Cos(rotRad), Mathf.Sin(rotRad));
            Vector2 perpendicular = new Vector2(-direction.y, direction.x);

            path[0] = new Vector2(startX, startY);

            for (int i = 1; i <= segments; i++)
            {
                // Base position along the main direction
                Vector2 basePos = path[0] + direction * (segmentLength * i);
                
                // Add zigzag offset
                float zigzagOffset = (Random.value - 0.5f) * 2f * zigzagIntensity * minSize * 0.2f;
                Vector2 finalPos = basePos + perpendicular * zigzagOffset;
                
                path[i] = finalPos;
            }

            Random.state = oldState;
            return path;
        }

        private bool IsNearLightningPath(Vector2 point, Vector2[] path, float thickness)
        {
            float halfThickness = thickness * 0.5f;

            for (int i = 0; i < path.Length - 1; i++)
            {
                Vector2 segmentStart = path[i];
                Vector2 segmentEnd = path[i + 1];
                
                float distanceToSegment = DistancePointToLineSegment(point, segmentStart, segmentEnd);
                
                if (distanceToSegment <= halfThickness)
                    return true;
            }

            return false;
        }

        private float DistancePointToLineSegment(Vector2 point, Vector2 lineStart, Vector2 lineEnd)
        {
            Vector2 line = lineEnd - lineStart;
            float lineLength = line.magnitude;
            
            if (lineLength == 0)
                return Vector2.Distance(point, lineStart);

            Vector2 lineDirection = line / lineLength;
            Vector2 pointToStart = point - lineStart;
            
            float projection = Vector2.Dot(pointToStart, lineDirection);
            projection = Mathf.Clamp(projection, 0, lineLength);
            
            Vector2 closestPoint = lineStart + lineDirection * projection;
            return Vector2.Distance(point, closestPoint);
        }
    }
}
