using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Pattern
{
    /// <summary>
    /// Filter tạo pattern chấm tròn
    /// </summary>
    public class DotsFilter : IFilter
    {
        private readonly int spacing;
        private readonly float dotRadius;
        private readonly bool fillDots;
        private readonly int offsetX;
        private readonly int offsetY;
        private readonly float randomness;
        private readonly int seed;

        public DotsFilter(int spacing = 6, float dotRadius = 2f, bool fillDots = true,
            int offsetX = 0, int offsetY = 0, float randomness = 0f, int seed = 0)
        {
            // Với matrix siêu nhỏ (6x12), spacing và radius cần nhỏ hơn
            this.spacing = Mathf.Max(1, spacing);
            this.dotRadius = Mathf.Max(0.3f, dotRadius);
            this.fillDots = fillDots;
            this.offsetX = offsetX;
            this.offsetY = offsetY;
            this.randomness = Mathf.Clamp01(randomness);
            this.seed = seed;
        }

        public T[,] Filter<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            Random.State oldState = Random.state;
            if (seed != 0)
                Random.InitState(seed);

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    bool isOnDot = IsOnDot(i, j);

                    if ((fillDots && isOnDot) || (!fillDots && !isOnDot))
                    {
                        result[i, j] = matrix[i, j];
                    }
                    else
                    {
                        result[i, j] = default(T);
                    }
                }
            }

            Random.state = oldState;
            return result;
        }

        private bool IsOnDot(int row, int col)
        {
            int adjustedRow = row + offsetY;
            int adjustedCol = col + offsetX;

            // VERTICAL SYMMETRY: Simple symmetric dot pattern
            // Find the nearest grid point
            int gridRow = Mathf.RoundToInt((float)adjustedRow / spacing) * spacing;
            int gridCol = Mathf.RoundToInt((float)adjustedCol / spacing) * spacing;

            // VERTICAL SYMMETRY: For randomness, use symmetric seeds
            if (randomness > 0)
            {
                // Use absolute column position to ensure symmetric randomness
                Random.InitState(gridRow * 1000 + Mathf.Abs(gridCol));
                float randomOffsetY = (Random.value - 0.5f) * 2f * randomness * spacing * 0.5f;

                // For X offset, maintain symmetry
                Random.InitState(gridRow * 1000 + Mathf.Abs(gridCol)); // Same seed for symmetric positions
                float randomOffsetX = (Random.value - 0.5f) * 2f * randomness * spacing * 0.5f;

                // Apply sign to maintain left-right symmetry
                if (gridCol < 0) randomOffsetX = -randomOffsetX;

                gridCol += Mathf.RoundToInt(randomOffsetX);
                gridRow += Mathf.RoundToInt(randomOffsetY);
            }

            // Calculate distance from current point to grid point
            float distance = Mathf.Sqrt((row - gridRow) * (row - gridRow) + (col - gridCol) * (col - gridCol));
            
            return distance <= dotRadius;
        }
    }
}
