using UnityEditor;
using UnityEngine;

namespace LevelGen.Scripts
{
    [System.Serializable]
    public class LevelShapeData
    {
        public string levelName;
        public int width;
        public int height;
        public bool[,] grid;
        public ShapeType shapeType;
    
        public LevelShapeData(int w, int h)
        {
            width = w;
            height = h;
            grid = new bool[w, h];
            levelName = "New Level";
            shapeType = ShapeType.Custom;
        }
    }

    public enum ShapeType
    {
        Custom,
        Circle,
        Diamond,
        Heart,
        Star,
        Cross,
        Hexagon,
        Triangle,
        Flower,
        Butterfly
    }

#if UNITY_EDITOR
    [CreateAssetMenu(fileName = "LevelShapeGenerator", menuName = "Tools/Level Shape Generator")]
    public class LevelShapeGenerator : ScriptableObject
    {
        [Header("Grid Settings")]
        public int gridWidth = 10;
        public int gridHeight = 10;
    
        [Header("Shape Generation")]
        public ShapeType currentShape = ShapeType.Circle;
        public float shapeScale = 1f;
        public Vector2 shapeOffset = Vector2.zero;
    
        [Header("Preview")]
        public bool showPreview = true;
        public Color activeColor = Color.green;
        public Color inactiveColor = Color.gray;

        public LevelShapeData currentLevel;
        private bool[,] previewGrid;
    
        public void Initialize()
        {
            currentLevel = new LevelShapeData(gridWidth, gridHeight);
            previewGrid = new bool[gridWidth, gridHeight];
            GenerateShape();
        }
    
        public void GenerateShape()
        {
            if (currentLevel == null) Initialize();
        
            // Clear grid
            for (int x = 0; x < gridWidth; x++)
            {
                for (int y = 0; y < gridHeight; y++)
                {
                    currentLevel.grid[x, y] = false;
                }
            }
        
            Vector2 center = new Vector2(gridWidth * 0.5f, gridHeight * 0.5f) + shapeOffset;
        
            switch (currentShape)
            {
                case ShapeType.Circle:
                    GenerateCircle(center);
                    break;
                case ShapeType.Diamond:
                    GenerateDiamond(center);
                    break;
                case ShapeType.Heart:
                    GenerateHeart(center);
                    break;
                case ShapeType.Star:
                    GenerateStar(center);
                    break;
                case ShapeType.Cross:
                    GenerateCross(center);
                    break;
                case ShapeType.Hexagon:
                    GenerateHexagon(center);
                    break;
                case ShapeType.Triangle:
                    GenerateTriangle(center);
                    break;
                case ShapeType.Flower:
                    GenerateFlower(center);
                    break;
                case ShapeType.Butterfly:
                    GenerateButterfly(center);
                    break;
            }
        
            currentLevel.shapeType = currentShape;
        }
    
        private void GenerateCircle(Vector2 center)
        {
            float radius = Mathf.Min(gridWidth, gridHeight) * 0.4f * shapeScale;
        
            for (int x = 0; x < gridWidth; x++)
            {
                for (int y = 0; y < gridHeight; y++)
                {
                    float distance = Vector2.Distance(new Vector2(x, y), center);
                    if (distance <= radius)
                    {
                        currentLevel.grid[x, y] = true;
                    }
                }
            }
        }
    
        private void GenerateDiamond(Vector2 center)
        {
            float size = Mathf.Min(gridWidth, gridHeight) * 0.4f * shapeScale;
        
            for (int x = 0; x < gridWidth; x++)
            {
                for (int y = 0; y < gridHeight; y++)
                {
                    float manhattanDistance = Mathf.Abs(x - center.x) + Mathf.Abs(y - center.y);
                    if (manhattanDistance <= size)
                    {
                        currentLevel.grid[x, y] = true;
                    }
                }
            }
        }
    
        private void GenerateHeart(Vector2 center)
        {
            float size = Mathf.Min(gridWidth, gridHeight) * 0.3f * shapeScale;
        
            for (int x = 0; x < gridWidth; x++)
            {
                for (int y = 0; y < gridHeight; y++)
                {
                    float px = (x - center.x) / size;
                    float py = (y - center.y) / size;
                
                    // Heart equation: (x²+y²-1)³ - x²y³ ≤ 0
                    float heartEq = Mathf.Pow(px * px + py * py - 1, 3) - px * px * py * py * py;
                    if (heartEq <= 0.1f)
                    {
                        currentLevel.grid[x, y] = true;
                    }
                }
            }
        }
    
        private void GenerateStar(Vector2 center)
        {
            float outerRadius = Mathf.Min(gridWidth, gridHeight) * 0.4f * shapeScale;
            float innerRadius = outerRadius * 0.4f;
            int points = 5;
        
            for (int x = 0; x < gridWidth; x++)
            {
                for (int y = 0; y < gridHeight; y++)
                {
                    Vector2 pos = new Vector2(x, y) - center;
                    float angle = Mathf.Atan2(pos.y, pos.x) + Mathf.PI;
                    float distance = pos.magnitude;
                
                    // Calculate star radius at this angle
                    float starAngle = (angle * points) % (2 * Mathf.PI);
                    float radius = Mathf.Lerp(innerRadius, outerRadius, 
                        (Mathf.Cos(starAngle - Mathf.PI) + 1) * 0.5f);
                
                    if (distance <= radius)
                    {
                        currentLevel.grid[x, y] = true;
                    }
                }
            }
        }
    
        private void GenerateCross(Vector2 center)
        {
            float size = Mathf.Min(gridWidth, gridHeight) * 0.4f * shapeScale;
            float thickness = size * 0.3f;
        
            for (int x = 0; x < gridWidth; x++)
            {
                for (int y = 0; y < gridHeight; y++)
                {
                    float dx = Mathf.Abs(x - center.x);
                    float dy = Mathf.Abs(y - center.y);
                
                    bool inVertical = dx <= thickness && dy <= size;
                    bool inHorizontal = dy <= thickness && dx <= size;
                
                    if (inVertical || inHorizontal)
                    {
                        currentLevel.grid[x, y] = true;
                    }
                }
            }
        }
    
        private void GenerateHexagon(Vector2 center)
        {
            float size = Mathf.Min(gridWidth, gridHeight) * 0.4f * shapeScale;
        
            for (int x = 0; x < gridWidth; x++)
            {
                for (int y = 0; y < gridHeight; y++)
                {
                    Vector2 pos = new Vector2(x, y) - center;
                
                    // Hexagon using 6 half-planes
                    bool inside = true;
                    for (int i = 0; i < 6; i++)
                    {
                        float angle = i * Mathf.PI / 3f;
                        Vector2 normal = new Vector2(Mathf.Cos(angle), Mathf.Sin(angle));
                    
                        if (Vector2.Dot(pos, normal) > size)
                        {
                            inside = false;
                            break;
                        }
                    }
                
                    if (inside)
                    {
                        currentLevel.grid[x, y] = true;
                    }
                }
            }
        }
    
        private void GenerateTriangle(Vector2 center)
        {
            float size = Mathf.Min(gridWidth, gridHeight) * 0.4f * shapeScale;
        
            for (int x = 0; x < gridWidth; x++)
            {
                for (int y = 0; y < gridHeight; y++)
                {
                    Vector2 pos = new Vector2(x, y) - center;
                
                    // Equilateral triangle
                    bool inside = pos.y <= size && 
                                  pos.y >= -size * 0.5f && 
                                  Mathf.Abs(pos.x) <= (size + pos.y) * 0.866f;
                
                    if (inside)
                    {
                        currentLevel.grid[x, y] = true;
                    }
                }
            }
        }
    
        private void GenerateFlower(Vector2 center)
        {
            float radius = Mathf.Min(gridWidth, gridHeight) * 0.3f * shapeScale;
            int petals = 6;
        
            for (int x = 0; x < gridWidth; x++)
            {
                for (int y = 0; y < gridHeight; y++)
                {
                    Vector2 pos = new Vector2(x, y) - center;
                    float angle = Mathf.Atan2(pos.y, pos.x);
                    float distance = pos.magnitude;
                
                    // Flower shape using sine wave
                    float petalRadius = radius * (1 + 0.5f * Mathf.Sin(petals * angle));
                
                    if (distance <= petalRadius)
                    {
                        currentLevel.grid[x, y] = true;
                    }
                }
            }
        }
    
        private void GenerateButterfly(Vector2 center)
        {
            float size = Mathf.Min(gridWidth, gridHeight) * 0.3f * shapeScale;
        
            for (int x = 0; x < gridWidth; x++)
            {
                for (int y = 0; y < gridHeight; y++)
                {
                    float px = (x - center.x) / size;
                    float py = (y - center.y) / size;
                
                    // Butterfly equation
                    float r = Mathf.Sqrt(px * px + py * py);
                    float theta = Mathf.Atan2(py, px);
                
                    float butterflyR = Mathf.Exp(Mathf.Sin(theta)) - 2 * Mathf.Cos(4 * theta) + Mathf.Pow(Mathf.Sin((2 * theta - Mathf.PI) / 24), 5);
                
                    if (r <= butterflyR * 0.5f)
                    {
                        currentLevel.grid[x, y] = true;
                    }
                }
            }
        }
    
        public void SaveLevel(string fileName)
        {
            if (currentLevel == null) return;
        
            string path = $"Assets/Levels/{fileName}.json";
            string jsonData = JsonUtility.ToJson(new SerializableLevelShapeData(currentLevel), true);
            System.IO.File.WriteAllText(path, jsonData);
            AssetDatabase.Refresh();
        
            Debug.Log($"Level saved to: {path}");
        }
    
        public LevelShapeData LoadLevel(string fileName)
        {
            string path = $"Assets/Levels/{fileName}.json";
            if (System.IO.File.Exists(path))
            {
                string jsonData = System.IO.File.ReadAllText(path);
                SerializableLevelShapeData data = JsonUtility.FromJson<SerializableLevelShapeData>(jsonData);
                return data.ToLevelShapeData();
            }
            return null;
        }
    }

    [System.Serializable]
    public class SerializableLevelShapeData
    {
        public string levelName;
        public int width;
        public int height;
        public bool[] gridData;
        public ShapeType shapeType;
    
        public SerializableLevelShapeData(LevelShapeData level)
        {
            levelName = level.levelName;
            width = level.width;
            height = level.height;
            shapeType = level.shapeType;
        
            gridData = new bool[width * height];
            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    gridData[y * width + x] = level.grid[x, y];
                }
            }
        }
    
        public LevelShapeData ToLevelShapeData()
        {
            LevelShapeData level = new LevelShapeData(width, height);
            level.levelName = levelName;
            level.shapeType = shapeType;
        
            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    level.grid[x, y] = gridData[y * width + x];
                }
            }
        
            return level;
        }
    }

// Custom Editor
    [CustomEditor(typeof(LevelShapeGenerator))]
    public class LevelShapeGeneratorEditor : Editor
    {
        public override void OnInspectorGUI()
        {
            LevelShapeGenerator generator = (LevelShapeGenerator)target;
        
            EditorGUI.BeginChangeCheck();
            DrawDefaultInspector();
        
            if (EditorGUI.EndChangeCheck())
            {
                generator.GenerateShape();
            }
        
            GUILayout.Space(20);
        
            if (GUILayout.Button("Initialize", GUILayout.Height(30)))
            {
                generator.Initialize();
            }
        
            if (GUILayout.Button("Generate Shape", GUILayout.Height(30)))
            {
                generator.GenerateShape();
            }
        
            GUILayout.Space(10);
        
            GUILayout.BeginHorizontal();
            if (GUILayout.Button("Save Level"))
            {
                string fileName = $"Level_{generator.currentShape}_{System.DateTime.Now:yyyyMMdd_HHmmss}";
                generator.SaveLevel(fileName);
            }
        
            if (GUILayout.Button("Load Level"))
            {
                string path = EditorUtility.OpenFilePanel("Load Level", "Assets/Levels", "json");
                if (!string.IsNullOrEmpty(path))
                {
                    string fileName = System.IO.Path.GetFileNameWithoutExtension(path);
                    generator.LoadLevel(fileName);
                }
            }
            GUILayout.EndHorizontal();
        
            // Draw grid preview
            if (generator.showPreview && generator.currentLevel != null)
            {
                DrawGridPreview(generator);
            }
        }
    
        private void DrawGridPreview(LevelShapeGenerator generator)
        {
            GUILayout.Space(20);
            GUILayout.Label("Preview:", EditorStyles.boldLabel);
        
            Rect rect = GUILayoutUtility.GetRect(300, 300);
            float cellSize = Mathf.Min(rect.width / generator.gridWidth, rect.height / generator.gridHeight);
        
            Vector2 startPos = new Vector2(
                rect.x + (rect.width - cellSize * generator.gridWidth) * 0.5f,
                rect.y + (rect.height - cellSize * generator.gridHeight) * 0.5f
            );
        
            for (int x = 0; x < generator.gridWidth; x++)
            {
                for (int y = 0; y < generator.gridHeight; y++)
                {
                    Rect cellRect = new Rect(
                        startPos.x + x * cellSize,
                        startPos.y + y * cellSize,
                        cellSize - 1,
                        cellSize - 1
                    );
                
                    bool isActive = generator.currentLevel.grid[x, y];
                    EditorGUI.DrawRect(cellRect, isActive ? generator.activeColor : generator.inactiveColor);
                
                    // Add grid lines
                    EditorGUI.DrawRect(new Rect(cellRect.x, cellRect.y, cellRect.width, 1), Color.black);
                    EditorGUI.DrawRect(new Rect(cellRect.x, cellRect.y, 1, cellRect.height), Color.black);
                }
            }
        }
    }
#endif
}