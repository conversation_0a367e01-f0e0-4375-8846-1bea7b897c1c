using System.Collections.Generic;
using UnityEngine;

namespace LevelGen.Scripts.Base
{
    public enum NoiseType
    {
        Perlin, // Classic Perlin noise
        Simplex, // Simplex noise (better gradients)
        Worley, // Worley/Cellular noise
        Ridged, // Ridged multifractal
        Billowy, // Billowy noise
        Value, // Value noise
        White, // White noise (random)
        Fractal // Fractal noise (multiple octaves)
    }

    public enum NoiseFilter
    {
        None, // No filtering
        Threshold, // Simple threshold
        Smooth, // Smooth step
        Quantize, // Quantize to levels
        Invert, // Invert values
        Abs, // Absolute values
        Clamp // Clamp to range
    }

    public enum NoiseCombineMode
    {
        Add, // Add noises together
        Multiply, // Multiply noises
        Subtract, // Subtract second from first
        Min, // Take minimum
        Max, // Take maximum
        Average, // Average the values
        Overlay // Overlay blend mode
    }

    public class NoiseGenerator
    {
        public struct NoiseSettings
        {
            public int gridWidth;
            public int gridHeight;
            public NoiseType noiseType;
            public float scale; // Noise scale (smaller = more zoomed in)
            public Vector2 offset; // Offset for noise sampling
            public int octaves; // Number of octaves for fractal noise
            public float persistence; // Amplitude multiplier per octave
            public float lacunarity; // Frequency multiplier per octave
            public float threshold; // Threshold for binary conversion
            public NoiseFilter filter; // Post-processing filter
            public float filterParam1; // Filter parameter 1
            public float filterParam2; // Filter parameter 2
            public bool useFalloffMap; // Apply distance-based falloff
            public AnimationCurve falloffCurve; // Custom falloff curve
            public bool seamless; // Make noise tileable

            public static NoiseSettings Default => new NoiseSettings
            {
                gridWidth = 15,
                gridHeight = 8,
                noiseType = NoiseType.Perlin,
                scale = 0.1f,
                offset = Vector2.zero,
                octaves = 4,
                persistence = 0.5f,
                lacunarity = 2.0f,
                threshold = 0.5f,
                filter = NoiseFilter.Threshold,
                filterParam1 = 0.0f,
                filterParam2 = 1.0f,
                useFalloffMap = false,
                falloffCurve = null,
                seamless = false
            };
        }

        private NoiseSettings settings;
        private float[,] noiseMap;
        private int[,] grid;
        private System.Random random;

        // Permutation table for Perlin noise
        private static readonly int[] permutation =
        {
            151, 160, 137, 91, 90, 15, 131, 13, 201, 95, 96, 53, 194, 233, 7, 225, 140, 36, 103, 30, 69, 142,
            8, 99, 37, 240, 21, 10, 23, 190, 6, 148, 247, 120, 234, 75, 0, 26, 197, 62, 94, 252, 219, 203, 117,
            35, 11, 32, 57, 177, 33, 88, 237, 149, 56, 87, 174, 20, 125, 136, 171, 168, 68, 175, 74, 165, 71,
            134, 139, 48, 27, 166, 77, 146, 158, 231, 83, 111, 229, 122, 60, 211, 133, 230, 220, 105, 92, 41,
            55, 46, 245, 40, 244, 102, 143, 54, 65, 25, 63, 161, 1, 216, 80, 73, 209, 76, 132, 187, 208, 89,
            18, 169, 200, 196, 135, 130, 116, 188, 159, 86, 164, 100, 109, 198, 173, 186, 3, 64, 52, 217, 226,
            250, 124, 123, 5, 202, 38, 147, 118, 126, 255, 82, 85, 212, 207, 206, 59, 227, 47, 16, 58, 17, 182,
            189, 28, 42, 223, 183, 170, 213, 119, 248, 152, 2, 44, 154, 163, 70, 221, 153, 101, 155, 167, 43,
            172, 9, 129, 22, 39, 253, 19, 98, 108, 110, 79, 113, 224, 232, 178, 185, 112, 104, 218, 246, 97,
            228, 251, 34, 242, 193, 238, 210, 144, 12, 191, 179, 162, 241, 81, 51, 145, 235, 249, 14, 239,
            107, 49, 192, 214, 31, 181, 199, 106, 157, 184, 84, 204, 176, 115, 121, 50, 45, 127, 4, 150, 254,
            138, 236, 205, 93, 222, 114, 67, 29, 24, 72, 243, 141, 128, 195, 78, 66, 215, 61, 156, 180
        };

        private static readonly int[] p;

        static NoiseGenerator()
        {
            p = new int[512];
            for (int i = 0; i < 512; i++)
            {
                p[i] = permutation[i % 256];
            }
        }

        public NoiseGenerator(NoiseSettings settings, int? seed = null)
        {
            this.settings = settings;
            this.random = seed.HasValue ? new System.Random(seed.Value) : new System.Random();
        }

        public NoiseGenerator(int width, int height, NoiseType type = NoiseType.Perlin, int? seed = null)
        {
            this.settings = NoiseSettings.Default;
            this.settings.gridWidth = width;
            this.settings.gridHeight = height;
            this.settings.noiseType = type;
            this.random = seed.HasValue ? new System.Random(seed.Value) : new System.Random();
        }

        public int[,] GenerateLevel()
        {
            GenerateNoiseMap();
            ApplyFilter();
            ConvertToGrid();

            return grid;
        }

        public float[,] GenerateNoiseMap()
        {
            noiseMap = new float[settings.gridWidth, settings.gridHeight];

            for (int x = 0; x < settings.gridWidth; x++)
            {
                for (int y = 0; y < settings.gridHeight; y++)
                {
                    float noiseValue = SampleNoise(x, y);

                    if (settings.useFalloffMap)
                    {
                        float falloff = CalculateFalloff(x, y);
                        noiseValue = Mathf.Clamp01(noiseValue - falloff);
                    }

                    noiseMap[x, y] = noiseValue;
                }
            }

            return noiseMap;
        }

        private float SampleNoise(int x, int y)
        {
            float sampleX = (x + settings.offset.x) * settings.scale;
            float sampleY = (y + settings.offset.y) * settings.scale;

            switch (settings.noiseType)
            {
                case NoiseType.Perlin:
                    return SamplePerlinNoise(sampleX, sampleY);

                case NoiseType.Simplex:
                    return SampleSimplexNoise(sampleX, sampleY);

                case NoiseType.Worley:
                    return SampleWorleyNoise(sampleX, sampleY);

                case NoiseType.Ridged:
                    return SampleRidgedNoise(sampleX, sampleY);

                case NoiseType.Billowy:
                    return SampleBillowyNoise(sampleX, sampleY);

                case NoiseType.Value:
                    return SampleValueNoise(sampleX, sampleY);

                case NoiseType.White:
                    return SampleWhiteNoise(x, y);

                case NoiseType.Fractal:
                    return SampleFractalNoise(sampleX, sampleY);

                default:
                    return SamplePerlinNoise(sampleX, sampleY);
            }
        }

        private float SamplePerlinNoise(float x, float y)
        {
            if (settings.seamless)
            {
                // For seamless noise, sample at 4 points and blend
                float s = settings.gridWidth * settings.scale;
                float t = settings.gridHeight * settings.scale;

                float nx = x / s;
                float ny = y / t;

                float a = Mathf.PerlinNoise(x, y);
                float b = Mathf.PerlinNoise(x + s, y);
                float c = Mathf.PerlinNoise(x, y + t);
                float d = Mathf.PerlinNoise(x + s, y + t);

                float i1 = Mathf.Lerp(a, b, nx);
                float i2 = Mathf.Lerp(c, d, nx);

                return Mathf.Lerp(i1, i2, ny);
            }
            else
            {
                return Mathf.PerlinNoise(x, y);
            }
        }

        private float SampleSimplexNoise(float x, float y)
        {
            // Simplified 2D Simplex noise implementation
            float F2 = 0.5f * (Mathf.Sqrt(3.0f) - 1.0f);
            float G2 = (3.0f - Mathf.Sqrt(3.0f)) / 6.0f;

            float n0, n1, n2;

            float s = (x + y) * F2;
            int i = Mathf.FloorToInt(x + s);
            int j = Mathf.FloorToInt(y + s);

            float t = (i + j) * G2;
            float X0 = i - t;
            float Y0 = j - t;
            float x0 = x - X0;
            float y0 = y - Y0;

            int i1, j1;
            if (x0 > y0)
            {
                i1 = 1;
                j1 = 0;
            }
            else
            {
                i1 = 0;
                j1 = 1;
            }

            float x1 = x0 - i1 + G2;
            float y1 = y0 - j1 + G2;
            float x2 = x0 - 1.0f + 2.0f * G2;
            float y2 = y0 - 1.0f + 2.0f * G2;

            int ii = i & 255;
            int jj = j & 255;
            int gi0 = p[ii + p[jj]] % 12;
            int gi1 = p[ii + i1 + p[jj + j1]] % 12;
            int gi2 = p[ii + 1 + p[jj + 1]] % 12;

            float t0 = 0.5f - x0 * x0 - y0 * y0;
            if (t0 < 0) n0 = 0.0f;
            else
            {
                t0 *= t0;
                n0 = t0 * t0 * Dot(grad3[gi0], x0, y0);
            }

            float t1 = 0.5f - x1 * x1 - y1 * y1;
            if (t1 < 0) n1 = 0.0f;
            else
            {
                t1 *= t1;
                n1 = t1 * t1 * Dot(grad3[gi1], x1, y1);
            }

            float t2 = 0.5f - x2 * x2 - y2 * y2;
            if (t2 < 0) n2 = 0.0f;
            else
            {
                t2 *= t2;
                n2 = t2 * t2 * Dot(grad3[gi2], x2, y2);
            }

            return 70.0f * (n0 + n1 + n2) * 0.5f + 0.5f; // Normalize to 0-1
        }

        private static readonly int[][] grad3 =
        {
            new int[] { 1, 1, 0 }, new int[] { -1, 1, 0 }, new int[] { 1, -1, 0 }, new int[] { -1, -1, 0 },
            new int[] { 1, 0, 1 }, new int[] { -1, 0, 1 }, new int[] { 1, 0, -1 }, new int[] { -1, 0, -1 },
            new int[] { 0, 1, 1 }, new int[] { 0, -1, 1 }, new int[] { 0, 1, -1 }, new int[] { 0, -1, -1 }
        };

        private float Dot(int[] g, float x, float y)
        {
            return g[0] * x + g[1] * y;
        }

        private float SampleWorleyNoise(float x, float y)
        {
            // Worley/Cellular noise - distance to nearest point
            int cellX = Mathf.FloorToInt(x);
            int cellY = Mathf.FloorToInt(y);

            float minDist = float.MaxValue;

            // Check 3x3 grid of cells
            for (int i = -1; i <= 1; i++)
            {
                for (int j = -1; j <= 1; j++)
                {
                    int neighborX = cellX + i;
                    int neighborY = cellY + j;

                    // Generate random point in this cell
                    Vector2 point = GetCellPoint(neighborX, neighborY);

                    float dist = Vector2.Distance(new Vector2(x, y), point);
                    minDist = Mathf.Min(minDist, dist);
                }
            }

            return Mathf.Clamp01(minDist);
        }

        private Vector2 GetCellPoint(int cellX, int cellY)
        {
            // Generate consistent random point for this cell
            int hash = (cellX * 73856093) ^ (cellY * 19349663);
            System.Random cellRandom = new System.Random(hash);

            float pointX = cellX + (float)cellRandom.NextDouble();
            float pointY = cellY + (float)cellRandom.NextDouble();

            return new Vector2(pointX, pointY);
        }

        private float SampleRidgedNoise(float x, float y)
        {
            float value = SamplePerlinNoise(x, y);
            return 1.0f - Mathf.Abs(value * 2.0f - 1.0f); // Create ridges
        }

        private float SampleBillowyNoise(float x, float y)
        {
            float value = SamplePerlinNoise(x, y);
            return Mathf.Abs(value * 2.0f - 1.0f); // Create billowy clouds
        }

        private float SampleValueNoise(float x, float y)
        {
            // Simple value noise using interpolation
            int x0 = Mathf.FloorToInt(x);
            int y0 = Mathf.FloorToInt(y);
            int x1 = x0 + 1;
            int y1 = y0 + 1;

            float fx = x - x0;
            float fy = y - y0;

            // Smooth interpolation
            fx = fx * fx * (3.0f - 2.0f * fx);
            fy = fy * fy * (3.0f - 2.0f * fy);

            float a = GetValueNoiseAt(x0, y0);
            float b = GetValueNoiseAt(x1, y0);
            float c = GetValueNoiseAt(x0, y1);
            float d = GetValueNoiseAt(x1, y1);

            float i1 = Mathf.Lerp(a, b, fx);
            float i2 = Mathf.Lerp(c, d, fx);

            return Mathf.Lerp(i1, i2, fy);
        }

        private float GetValueNoiseAt(int x, int y)
        {
            int hash = (x * 73856093) ^ (y * 19349663);
            System.Random noiseRandom = new System.Random(hash);
            return (float)noiseRandom.NextDouble();
        }

        private float SampleWhiteNoise(int x, int y)
        {
            int hash = (x * 73856093) ^ (y * 19349663) ^ random.Next();
            System.Random whiteRandom = new System.Random(hash);
            return (float)whiteRandom.NextDouble();
        }

        private float SampleFractalNoise(float x, float y)
        {
            float value = 0.0f;
            float amplitude = 1.0f;
            float frequency = 1.0f;
            float maxValue = 0.0f;

            for (int i = 0; i < settings.octaves; i++)
            {
                value += SamplePerlinNoise(x * frequency, y * frequency) * amplitude;
                maxValue += amplitude;
                amplitude *= settings.persistence;
                frequency *= settings.lacunarity;
            }

            return value / maxValue; // Normalize
        }

        private float CalculateFalloff(int x, int y)
        {
            float centerX = settings.gridWidth * 0.5f;
            float centerY = settings.gridHeight * 0.5f;

            float maxDistance = Mathf.Min(centerX, centerY);
            float distance = Vector2.Distance(new Vector2(x, y), new Vector2(centerX, centerY));
            float normalizedDistance = distance / maxDistance;

            if (settings.falloffCurve != null)
            {
                return settings.falloffCurve.Evaluate(normalizedDistance);
            }
            else
            {
                // Default quadratic falloff
                return normalizedDistance * normalizedDistance;
            }
        }

        private void ApplyFilter()
        {
            if (settings.filter == NoiseFilter.None) return;

            for (int x = 0; x < settings.gridWidth; x++)
            {
                for (int y = 0; y < settings.gridHeight; y++)
                {
                    float value = noiseMap[x, y];

                    switch (settings.filter)
                    {
                        case NoiseFilter.Threshold:
                            value = value > settings.threshold ? 1.0f : 0.0f;
                            break;

                        case NoiseFilter.Smooth:
                            value = SmoothStep(settings.filterParam1, settings.filterParam2, value);
                            break;

                        case NoiseFilter.Quantize:
                            int levels = Mathf.RoundToInt(settings.filterParam1);
                            if (levels > 1)
                            {
                                value = Mathf.Round(value * (levels - 1)) / (levels - 1);
                            }

                            break;

                        case NoiseFilter.Invert:
                            value = 1.0f - value;
                            break;

                        case NoiseFilter.Abs:
                            value = Mathf.Abs(value * 2.0f - 1.0f);
                            break;

                        case NoiseFilter.Clamp:
                            value = Mathf.Clamp(value, settings.filterParam1, settings.filterParam2);
                            break;
                    }

                    noiseMap[x, y] = value;
                }
            }
        }

        private float SmoothStep(float edge0, float edge1, float x)
        {
            float t = Mathf.Clamp01((x - edge0) / (edge1 - edge0));
            return t * t * (3.0f - 2.0f * t);
        }

        private void ConvertToGrid()
        {
            grid = new int[settings.gridWidth, settings.gridHeight];

            for (int x = 0; x < settings.gridWidth; x++)
            {
                for (int y = 0; y < settings.gridHeight; y++)
                {
                    grid[x, y] = noiseMap[x, y] > settings.threshold ? 1 : 0;
                }
            }
        }

        // Utility methods for combining multiple noise generators
        public static float[,] CombineNoiseMaps(float[,] map1, float[,] map2, NoiseCombineMode mode)
        {
            int width = map1.GetLength(0);
            int height = map1.GetLength(1);
            float[,] result = new float[width, height];

            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    float value1 = map1[x, y];
                    float value2 = map2[x, y];

                    switch (mode)
                    {
                        case NoiseCombineMode.Add:
                            result[x, y] = Mathf.Clamp01(value1 + value2);
                            break;

                        case NoiseCombineMode.Multiply:
                            result[x, y] = value1 * value2;
                            break;

                        case NoiseCombineMode.Subtract:
                            result[x, y] = Mathf.Clamp01(value1 - value2);
                            break;

                        case NoiseCombineMode.Min:
                            result[x, y] = Mathf.Min(value1, value2);
                            break;

                        case NoiseCombineMode.Max:
                            result[x, y] = Mathf.Max(value1, value2);
                            break;

                        case NoiseCombineMode.Average:
                            result[x, y] = (value1 + value2) * 0.5f;
                            break;

                        case NoiseCombineMode.Overlay:
                            result[x, y] = value1 < 0.5f
                                ? 2.0f * value1 * value2
                                : 1.0f - 2.0f * (1.0f - value1) * (1.0f - value2);
                            break;
                    }
                }
            }

            return result;
        }

        public int GetTileCount()
        {
            int count = 0;
            for (int x = 0; x < settings.gridWidth; x++)
            {
                for (int y = 0; y < settings.gridHeight; y++)
                {
                    if (grid[x, y] == 1)
                        count++;
                }
            }

            return count;
        }

        public float GetFillPercentage()
        {
            return (float)GetTileCount() / (settings.gridWidth * settings.gridHeight);
        }

        public void PrintGrid()
        {
            string output =
                $"Noise Generated Grid ({GetTileCount()}/{settings.gridWidth * settings.gridHeight} tiles, {GetFillPercentage():P1} filled):\n";
            for (int y = settings.gridHeight - 1; y >= 0; y--)
            {
                for (int x = 0; x < settings.gridWidth; x++)
                {
                    output += grid[x, y] == 1 ? "█" : "·";
                }

                output += "\n";
            }

            Debug.Log(output);
        }

        public void PrintNoiseMap()
        {
            string output = "Noise Map (grayscale):\n";
            for (int y = settings.gridHeight - 1; y >= 0; y--)
            {
                for (int x = 0; x < settings.gridWidth; x++)
                {
                    float value = noiseMap[x, y];
                    char symbol = value > 0.8f ? '█' :
                        value > 0.6f ? '▓' :
                        value > 0.4f ? '▒' :
                        value > 0.2f ? '░' : '·';
                    output += symbol;
                }

                output += "\n";
            }

            Debug.Log(output);
        }

        // Utility methods for common noise configurations
        public static NoiseSettings CreateTerrainSettings(int width, int height)
        {
            var settings = NoiseSettings.Default;
            settings.gridWidth = width;
            settings.gridHeight = height;
            settings.noiseType = NoiseType.Fractal;
            settings.scale = 0.05f;
            settings.octaves = 6;
            settings.persistence = 0.5f;
            settings.lacunarity = 2.0f;
            settings.threshold = 0.4f;
            settings.useFalloffMap = true;
            return settings;
        }

        public static NoiseSettings CreateCaveSettings(int width, int height)
        {
            var settings = NoiseSettings.Default;
            settings.gridWidth = width;
            settings.gridHeight = height;
            settings.noiseType = NoiseType.Worley;
            settings.scale = 0.1f;
            settings.threshold = 0.6f;
            settings.filter = NoiseFilter.Invert;
            return settings;
        }

        public static NoiseSettings CreateCloudSettings(int width, int height)
        {
            var settings = NoiseSettings.Default;
            settings.gridWidth = width;
            settings.gridHeight = height;
            settings.noiseType = NoiseType.Billowy;
            settings.scale = 0.08f;
            settings.octaves = 4;
            settings.threshold = 0.5f;
            settings.filter = NoiseFilter.Smooth;
            settings.filterParam1 = 0.3f;
            settings.filterParam2 = 0.7f;
            return settings;
        }

        public static NoiseSettings CreateMountainSettings(int width, int height)
        {
            var settings = NoiseSettings.Default;
            settings.gridWidth = width;
            settings.gridHeight = height;
            settings.noiseType = NoiseType.Ridged;
            settings.scale = 0.06f;
            settings.octaves = 5;
            settings.threshold = 0.3f;
            settings.useFalloffMap = true;
            return settings;
        }
    }
}