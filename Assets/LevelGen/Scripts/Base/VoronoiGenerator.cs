using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace LevelGen.Scripts.Base
{
    public enum VoronoiDistanceMetric
    {
        Euclidean,      // Standard distance
        Manhattan,      // City block distance
        Chebyshev,      // Chess king distance
        Minkowski       // Generalized distance
    }
    
    public enum RegionGrowthPattern
    {
        Uniform,        // All regions grow at same rate
        Random,         // Random growth rates
        Weighted,       // Growth based on region properties
        Directional     // Growth in preferred directions
    }

    public class VoronoiGenerator
    {
        public struct VoronoiSettings
        {
            public int gridWidth;
            public int gridHeight;
            public int seedCount;
            public VoronoiDistanceMetric distanceMetric;
            public RegionGrowthPattern growthPattern;
            public float minkowskiP;               // For Minkowski distance (p=2 is Euclidean)
            public bool useRegionGrowing;          // Use iterative region growing vs direct Voronoi
            public int maxGrowthSteps;             // For region growing
            public float regionFillThreshold;      // 0.0-1.0, how much of region to fill
            public bool connectRegions;            // Connect isolated regions
            public List<Vector2Int> customSeeds;   // Custom seed positions
            public bool avoidBorders;              // Don't place seeds near borders
            public float borderMargin;             // Margin from borders (0.0-0.5)
            
            public static VoronoiSettings Default => new VoronoiSettings
            {
                gridWidth = 15,
                gridHeight = 8,
                seedCount = 5,
                distanceMetric = VoronoiDistanceMetric.Euclidean,
                growthPattern = RegionGrowthPattern.Uniform,
                minkowskiP = 2.0f,
                useRegionGrowing = true,
                maxGrowthSteps = 50,
                regionFillThreshold = 0.7f,
                connectRegions = true,
                customSeeds = new List<Vector2Int>(),
                avoidBorders = true,
                borderMargin = 0.15f
            };
        }
        
        private VoronoiSettings settings;
        private int[,] grid;
        private int[,] regionMap;  // Which region each cell belongs to
        private List<Vector2Int> seeds;
        private List<float> regionGrowthRates;
        private System.Random random;
        
        public VoronoiGenerator(VoronoiSettings settings, int? seed = null)
        {
            this.settings = settings;
            this.random = seed.HasValue ? new System.Random(seed.Value) : new System.Random();
        }
        
        public VoronoiGenerator(int width, int height, int seedCount, int? seed = null)
        {
            this.settings = VoronoiSettings.Default;
            this.settings.gridWidth = width;
            this.settings.gridHeight = height;
            this.settings.seedCount = seedCount;
            this.random = seed.HasValue ? new System.Random(seed.Value) : new System.Random();
        }
        
        public int[,] GenerateLevel()
        {
            InitializeGrid();
            GenerateSeeds();
            
            if (settings.useRegionGrowing)
            {
                GrowRegions();
            }
            else
            {
                GenerateVoronoiDiagram();
            }
            
            FillRegions();
            
            if (settings.connectRegions)
            {
                ConnectIsolatedRegions();
            }
            
            return grid;
        }
        
        private void InitializeGrid()
        {
            grid = new int[settings.gridWidth, settings.gridHeight];
            regionMap = new int[settings.gridWidth, settings.gridHeight];
            
            // Initialize all cells to empty and no region
            for (int x = 0; x < settings.gridWidth; x++)
            {
                for (int y = 0; y < settings.gridHeight; y++)
                {
                    grid[x, y] = 0;
                    regionMap[x, y] = -1; // -1 means no region assigned
                }
            }
        }
        
        private void GenerateSeeds()
        {
            seeds = new List<Vector2Int>();
            regionGrowthRates = new List<float>();
            
            if (settings.customSeeds.Count > 0)
            {
                seeds.AddRange(settings.customSeeds);
            }
            else
            {
                // Generate random seeds
                int attempts = 0;
                while (seeds.Count < settings.seedCount && attempts < settings.seedCount * 10)
                {
                    Vector2Int newSeed = GenerateRandomSeed();
                    
                    // Ensure seeds are not too close to each other
                    bool tooClose = false;
                    foreach (var existingSeed in seeds)
                    {
                        if (Vector2Int.Distance(newSeed, existingSeed) < 2.0f)
                        {
                            tooClose = true;
                            break;
                        }
                    }
                    
                    if (!tooClose)
                    {
                        seeds.Add(newSeed);
                    }
                    
                    attempts++;
                }
            }
            
            // Generate growth rates for each region
            for (int i = 0; i < seeds.Count; i++)
            {
                switch (settings.growthPattern)
                {
                    case RegionGrowthPattern.Uniform:
                        regionGrowthRates.Add(1.0f);
                        break;
                    case RegionGrowthPattern.Random:
                        regionGrowthRates.Add((float)(random.NextDouble() * 0.8 + 0.2)); // 0.2 to 1.0
                        break;
                    case RegionGrowthPattern.Weighted:
                        // Weight based on distance from center
                        Vector2 center = new Vector2(settings.gridWidth * 0.5f, settings.gridHeight * 0.5f);
                        float distFromCenter = Vector2.Distance(seeds[i], center);
                        float maxDist = Vector2.Distance(Vector2.zero, center);
                        regionGrowthRates.Add(1.0f - (distFromCenter / maxDist) * 0.5f);
                        break;
                    case RegionGrowthPattern.Directional:
                        regionGrowthRates.Add((float)(random.NextDouble() * 0.6 + 0.4)); // 0.4 to 1.0
                        break;
                }
            }
        }
        
        private Vector2Int GenerateRandomSeed()
        {
            int x, y;
            
            if (settings.avoidBorders)
            {
                int marginX = Mathf.RoundToInt(settings.gridWidth * settings.borderMargin);
                int marginY = Mathf.RoundToInt(settings.gridHeight * settings.borderMargin);
                
                x = random.Next(marginX, settings.gridWidth - marginX);
                y = random.Next(marginY, settings.gridHeight - marginY);
            }
            else
            {
                x = random.Next(0, settings.gridWidth);
                y = random.Next(0, settings.gridHeight);
            }
            
            return new Vector2Int(x, y);
        }
        
        private void GrowRegions()
        {
            // Initialize seeds in region map
            for (int i = 0; i < seeds.Count; i++)
            {
                Vector2Int seed = seeds[i];
                regionMap[seed.x, seed.y] = i;
            }
            
            Queue<Vector2Int> growthQueue = new Queue<Vector2Int>();
            foreach (var seed in seeds)
            {
                growthQueue.Enqueue(seed);
            }
            
            int step = 0;
            while (growthQueue.Count > 0 && step < settings.maxGrowthSteps)
            {
                int currentQueueSize = growthQueue.Count;
                
                for (int i = 0; i < currentQueueSize; i++)
                {
                    Vector2Int current = growthQueue.Dequeue();
                    int regionId = regionMap[current.x, current.y];
                    
                    // Check if this region should grow this step
                    if (random.NextDouble() > regionGrowthRates[regionId])
                        continue;
                    
                    // Try to grow to neighboring cells
                    Vector2Int[] neighbors = GetNeighbors(current);
                    foreach (var neighbor in neighbors)
                    {
                        if (IsValidPosition(neighbor) && regionMap[neighbor.x, neighbor.y] == -1)
                        {
                            regionMap[neighbor.x, neighbor.y] = regionId;
                            growthQueue.Enqueue(neighbor);
                        }
                    }
                }
                
                step++;
            }
        }
        
        private void GenerateVoronoiDiagram()
        {
            for (int x = 0; x < settings.gridWidth; x++)
            {
                for (int y = 0; y < settings.gridHeight; y++)
                {
                    Vector2Int pos = new Vector2Int(x, y);
                    int closestSeedIndex = FindClosestSeed(pos);
                    regionMap[x, y] = closestSeedIndex;
                }
            }
        }
        
        private int FindClosestSeed(Vector2Int pos)
        {
            float minDistance = float.MaxValue;
            int closestIndex = 0;
            
            for (int i = 0; i < seeds.Count; i++)
            {
                float distance = CalculateDistance(pos, seeds[i]);
                if (distance < minDistance)
                {
                    minDistance = distance;
                    closestIndex = i;
                }
            }
            
            return closestIndex;
        }
        
        private float CalculateDistance(Vector2Int a, Vector2Int b)
        {
            switch (settings.distanceMetric)
            {
                case VoronoiDistanceMetric.Euclidean:
                    return Vector2Int.Distance(a, b);
                    
                case VoronoiDistanceMetric.Manhattan:
                    return Mathf.Abs(a.x - b.x) + Mathf.Abs(a.y - b.y);
                    
                case VoronoiDistanceMetric.Chebyshev:
                    return Mathf.Max(Mathf.Abs(a.x - b.x), Mathf.Abs(a.y - b.y));
                    
                case VoronoiDistanceMetric.Minkowski:
                    float dx = Mathf.Abs(a.x - b.x);
                    float dy = Mathf.Abs(a.y - b.y);
                    return Mathf.Pow(Mathf.Pow(dx, settings.minkowskiP) + Mathf.Pow(dy, settings.minkowskiP), 1.0f / settings.minkowskiP);
                    
                default:
                    return Vector2Int.Distance(a, b);
            }
        }
        
        private void FillRegions()
        {
            // Count cells in each region
            Dictionary<int, List<Vector2Int>> regionCells = new Dictionary<int, List<Vector2Int>>();
            
            for (int x = 0; x < settings.gridWidth; x++)
            {
                for (int y = 0; y < settings.gridHeight; y++)
                {
                    int regionId = regionMap[x, y];
                    if (regionId >= 0)
                    {
                        if (!regionCells.ContainsKey(regionId))
                            regionCells[regionId] = new List<Vector2Int>();
                        regionCells[regionId].Add(new Vector2Int(x, y));
                    }
                }
            }
            
            // Fill each region based on threshold
            foreach (var kvp in regionCells)
            {
                var cells = kvp.Value;
                int cellsToFill = Mathf.RoundToInt(cells.Count * settings.regionFillThreshold);
                
                // Shuffle and take first N cells
                for (int i = 0; i < cells.Count; i++)
                {
                    int randomIndex = random.Next(i, cells.Count);
                    var temp = cells[i];
                    cells[i] = cells[randomIndex];
                    cells[randomIndex] = temp;
                }
                
                for (int i = 0; i < cellsToFill; i++)
                {
                    var cell = cells[i];
                    grid[cell.x, cell.y] = 1;
                }
            }
        }
        
        private void ConnectIsolatedRegions()
        {
            // Simple connection: draw lines between region centers
            for (int i = 0; i < seeds.Count - 1; i++)
            {
                Vector2Int start = seeds[i];
                Vector2Int end = seeds[i + 1];
                DrawLine(start, end);
            }
        }
        
        private void DrawLine(Vector2Int start, Vector2Int end)
        {
            // Bresenham's line algorithm
            int dx = Mathf.Abs(end.x - start.x);
            int dy = Mathf.Abs(end.y - start.y);
            int sx = start.x < end.x ? 1 : -1;
            int sy = start.y < end.y ? 1 : -1;
            int err = dx - dy;
            
            Vector2Int current = start;
            
            while (true)
            {
                if (IsValidPosition(current))
                {
                    grid[current.x, current.y] = 1;
                }
                
                if (current.x == end.x && current.y == end.y) break;
                
                int e2 = 2 * err;
                if (e2 > -dy)
                {
                    err -= dy;
                    current.x += sx;
                }
                if (e2 < dx)
                {
                    err += dx;
                    current.y += sy;
                }
            }
        }
        
        private Vector2Int[] GetNeighbors(Vector2Int pos)
        {
            return new Vector2Int[]
            {
                new Vector2Int(pos.x + 1, pos.y),
                new Vector2Int(pos.x - 1, pos.y),
                new Vector2Int(pos.x, pos.y + 1),
                new Vector2Int(pos.x, pos.y - 1)
            };
        }
        
        private bool IsValidPosition(Vector2Int pos)
        {
            return pos.x >= 0 && pos.x < settings.gridWidth && 
                   pos.y >= 0 && pos.y < settings.gridHeight;
        }
        
        public int GetTileCount()
        {
            int count = 0;
            for (int x = 0; x < settings.gridWidth; x++)
            {
                for (int y = 0; y < settings.gridHeight; y++)
                {
                    if (grid[x, y] == 1)
                        count++;
                }
            }
            return count;
        }
        
        public float GetFillPercentage()
        {
            return (float)GetTileCount() / (settings.gridWidth * settings.gridHeight);
        }
        
        public void PrintGrid()
        {
            string output = $"Voronoi Generated Grid ({GetTileCount()}/{settings.gridWidth * settings.gridHeight} tiles, {GetFillPercentage():P1} filled):\n";
            for (int y = settings.gridHeight - 1; y >= 0; y--)
            {
                for (int x = 0; x < settings.gridWidth; x++)
                {
                    output += grid[x, y] == 1 ? "█" : "·";
                }
                output += "\n";
            }
            Debug.Log(output);
        }
        
        // Utility methods
        public static VoronoiSettings CreateOrganicSettings(int width, int height, int seedCount)
        {
            var settings = VoronoiSettings.Default;
            settings.gridWidth = width;
            settings.gridHeight = height;
            settings.seedCount = seedCount;
            settings.useRegionGrowing = true;
            settings.growthPattern = RegionGrowthPattern.Random;
            settings.regionFillThreshold = 0.8f;
            return settings;
        }
        
        public static VoronoiSettings CreateStructuredSettings(int width, int height, int seedCount)
        {
            var settings = VoronoiSettings.Default;
            settings.gridWidth = width;
            settings.gridHeight = height;
            settings.seedCount = seedCount;
            settings.useRegionGrowing = false;
            settings.distanceMetric = VoronoiDistanceMetric.Manhattan;
            settings.regionFillThreshold = 0.6f;
            return settings;
        }
    }
}
