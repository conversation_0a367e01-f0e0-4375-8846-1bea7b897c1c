using System.Collections.Generic;
using UnityEngine;

namespace LevelGen.Scripts.Base
{
    public enum CAInitializationMethod
    {
        Random,         // Khởi tạo ngẫu nhiên
        Noise,          // Sử dụng Perlin noise
        Seed,           // Từ một số điểm seed
        Border          // Viền ngoài solid, trong rỗng
    }
    
    public enum CARuleType
    {
        Conway,         // Game of Life rules
        Cave,           // Cave generation rules
        Maze,           // Maze-like structures
        Custom          // Custom rules
    }

    public class CellularAutomataGenerator
    {
        public struct CASettings
        {
            public int gridWidth;
            public int gridHeight;
            public int iterations;
            public float initialDensity;           // 0.0 - 1.0 for random initialization
            public CAInitializationMethod initMethod;
            public CARuleType ruleType;
            public int birthLimit;                 // Minimum neighbors to birth a cell
            public int deathLimit;                 // Minimum neighbors to keep a cell alive
            public bool useExtendedNeighborhood;   // 8-connected vs 4-connected
            public List<Vector2Int> seedPoints;    // For seed initialization
            public float noiseScale;               // For noise initialization
            
            public static CASettings Default => new CASettings
            {
                gridWidth = 15,
                gridHeight = 8,
                iterations = 5,
                initialDensity = 0.45f,
                initMethod = CAInitializationMethod.Random,
                ruleType = CARuleType.Cave,
                birthLimit = 4,
                deathLimit = 3,
                useExtendedNeighborhood = true,
                seedPoints = new List<Vector2Int>(),
                noiseScale = 0.1f
            };
        }
        
        private CASettings settings;
        private int[,] grid;
        private System.Random random;
        
        public CellularAutomataGenerator(CASettings settings, int? seed = null)
        {
            this.settings = settings;
            this.random = seed.HasValue ? new System.Random(seed.Value) : new System.Random();
        }
        
        public CellularAutomataGenerator(int width, int height, int? seed = null)
        {
            this.settings = CASettings.Default;
            this.settings.gridWidth = width;
            this.settings.gridHeight = height;
            this.random = seed.HasValue ? new System.Random(seed.Value) : new System.Random();
        }
        
        public int[,] GenerateLevel()
        {
            InitializeGrid();
            
            for (int i = 0; i < settings.iterations; i++)
            {
                ApplyRules();
            }
            
            return grid;
        }
        
        private void InitializeGrid()
        {
            grid = new int[settings.gridWidth, settings.gridHeight];
            
            switch (settings.initMethod)
            {
                case CAInitializationMethod.Random:
                    InitializeRandom();
                    break;
                case CAInitializationMethod.Noise:
                    InitializeNoise();
                    break;
                case CAInitializationMethod.Seed:
                    InitializeSeed();
                    break;
                case CAInitializationMethod.Border:
                    InitializeBorder();
                    break;
            }
        }
        
        private void InitializeRandom()
        {
            for (int x = 0; x < settings.gridWidth; x++)
            {
                for (int y = 0; y < settings.gridHeight; y++)
                {
                    grid[x, y] = random.NextDouble() < settings.initialDensity ? 1 : 0;
                }
            }
        }
        
        private void InitializeNoise()
        {
            for (int x = 0; x < settings.gridWidth; x++)
            {
                for (int y = 0; y < settings.gridHeight; y++)
                {
                    float noiseValue = Mathf.PerlinNoise(x * settings.noiseScale, y * settings.noiseScale);
                    grid[x, y] = noiseValue > 0.5f ? 1 : 0;
                }
            }
        }
        
        private void InitializeSeed()
        {
            // Start with empty grid
            for (int x = 0; x < settings.gridWidth; x++)
            {
                for (int y = 0; y < settings.gridHeight; y++)
                {
                    grid[x, y] = 0;
                }
            }
            
            // Place seed points
            foreach (var seed in settings.seedPoints)
            {
                if (IsValidPosition(seed))
                {
                    grid[seed.x, seed.y] = 1;
                }
            }
        }
        
        private void InitializeBorder()
        {
            for (int x = 0; x < settings.gridWidth; x++)
            {
                for (int y = 0; y < settings.gridHeight; y++)
                {
                    // Border cells are solid
                    if (x == 0 || x == settings.gridWidth - 1 || y == 0 || y == settings.gridHeight - 1)
                    {
                        grid[x, y] = 1;
                    }
                    else
                    {
                        grid[x, y] = 0;
                    }
                }
            }
        }
        
        private void ApplyRules()
        {
            int[,] newGrid = new int[settings.gridWidth, settings.gridHeight];
            
            for (int x = 0; x < settings.gridWidth; x++)
            {
                for (int y = 0; y < settings.gridHeight; y++)
                {
                    int neighbors = CountNeighbors(x, y);
                    newGrid[x, y] = ApplyRule(grid[x, y], neighbors);
                }
            }
            
            grid = newGrid;
        }
        
        private int CountNeighbors(int x, int y)
        {
            int count = 0;
            
            if (settings.useExtendedNeighborhood)
            {
                // 8-connected neighborhood
                for (int dx = -1; dx <= 1; dx++)
                {
                    for (int dy = -1; dy <= 1; dy++)
                    {
                        if (dx == 0 && dy == 0) continue;
                        
                        int nx = x + dx;
                        int ny = y + dy;
                        
                        if (IsValidPosition(new Vector2Int(nx, ny)))
                        {
                            count += grid[nx, ny];
                        }
                        else
                        {
                            // Treat out-of-bounds as solid for cave generation
                            count += 1;
                        }
                    }
                }
            }
            else
            {
                // 4-connected neighborhood
                Vector2Int[] directions = {
                    new Vector2Int(0, 1), new Vector2Int(1, 0),
                    new Vector2Int(0, -1), new Vector2Int(-1, 0)
                };
                
                foreach (var dir in directions)
                {
                    int nx = x + dir.x;
                    int ny = y + dir.y;
                    
                    if (IsValidPosition(new Vector2Int(nx, ny)))
                    {
                        count += grid[nx, ny];
                    }
                    else
                    {
                        count += 1;
                    }
                }
            }
            
            return count;
        }
        
        private int ApplyRule(int currentState, int neighbors)
        {
            switch (settings.ruleType)
            {
                case CARuleType.Conway:
                    // Conway's Game of Life rules
                    if (currentState == 1)
                    {
                        return (neighbors == 2 || neighbors == 3) ? 1 : 0;
                    }
                    else
                    {
                        return neighbors == 3 ? 1 : 0;
                    }
                    
                case CARuleType.Cave:
                    // Cave generation rules
                    if (currentState == 1)
                    {
                        return neighbors >= settings.deathLimit ? 1 : 0;
                    }
                    else
                    {
                        return neighbors > settings.birthLimit ? 1 : 0;
                    }
                    
                case CARuleType.Maze:
                    // Maze-like structure rules
                    if (currentState == 1)
                    {
                        return neighbors >= 1 && neighbors <= 5 ? 1 : 0;
                    }
                    else
                    {
                        return neighbors == 3 ? 1 : 0;
                    }
                    
                case CARuleType.Custom:
                default:
                    // Use custom birth/death limits
                    if (currentState == 1)
                    {
                        return neighbors >= settings.deathLimit ? 1 : 0;
                    }
                    else
                    {
                        return neighbors > settings.birthLimit ? 1 : 0;
                    }
            }
        }
        
        private bool IsValidPosition(Vector2Int pos)
        {
            return pos.x >= 0 && pos.x < settings.gridWidth && 
                   pos.y >= 0 && pos.y < settings.gridHeight;
        }
        
        public int GetTileCount()
        {
            int count = 0;
            for (int x = 0; x < settings.gridWidth; x++)
            {
                for (int y = 0; y < settings.gridHeight; y++)
                {
                    if (grid[x, y] == 1)
                        count++;
                }
            }
            return count;
        }
        
        public float GetFillPercentage()
        {
            return (float)GetTileCount() / (settings.gridWidth * settings.gridHeight);
        }
        
        public void PrintGrid()
        {
            string output = $"CA Generated Grid ({GetTileCount()}/{settings.gridWidth * settings.gridHeight} tiles, {GetFillPercentage():P1} filled):\n";
            for (int y = settings.gridHeight - 1; y >= 0; y--)
            {
                for (int x = 0; x < settings.gridWidth; x++)
                {
                    output += grid[x, y] == 1 ? "█" : "·";
                }
                output += "\n";
            }
            Debug.Log(output);
        }
        
        // Utility methods
        public static CASettings CreateCaveSettings(int width, int height)
        {
            var settings = CASettings.Default;
            settings.gridWidth = width;
            settings.gridHeight = height;
            settings.ruleType = CARuleType.Cave;
            settings.initialDensity = 0.45f;
            settings.iterations = 5;
            settings.birthLimit = 4;
            settings.deathLimit = 3;
            return settings;
        }
        
        public static CASettings CreateMazeSettings(int width, int height)
        {
            var settings = CASettings.Default;
            settings.gridWidth = width;
            settings.gridHeight = height;
            settings.ruleType = CARuleType.Maze;
            settings.initialDensity = 0.3f;
            settings.iterations = 3;
            return settings;
        }
    }
}
