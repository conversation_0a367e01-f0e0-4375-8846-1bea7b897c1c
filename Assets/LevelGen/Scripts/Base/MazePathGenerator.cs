using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace LevelGen.Scripts.Base
{
    public enum MazeAlgorithm
    {
        RecursiveBacktracking,  // Classic maze generation
        Kruskal,               // Minimum spanning tree
        Prim,                  // Growing tree algorithm
        Wilson,                // Loop-erased random walk
        BinaryTree,            // Simple binary tree maze
        Sidewinder             // Row-by-row generation
    }
    
    public enum PathStyle
    {
        Corridors,             // Thin paths
        Rooms,                 // Wide areas connected by paths
        Mixed,                 // Combination of both
        Organic                // Natural-looking paths
    }

    public class MazePathGenerator
    {
        public struct MazeSettings
        {
            public int gridWidth;
            public int gridHeight;
            public MazeAlgorithm algorithm;
            public PathStyle pathStyle;
            public float pathWidth;                // 1.0 = single cell, 2.0 = double width, etc.
            public float roomProbability;          // 0.0-1.0, chance to create rooms
            public Vector2Int roomSizeRange;       // Min/max room size
            public bool ensureConnectivity;        // Ensure all areas are connected
            public bool addDeadEnds;              // Keep or remove dead ends
            public float branchingFactor;         // 0.0-1.0, how much branching
            public bool createLoops;              // Add loops to make it less tree-like
            public float loopProbability;         // Chance to create loops
            public List<Vector2Int> mandatoryPoints; // Points that must be connected
            
            public static MazeSettings Default => new MazeSettings
            {
                gridWidth = 15,
                gridHeight = 8,
                algorithm = MazeAlgorithm.RecursiveBacktracking,
                pathStyle = PathStyle.Mixed,
                pathWidth = 1.0f,
                roomProbability = 0.3f,
                roomSizeRange = new Vector2Int(3, 6),
                ensureConnectivity = true,
                addDeadEnds = false,
                branchingFactor = 0.7f,
                createLoops = true,
                loopProbability = 0.2f,
                mandatoryPoints = new List<Vector2Int>()
            };
        }
        
        private MazeSettings settings;
        private int[,] grid;
        private bool[,] visited;
        private List<Vector2Int> rooms;
        private System.Random random;
        
        public MazePathGenerator(MazeSettings settings, int? seed = null)
        {
            this.settings = settings;
            this.random = seed.HasValue ? new System.Random(seed.Value) : new System.Random();
        }
        
        public MazePathGenerator(int width, int height, int? seed = null)
        {
            this.settings = MazeSettings.Default;
            this.settings.gridWidth = width;
            this.settings.gridHeight = height;
            this.random = seed.HasValue ? new System.Random(seed.Value) : new System.Random();
        }
        
        public int[,] GenerateLevel()
        {
            InitializeGrid();
            
            switch (settings.algorithm)
            {
                case MazeAlgorithm.RecursiveBacktracking:
                    GenerateRecursiveBacktracking();
                    break;
                case MazeAlgorithm.Kruskal:
                    GenerateKruskal();
                    break;
                case MazeAlgorithm.Prim:
                    GeneratePrim();
                    break;
                case MazeAlgorithm.Wilson:
                    GenerateWilson();
                    break;
                case MazeAlgorithm.BinaryTree:
                    GenerateBinaryTree();
                    break;
                case MazeAlgorithm.Sidewinder:
                    GenerateSidewinder();
                    break;
            }
            
            if (settings.pathStyle == PathStyle.Rooms || settings.pathStyle == PathStyle.Mixed)
            {
                AddRooms();
            }
            
            if (settings.pathWidth > 1.0f)
            {
                WidenPaths();
            }
            
            if (settings.createLoops)
            {
                AddLoops();
            }
            
            if (!settings.addDeadEnds)
            {
                RemoveDeadEnds();
            }
            
            if (settings.ensureConnectivity)
            {
                EnsureConnectivity();
            }
            
            return grid;
        }
        
        private void InitializeGrid()
        {
            grid = new int[settings.gridWidth, settings.gridHeight];
            visited = new bool[settings.gridWidth, settings.gridHeight];
            rooms = new List<Vector2Int>();
            
            // Initialize all cells to walls (0)
            for (int x = 0; x < settings.gridWidth; x++)
            {
                for (int y = 0; y < settings.gridHeight; y++)
                {
                    grid[x, y] = 0;
                    visited[x, y] = false;
                }
            }
        }
        
        private void GenerateRecursiveBacktracking()
        {
            Stack<Vector2Int> stack = new Stack<Vector2Int>();
            Vector2Int start = new Vector2Int(1, 1); // Start from odd coordinates for proper maze
            
            grid[start.x, start.y] = 1;
            visited[start.x, start.y] = true;
            stack.Push(start);
            
            while (stack.Count > 0)
            {
                Vector2Int current = stack.Peek();
                List<Vector2Int> neighbors = GetUnvisitedNeighbors(current, 2); // Step by 2 for maze
                
                if (neighbors.Count > 0)
                {
                    Vector2Int next = neighbors[random.Next(neighbors.Count)];
                    
                    // Carve path between current and next
                    Vector2Int between = new Vector2Int(
                        (current.x + next.x) / 2,
                        (current.y + next.y) / 2
                    );
                    
                    grid[next.x, next.y] = 1;
                    grid[between.x, between.y] = 1;
                    visited[next.x, next.y] = true;
                    
                    stack.Push(next);
                }
                else
                {
                    stack.Pop();
                }
            }
        }
        
        private void GeneratePrim()
        {
            List<Vector2Int> walls = new List<Vector2Int>();
            Vector2Int start = new Vector2Int(1, 1);
            
            grid[start.x, start.y] = 1;
            visited[start.x, start.y] = true;
            AddWallsToList(start, walls);
            
            while (walls.Count > 0)
            {
                int randomIndex = random.Next(walls.Count);
                Vector2Int wall = walls[randomIndex];
                walls.RemoveAt(randomIndex);
                
                List<Vector2Int> neighbors = GetVisitedNeighbors(wall, 2);
                if (neighbors.Count == 1) // Only one side is visited
                {
                    grid[wall.x, wall.y] = 1;
                    
                    // Find the unvisited side
                    List<Vector2Int> unvisited = GetUnvisitedNeighbors(wall, 2);
                    if (unvisited.Count > 0)
                    {
                        Vector2Int next = unvisited[0];
                        grid[next.x, next.y] = 1;
                        visited[next.x, next.y] = true;
                        AddWallsToList(next, walls);
                    }
                }
            }
        }
        
        private void GenerateBinaryTree()
        {
            for (int x = 1; x < settings.gridWidth; x += 2)
            {
                for (int y = 1; y < settings.gridHeight; y += 2)
                {
                    grid[x, y] = 1;
                    
                    List<Vector2Int> directions = new List<Vector2Int>();
                    
                    // Can go north?
                    if (y + 2 < settings.gridHeight)
                        directions.Add(new Vector2Int(0, 1));
                    
                    // Can go east?
                    if (x + 2 < settings.gridWidth)
                        directions.Add(new Vector2Int(1, 0));
                    
                    if (directions.Count > 0)
                    {
                        Vector2Int dir = directions[random.Next(directions.Count)];
                        Vector2Int wall = new Vector2Int(x + dir.x, y + dir.y);
                        Vector2Int next = new Vector2Int(x + dir.x * 2, y + dir.y * 2);
                        
                        grid[wall.x, wall.y] = 1;
                        grid[next.x, next.y] = 1;
                    }
                }
            }
        }
        
        private void GenerateKruskal()
        {
            // Simplified Kruskal's algorithm
            Dictionary<Vector2Int, int> cellSets = new Dictionary<Vector2Int, int>();
            List<(Vector2Int, Vector2Int)> edges = new List<(Vector2Int, Vector2Int)>();
            int setCounter = 0;
            
            // Initialize cells and edges
            for (int x = 1; x < settings.gridWidth; x += 2)
            {
                for (int y = 1; y < settings.gridHeight; y += 2)
                {
                    Vector2Int cell = new Vector2Int(x, y);
                    cellSets[cell] = setCounter++;
                    grid[x, y] = 1;
                    
                    // Add edges to neighbors
                    if (x + 2 < settings.gridWidth)
                        edges.Add((cell, new Vector2Int(x + 2, y)));
                    if (y + 2 < settings.gridHeight)
                        edges.Add((cell, new Vector2Int(x, y + 2)));
                }
            }
            
            // Shuffle edges
            for (int i = 0; i < edges.Count; i++)
            {
                int randomIndex = random.Next(i, edges.Count);
                var temp = edges[i];
                edges[i] = edges[randomIndex];
                edges[randomIndex] = temp;
            }
            
            // Process edges
            foreach (var edge in edges)
            {
                Vector2Int cell1 = edge.Item1;
                Vector2Int cell2 = edge.Item2;
                
                if (cellSets[cell1] != cellSets[cell2])
                {
                    // Connect the cells
                    Vector2Int wall = new Vector2Int(
                        (cell1.x + cell2.x) / 2,
                        (cell1.y + cell2.y) / 2
                    );
                    grid[wall.x, wall.y] = 1;
                    
                    // Union the sets
                    int oldSet = cellSets[cell2];
                    int newSet = cellSets[cell1];
                    foreach (var kvp in cellSets.ToList())
                    {
                        if (kvp.Value == oldSet)
                            cellSets[kvp.Key] = newSet;
                    }
                }
            }
        }
        
        private void GenerateWilson()
        {
            // Simplified Wilson's algorithm
            List<Vector2Int> unvisited = new List<Vector2Int>();
            
            // Add all odd coordinates to unvisited
            for (int x = 1; x < settings.gridWidth; x += 2)
            {
                for (int y = 1; y < settings.gridHeight; y += 2)
                {
                    unvisited.Add(new Vector2Int(x, y));
                }
            }
            
            if (unvisited.Count == 0) return;
            
            // Mark first cell as visited
            Vector2Int first = unvisited[random.Next(unvisited.Count)];
            unvisited.Remove(first);
            grid[first.x, first.y] = 1;
            visited[first.x, first.y] = true;
            
            while (unvisited.Count > 0)
            {
                Vector2Int start = unvisited[random.Next(unvisited.Count)];
                List<Vector2Int> path = new List<Vector2Int> { start };
                Vector2Int current = start;
                
                // Random walk until we hit a visited cell
                while (!visited[current.x, current.y])
                {
                    List<Vector2Int> neighbors = GetAllNeighbors(current, 2);
                    if (neighbors.Count > 0)
                    {
                        current = neighbors[random.Next(neighbors.Count)];
                        
                        // Remove loops in path
                        int existingIndex = path.IndexOf(current);
                        if (existingIndex >= 0)
                        {
                            path.RemoveRange(existingIndex + 1, path.Count - existingIndex - 1);
                        }
                        else
                        {
                            path.Add(current);
                        }
                    }
                    else break;
                }
                
                // Carve the path
                for (int i = 0; i < path.Count - 1; i++)
                {
                    Vector2Int cell = path[i];
                    Vector2Int next = path[i + 1];
                    
                    grid[cell.x, cell.y] = 1;
                    visited[cell.x, cell.y] = true;
                    unvisited.Remove(cell);
                    
                    // Carve wall between cells
                    Vector2Int wall = new Vector2Int(
                        (cell.x + next.x) / 2,
                        (cell.y + next.y) / 2
                    );
                    grid[wall.x, wall.y] = 1;
                }
            }
        }
        
        private void GenerateSidewinder()
        {
            for (int y = 1; y < settings.gridHeight; y += 2)
            {
                List<Vector2Int> run = new List<Vector2Int>();
                
                for (int x = 1; x < settings.gridWidth; x += 2)
                {
                    Vector2Int cell = new Vector2Int(x, y);
                    grid[x, y] = 1;
                    run.Add(cell);
                    
                    bool carveEast = (x + 2 < settings.gridWidth) && 
                                   (y == settings.gridHeight - 2 || random.NextDouble() < 0.5);
                    
                    if (carveEast)
                    {
                        grid[x + 1, y] = 1; // Carve east wall
                    }
                    else
                    {
                        // Carve north from random cell in run
                        if (y + 2 < settings.gridHeight && run.Count > 0)
                        {
                            Vector2Int randomCell = run[random.Next(run.Count)];
                            grid[randomCell.x, randomCell.y + 1] = 1; // Carve north wall
                        }
                        run.Clear();
                    }
                }
            }
        }
        
        private void AddRooms()
        {
            int roomCount = Mathf.RoundToInt(settings.roomProbability * 10);
            
            for (int i = 0; i < roomCount; i++)
            {
                int roomWidth = random.Next(settings.roomSizeRange.x, settings.roomSizeRange.y + 1);
                int roomHeight = random.Next(settings.roomSizeRange.x, settings.roomSizeRange.y + 1);
                
                int x = random.Next(1, settings.gridWidth - roomWidth - 1);
                int y = random.Next(1, settings.gridHeight - roomHeight - 1);
                
                // Carve room
                for (int rx = x; rx < x + roomWidth; rx++)
                {
                    for (int ry = y; ry < y + roomHeight; ry++)
                    {
                        grid[rx, ry] = 1;
                    }
                }
                
                rooms.Add(new Vector2Int(x + roomWidth / 2, y + roomHeight / 2));
            }
        }
        
        private void WidenPaths()
        {
            int[,] newGrid = new int[settings.gridWidth, settings.gridHeight];
            int radius = Mathf.RoundToInt(settings.pathWidth / 2);
            
            for (int x = 0; x < settings.gridWidth; x++)
            {
                for (int y = 0; y < settings.gridHeight; y++)
                {
                    if (grid[x, y] == 1)
                    {
                        for (int dx = -radius; dx <= radius; dx++)
                        {
                            for (int dy = -radius; dy <= radius; dy++)
                            {
                                int nx = x + dx;
                                int ny = y + dy;
                                if (IsValidPosition(new Vector2Int(nx, ny)))
                                {
                                    newGrid[nx, ny] = 1;
                                }
                            }
                        }
                    }
                }
            }
            
            grid = newGrid;
        }
        
        private void AddLoops()
        {
            List<Vector2Int> walls = new List<Vector2Int>();
            
            // Find walls that could create loops
            for (int x = 1; x < settings.gridWidth - 1; x++)
            {
                for (int y = 1; y < settings.gridHeight - 1; y++)
                {
                    if (grid[x, y] == 0)
                    {
                        int pathNeighbors = CountPathNeighbors(new Vector2Int(x, y));
                        if (pathNeighbors >= 2)
                        {
                            walls.Add(new Vector2Int(x, y));
                        }
                    }
                }
            }
            
            // Randomly remove some walls to create loops
            int loopsToAdd = Mathf.RoundToInt(walls.Count * settings.loopProbability);
            for (int i = 0; i < loopsToAdd && walls.Count > 0; i++)
            {
                int randomIndex = random.Next(walls.Count);
                Vector2Int wall = walls[randomIndex];
                walls.RemoveAt(randomIndex);
                grid[wall.x, wall.y] = 1;
            }
        }
        
        private void RemoveDeadEnds()
        {
            bool changed = true;
            while (changed)
            {
                changed = false;
                for (int x = 1; x < settings.gridWidth - 1; x++)
                {
                    for (int y = 1; y < settings.gridHeight - 1; y++)
                    {
                        if (grid[x, y] == 1 && CountPathNeighbors(new Vector2Int(x, y)) <= 1)
                        {
                            grid[x, y] = 0;
                            changed = true;
                        }
                    }
                }
            }
        }
        
        private void EnsureConnectivity()
        {
            // Simple connectivity check and fix
            // This is a simplified version - a full implementation would use flood fill
            for (int i = 0; i < rooms.Count - 1; i++)
            {
                DrawLine(rooms[i], rooms[i + 1]);
            }
        }
        
        private void DrawLine(Vector2Int start, Vector2Int end)
        {
            // Simple line drawing
            int dx = Mathf.Abs(end.x - start.x);
            int dy = Mathf.Abs(end.y - start.y);
            int sx = start.x < end.x ? 1 : -1;
            int sy = start.y < end.y ? 1 : -1;
            int err = dx - dy;
            
            Vector2Int current = start;
            
            while (true)
            {
                if (IsValidPosition(current))
                {
                    grid[current.x, current.y] = 1;
                }
                
                if (current.x == end.x && current.y == end.y) break;
                
                int e2 = 2 * err;
                if (e2 > -dy)
                {
                    err -= dy;
                    current.x += sx;
                }
                if (e2 < dx)
                {
                    err += dx;
                    current.y += sy;
                }
            }
        }
        
        private List<Vector2Int> GetUnvisitedNeighbors(Vector2Int pos, int step)
        {
            List<Vector2Int> neighbors = new List<Vector2Int>();
            Vector2Int[] directions = {
                new Vector2Int(0, step), new Vector2Int(step, 0),
                new Vector2Int(0, -step), new Vector2Int(-step, 0)
            };
            
            foreach (var dir in directions)
            {
                Vector2Int neighbor = pos + dir;
                if (IsValidPosition(neighbor) && !visited[neighbor.x, neighbor.y])
                {
                    neighbors.Add(neighbor);
                }
            }
            
            return neighbors;
        }
        
        private List<Vector2Int> GetVisitedNeighbors(Vector2Int pos, int step)
        {
            List<Vector2Int> neighbors = new List<Vector2Int>();
            Vector2Int[] directions = {
                new Vector2Int(0, step), new Vector2Int(step, 0),
                new Vector2Int(0, -step), new Vector2Int(-step, 0)
            };
            
            foreach (var dir in directions)
            {
                Vector2Int neighbor = pos + dir;
                if (IsValidPosition(neighbor) && visited[neighbor.x, neighbor.y])
                {
                    neighbors.Add(neighbor);
                }
            }
            
            return neighbors;
        }
        
        private List<Vector2Int> GetAllNeighbors(Vector2Int pos, int step)
        {
            List<Vector2Int> neighbors = new List<Vector2Int>();
            Vector2Int[] directions = {
                new Vector2Int(0, step), new Vector2Int(step, 0),
                new Vector2Int(0, -step), new Vector2Int(-step, 0)
            };
            
            foreach (var dir in directions)
            {
                Vector2Int neighbor = pos + dir;
                if (IsValidPosition(neighbor))
                {
                    neighbors.Add(neighbor);
                }
            }
            
            return neighbors;
        }
        
        private void AddWallsToList(Vector2Int cell, List<Vector2Int> walls)
        {
            Vector2Int[] directions = {
                new Vector2Int(0, 2), new Vector2Int(2, 0),
                new Vector2Int(0, -2), new Vector2Int(-2, 0)
            };
            
            foreach (var dir in directions)
            {
                Vector2Int wall = cell + new Vector2Int(dir.x / 2, dir.y / 2);
                Vector2Int neighbor = cell + dir;
                
                if (IsValidPosition(neighbor) && !visited[neighbor.x, neighbor.y] && 
                    IsValidPosition(wall) && !walls.Contains(wall))
                {
                    walls.Add(wall);
                }
            }
        }
        
        private int CountPathNeighbors(Vector2Int pos)
        {
            int count = 0;
            Vector2Int[] directions = {
                new Vector2Int(0, 1), new Vector2Int(1, 0),
                new Vector2Int(0, -1), new Vector2Int(-1, 0)
            };
            
            foreach (var dir in directions)
            {
                Vector2Int neighbor = pos + dir;
                if (IsValidPosition(neighbor) && grid[neighbor.x, neighbor.y] == 1)
                {
                    count++;
                }
            }
            
            return count;
        }
        
        private bool IsValidPosition(Vector2Int pos)
        {
            return pos.x >= 0 && pos.x < settings.gridWidth && 
                   pos.y >= 0 && pos.y < settings.gridHeight;
        }
        
        public int GetTileCount()
        {
            int count = 0;
            for (int x = 0; x < settings.gridWidth; x++)
            {
                for (int y = 0; y < settings.gridHeight; y++)
                {
                    if (grid[x, y] == 1)
                        count++;
                }
            }
            return count;
        }
        
        public float GetFillPercentage()
        {
            return (float)GetTileCount() / (settings.gridWidth * settings.gridHeight);
        }
        
        public void PrintGrid()
        {
            string output = $"Maze Generated Grid ({GetTileCount()}/{settings.gridWidth * settings.gridHeight} tiles, {GetFillPercentage():P1} filled):\n";
            for (int y = settings.gridHeight - 1; y >= 0; y--)
            {
                for (int x = 0; x < settings.gridWidth; x++)
                {
                    output += grid[x, y] == 1 ? "█" : "·";
                }
                output += "\n";
            }
            Debug.Log(output);
        }
        
        // Utility methods
        public static MazeSettings CreateClassicMazeSettings(int width, int height)
        {
            var settings = MazeSettings.Default;
            settings.gridWidth = width;
            settings.gridHeight = height;
            settings.algorithm = MazeAlgorithm.RecursiveBacktracking;
            settings.pathStyle = PathStyle.Corridors;
            settings.createLoops = false;
            settings.addDeadEnds = true;
            return settings;
        }
        
        public static MazeSettings CreateDungeonSettings(int width, int height)
        {
            var settings = MazeSettings.Default;
            settings.gridWidth = width;
            settings.gridHeight = height;
            settings.algorithm = MazeAlgorithm.Prim;
            settings.pathStyle = PathStyle.Mixed;
            settings.roomProbability = 0.4f;
            settings.createLoops = true;
            settings.loopProbability = 0.3f;
            settings.addDeadEnds = false;
            return settings;
        }
    }
}
