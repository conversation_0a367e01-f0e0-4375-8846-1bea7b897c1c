using System.Collections.Generic;
using UnityEngine;

namespace LevelGen.Scripts.Base
{
    public enum BoundaryBehavior
    {
        Remove,     // <PERSON><PERSON>a walker khi chạm biên (mặc định cũ)
        Bounce,     // <PERSON><PERSON><PERSON> hướng khi chạm biên
        Wrap,       // Teleport sang phía đối diện
        Stop        // Dừng walker tại vị trí hiện tại
    }

    public enum StopCondition
    {
        MaxSteps,           // Dừng khi đạt maxSteps
        NoWalkers,          // Dừng khi hết walker
        FillPercentage,     // Dừng khi đạt % fill nhất định
        TileCount,          // Dừng khi đạt số tile nhất định
        Combined            // Kết hợp các điều kiện
    }

    public class RandomWalker
    {
        public struct WalkerSettings
        {
            public int gridWidth;
            public int gridHeight;
            public int maxSteps;
            public float changeDirectionChance;
            public float branchChance;
            public int maxBranches;
            public bool allowDiagonal;
            public Vector2Int startPosition;
            public BoundaryBehavior boundaryBehavior;
            public StopCondition stopCondition;
            public float targetFillPercentage;  // 0.0 - 1.0
            public int targetTileCount;
            public bool avoidRevisiting;        // Tránh đi lại ô đã đi

            public static WalkerSettings Default => new WalkerSettings
            {
                gridWidth = 15,
                gridHeight = 8,
                maxSteps = 200,
                changeDirectionChance = 0.3f,
                branchChance = 0.1f,
                maxBranches = 3,
                allowDiagonal = false,
                startPosition = new Vector2Int(-1, -1), // -1 means auto center
                boundaryBehavior = BoundaryBehavior.Bounce,
                stopCondition = StopCondition.Combined,
                targetFillPercentage = 0.6f,
                targetTileCount = -1, // -1 means ignore
                avoidRevisiting = false
            };
        }

        private WalkerSettings settings;
        private int[,] grid;
        private List<Vector2Int> walkers;
        private List<Vector2Int> directions;
        private System.Random random;

        public RandomWalker(WalkerSettings settings, int? seed = null)
        {
            this.settings = settings;
            this.random = seed.HasValue ? new System.Random(seed.Value) : new System.Random();
            InitializeDirections();
        }

        public RandomWalker(int width, int height, int? seed = null)
        {
            this.settings = WalkerSettings.Default;
            this.settings.gridWidth = width;
            this.settings.gridHeight = height;
            this.random = seed.HasValue ? new System.Random(seed.Value) : new System.Random();
            InitializeDirections();
        }

        private void InitializeDirections()
        {
            directions = new List<Vector2Int>();

            // Cardinal directions
            directions.Add(new Vector2Int(0, 1));  // Up
            directions.Add(new Vector2Int(1, 0));  // Right
            directions.Add(new Vector2Int(0, -1)); // Down
            directions.Add(new Vector2Int(-1, 0)); // Left

            // Diagonal directions (if allowed)
            if (settings.allowDiagonal)
            {
                directions.Add(new Vector2Int(1, 1));   // Up-Right
                directions.Add(new Vector2Int(1, -1));  // Down-Right
                directions.Add(new Vector2Int(-1, -1)); // Down-Left
                directions.Add(new Vector2Int(-1, 1));  // Up-Left
            }
        }

        public int[,] GenerateLevel()
        {
            InitializeGrid();
            InitializeWalkers();

            int steps = 0;
            while (!ShouldStop(steps))
            {
                WalkStep();
                steps++;
            }

            return grid;
        }

        private bool ShouldStop(int steps)
        {
            switch (settings.stopCondition)
            {
                case StopCondition.MaxSteps:
                    return steps >= settings.maxSteps;

                case StopCondition.NoWalkers:
                    return walkers.Count == 0;

                case StopCondition.FillPercentage:
                    float currentFill = (float)GetTileCount() / (settings.gridWidth * settings.gridHeight);
                    return currentFill >= settings.targetFillPercentage;

                case StopCondition.TileCount:
                    return settings.targetTileCount > 0 && GetTileCount() >= settings.targetTileCount;

                case StopCondition.Combined:
                default:
                    // Dừng nếu một trong các điều kiện sau xảy ra:
                    bool maxStepsReached = steps >= settings.maxSteps;
                    bool noWalkersLeft = walkers.Count == 0;
                    bool fillTargetReached = false;
                    bool tileCountReached = false;

                    if (settings.targetFillPercentage > 0)
                    {
                        float currentFill1 = (float)GetTileCount() / (settings.gridWidth * settings.gridHeight);
                        fillTargetReached = currentFill1 >= settings.targetFillPercentage;
                    }

                    if (settings.targetTileCount > 0)
                    {
                        tileCountReached = GetTileCount() >= settings.targetTileCount;
                    }

                    return maxStepsReached || noWalkersLeft || fillTargetReached || tileCountReached;
            }
        }

        private void InitializeGrid()
        {
            grid = new int[settings.gridWidth, settings.gridHeight];

            // Initialize all cells to 0
            for (int x = 0; x < settings.gridWidth; x++)
            {
                for (int y = 0; y < settings.gridHeight; y++)
                {
                    grid[x, y] = 0;
                }
            }
        }

        private void InitializeWalkers()
        {
            walkers = new List<Vector2Int>();

            // Determine start position
            Vector2Int startPos = settings.startPosition;
            if (startPos.x == -1 || startPos.y == -1)
            {
                // Auto center
                startPos = new Vector2Int(settings.gridWidth / 2, settings.gridHeight / 2);
            }

            // Ensure start position is within bounds
            startPos.x = Mathf.Clamp(startPos.x, 0, settings.gridWidth - 1);
            startPos.y = Mathf.Clamp(startPos.y, 0, settings.gridHeight - 1);

            walkers.Add(startPos);
            grid[startPos.x, startPos.y] = 1;
        }

        private void WalkStep()
        {
            List<Vector2Int> newWalkers = new List<Vector2Int>();

            for (int i = walkers.Count - 1; i >= 0; i--)
            {
                Vector2Int currentWalker = walkers[i];

                // Try to create branches
                if (walkers.Count < settings.maxBranches && random.NextDouble() < settings.branchChance)
                {
                    Vector2Int branchDirection = GetRandomDirection();
                    Vector2Int branchPos = HandleBoundary(currentWalker + branchDirection);

                    if (branchPos != Vector2Int.one * -1 && ShouldPlaceTile(branchPos))
                    {
                        newWalkers.Add(branchPos);
                        grid[branchPos.x, branchPos.y] = 1;
                    }
                }

                // Move current walker
                Vector2Int direction = GetRandomDirection();

                // Chance to change direction
                if (random.NextDouble() < settings.changeDirectionChance)
                {
                    direction = GetRandomDirection();
                }

                Vector2Int newPos = HandleBoundary(currentWalker + direction);

                if (newPos == Vector2Int.one * -1)
                {
                    // Walker should be removed
                    walkers.RemoveAt(i);
                }
                else
                {
                    walkers[i] = newPos;
                    if (ShouldPlaceTile(newPos))
                    {
                        grid[newPos.x, newPos.y] = 1;
                    }
                }
            }

            // Add new branch walkers
            walkers.AddRange(newWalkers);
        }

        private Vector2Int HandleBoundary(Vector2Int pos)
        {
            if (IsValidPosition(pos))
                return pos;

            switch (settings.boundaryBehavior)
            {
                case BoundaryBehavior.Remove:
                    return Vector2Int.one * -1; // Signal to remove walker

                case BoundaryBehavior.Bounce:
                    // Clamp to valid bounds
                    return new Vector2Int(
                        Mathf.Clamp(pos.x, 0, settings.gridWidth - 1),
                        Mathf.Clamp(pos.y, 0, settings.gridHeight - 1)
                    );

                case BoundaryBehavior.Wrap:
                    // Wrap around to opposite side
                    int wrappedX = pos.x < 0 ? settings.gridWidth - 1 :
                                  pos.x >= settings.gridWidth ? 0 : pos.x;
                    int wrappedY = pos.y < 0 ? settings.gridHeight - 1 :
                                  pos.y >= settings.gridHeight ? 0 : pos.y;
                    return new Vector2Int(wrappedX, wrappedY);

                case BoundaryBehavior.Stop:
                    // Keep walker at current position (don't move)
                    return pos; // This will be handled by caller

                default:
                    return Vector2Int.one * -1;
            }
        }

        private bool ShouldPlaceTile(Vector2Int pos)
        {
            if (!IsValidPosition(pos))
                return false;

            // If avoiding revisiting, don't place tile if already occupied
            if (settings.avoidRevisiting && grid[pos.x, pos.y] == 1)
                return false;

            return true;
        }

        private Vector2Int GetRandomDirection()
        {
            return directions[random.Next(directions.Count)];
        }

        private bool IsValidPosition(Vector2Int pos)
        {
            return pos.x >= 0 && pos.x < settings.gridWidth &&
                   pos.y >= 0 && pos.y < settings.gridHeight;
        }

        // Utility methods for post-processing
        public int[,] ApplySmoothing(int[,] inputGrid, int iterations = 1)
        {
            int[,] result = (int[,])inputGrid.Clone();

            for (int iter = 0; iter < iterations; iter++)
            {
                int[,] temp = new int[settings.gridWidth, settings.gridHeight];

                for (int x = 0; x < settings.gridWidth; x++)
                {
                    for (int y = 0; y < settings.gridHeight; y++)
                    {
                        int neighbors = CountNeighbors(result, x, y);

                        // Smoothing rules
                        if (result[x, y] == 1)
                        {
                            temp[x, y] = neighbors >= 2 ? 1 : 0;
                        }
                        else
                        {
                            temp[x, y] = neighbors >= 5 ? 1 : 0;
                        }
                    }
                }

                result = temp;
            }

            return result;
        }

        private int CountNeighbors(int[,] grid, int x, int y)
        {
            int count = 0;

            for (int dx = -1; dx <= 1; dx++)
            {
                for (int dy = -1; dy <= 1; dy++)
                {
                    if (dx == 0 && dy == 0) continue;

                    int nx = x + dx;
                    int ny = y + dy;

                    if (nx >= 0 && nx < settings.gridWidth && ny >= 0 && ny < settings.gridHeight)
                    {
                        count += grid[nx, ny];
                    }
                }
            }

            return count;
        }

        public int GetTileCount()
        {
            int count = 0;
            for (int x = 0; x < settings.gridWidth; x++)
            {
                for (int y = 0; y < settings.gridHeight; y++)
                {
                    if (grid[x, y] == 1)
                        count++;
                }
            }
            return count;
        }

        public void PrintGrid()
        {
            string output = $"Generated Grid ({GetTileCount()}/{settings.gridWidth * settings.gridHeight} tiles, {GetFillPercentage():P1} filled):\n";
            for (int y = settings.gridHeight - 1; y >= 0; y--)
            {
                for (int x = 0; x < settings.gridWidth; x++)
                {
                    output += grid[x, y] == 1 ? "█" : "·";
                }
                output += "\n";
            }
            Debug.Log(output);
        }

        public float GetFillPercentage()
        {
            return (float)GetTileCount() / (settings.gridWidth * settings.gridHeight);
        }

        // Utility method để tạo settings nhanh cho các use case phổ biến
        public static WalkerSettings CreateSettings(int width, int height, float fillTarget = 0.6f, BoundaryBehavior boundary = BoundaryBehavior.Bounce)
        {
            var settings = WalkerSettings.Default;
            settings.gridWidth = width;
            settings.gridHeight = height;
            settings.targetFillPercentage = fillTarget;
            settings.boundaryBehavior = boundary;
            settings.maxSteps = width * height; // Đủ steps để có thể fill hết grid
            return settings;
        }
    }
}