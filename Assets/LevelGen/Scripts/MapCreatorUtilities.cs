using System;
using System.Collections.Generic;
using b100SDK.Scripts.Utilities.Extensions;
using GenerateData.Scripts;
using UnityEngine;
using Random = UnityEngine.Random;

namespace LevelGen.Scripts
{
    public static class MapCreatorUtilities
    {
        public enum SymmetryType
        {
            Horizontal = 1 << 0,
            Vertical = 1 << 1,
            Both = Horizontal | Vertical,
        }
        
        
        public static int[,] GetSymmetryMatrix(this int[,] originalMatrix, SymmetryType type = SymmetryType.Both, bool? isToRight = null, bool? isToBottom = null)
        {
            if (type == 0)
            {
                return originalMatrix;
            }
            else if (type == SymmetryType.Both)
            {
                var useToRight = isToRight ?? Random.value > .5f;
                List<int> symmetricColumn = new List<int>();
                
                var temp = GetHorizontalSymmetricMatrix(originalMatrix, symmetricColumn, useToRight);
                
                var useToBottom = isToBottom ?? Random.value > .5f;
                List<int> symmetricRow = new List<int>();
                
                var result = GetVerticalSymmetricMatrix(temp, symmetricRow, useToBottom);
                
                return result;
            }
            else if ((type & SymmetryType.Horizontal) != 0)
            {
                //Horizontal
                var originalCol = originalMatrix.GetLength(1);
                var useToRight = isToRight ?? Random.value > .5f;

                List<int> symmetricColumn = new List<int>();

                if (useToRight)
                {
                    symmetricColumn.Clear();

                    var columnCount = originalCol % 2;
                    
                    for (int i = 0; i < columnCount; i++)
                    {
                        symmetricColumn.Add(originalCol - 1 - i);
                    }
                }
                else
                {
                    symmetricColumn.Clear();

                    var columnCount = originalCol % 2;

                    for (int i = 0; i < columnCount; i++)
                    {
                        symmetricColumn.Add(i);
                    }
                }
                
                symmetricColumn.Sort();

                var temp = GetHorizontalSymmetricMatrix(originalMatrix, symmetricColumn, useToRight);
                
                return temp;
            }
            else
            {
                //Vertical
                var originalRow = originalMatrix.GetLength(0);
                var useToBottom = isToBottom ?? Random.value > .5f;

                List<int> symmetricRow = new List<int>();

                if (useToBottom)
                {
                    symmetricRow.Clear();

                    var rowCount = originalRow % 2;

                    for (int i = 0; i < rowCount; i++)
                    {
                        symmetricRow.Add(originalRow - 1 - i);
                    }
                }
                else
                {
                    symmetricRow.Clear();

                    var rowCount = originalRow % 2;

                    for (int i = 0; i < rowCount; i++)
                    {
                        symmetricRow.Add(i);
                    }
                }
                
                symmetricRow.Sort();
                
                var result = GetVerticalSymmetricMatrix(originalMatrix, symmetricRow, useToBottom);
                
                return result;
            }
        }
        public static int[,] GetHorizontalSymmetricMatrix(int[,] originalMatrix, List<int> symmetryColumns, bool isToRight)
        {
            int rows = originalMatrix.GetLength(0);
            int cols = originalMatrix.GetLength(1);
            
            if (symmetryColumns.Count == cols)
            {
                return originalMatrix;
            } 

            if (!symmetryColumns.IsConsecutiveAscending())
            {
                throw new ArgumentException("Symmetry columns must be consecutive ascending.");
                return null;
            }

            var symmetricColumnCount = symmetryColumns.Count;
            
            // Calculate number col
            int newCols = symmetryColumns.Count;
            
            if (isToRight)
            {
                if (symmetryColumns.Count > 0)
                {
                    newCols += symmetryColumns[0] * 2;
                }
                else
                {
                    newCols = cols * 2;
                }
            }
            else
            {
                if (symmetricColumnCount > 0)
                {
                    newCols += (cols - symmetryColumns.GetLast() - 1) * 2;
                }
                else
                {
                    newCols = cols * 2;
                }
            }
            
            int[,] result = new int[rows, newCols];
            
            if (isToRight)
            {
                if (symmetricColumnCount > 0)
                {
                    // Copy symmetric columns
                    for (int i = 0; i < rows; i++)
                    {
                        for (int j = 0; j <= symmetryColumns.GetLast(); j++)
                        {
                            result[i, j] = originalMatrix[i, j];
                        }
                    }

                    for (int i = 0; i < rows; i++)
                    {
                        for (int j = 0; j < symmetryColumns[0]; j++)
                        {
                            result[i, newCols - 1 - j] = originalMatrix[i, j];
                        }
                    }
                }
                else
                {
                    // Copy symmetric columns
                    for (int i = 0; i < rows; i++)
                    {
                        for (int j = 0; j < cols; j++)
                        {
                            result[i, j] = originalMatrix[i, j];
                            result[i, newCols - 1 - j] = originalMatrix[i, j];
                        }
                    }
                }
            }
            else
            {
                var offset = newCols - cols;
                
                if (symmetricColumnCount > 0)
                {
                    // Copy
                    for (int i = 0; i < rows; i++)
                    {
                        for (int j = cols - 1; j >= symmetryColumns[0]; j--)
                        {
                            result[i, j + offset] = originalMatrix[i, j];
                        }
                    }
                
                    // Symmetry
                    for (int i = 0; i < rows; i++)
                    {
                        for (int j = 0; j < offset; j++)
                        {
                            result[i, j] = originalMatrix[i, cols - 1 - j];
                        }
                    }
                }
                else
                {
                    // Copy
                    for (int i = 0; i < rows; i++)
                    {
                        for (int j = 0; j < cols; j++)
                        {
                            result[i, j + offset] = originalMatrix[i, j];
                            result[i, j] = originalMatrix[i, cols - 1 - j];
                        }
                    }
                }

            }

            return result;
        }


        public static int[,] GetVerticalSymmetricMatrix(int[,] originalMatrix, List<int> symmetryRows, bool isToBottom)
        {
            int rows = originalMatrix.GetLength(0);
            int cols = originalMatrix.GetLength(1);
            
            if (symmetryRows.Count == rows)
            {
                return originalMatrix;
            } 

            if (!symmetryRows.IsConsecutiveAscending())
            {
                throw new ArgumentException("Symmetry rows must be consecutive ascending.");
                return null;
            }
            
            var symmetricRowCount = symmetryRows.Count;
            
            // Calculate number rows
            int newRows = symmetryRows.Count;
            
            if (isToBottom)
            {
                if (symmetricRowCount > 0)
                {
                    newRows += symmetryRows[0] * 2;
                }
                else
                {
                    newRows = rows * 2;
                }
            }
            else
            {
                if (symmetricRowCount > 0)
                {
                    newRows += (rows - symmetryRows.GetLast() - 1) * 2;
                }
                else
                {
                    newRows = rows * 2;
                }
            }
            
            int[,] result = new int[newRows, cols];

            if (isToBottom)
            {
                if (symmetricRowCount > 0)
                {
                    // Copy symmetric rows
                    for (int j = 0; j < cols; j++)
                    {
                        for (int i = 0; i <= symmetryRows.GetLast(); i++)
                        {
                            result[i, j] = originalMatrix[i, j];
                        }
                    }

                    for (int j = 0; j < cols; j++)
                    {
                        for (int i = 0; i < symmetryRows[0]; i++)
                        {
                            result[newRows - 1 - i, j] = originalMatrix[i, j];
                        }
                    }
                }
                else
                {
                    // Copy symmetric rows
                    for (int j = 0; j < cols; j++)
                    {
                        for (int i = 0; i < rows; i++)
                        {
                            result[i, j] = originalMatrix[i, j];
                            result[newRows - 1 - i, j] = originalMatrix[i, j];
                        }
                    }
                }
            }
            else
            {
                var offset = newRows - rows;

                if (symmetricRowCount > 0)
                {
                    // Copy
                    for (int j = 0; j < cols; j++)
                    {
                        for (int i = rows - 1; i >= symmetryRows[0]; i--)
                        {
                            result[i + offset, j] = originalMatrix[i, j];
                        }
                    }
                    
                    // Symmetry
                    for (int j = 0; j < cols; j++)
                    {
                        for (int i = 0; i < offset; i++)
                        {
                            result[i, j] = originalMatrix[rows - 1 - i, j];
                        }
                    }
                }
                else
                {
                    // Copy
                    for (int j = 0; j < cols; j++)
                    {
                        for (int i = 0; i < rows; i++)
                        {
                            result[i + offset, j] = originalMatrix[i, j];
                            result[i, j] = originalMatrix[rows - 1 - i, j];
                        }
                    }
                }
                

            }

            return result;
        }
        
        
        public static bool IsConsecutiveAscending(this List<int> numbers)
        {
            if (numbers == null)
                return false;

            if (numbers.Count < 2)
                return true;

            for (int i = 1; i < numbers.Count; i++)
            {
                if (numbers[i] != numbers[i - 1] + 1)
                    return false;
            }

            return true;
        }
        
        public static int[,,] FindSquaresSymmetric3D(this int[,,] matrix, out int count)
        {
            count = 0;
            int depth = matrix.GetLength(0);
            int rows = matrix.GetLength(1);
            int cols = matrix.GetLength(2);

            int[,,] result = new int[depth, rows, cols]; // Kết quả đánh dấu ID vùng 2x2
            bool[,,] visited = new bool[depth, rows, cols]; // Đánh dấu đã kiểm tra
            int regionId = 1; // ID vùng, bắt đầu từ 1

            for (int d = 0; d < depth; d++) // Duyệt từng layer (depth)
            {
                for (int i = 0; i < rows - 1; i++)
                {
                    for (int j = 0; j < cols / 2; j++)
                    {
                        int mirrorJ = cols - 2 - j; // Vị trí đối xứng

                        // Kiểm tra có thể tạo hình vuông 2x2 đối xứng
                        bool isValidSquare =
                            matrix[d, i, j] == 1 && 
                            //matrix[d, i, j + 1] == 1 &&
                            //matrix[d, i + 1, j] == 1 && 
                            //matrix[d, i + 1, j + 1] == 1 &&
                            //matrix[d, i, mirrorJ] == 1 && 
                            matrix[d, i, mirrorJ + 1] == 1 &&
                            //matrix[d, i + 1, mirrorJ] == 1 && 
                            //matrix[d, i + 1, mirrorJ + 1] == 1 &&
                            !visited[d, i, j] && !visited[d, i, j + 1] &&
                            !visited[d, i + 1, j] && !visited[d, i + 1, j + 1] &&
                            !visited[d, i, mirrorJ] && !visited[d, i, mirrorJ + 1] &&
                            !visited[d, i + 1, mirrorJ] && !visited[d, i + 1, mirrorJ + 1];

                        // Nếu đang ở layer trên, yêu cầu layer dưới cũng phải có vùng 2x2
                        if (d > 0)
                        {
                            isValidSquare &= result[d - 1, i, j] > 0 &&
                                             result[d - 1, i, j + 1] > 0 &&
                                             result[d - 1, i + 1, j] > 0 &&
                                             result[d - 1, i + 1, j + 1] > 0 &&
                                             result[d - 1, i, mirrorJ] > 0 &&
                                             result[d - 1, i, mirrorJ + 1] > 0 &&
                                             result[d - 1, i + 1, mirrorJ] > 0 &&
                                             result[d - 1, i + 1, mirrorJ + 1] > 0;
                        }

                        if (isValidSquare)
                        {
                            // Đánh dấu vùng với ID mới
                            result[d, i, j] = result[d, i, j + 1] = 1;
                            result[d, i + 1, j] = result[d, i + 1, j + 1] = 1;

                            result[d, i, mirrorJ] = result[d, i, mirrorJ + 1] = 1;
                            result[d, i + 1, mirrorJ] = result[d, i + 1, mirrorJ + 1] = 1;

                            // Đánh dấu đã sử dụng
                            visited[d, i, j] = visited[d, i, j + 1] = true;
                            visited[d, i + 1, j] = visited[d, i + 1, j + 1] = true;
                            visited[d, i, mirrorJ] = visited[d, i, mirrorJ + 1] = true;
                            visited[d, i + 1, mirrorJ] = visited[d, i + 1, mirrorJ + 1] = true;

                            regionId++; // Tăng ID vùng tiếp theo

                            if (Mathf.Abs(j - mirrorJ) == 0)
                            {
                                count++;
                            }
                            else
                            {
                                count += 2;
                            }
                        }
                    }
                }
            }

            return result;
        }
    }
}