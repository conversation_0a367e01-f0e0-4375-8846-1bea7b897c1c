using Sirenix.OdinInspector;
using UnityEngine;

namespace LevelGen.Scripts.Scale
{
    [System.Serializable]
    public class ScaleSettings
    {
        [Header("Scale Factors")]
        public float scaleX = 1.0f;

        public float scaleY = 1.0f;

        [Header("Output Size")]
        public bool maintainOriginalSize = true;

        [Header("Pivot Point (Normalized 0-1)")]
        [Range(0f, 1f)]
        public float pivotX = 0.5f;

        [Range(0f, 1f)]
        public float pivotY = 0.5f;

        [Header("Interpolation Method")]
        public InterpolationMethod interpolation = InterpolationMethod.NearestNeighbor;
        
        [Header("Threshold")]
        [ShowIf(nameof(interpolation),InterpolationMethod.Bilinear)]
        public float threshold = 0.5f;
    }

    public enum InterpolationMethod
    {
        NearestNeighbor,
        Bilinear
    }

    public class MatrixScale
    {
        /// <summary>
        /// Scale a 2D binary matrix with pivot point and per-axis scaling
        /// </summary>
        /// <param name="matrix">Input matrix (0s and 1s only)</param>
        /// <param name="settings">Scale settings including factors, pivot, and interpolation</param>
        /// <returns>Scaled matrix</returns>
        public int[,] ScaleMatrix(int[,] matrix, ScaleSettings settings)
        {
            if (matrix == null)
            {
                Debug.LogError("Input matrix is null");
                return null;
            }

            int originalWidth = matrix.GetLength(1);
            int originalHeight = matrix.GetLength(0);

            // Calculate output dimensions based on maintainOriginalSize setting
            int outputWidth, outputHeight;

            if (settings.maintainOriginalSize)
            {
                // Keep original size regardless of scale factor
                outputWidth = originalWidth;
                outputHeight = originalHeight;
            }
            else
            {
                // Calculate new dimensions based on scale factors
                outputWidth = Mathf.RoundToInt(originalWidth * settings.scaleX);
                outputHeight = Mathf.RoundToInt(originalHeight * settings.scaleY);

                // Ensure minimum size
                outputWidth = Mathf.Max(1, outputWidth);
                outputHeight = Mathf.Max(1, outputHeight);
            }

            int[,] scaledMatrix = new int[outputHeight, outputWidth];

            // Scale each pixel
            for (int y = 0; y < outputHeight; y++)
            {
                for (int x = 0; x < outputWidth; x++)
                {
                    // Convert output coordinates back to original coordinates
                    Vector2 originalCoord = GetOriginalCoordinate(
                        new Vector2(x, y),
                        new Vector2(outputWidth, outputHeight),
                        new Vector2(originalWidth, originalHeight),
                        settings
                    );

                    // Sample the original matrix
                    int value = SampleMatrix(matrix, originalCoord, settings.interpolation, settings.threshold);
                    scaledMatrix[y, x] = value;
                }
            }

            return scaledMatrix;
        }

        /// <summary>
        /// Get the corresponding coordinate in the original matrix
        /// </summary>
        private Vector2 GetOriginalCoordinate(Vector2 outputCoord, Vector2 outputSize, Vector2 originalSize,
            ScaleSettings settings)
        {
            // Convert output coordinates to normalized space (0-1)
            Vector2 normalizedOutput = new Vector2(
                outputCoord.x / (outputSize.x - 1),
                outputCoord.y / (outputSize.y - 1)
            );

            // Get pivot point in normalized space
            Vector2 pivotNormalized = new Vector2(settings.pivotX, settings.pivotY);

            // Calculate offset from pivot
            Vector2 offsetFromPivot = normalizedOutput - pivotNormalized;

            // Apply inverse scaling to the offset
            Vector2 originalOffset = new Vector2(
                offsetFromPivot.x / settings.scaleX,
                offsetFromPivot.y / settings.scaleY
            );

            // Get original normalized coordinate
            Vector2 originalNormalized = pivotNormalized + originalOffset;

            // Convert back to matrix coordinates
            return new Vector2(
                originalNormalized.x * (originalSize.x - 1),
                originalNormalized.y * (originalSize.y - 1)
            );
        }

        /// <summary>
        /// Sample value from matrix with interpolation
        /// </summary>
        private int SampleMatrix(int[,] matrix, Vector2 coord, InterpolationMethod method, float threshold)
        {
            int width = matrix.GetLength(1);
            int height = matrix.GetLength(0);

            // Check if coordinate is outside matrix bounds
            if (coord.x < 0 || coord.x >= width || coord.y < 0 || coord.y >= height)
            {
                return 0; // Return 0 for out-of-bounds coordinates
            }

            switch (method)
            {
                case InterpolationMethod.NearestNeighbor:
                    return SampleNearestNeighbor(matrix, coord);

                case InterpolationMethod.Bilinear:
                    return SampleBilinear(matrix, coord, threshold);

                default:
                    return SampleNearestNeighbor(matrix, coord);
            }
        }

        /// <summary>
        /// Nearest neighbor sampling
        /// </summary>
        private int SampleNearestNeighbor(int[,] matrix, Vector2 coord)
        {
            int width = matrix.GetLength(1);
            int height = matrix.GetLength(0);

            int x = Mathf.RoundToInt(coord.x);
            int y = Mathf.RoundToInt(coord.y);

            // Check bounds
            if (x < 0 || x >= width || y < 0 || y >= height)
                return 0;

            return matrix[y, x];
        }

        /// <summary>
        /// Bilinear interpolation sampling (for smoother results)
        /// </summary>
        private int SampleBilinear(int[,] matrix, Vector2 coord, float threshold)
        {
            int width = matrix.GetLength(1);
            int height = matrix.GetLength(0);

            int x1 = Mathf.FloorToInt(coord.x);
            int y1 = Mathf.FloorToInt(coord.y);
            int x2 = x1 + 1;
            int y2 = y1 + 1;

            float fx = coord.x - x1;
            float fy = coord.y - y1;

            // Get four corner values with bounds checking
            float v11 = (x1 >= 0 && x1 < width && y1 >= 0 && y1 < height) ? matrix[y1, x1] : 0;
            float v12 = (x1 >= 0 && x1 < width && y2 >= 0 && y2 < height) ? matrix[y2, x1] : 0;
            float v21 = (x2 >= 0 && x2 < width && y1 >= 0 && y1 < height) ? matrix[y1, x2] : 0;
            float v22 = (x2 >= 0 && x2 < width && y2 >= 0 && y2 < height) ? matrix[y2, x2] : 0;

            // Bilinear interpolation
            float value = v11 * (1 - fx) * (1 - fy) +
                          v21 * fx * (1 - fy) +
                          v12 * (1 - fx) * fy +
                          v22 * fx * fy;

            // Threshold for binary result (0 or 1)
            return value >= threshold ? 1 : 0;
        }

        /// <summary>
        /// Public method to scale matrix with custom parameters
        /// </summary>
        public int[,] ScaleMatrix(int[,] matrix, float scaleX, float scaleY, float pivotX, float pivotY,
            bool maintainOriginalSize = true, InterpolationMethod interpolation = InterpolationMethod.NearestNeighbor)
        {
            ScaleSettings customSettings = new ScaleSettings
            {
                scaleX = scaleX,
                scaleY = scaleY,
                pivotX = Mathf.Clamp01(pivotX),
                pivotY = Mathf.Clamp01(pivotY),
                maintainOriginalSize = maintainOriginalSize,
                interpolation = interpolation
            };

            return ScaleMatrix(matrix, customSettings);
        }
    }
}