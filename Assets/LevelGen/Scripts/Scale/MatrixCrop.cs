using UnityEngine;

namespace LevelGen.Scripts.Scale
{
    [System.Serializable]
    public class CropSettings
    {
        [Header("Crop Factors (0-1 for crop in, >1 for zoom out)")]
        [Range(0.1f, 2.0f)]
        public float scaleX = 1.0f;

        [Range(0.1f, 2.0f)]
        public float scaleY = 1.0f;

        [Header("Crop Center (Normalized 0-1)")]
        [Range(0f, 1f)]
        public float anchorX = 0.5f;

        [Range(0f, 1f)]
        public float anchorY = 0.5f;

        [Header("Background Value (for empty areas)")]
        [Range(0, 1)]
        public int backgroundValue = 0;
        
        [Header("Matrix Size Options")]
        public bool maintainOriginalSize = true;
    }

    public class MatrixCrop
    {
        /// <summary>
        /// Crop content while maintaining matrix size
        /// Scale < 1.0 = Crop content (chỉ giữ lại một phần, phần còn lại = background)
        /// Scale > 1.0 = Scale down content (thu nhỏ nội dung, thêm padding)
        /// Scale = 1.0 = Giữ nguyên
        /// </summary>
        public int[,] CropMatrix(int[,] matrix, CropSettings settings)
        {
            if (matrix == null)
            {
                Debug.LogError("Input matrix is null");
                return null;
            }

            int originalWidth = matrix.GetLength(1);
            int originalHeight = matrix.GetLength(0);
            
            int resultWidth, resultHeight;
            
            if (settings.maintainOriginalSize)
            {
                // Keep original matrix size
                resultWidth = originalWidth;
                resultHeight = originalHeight;
            }
            else
            {
                // Allow matrix size change
                resultWidth = Mathf.RoundToInt(originalWidth * settings.scaleX);
                resultHeight = Mathf.RoundToInt(originalHeight * settings.scaleY);
                resultWidth = Mathf.Max(1, resultWidth);
                resultHeight = Mathf.Max(1, resultHeight);
            }

            int[,] resultMatrix = new int[resultHeight, resultWidth];

            // Fill with background value first
            for (int y = 0; y < resultHeight; y++)
            {
                for (int x = 0; x < resultWidth; x++)
                {
                    resultMatrix[y, x] = settings.backgroundValue;
                }
            }

            if (settings.maintainOriginalSize)
            {
                // Content scaling with fixed matrix size
                CropContentInPlace(matrix, resultMatrix, settings);
            }
            else
            {
                // Traditional crop/pad with size change
                CropWithSizeChange(matrix, resultMatrix, settings);
            }

            return resultMatrix;
        }

        /// <summary>
        /// Crop content while keeping matrix size fixed
        /// Scale < 1.0 = Keep only elements within crop region, others become background
        /// Scale > 1.0 = Scale down entire content and add padding
        /// Anchor determines which part to keep when cropping or where to place scaled content
        /// </summary>
        private void CropContentInPlace(int[,] source, int[,] destination, CropSettings settings)
        {
            int originalWidth = source.GetLength(1);
            int originalHeight = source.GetLength(0);
            int resultWidth = destination.GetLength(1);
            int resultHeight = destination.GetLength(0);

            if (settings.scaleX < 1.0f || settings.scaleY < 1.0f)
            {
                // CROP MODE: Keep only elements within crop region
                CropContent(source, destination, settings);
            }
            else
            {
                // SCALE DOWN MODE: Scale down entire content with padding
                ScaleDownContent(source, destination, settings);
            }
        }

        /// <summary>
        /// Crop mode: Keep only elements that fall within the crop region, set others to background
        /// </summary>
        private void CropContent(int[,] source, int[,] destination, CropSettings settings)
        {
            int originalWidth = source.GetLength(1);
            int originalHeight = source.GetLength(0);
            int resultWidth = destination.GetLength(1);
            int resultHeight = destination.GetLength(0);

            // Calculate the size of the region we want to keep
            int cropWidth = Mathf.RoundToInt(originalWidth * settings.scaleX);
            int cropHeight = Mathf.RoundToInt(originalHeight * settings.scaleY);

            // Ensure minimum size
            cropWidth = Mathf.Max(1, cropWidth);
            cropHeight = Mathf.Max(1, cropHeight);

            // Calculate the bounds of the crop region based on anchor
            int cropStartX = Mathf.RoundToInt(settings.anchorX * (originalWidth - cropWidth));
            int cropStartY = Mathf.RoundToInt(settings.anchorY * (originalHeight - cropHeight));
            int cropEndX = cropStartX + cropWidth;
            int cropEndY = cropStartY + cropHeight;

            // Clamp to valid bounds
            cropStartX = Mathf.Clamp(cropStartX, 0, originalWidth);
            cropStartY = Mathf.Clamp(cropStartY, 0, originalHeight);
            cropEndX = Mathf.Clamp(cropEndX, 0, originalWidth);
            cropEndY = Mathf.Clamp(cropEndY, 0, originalHeight);

            // Copy elements: keep original elements within crop region, background for others
            for (int destY = 0; destY < resultHeight; destY++)
            {
                for (int destX = 0; destX < resultWidth; destX++)
                {
                    // Check if this position is within the crop region
                    if (destX >= cropStartX && destX < cropEndX &&
                        destY >= cropStartY && destY < cropEndY)
                    {
                        // Keep original element
                        destination[destY, destX] = source[destY, destX];
                    }
                    // else: keep background value (already set)
                }
            }
        }

        /// <summary>
        /// Scale down mode: Show entire content scaled down with padding
        /// </summary>
        private void ScaleDownContent(int[,] source, int[,] destination, CropSettings settings)
        {
            int originalWidth = source.GetLength(1);
            int originalHeight = source.GetLength(0);
            int resultWidth = destination.GetLength(1);
            int resultHeight = destination.GetLength(0);

            // Calculate the size of the scaled content
            int scaledWidth = Mathf.RoundToInt(originalWidth / settings.scaleX);
            int scaledHeight = Mathf.RoundToInt(originalHeight / settings.scaleY);

            // Calculate position to center the scaled content based on anchor
            int offsetX = Mathf.RoundToInt(settings.anchorX * (resultWidth - scaledWidth));
            int offsetY = Mathf.RoundToInt(settings.anchorY * (resultHeight - scaledHeight));

            // Copy and scale the content
            for (int destY = 0; destY < resultHeight; destY++)
            {
                for (int destX = 0; destX < resultWidth; destX++)
                {
                    // Check if we're within the scaled content area
                    if (destX >= offsetX && destX < offsetX + scaledWidth &&
                        destY >= offsetY && destY < offsetY + scaledHeight)
                    {
                        // Map to source coordinates
                        float sourceX = ((float)(destX - offsetX) / (scaledWidth - 1)) * (originalWidth - 1);
                        float sourceY = ((float)(destY - offsetY) / (scaledHeight - 1)) * (originalHeight - 1);

                        int value = SampleMatrix(source, sourceX, sourceY);
                        destination[destY, destX] = value;
                    }
                    // else: keep background value (already set)
                }
            }
        }

        /// <summary>
        /// Traditional crop/pad with matrix size change
        /// </summary>
        private void CropWithSizeChange(int[,] source, int[,] destination, CropSettings settings)
        {
            int originalWidth = source.GetLength(1);
            int originalHeight = source.GetLength(0);
            int resultWidth = destination.GetLength(1);
            int resultHeight = destination.GetLength(0);

            // Calculate anchor position in original matrix
            Vector2Int anchorPos = new Vector2Int(
                Mathf.RoundToInt(settings.anchorX * (originalWidth - 1)),
                Mathf.RoundToInt(settings.anchorY * (originalHeight - 1))
            );

            // Calculate anchor position in result matrix
            Vector2Int resultAnchorPos = new Vector2Int(
                Mathf.RoundToInt(settings.anchorX * (resultWidth - 1)),
                Mathf.RoundToInt(settings.anchorY * (resultHeight - 1))
            );

            // Calculate offset from anchor to top-left corner of the region
            Vector2Int originalOffset = anchorPos - resultAnchorPos;

            // Fill the result matrix
            for (int y = 0; y < resultHeight; y++)
            {
                for (int x = 0; x < resultWidth; x++)
                {
                    // Calculate corresponding position in original matrix
                    int originalX = x + originalOffset.x;
                    int originalY = y + originalOffset.y;

                    // Check if the position is within original matrix bounds
                    if (originalX >= 0 && originalX < originalWidth &&
                        originalY >= 0 && originalY < originalHeight)
                    {
                        // Copy value from original matrix
                        destination[y, x] = source[originalY, originalX];
                    }
                    // else: keep background value (already set)
                }
            }
        }

        /// <summary>
        /// Sample matrix with nearest neighbor interpolation
        /// </summary>
        private int SampleMatrix(int[,] matrix, float x, float y)
        {
            int width = matrix.GetLength(1);
            int height = matrix.GetLength(0);

            int sampleX = Mathf.RoundToInt(x);
            int sampleY = Mathf.RoundToInt(y);

            // Check bounds strictly
            if (sampleX < 0 || sampleX >= width || sampleY < 0 || sampleY >= height)
                return 0; // Return background value for out-of-bounds

            return matrix[sampleY, sampleX];
        }

        /// <summary>
        /// Public method with direct parameters (maintains original size by default)
        /// </summary>
        public int[,] CropMatrix(int[,] matrix, float scaleX, float scaleY, float anchorX = 0.5f,
            float anchorY = 0.5f, int backgroundValue = 0, bool maintainOriginalSize = true)
        {
            CropSettings customSettings = new CropSettings
            {
                scaleX = scaleX,
                scaleY = scaleY,
                anchorX = Mathf.Clamp01(anchorX),
                anchorY = Mathf.Clamp01(anchorY),
                backgroundValue = backgroundValue,
                maintainOriginalSize = maintainOriginalSize
            };

            return CropMatrix(matrix, customSettings);
        }

        /// <summary>
        /// Crop content to specific scale while maintaining matrix size
        /// </summary>
        public int[,] CropContentToScale(int[,] matrix, float scale, float anchorX = 0.5f, float anchorY = 0.5f, int backgroundValue = 0)
        {
            return CropMatrix(matrix, scale, scale, anchorX, anchorY, backgroundValue, true);
        }

        /// <summary>
        /// Crop matrix to specific size around anchor point (changes matrix size)
        /// </summary>
        public int[,] CropToSize(int[,] matrix, int targetWidth, int targetHeight, float anchorX = 0.5f,
            float anchorY = 0.5f, int backgroundValue = 0)
        {
            if (matrix == null) return null;

            int originalWidth = matrix.GetLength(1);
            int originalHeight = matrix.GetLength(0);

            // Calculate scale factors
            float scaleX = (float)targetWidth / originalWidth;
            float scaleY = (float)targetHeight / originalHeight;

            return CropMatrix(matrix, scaleX, scaleY, anchorX, anchorY, backgroundValue, false);
        }

        /// <summary>
        /// Extract a region from matrix
        /// </summary>
        public int[,] ExtractRegion(int[,] matrix, int startX, int startY, int width, int height, int backgroundValue = 0)
        {
            if (matrix == null) return null;

            int originalWidth = matrix.GetLength(1);
            int originalHeight = matrix.GetLength(0);

            int[,] result = new int[height, width];

            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    int sourceX = startX + x;
                    int sourceY = startY + y;

                    if (sourceX >= 0 && sourceX < originalWidth &&
                        sourceY >= 0 && sourceY < originalHeight)
                    {
                        result[y, x] = matrix[sourceY, sourceX];
                    }
                    else
                    {
                        result[y, x] = backgroundValue;
                    }
                }
            }

            return result;
        }
    }
}