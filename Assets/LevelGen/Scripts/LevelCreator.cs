using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using b100SDK.Scripts.UI.Panel;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Extensions;
using GenerateData.Scripts;
using LibNoise.Primitive;
using MahjongGame.Scripts.Gameplay.Data;
using Sirenix.OdinInspector;
using UnityEngine;
using Random = UnityEngine.Random;

namespace LevelGen.Scripts
{
    public class LevelCreator
    {
        public Setting setting;
        

        int GetRandomTypeCount(int totalTileCount)
        {
            var min = Mathf.CeilToInt((float)totalTileCount / setting.maxItemPerType);
            var max = Mathf.FloorToInt((float)totalTileCount / setting.minItemPerType);

            return Random.Range(min, Mathf.Min(max, setting.maxType));
        }


        [Button]
        async Task<LevelDataRaw> GetData(int totalTileCount, int? layerCount, int? typeCount, bool hasFlower, bool hasSeason, CancellationTokenSource cancellationTokenSource)
        {
            var attempt = 0; 
            while (attempt < setting.maxAttemptCreateData)
            {
                if (cancellationTokenSource.IsCancellationRequested)
                {
                    //BhDebug.Log("Cancel");
                    return null;
                }
                
                var thresholdMap = new List<(float, float)>();

                if (layerCount.HasValue)
                {
                    setting.offset = Random.Range(0f, (layerCount.Value - 1) * setting.maxValue - 1.2f);
                    //BhDebug.Log("Offset: " + setting.offset);
                    
                    thresholdMap = setting.PickRandomThresholds(layerCount.Value);
                }
                else
                {
                    setting.offset = Random.Range(setting.minValue, setting.maxValueRandom);
                    
                    thresholdMap = setting.PickRandomThresholds();
                }

                var correctLayerCount = thresholdMap.Count;

                /*
                if (isAutoLayer)
                {
                    thresholdMap = setting.PickRandomThresholds();
                    layerCount = thresholdMap.Count;
                }
                else
                {
                    thresholdMap = setting.PickRandomThresholds(layerCount);
                }
                */

                if (thresholdMap.Count == 0)
                {
                    attempt++;
                    await Task.Delay(setting.delay);
                    continue;
                }
                
                thresholdMap.Sort((a, b) => a.Item1.CompareTo(b.Item1));

                var maxRate = thresholdMap.Max(x => x.Item2);

                var itemInLayer0 = (int)(totalTileCount * maxRate);

                var rowColList = setting.FindFactorsWithConstraints(itemInLayer0);
                
                if (rowColList == null || rowColList.Count == 0)
                {
                    attempt++;
                    await Task.Delay(setting.delay);
                    continue;
                }

                var rowCol = rowColList.GetRandom();

                /*var rows = rowCol.Item1 >= rowCol.Item2 ? rowCol.Item1 : rowCol.Item2;
                var columns = rowCol.Item2 <= rowCol.Item1 ? rowCol.Item2 : rowCol.Item1;*/
                
                var rows = rowCol.row;
                var columns = rowCol.col;
            
                BhDebug.Log($"Generate: {correctLayerCount}x{rows}x{columns}");
            
                //Create
                var result = Create(correctLayerCount, rows, columns, thresholdMap.Select(x => x.Item1).ToList(), totalTileCount,
                    setting.maxAttemptCreateData, setting.offsetTileCount);

                if (result == null)
                {
                    //BhDebug.Log("Create failed, create again");
                    attempt++;
                    await Task.Delay(setting.delay);
                    continue;
                }

                if (layerCount.HasValue && !CheckLayer(result, layerCount.Value))
                {
                    //BhDebug.Log("Layer count not match, create again");
                    attempt++;
                    await Task.Delay(setting.delay);
                    continue;
                }

                var tmp = result.FindSquaresSymmetric3D(out int correctCount);
                
                //BhDebug.Log("Correct count: " + correctCount);

                totalTileCount = correctCount;
                
                var correctTypeCount = typeCount ?? GetRandomTypeCount(totalTileCount);

                var typeList = GetTypeList(totalTileCount, correctTypeCount);

                if (typeList == null || typeList.Count == 0)
                {
                    attempt++;
                    await Task.Delay(setting.delay);
                    continue;
                }
                
                //Evm.OnUpdateSize.Dispatch($"{correctLayerCount}x{rows}x{columns} - {correctTypeCount}");
                
                /*BhDebug.Log("Update board info: ");
                BhDebug.Log($"{correctLayerCount}x{rows}x{columns} - {correctTypeCount}");*/
                
                var levelDataRaw = result.CreateLevelDataRaw(typeList, "test", hasFlower, hasSeason);

                return levelDataRaw;
            }


            return null;


        }


        bool CheckLayer(int[,,] board, int layerCount)
        {
            if (board.GetLength(0) != layerCount)
                return false;

            var depth = board.GetLength(0);
            var row = board.GetLength(1);
            var col = board.GetLength(2);

            for (int d = 0; d < depth; d++)
            {
                var check = false;
                
                for (int r = 0; r < row; r++)
                {
                    for (int c = 0; c < col; c++)
                    {
                        if (board[d, r, c] != 0)
                        {
                            check = true;
                            break;
                        }
                    }
                }

                if (!check)
                {
                    return false;
                }
            }
            
            return true;
        }

        [Button]
        int[,,] Create(int depth, int row, int col, List<float> thresholds, int tileCount, int maxAttempts = 1000, float offset = .05f)
        {
            List<int[,,]> tempResult = new();
            
            thresholds.Sort();
            var attemptCount = 0;

            while (attemptCount < maxAttempts)
            {
                //.Log($"Attempt: {attemptCount} =========================");
                
                var matrix2DList = new List<int[,]>();
                    var randomScaleMin = -Random.Range(setting.minScale, setting.maxScale);
                    var randomScaleMax = -Random.Range(setting.minScale, setting.maxScale);
                    var scale = Random.Range(randomScaleMin, randomScaleMax);
                    
                    var noise = new SimplexPerlin();

                for (int d = 0; d < depth; d++)
                {
                    var threshold = thresholds[d];
                    
                    //BhDebug.Log($"Depth: {d}, threshold: {threshold}");
                
                    var toRight = Random.Range(0, 2) == 0;
                    var toBottom = Random.Range(0, 2) == 0;
                
                    var matrix = new float[row, col];
                    var temp = new int[row, col];

                    for (int i = 0; i < row; i++)
                    {
                        for (int j = 0; j < col; j++)
                        {
                            matrix[i, j] = noise.GetValue(i * scale, j * scale, d * scale);

                            if (matrix[i, j] >= threshold)
                            {
                                temp[i, j] = 1;
                            }
                            else
                            {
                                temp[i, j] = 0;
                            }
                        }
                    }

                    var result = LevelGenerateUtils.GetSymmetryMatrixMaxSize(temp, SymmetryType.Both, Random.value > .5f, Random.value > .5f);
                
                    matrix2DList.Add(result);
                }

                if (matrix2DList.Count == 0)
                {
                    attemptCount++;
                    continue;
                }

                var matrix3D = ConvertTo3DMatrix(matrix2DList);
                
                if (matrix3D == null)
                {
                    attemptCount++;
                    continue;
                }

                var levelData = matrix3D.FindSquaresSymmetric3D(out var count);

                if (count % 2 != 0)
                {
                    attemptCount++;
                    continue;
                }
                
                if (count == tileCount)
                {
                    return levelData;
                }
                else
                {
                    var offsetRate = (float)Mathf.Abs(count - tileCount) / tileCount;
                    if (offsetRate < offset)
                    {
                        tempResult.Add(levelData);
                    }
                }
                
                attemptCount++;
            }

            if (tempResult.Count > 0)
            {
                return tempResult.GetRandom();
            }

            return null;

        }
        
        
        int[,,] CreateRandomThreshold(int depth, int row, int col, List<float> thresholds, int tileCount, int maxAttempts = 1000, float offset = .05f)
        {
            List<int[,,]> tempResult = new();
            
            thresholds.Sort();

            /*for (int i = 0; i < thresholds.Count; i++)
            {
                Debug.Log($"Threshold: {i}:  {thresholds[i]}");
            }*/
            
            var attemptCount = 0;

            while (attemptCount < maxAttempts)
            {
                //BhDebug.Log($"Attempt: {attemptCount} =========================");
                
                var matrix2DList = new List<int[,]>();

                for (int d = 0; d < depth; d++)
                {
                    var scale = Random.Range(-100f, 100f);
                    var threshold = Random.Range(-.8f, .8f);
                    
                    //BhDebug.Log($"Depth: {d}, threshold: {threshold}");
                
                    var toRight = Random.Range(0, 2) == 0;
                    var toBottom = Random.Range(0, 2) == 0;
                
                    var matrix = new float[row, col];
                    var temp = new int[row, col];
                    var noise = new SimplexPerlin();

                    for (int i = 0; i < row; i++)
                    {
                        for (int j = 0; j < col; j++)
                        {
                            matrix[i, j] = noise.GetValue(i * scale, j * scale);

                            if (matrix[i, j] >= threshold)
                            {
                                temp[i, j] = 1;
                            }
                            else
                            {
                                temp[i, j] = 0;
                            }
                        }
                    }

                    var result = LevelGenerateUtils.GetSymmetryMatrixMaxSize(temp, SymmetryType.Both, toRight, toBottom);
                
                    matrix2DList.Add(result);
                }


                var matrix3D = ConvertTo3DMatrix(matrix2DList);

                var levelData = matrix3D.FindSquaresSymmetric3D(out var count);

                if (count % 2 != 0)
                {
                    attemptCount++;
                    continue;
                }
                
                if (count == tileCount)
                {
                    //BhDebug.Log("Confirm count: " + count);
                    return levelData;
                }
                else
                {
                    var offsetRate = (float)Mathf.Abs(count - tileCount) / tileCount;
                    if (offsetRate < offset)
                    {
                        tempResult.Add(levelData);
                    }
                }
                
                attemptCount++;
            }

            if (tempResult.Count > 0)
            {
                return tempResult.GetRandom();
            }

            return null;

        }
        
        
        static int[,,] ConvertTo3DMatrix(List<int[,]> matrices)
        {
            if (matrices == null || matrices.Count == 0)
                throw new ArgumentException("Danh sách ma trận không được rỗng");

            int depth = matrices.Count;
            int rows = matrices[0].GetLength(0);
            int cols = matrices[0].GetLength(1);

            int[,,] matrix3D = new int[depth, rows, cols];

            for (int d = 0; d < depth; d++)
            {
                if (matrices[d].GetLength(0) != rows || matrices[d].GetLength(1) != cols)
                    throw new ArgumentException("Tất cả các ma trận phải có cùng kích thước");

                for (int i = 0; i < rows; i++)
                {
                    for (int j = 0; j < cols; j++)
                    {
                        matrix3D[d, i, j] = matrices[d][i, j];
                    }
                }
            }

            return matrix3D;
        }
        
        public static int[,,] ConvertTo3DMatrixAlignCenter(List<int[,]> matrices)
        {
            if (matrices == null || matrices.Count == 0)
                throw new ArgumentException("Danh sách ma trận không được rỗng");

            // Tìm kích thước lớn nhất
            int maxRows = 0, maxCols = 0;
            foreach (var matrix in matrices)
            {
                maxRows = Math.Max(maxRows, matrix.GetLength(0));
                maxCols = Math.Max(maxCols, matrix.GetLength(1));
            }

            int depth = matrices.Count;
            int[,,] matrix3D = new int[depth, maxRows, maxCols];

            for (int d = 0; d < depth; d++)
            {
                int[,] originalMatrix = matrices[d];
                int originalRows = originalMatrix.GetLength(0);
                int originalCols = originalMatrix.GetLength(1);

                // Tính toán vị trí để căn giữa
                int rowOffset = (maxRows - originalRows) / 2;
                int colOffset = (maxCols - originalCols) / 2;

                for (int i = 0; i < originalRows; i++)
                {
                    for (int j = 0; j < originalCols; j++)
                    {
                        matrix3D[d, rowOffset + i, colOffset + j] = originalMatrix[i, j];
                    }
                }
            }

            return matrix3D;
        }

        
        void VisualizeMatrix3D(int[,,] matrix3D)
        {
            /*containerGenerator.DestroyChildrenImmediate(); // Xóa tất cả các game object hiện tại trong containerGenerator

            int depth = matrix3D.GetLength(0);
            int rows = matrix3D.GetLength(1);
            int cols = matrix3D.GetLength(2);

            for (int d = 0; d < depth; d++)
            {
                GameObject layerParent = new GameObject($"Layer_{d}");
                layerParent.transform.parent = containerGenerator;
                
                for (int i = 0; i < rows; i++)
                {
                    for (int j = 0; j < cols; j++)
                    {
                        if (matrix3D[d, i, j] != 0) // Chỉ hiển thị nếu giá trị khác 0
                        {
                            Vector3 position = new Vector3(j, -i, d); // (-i để y ngược xuống)
                            GameObject go = Instantiate(prefab, position, Quaternion.identity, layerParent.transform);
                            go.transform.localPosition = position;
                            var children = go.GetComponentsInChildren<TMP_Text>();
                            
                            var index = children.FirstOrDefault(t => t.gameObject.name == "Index")!.text = $"{(int)Mathf.Abs(d)} - {(int)i} - {(int)j}";
                            var value = children.FirstOrDefault(t => t.gameObject.name == "Value")!.text =
                                matrix3D[d, i, j].ToString("F5");
                        }
                    }
                }
            }*/
        }

        Dictionary<int, int> GetRandomTypeMap(int totalTileCount, int typeCount)
        {
            var allType = new List<int>();

            for (int i = 0; i < setting.totalTypeCount; i++)
            {
                allType.Add(i);
            }
            
            //BhDebug.Log("Total type: " + allType.Count);
            
            List<int> numbers = new List<int>();

            for (int i = 0; i < typeCount; i++)
            {
                numbers.Add(allType.GetRandomAndRemove());
            }

            Dictionary<int, int> result = DistributeNumbers(numbers, totalTileCount, setting.maxItemPerType);

            /*
            // In kết quả
            foreach (var kvp in result)
            {
                Debug.Log($"Số {kvp.Key}: {kvp.Value} lần");
            }
            */

            return result;
        }




        List<char> GetTypeList(Dictionary<int, int> randomTypeMap)
        {
            var result = new List<char>();

            foreach (var kvp in randomTypeMap)
            {
                for (int j = 0; j < kvp.Value; j++)
                {
                    result.Add(kvp.Key.ToChar());
                }
            }

            result.Shuffle(null);

            return result;
        }
        
        List<char> GetTypeList(int totalTileCount, int typeCount)
        {
            var randomTypeMap = GetRandomTypeMap(totalTileCount, typeCount);

            if (randomTypeMap == null || randomTypeMap.Count == 0)
            {
                return null;
            }
            
            var result = new List<char>();

            foreach (var kvp in randomTypeMap)
            {
                for (int j = 0; j < kvp.Value; j++)
                {
                    var key = kvp.Key.ToChar();
                    result.Add(kvp.Key.ToChar());
                }
            }

            result.Shuffle(null);

            return result;
        }







        /*[Button]
        void RandomType()
        {
            var allType = new List<int>();

            for (int i = 0; i < 28; i++)
            {
                allType.Add(i);
            }
            
            List<int> numbers = new List<int>();

            for (int i = 0; i < typeCount; i++)
            {
                numbers.Add(allType.GetRandomAndRemove());
            }

            Dictionary<int, int> result = DistributeNumbers(numbers, totalTileCount, 8);

            // In kết quả
            foreach (var kvp in result)
            {
                Debug.Log($"Số {kvp.Key}: {kvp.Value} lần");
            }
        }*/
        
        static Dictionary<int, int> DistributeNumbers(List<int> numbers, int total, int max)
        {
            Dictionary<int, int> distribution = new Dictionary<int, int>();
            int count = numbers.Count;

            if (count == 0 || total < count * 2 || total % 2 != 0)
            {
                Debug.LogError("Tổng số lượng phải đủ để mỗi số có ít nhất 2 lần và phải là số chẵn.");
                return null;
            }

            int remaining = total;

            for (int i = 0; i < count; i++)
            {
                distribution[numbers[i]] = 2;
                remaining -= 2;
            }

            while (remaining > 0)
            {
                var randomPair = distribution.GetRandom();
                if (randomPair.Value < max)
                {
                    distribution[randomPair.Key]+=2;
                    remaining -= 2;
                }
            }

            return distribution;
        }


    }
}