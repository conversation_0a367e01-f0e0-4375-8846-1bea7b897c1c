using UnityEngine;

namespace LevelGen.Scripts
{
    public class CreatorStructure
    {
        
    }


    [System.Serializable]
    public class GridSetting
    {
        [Header("Row")]
        [SerializeField]
        private int minRow;

        [SerializeField]
        private int maxRow;
        
        [Header("Col")]
        [SerializeField]
        private int minCol;

        [SerializeField]
        private int maxCol;
        
        [Header("Layer")]
        [SerializeField]
        private int minLayer;

        [SerializeField]
        private int maxLayer;

        [SerializeField]
        private float maxFillRate;
        
        [SerializeField]
        private float minFillRate;
        
        [SerializeField]
        private float minOffsetTileCount;
        
        [SerializeField]
        private float maxOffsetTileCount;
        
        [Header("Tile")]
        [SerializeField]
        private int minTile;

        [SerializeField]
        private int maxTile;
        
        [SerializeField]
        private float offsetTileCount;
        
        
        public GridCreateInfo GetRandom()
        {
            var row = Random.Range(minRow, maxRow + 1);
            var col = Random.Range(minCol, maxCol + 1);
            var layer = Random.Range(minLayer, maxLayer + 1);
            var tileCount = Random.Range(minTile, maxTile + 1);
            return new GridCreateInfo(row, col, layer, tileCount, minFillRate, maxFillRate, minOffsetTileCount, maxOffsetTileCount);
        }
        
        public GridCreateInfo GetRandom(int tileCount)
        {
            var row = Random.Range(minRow, maxRow + 1);
            var col = Random.Range(minCol, maxCol + 1);
            var layer = Random.Range(minLayer, maxLayer + 1);
            
            var correctTileCount = Mathf.RoundToInt(tileCount * (1 + offsetTileCount * Random.Range(-1f, 1f)));
            
            if (correctTileCount % 2 != 0)
            {
                correctTileCount += 1;
            }
            
            return new GridCreateInfo(row, col, layer, correctTileCount, minFillRate, maxFillRate, minOffsetTileCount, maxOffsetTileCount);
        }
        
        public GridCreateInfo GetRandom(int row, int col, int layer, int tileCount)
        {
            return new GridCreateInfo(row, col, layer, tileCount, minFillRate, maxFillRate, minOffsetTileCount, maxOffsetTileCount);
        }
    }
    
    public class GridCreateInfo
    {
        private int _row;
        private int _col;
        private int _layer;

        private int _tileCount;
        
        private float _minFillRate;
        private float _maxFillRate;
        private float _minOffsetTileCount;
        private float _maxOffsetTileCount;
        
        public GridCreateInfo(int row, int col, int layer, int tileCount, float minFillRate, float maxFillRate, float minOffsetTileCount, float maxOffsetTileCount)
        {
            this._row = row;
            this._col = col;
            this._layer = layer;
            this._tileCount = tileCount;
            this._minFillRate = minFillRate;
            this._maxFillRate = maxFillRate;
            this._minOffsetTileCount = minOffsetTileCount;
            this._maxOffsetTileCount = maxOffsetTileCount;
        }
        
        public int GetRow()
        {
            return _row;
        }
        
        public int GetCol()
        {
            return _col;
        }
        
        public int GetLayer()
        {
            return _layer;
        }
        
        public int GetTileCount()
        {
            return _tileCount;
        }
        
        public float GetMinFillRate()
        {
            return _minFillRate;
        }
        
        public float GetMaxFillRate()
        {
            return _maxFillRate;
        }
        
        public float GetMinOffsetTileCount()
        {
            return _minOffsetTileCount;
        }
        
        public float GetMaxOffsetTileCount()
        {
            return _maxOffsetTileCount;
        }
        
        public bool IsPassFillRate(float fillRate)
        {
            return fillRate >= _minFillRate && fillRate <= _maxFillRate;
        }
        
        public bool IsPassOffsetTileCount(float offsetTileCount)
        {
            return offsetTileCount >= _minOffsetTileCount && offsetTileCount <= _maxOffsetTileCount;
        }
    }
}