using UnityEngine;

namespace LevelGen.Scripts.Erosion
{
    /// <summary>
    /// Ch<PERSON><PERSON> các thuật toán tĩnh để tạo và biến đổi layout level.
    /// </summary>
    public static partial class LayoutAlgorithms
    {
        #region --- <PERSON><PERSON><PERSON> (Đã có từ trước) ---

        public enum Neighborhood
        {
            FourWay,
            EightWay
        }

        public static int[,] Erode(int[,] baseLayer, int iterations = 1, Neighborhood neighborhoodType = Neighborhood.EightWay)
        {
            if (baseLayer == null)
            {
                Debug.LogError("Input 'baseLayer' cannot be null.");
                return null;
            }

            if (iterations < 1) return (int[,])baseLayer.Clone();

            int[,] currentLayer = (int[,])baseLayer.Clone();
            for (int i = 0; i < iterations; i++)
            {
                currentLayer = PerformSingleErosionPass(currentLayer, neighborhoodType);
            }
            return currentLayer;
        }

        private static int[,] PerformSingleErosionPass(int[,] layer, Neighborhood neighborhoodType)
        {
            int width = layer.GetLength(0);
            int height = layer.GetLength(1);
            int[,] erodedLayer = new int[width, height];

            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    if (layer[x, y] == 0) continue;

                    bool isInternalTile = true;
                    int[] dx = neighborhoodType == Neighborhood.EightWay ? new int[] { -1, 0, 1, -1, 1, -1, 0, 1 } : new int[] { 0, -1, 0, 1 };
                    int[] dy = neighborhoodType == Neighborhood.EightWay ? new int[] { -1, -1, -1, 0, 0, 1, 1, 1 } : new int[] { -1, 0, 1, 0 };

                    for (int i = 0; i < dx.Length; i++)
                    {
                        int nX = x + dx[i];
                        int nY = y + dy[i];
                        if (nX < 0 || nX >= width || nY < 0 || nY >= height || layer[nX, nY] == 0)
                        {
                            isInternalTile = false;
                            break;
                        }
                    }
                    if (isInternalTile)
                    {
                        erodedLayer[x, y] = 1;
                    }
                }
            }
            return erodedLayer;
        }

        #endregion

        #region --- [MỚI] Ăn Mòn Không Đồng Đều (Probabilistic Erosion) ---

        /// <summary>
        /// Áp dụng thuật toán ăn mòn không đồng đều. Các tile ở rìa có một xác suất bị loại bỏ.
        /// </summary>
        /// <param name="baseLayer">Layout gốc.</param>
        /// <param name="erosionChance">Xác suất (0.0 đến 1.0) một tile ở rìa sẽ bị ăn mòn.</param>
        /// <param name="iterations">Số lần thực hiện.</param>
        /// <param name="neighborhoodType">Kiểu kiểm tra hàng xóm.</param>
        /// <returns>Layout mới với đường viền tự nhiên, lởm chởm.</returns>
        public static int[,] ProbabilisticErode(int[,] baseLayer, float erosionChance, int iterations = 1, Neighborhood neighborhoodType = Neighborhood.EightWay)
        {
            if (baseLayer == null) return null;
        
            // Đảm bảo xác suất nằm trong khoảng hợp lệ
            erosionChance = Mathf.Clamp01(erosionChance);

            int[,] currentLayer = (int[,])baseLayer.Clone();
            for (int i = 0; i < iterations; i++)
            {
                currentLayer = PerformSingleProbabilisticErosionPass(currentLayer, erosionChance, neighborhoodType);
            }
            return currentLayer;
        }

        private static int[,] PerformSingleProbabilisticErosionPass(int[,] layer, float erosionChance, Neighborhood neighborhoodType)
        {
            int width = layer.GetLength(0);
            int height = layer.GetLength(1);
            int[,] erodedLayer = (int[,])layer.Clone(); // Bắt đầu bằng một bản sao hoàn chỉnh

            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    if (layer[x, y] == 0) continue;

                    // Kiểm tra xem có phải là tile ở rìa không
                    bool isEdgeTile = false;
                    int[] dx = neighborhoodType == Neighborhood.EightWay ? new int[] { -1, 0, 1, -1, 1, -1, 0, 1 } : new int[] { 0, -1, 0, 1 };
                    int[] dy = neighborhoodType == Neighborhood.EightWay ? new int[] { -1, -1, -1, 0, 0, 1, 1, 1 } : new int[] { -1, 0, 1, 0 };

                    for (int i = 0; i < dx.Length; i++)
                    {
                        int nX = x + dx[i];
                        int nY = y + dy[i];
                        if (nX < 0 || nX >= width || nY < 0 || nY >= height || layer[nX, nY] == 0)
                        {
                            isEdgeTile = true;
                            break;
                        }
                    }
                
                    // Nếu là tile rìa, tung xúc xắc xem có xóa nó không
                    if (isEdgeTile && Random.value < erosionChance)
                    {
                        erodedLayer[x, y] = 0; // Bị ăn mòn!
                    }
                }
            }
            return erodedLayer;
        }

        #endregion

        #region --- [MỚI] Ăn Mòn Theo Hướng (Directional Erosion) ---

        /// <summary>
        /// Áp dụng thuật toán ăn mòn chỉ từ các hướng được chỉ định.
        /// </summary>
        /// <param name="baseLayer">Layout gốc.</param>
        /// <param name="erosionDirections">Mảng các vector chỉ hướng ăn mòn. Vd: (0, 1) là từ trên xuống.</param>
        /// <param name="iterations">Số lần thực hiện.</param>
        /// <returns>Layout mới bị ăn mòn theo hướng, tạo hiệu ứng bóng đổ hoặc tuyết đọng.</returns>
        public static int[,] DirectionalErode(int[,] baseLayer, Vector2Int[] erosionDirections, int iterations = 1)
        {
            if (baseLayer == null || erosionDirections == null || erosionDirections.Length == 0) return null;

            int[,] currentLayer = (int[,])baseLayer.Clone();
            for (int i = 0; i < iterations; i++)
            {
                currentLayer = PerformSingleDirectionalErosionPass(currentLayer, erosionDirections);
            }
            return currentLayer;
        }

        private static int[,] PerformSingleDirectionalErosionPass(int[,] layer, Vector2Int[] erosionDirections)
        {
            int width = layer.GetLength(0);
            int height = layer.GetLength(1);
            int[,] erodedLayer = (int[,])layer.Clone();

            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    if (layer[x, y] == 0) continue;

                    bool shouldBeEroded = false;
                    foreach (Vector2Int dir in erosionDirections)
                    {
                        int nX = x + dir.x;
                        int nY = y + dir.y;

                        // Nếu hàng xóm theo hướng chỉ định nằm ngoài biên hoặc là ô trống
                        if (nX < 0 || nX >= width || nY < 0 || nY >= height || layer[nX, nY] == 0)
                        {
                            shouldBeEroded = true;
                            break; // Chỉ cần 1 hướng thỏa mãn là đủ để xóa
                        }
                    }

                    if (shouldBeEroded)
                    {
                        erodedLayer[x, y] = 0;
                    }
                }
            }
            return erodedLayer;
        }

        #endregion
    }
}