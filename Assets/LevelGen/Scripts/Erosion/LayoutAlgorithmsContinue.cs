using System.Collections.Generic;

// Cần dùng List

namespace LevelGen.Scripts.Erosion
{
    /// <summary>
    /// Chứa các thuật toán tĩnh để tạo và biến đổi layout level.
    /// </summary>
    public static partial class LayoutAlgorithms // Dùng partial class để dễ quản lý
    {
        #region --- [MỚI] Tìm Xương Sống & Làm Dày ---

        /// <summary>
        /// Tạo một lớp layout dựa trên "bộ xương" của layout gốc.
        /// </summary>
        /// <param name="baseLayer">Layout gốc.</param>
        /// <param name="dilationIterations">Số lần làm dày bộ xương sau khi tìm được. 0 sẽ trả về bộ xương 1-pixel.</param>
        /// <returns>Một lớp layout mới có hình dạng là bộ xương đã được làm dày.</returns>
        public static int[,] Skeletonize(int[,] baseLayer, int dilationIterations = 1)
        {
            if (baseLayer == null) return null;

            // Bước 1: Áp dụng thuật toán làm mỏng Zhang-Suen để tìm bộ xương 1-pixel.
            int[,] skeleton = PerformZhangSuenThinning(baseLayer);

            if (dilationIterations < 1)
            {
                return skeleton; // Trả về bộ xương thô nếu không cần làm dày
            }

            // Bước 2: Làm dày bộ xương lên để có thể chơi được.
            return Dilate(skeleton, dilationIterations, Neighborhood.EightWay);
        }

        /// <summary>
        /// Làm dày (giãn nở) một layout. Phép toán ngược lại của Ăn mòn (Erode).
        /// </summary>
        /// <param name="baseLayer">Layout gốc.</param>
        /// <param name="iterations">Số lần thực hiện làm dày.</param>
        /// <param name="neighborhoodType">Kiểu kiểm tra hàng xóm.</param>
        /// <returns>Layout mới đã được làm dày.</returns>
        public static int[,] Dilate(int[,] baseLayer, int iterations = 1, Neighborhood neighborhoodType = Neighborhood.EightWay)
        {
            if (baseLayer == null) return null;

            int[,] currentLayer = (int[,])baseLayer.Clone();
            for (int i = 0; i < iterations; i++)
            {
                currentLayer = PerformSingleDilationPass(currentLayer, neighborhoodType);
            }
            return currentLayer;
        }

        private static int[,] PerformSingleDilationPass(int[,] layer, Neighborhood neighborhoodType)
        {
            int width = layer.GetLength(0);
            int height = layer.GetLength(1);
            int[,] dilatedLayer = (int[,])layer.Clone(); // Bắt đầu bằng một bản sao

            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    // Chỉ xét các ô trống
                    if (layer[x, y] == 1) continue;

                    // Nếu một ô trống có ít nhất một hàng xóm là tile, ô đó sẽ được lấp đầy
                    bool hasFilledNeighbor = false;
                    int[] dx = neighborhoodType == Neighborhood.EightWay ? new int[] { -1, 0, 1, -1, 1, -1, 0, 1 } : new int[] { 0, -1, 0, 1 };
                    int[] dy = neighborhoodType == Neighborhood.EightWay ? new int[] { -1, -1, -1, 0, 0, 1, 1, 1 } : new int[] { -1, 0, 1, 0 };

                    for (int i = 0; i < dx.Length; i++)
                    {
                        int nX = x + dx[i];
                        int nY = y + dy[i];

                        if (nX >= 0 && nX < width && nY >= 0 && nY < height && layer[nX, nY] == 1)
                        {
                            hasFilledNeighbor = true;
                            break;
                        }
                    }

                    if (hasFilledNeighbor)
                    {
                        dilatedLayer[x, y] = 1;
                    }
                }
            }
            return dilatedLayer;
        }


        /// <summary>
        /// Triển khai thuật toán làm mỏng Zhang-Suen.
        /// </summary>
        private static int[,] PerformZhangSuenThinning(int[,] baseLayer)
        {
            int[,] thinningLayer = (int[,])baseLayer.Clone();
            int width = thinningLayer.GetLength(0);
            int height = thinningLayer.GetLength(1);
            List<Point> pointsToDelete = new List<Point>();
            bool hasChanged;

            do
            {
                hasChanged = false;

                // --- Giai đoạn 1 ---
                for (int x = 1; x < width - 1; x++)
                {
                    for (int y = 1; y < height - 1; y++)
                    {
                        if (thinningLayer[x, y] == 1)
                        {
                            int p2 = thinningLayer[x, y + 1];
                            int p3 = thinningLayer[x + 1, y + 1];
                            int p4 = thinningLayer[x + 1, y];
                            int p5 = thinningLayer[x + 1, y - 1];
                            int p6 = thinningLayer[x, y - 1];
                            int p7 = thinningLayer[x - 1, y - 1];
                            int p8 = thinningLayer[x - 1, y];
                            int p9 = thinningLayer[x - 1, y + 1];

                            int A = (p2 == 0 && p3 == 1 ? 1 : 0) + (p3 == 0 && p4 == 1 ? 1 : 0) +
                                    (p4 == 0 && p5 == 1 ? 1 : 0) + (p5 == 0 && p6 == 1 ? 1 : 0) +
                                    (p6 == 0 && p7 == 1 ? 1 : 0) + (p7 == 0 && p8 == 1 ? 1 : 0) +
                                    (p8 == 0 && p9 == 1 ? 1 : 0) + (p9 == 0 && p2 == 1 ? 1 : 0);
                        
                            int B = p2 + p3 + p4 + p5 + p6 + p7 + p8 + p9;

                            if (A == 1 && (B >= 2 && B <= 6) && (p2 * p4 * p6 == 0) && (p2 * p6 * p8 == 0))
                            {
                                pointsToDelete.Add(new Point(x, y));
                            }
                        }
                    }
                }
                if (pointsToDelete.Count > 0)
                {
                    foreach (Point p in pointsToDelete) thinningLayer[p.X, p.Y] = 0;
                    pointsToDelete.Clear();
                    hasChanged = true;
                }

                // --- Giai đoạn 2 ---
                for (int x = 1; x < width - 1; x++)
                {
                    for (int y = 1; y < height - 1; y++)
                    {
                        if (thinningLayer[x, y] == 1)
                        {
                            int p2 = thinningLayer[x, y + 1];
                            int p3 = thinningLayer[x + 1, y + 1];
                            int p4 = thinningLayer[x + 1, y];
                            int p5 = thinningLayer[x + 1, y - 1];
                            int p6 = thinningLayer[x, y - 1];
                            int p7 = thinningLayer[x - 1, y - 1];
                            int p8 = thinningLayer[x - 1, y];
                            int p9 = thinningLayer[x - 1, y + 1];

                            int A = (p2 == 0 && p3 == 1 ? 1 : 0) + (p3 == 0 && p4 == 1 ? 1 : 0) +
                                    (p4 == 0 && p5 == 1 ? 1 : 0) + (p5 == 0 && p6 == 1 ? 1 : 0) +
                                    (p6 == 0 && p7 == 1 ? 1 : 0) + (p7 == 0 && p8 == 1 ? 1 : 0) +
                                    (p8 == 0 && p9 == 1 ? 1 : 0) + (p9 == 0 && p2 == 1 ? 1 : 0);
                        
                            int B = p2 + p3 + p4 + p5 + p6 + p7 + p8 + p9;

                            if (A == 1 && (B >= 2 && B <= 6) && (p2 * p4 * p8 == 0) && (p4 * p6 * p8 == 0))
                            {
                                pointsToDelete.Add(new Point(x, y));
                            }
                        }
                    }
                }
                if (pointsToDelete.Count > 0)
                {
                    foreach (Point p in pointsToDelete) thinningLayer[p.X, p.Y] = 0;
                    pointsToDelete.Clear();
                    hasChanged = true;
                }

            } while (hasChanged);

            return thinningLayer;
        }

        // Một struct đơn giản để lưu tọa độ điểm
        private struct Point
        {
            public int X, Y;
            public Point(int x, int y) { X = x; Y = y; }
        }

        #endregion
    }
}