using System;
using System.Collections.Generic;
using System.Linq;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Extensions;
using Sirenix.OdinInspector;
using UnityEngine;

namespace LevelGen.Scripts
{
    [Serializable]
    public class Setting
    {
        //Threshold - Rate
        public SerializedDictionary<float, float> rateByThreshold = new();

        public float offset = .3f;
        public int maxAttemptCreateData = 1000;
        public float minValue = .1f;
        public float maxValue = .85f;

        [Space]
        public int minScale = 100;
        public int maxScale = 1200;

        [Space]
        public float minValueRandom = 1f;
        public float maxValueRandom = 3f;

        public int maxAttemptsGetThreshold = 1000;

        [Space(20f)]
        public int maxRowCount = 12;
        public int minRowCount = 4;
        
        public int maxColCount = 7;
        public int minColCount = 4;
        
        public float offsetRowCol = .4f;

        public float offsetRowColMultiplier = .2f;

        public float offsetTileCount = .05f;

        [Header("Type")]
        public int maxItemPerType = 10;

        public int minItemPerType = 2;

        public int maxType = 25;

        [Header("Item")]
        public int totalTypeCount = 36;

        [Space]
        public int delay = 1;
        
        
        
        
        public List<(float, float)> PickRandomThresholds(int layerCount)
        {
            float targetSum = 1f + offset;
            List<KeyValuePair<float, float>> candidates = rateByThreshold.Where(x => x.Value >= minValue && x.Value <= maxValue).ToList();
            System.Random rand = new();

            List<KeyValuePair<float, float>> selected = new();
            float bestDifference = float.MaxValue;
            List<KeyValuePair<float, float>> bestSelection = new();

            for (int attempt = 0; attempt < maxAttemptsGetThreshold; attempt++)
            {
                // Chỉ chọn các phần tử có giá trị >= minValue
                selected = candidates.OrderBy(x => rand.Next()).Take(layerCount).ToList();
                float total = selected.Sum(x => x.Value);

                float difference = Math.Abs(total - targetSum);
                if (difference < bestDifference)
                {
                    bestDifference = difference;
                    bestSelection = new List<KeyValuePair<float, float>>(selected);

                    if (bestDifference < 0.01f) break; // Nếu đã gần đúng thì dừng
                }
            }

            var result = new List<(float, float)>();
            
            // In kết quả
            //Debug.Log($"Selected {bestSelection.Count} pairs. Total Sum: {bestSelection.Sum(x => x.Value)}, Target: {targetSum}");
            foreach (var pair in bestSelection)
            {
                //Debug.Log($"Key: {pair.Key}, Value: {pair.Value}");
                result.Add((pair.Key, pair.Value));
            }
            
            return result;
        }
        
        [Button]
        public List<(float, float)> PickRandomThresholdsRandom(int layerCount)
        {
            float targetSum = 1f + offset;
            List<KeyValuePair<float, float>> candidates = rateByThreshold.Where(x => x.Value >= minValue && x.Value <= maxValue).ToList();
            System.Random rand = new();

            List<List<KeyValuePair<float, float>>> allItems = new();
            

            for (int attempt = 0; attempt < maxAttemptsGetThreshold; attempt++)
            {
                // Chỉ chọn các phần tử có giá trị >= minValue
                var selected = candidates.OrderBy(x => rand.Next()).Take(layerCount).ToList();
                float total = selected.Sum(x => x.Value);

                float difference = Math.Abs(total - targetSum);

                if (difference <= .01f)
                {
                    allItems.Add(selected);
                }
            }

            var result = new List<List<(float, float)>>();
            
            // In kết quả
            //Debug.Log($"Selected {allItems.Count} pairs. Target: {targetSum}");
            foreach (var bestSelection in allItems)
            {
                var temp = new List<(float, float)>();
                //BhDebug.Log("Select: ----");
                foreach (var pair in bestSelection)
                {
                    //Debug.Log($"Key: {pair.Key}, Value: {pair.Value}");
                    temp.Add((pair.Key, pair.Value));
                }
                
                result.Add(temp);
                
            }
            
            return result.GetRandom();
        }
        
        public List<(float, float)> PickRandomThresholds()
        {
            float targetSum = 1f + offset;
            List<KeyValuePair<float, float>> candidates = rateByThreshold.Where(x => x.Value >= minValue).ToList();
            System.Random rand = new();

            List<KeyValuePair<float, float>> bestSelection = new();
            float bestDifference = float.MaxValue;

            for (int attempt = 0; attempt < maxAttemptsGetThreshold; attempt++)
            {
                List<KeyValuePair<float, float>> selected = new();
                float total = 0f;
            
                // Chọn ngẫu nhiên số lượng phần tử
                while (total < targetSum && selected.Count < candidates.Count)
                {
                    var candidate = candidates[rand.Next(candidates.Count)];
                    if (!selected.Contains(candidate))
                    {
                        selected.Add(candidate);
                        total += candidate.Value;
                    }
                }

                float difference = Math.Abs(total - targetSum);
                if (difference < bestDifference)
                {
                    bestDifference = difference;
                    bestSelection = new List<KeyValuePair<float, float>>(selected);

                    if (bestDifference < 0.01f) break; // Nếu đã gần đúng thì dừng
                }
            }
            
            var result = new List<(float, float)>();

            // In kết quả
            //Debug.Log($"Selected {bestSelection.Count} pairs. Total Sum: {bestSelection.Sum(x => x.Value)}, Target: {targetSum}");
            foreach (var pair in bestSelection)
            {
                //Debug.Log($"Key: {pair.Key}, Value: {pair.Value}");
                result.Add((pair.Key, pair.Value));
            }
            
            return result;
        }
        
        
        public List<(int row, int col)> FindFactorsWithConstraints(int number)
        {
            return FindFactorsWithConstraints(number, offsetRowCol, offsetRowColMultiplier);
        }
        
        
        public List<(int, int)> FindFactorsWithConstraints(int number, float offsetNumber, float offsetMultiplier)
        {
            var result = new List<(int, int)>();

            int sqrt = (int) Mathf.CeilToInt(Mathf.Sqrt(number));

            for (int m = 1; m <= sqrt; m++)
            {
                var n = (float) number / m;

                var bigger = Mathf.CeilToInt(n);

                var isCorrectOffsetNumber = ((float)Mathf.Abs(bigger - m)) / m <= offsetNumber;
                var isCorrectOffsetMultiplier = ((float)Mathf.Abs(bigger * m - number)) / number <= offsetMultiplier;

                // Check if both row and column are within the defined constraints
                var isWithinRowColConstraints = m >= minRowCount && m <= maxRowCount && bigger >= minColCount && bigger <= maxColCount;

                if (isCorrectOffsetNumber && isCorrectOffsetMultiplier && isWithinRowColConstraints)
                {
                    if (!result.Any(x => (x.Item1 == m && x.Item2 == bigger) || (x.Item1 == bigger && x.Item2 == m)))
                    {
                        result.Add((m , bigger));
                    }
                }



                var smaller = Mathf.FloorToInt(n);

                var isCorrectOffsetNumberSmaller = ((float)Mathf.Abs(smaller - m)) / m <= offsetNumber;
                var isCorrectOffsetMultiplierSmaller = ((float)Mathf.Abs(smaller * m - number)) / number <= offsetMultiplier;

                // Check if both row and column are within the defined constraints for smaller values
                var isWithinRowColConstraintsSmaller = m >= minRowCount && m <= maxRowCount && smaller >= minColCount && smaller <= maxColCount;

                if (isCorrectOffsetNumberSmaller && isCorrectOffsetMultiplierSmaller && isWithinRowColConstraintsSmaller)
                {
                    if (!result.Any(x => (x.Item1 == m && x.Item2 == smaller) || (x.Item1 == smaller && x.Item2 == m)))
                    {
                        result.Add((m, smaller));
                    }
                }
            }

            /*foreach (var item in result)
            {
                BhDebug.Log($"Item: {item.Item1}x{item.Item2}");
            }*/

            return result;
        }
    }
}