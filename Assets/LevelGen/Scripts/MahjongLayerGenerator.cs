using System.Collections.Generic;
using Sirenix.OdinInspector;
using UnityEngine;

namespace LevelGen.Scripts
{
    public class MahjongLayerGenerator : MonoBehaviour
    {
        [Header("Grid Settings")]
        public int gridWidth = 15;
        public int gridHeight = 8;
        public int maxLayers = 5;
    
        [Header("Tile Settings")]
        public GameObject tilePrefab;
        public float tileSpacing = 1.1f;
        public float layerHeight = 0.2f;
    
        [Header("Shape Settings")]
        [Range(0.5f, 0.9f)]
        public float scaleFactor = 0.8f; // Tỷ lệ thu nhỏ mỗi layer (0.8 = 80% size)
    
        private bool[,] baseShape;
        private List<bool[,]> layerShapes;
    
        [Button]
        void Create()
        {
            GenerateBaseShape();
            GenerateLayers();
            SpawnTiles();
        }
    
        void GenerateBaseShape()
        {
            baseShape = new bool[gridWidth, gridHeight];
        
            // <PERSON><PERSON><PERSON> hì<PERSON> dạng cơ bản (c<PERSON> thể customize)
            CreateTraditionalShape();
        }
    
        void CreateTraditionalShape()
        {
            // Tạo hình truyền thống của <PERSON>jong (hình con rùa)
            for (int x = 0; x < gridWidth; x++)
            {
                for (int y = 0; y < gridHeight; y++)
                {
                    // Center area - main body
                    if (x >= 2 && x < gridWidth - 2 && y >= 1 && y < gridHeight - 1)
                    {
                        baseShape[x, y] = true;
                    }
                    // Left and right wings
                    else if ((x == 0 || x == 1 || x == gridWidth - 2 || x == gridWidth - 1) 
                             && y >= 2 && y < gridHeight - 2)
                    {
                        baseShape[x, y] = true;
                    }
                    // Top and bottom extensions
                    else if ((y == 0 || y == gridHeight - 1) && x >= 6 && x < gridWidth - 6)
                    {
                        baseShape[x, y] = true;
                    }
                }
            }
        }
    
        void GenerateLayers()
        {
            layerShapes = new List<bool[,]>();
        
            for (int layer = 0; layer < maxLayers; layer++)
            {
                float currentScale = Mathf.Pow(scaleFactor, layer);
                bool[,] scaledShape = ScaleShape(baseShape, currentScale);
            
                if (IsShapeEmpty(scaledShape))
                    break;
                
                layerShapes.Add(scaledShape);
            }
        
            Debug.Log($"Generated {layerShapes.Count} layers");
        }
    
        bool[,] ScaleShape(bool[,] originalShape, float scale)
        {
            bool[,] scaledShape = new bool[gridWidth, gridHeight];
        
            // Tính toán center của grid
            float centerX = (gridWidth - 1) / 2f;
            float centerY = (gridHeight - 1) / 2f;
        
            for (int x = 0; x < gridWidth; x++)
            {
                for (int y = 0; y < gridHeight; y++)
                {
                    // Tính toán vị trí source trong original shape
                    float sourceX = centerX + (x - centerX) / scale;
                    float sourceY = centerY + (y - centerY) / scale;
                
                    // Kiểm tra có trong bounds không
                    int srcX = Mathf.RoundToInt(sourceX);
                    int srcY = Mathf.RoundToInt(sourceY);
                
                    if (srcX >= 0 && srcX < gridWidth && srcY >= 0 && srcY < gridHeight)
                    {
                        scaledShape[x, y] = originalShape[srcX, srcY];
                    }
                }
            }
        
            return scaledShape;
        }
    
        bool[,] CloneArray(bool[,] source)
        {
            bool[,] clone = new bool[gridWidth, gridHeight];
            for (int x = 0; x < gridWidth; x++)
            {
                for (int y = 0; y < gridHeight; y++)
                {
                    clone[x, y] = source[x, y];
                }
            }
            return clone;
        }
    
        bool IsShapeEmpty(bool[,] shape)
        {
            for (int x = 0; x < gridWidth; x++)
            {
                for (int y = 0; y < gridHeight; y++)
                {
                    if (shape[x, y]) return false;
                }
            }
            return true;
        }
    
        void SpawnTiles()
        {
            // Clear existing tiles
            foreach (Transform child in transform)
            {
                Destroy(child.gameObject);
            }
        
            for (int layer = 0; layer < layerShapes.Count; layer++)
            {
                SpawnLayerTiles(layer);
            }
        }
    
        void SpawnLayerTiles(int layerIndex)
        {
            bool[,] shape = layerShapes[layerIndex];
            float zOffset = layerIndex * layerHeight;
        
            for (int x = 0; x < gridWidth; x++)
            {
                for (int y = 0; y < gridHeight; y++)
                {
                    if (!shape[x, y]) continue;
                
                    Vector3 position = new Vector3(
                        (x - gridWidth * 0.5f) * tileSpacing,
                        zOffset,
                        (y - gridHeight * 0.5f) * tileSpacing
                    );
                
                    GameObject tile = Instantiate(tilePrefab, position, Quaternion.identity, transform);
                
                    // Add tile component if needed
                    MahjongTile tileComponent = tile.GetComponent<MahjongTile>();
                    if (tileComponent != null)
                    {
                        tileComponent.SetGridPosition(x, y, layerIndex);
                        tileComponent.SetTileType(GetRandomTileType());
                    }
                
                    // Color coding for layers (optional - for debugging)
                    Renderer renderer = tile.GetComponent<Renderer>();
                    if (renderer != null)
                    {
                        Color layerColor = Color.HSVToRGB((float)layerIndex / maxLayers, 0.3f, 1f);
                        renderer.material.color = layerColor;
                    }
                }
            }
        }
    
        int GetRandomTileType()
        {
            // Return random tile type (customize based on your tile system)
            return Random.Range(1, 8); // Example: 7 different tile types
        }
    
        // Gizmos for visualization in Scene view
        void OnDrawGizmos()
        {
            if (layerShapes == null) return;
        
            for (int layer = 0; layer < layerShapes.Count; layer++)
            {
                bool[,] shape = layerShapes[layer];
                float zOffset = layer * layerHeight;
            
                Gizmos.color = Color.HSVToRGB((float)layer / maxLayers, 0.5f, 0.8f);
            
                for (int x = 0; x < gridWidth; x++)
                {
                    for (int y = 0; y < gridHeight; y++)
                    {
                        if (shape[x, y])
                        {
                            Vector3 position = transform.position + new Vector3(
                                (x - gridWidth * 0.5f) * tileSpacing,
                                zOffset,
                                (y - gridHeight * 0.5f) * tileSpacing
                            );
                        
                            Gizmos.DrawWireCube(position, Vector3.one * 0.8f);
                        }
                    }
                }
            }
        }
    }

// Optional: Tile component
    public class MahjongTile : MonoBehaviour
    {
        public int gridX, gridY, layer;
        public int tileType;
        public bool isBlocked = false;
    
        public void SetGridPosition(int x, int y, int z)
        {
            gridX = x;
            gridY = y;
            layer = z;
        }
    
        public void SetTileType(int type)
        {
            tileType = type;
        }
    
        public bool CanBeSelected()
        {
            // Check if tile is not blocked by tiles above or to the sides
            return !isBlocked;
        }
    }
}