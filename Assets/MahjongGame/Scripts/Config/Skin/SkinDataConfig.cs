using System.Collections.Generic;
using b100SDK.Scripts.Base;
using MahjongGame.Scripts.Gameplay;
using Sirenix.OdinInspector;
using UnityEngine;

namespace MahjongGame.Scripts.Config.Skin
{
    [CreateAssetMenu(fileName = "Skin Data Config", menuName = "Configs/Skin Data Config")]
    public class SkinDataConfig : ScriptableObject
    { 
        [SerializeField]
        private SkinData skinData;

        public SkinData GetSkinData()
        {
            return skinData;
        }
    }
}