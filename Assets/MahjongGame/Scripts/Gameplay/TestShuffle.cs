using System;
using System.Collections.Generic;
using b100SDK.Scripts.Base;
using Sirenix.OdinInspector;
using UnityEngine;

namespace MahjongGame.Scripts.Gameplay
{
    public class TestShuffle :  BhMonoBehavior
    {
        [SerializeField]
        private List<char> chars = new List<char> { 'a', 'b', 'c', 'a', 'd', 'e', 'b', 'c', 'f', 'g', 'h', 'c', 'c' };
        
        [Button]
        void Test()
        {
            bool hasNearDuplicates = CheckNearDuplicates(chars);
            bool hasNearDuplicatePairs = CheckNearDuplicatePairs(chars);

            Debug.Log("Has near duplicates: " + hasNearDuplicates);
            Debug.Log("Has near duplicate pairs: " + hasNearDuplicatePairs);
        }
        
        
        static bool CheckNearDuplicates(List<char> chars)
        {
            for (int i = 0; i < chars.Count - 1; i++)
            {
                for (int j = i + 1; j < chars.Count; j++)
                {
                    if (chars[i] == chars[j] && Math.Abs(i - j) == 1)
                    {
                        return true;
                    }
                }
            }
            return false;
        }
        
        static List<int> GetNearDuplicates(List<char> chars)
        {
            var result = new List<int>();
            
            var checkedList = new List<int>();
            
            for (int i = 0; i < chars.Count - 1; i++)
            {
                if (checkedList.Contains(i))
                    continue;
                
                if (chars[i] == chars[i + 1])
                {
                    result.Add(i);
                    checkedList.Add(i + 1);
                }
                
                checkedList.Add(i);
            }

            return result;
        }
        
        

        static bool CheckNearDuplicatePairs(List<char> chars)
        {
            var data = GetNearDuplicates(chars);

            for (int i = 0; i < data.Count - 1; i++)
            {
                if (data[i + 1] - data[i] == 2)
                {
                    return true;
                }
            }
            
            return false;
        }
    }
}