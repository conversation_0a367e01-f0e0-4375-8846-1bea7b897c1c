using System;
using System.Collections.Generic;
using System.Linq;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Extensions;
using MahjongGame.Scripts.Gameplay.Item;
using Newtonsoft.Json;
using UnityEngine;
using Random = UnityEngine.Random;

namespace MahjongGame.Scripts.Gameplay.Data
{
    public static class DataExtensions
    {
        public static LevelData GetLevelData(TextAsset textAsset, int index)
        {
            var allData = JsonConvert.DeserializeObject<LevelDataRaw[]>(textAsset.text);

            if (allData.TryGetValue(index, out var rawData))
            {
                return rawData.GetLevelData();
            }

            return null;
        }
        
        
        
        public static LevelData GetLevelData(this LevelDataRaw levelDataRaw)
        {
            var rawData = levelDataRaw.q;
            
            var result = new LevelData();

            result.hasFlower = levelDataRaw.hasFlower == 1;
            result.hasSeason = levelDataRaw.hasSeason == 1;

            var positions = new List<PositionData>();

            
            // Split by :
            var colonIndex = rawData.IndexOf(":", StringComparison.Ordinal);
            string rawDataPosition = rawData.Substring(0, colonIndex);
            string rawDataType = rawData.Substring(colonIndex + 1);
            
            // Handle position            
            
            // Split by ; to get layer
            string[] rawDataLayers = rawDataPosition.Split(';');

            for (int i = 0; i < rawDataLayers.Length; i++)
            {
                // Get layer
                var rawDataLayer = rawDataLayers[i];
                var layer = rawDataLayer[0];
                
                rawDataLayer = rawDataLayer.Substring(1);

                string[] rawDataRows = rawDataLayer.Split('.');

                for (int j = 0; j < rawDataRows.Length; j++)
                {
                    // Get row
                    var rawDataRow = rawDataRows[j];
                    var row = rawDataRow[0];
                    
                    rawDataRow = rawDataRow.Substring(1);

                    string[] rawDataCells = rawDataRow.Split(',');

                    for (int k = 0; k < rawDataCells.Length; k++)
                    {
                        //Get cell
                        var newItem = new PositionData(layer.ToNumber(), row.ToNumber(), rawDataCells[k][0].ToNumber());
                        
                        positions.Add(newItem);
                        
                        //result.items.Add(newItem);
                    }
                }
            }
            
            
            // Handle type
            var typeList = new List<char>();
            var typeStr = "";

            for (var index = 0; index < rawDataType.Length; index++)
            {
                var type = rawDataType[index];
                typeList.Add(type);
                //result.items[index].type = type;
                typeStr += type.ToString();
            }
            
            //BhDebug.Log("typeList: " + typeStr);

            
            
            
            // Create board
            var rowCount = positions.Max(x => x.RowIndex) + 2;
            var colCount = positions.Max(x => x.ColumnIndex) + 2;
            var depth = positions.Max(x => x.LayerIndex) + 1;

            result.boardGame = new char[depth, rowCount, colCount];

            for (int d = 0; d < depth; d++)
            {
                for (int r = 0; r < rowCount; r++)
                {
                    for (int c = 0; c < colCount; c++)
                    {
                        result.boardGame[d, r, c] = '.';
                    }
                }
            }


            for (var index = 0; index < positions.Count; index++)
            {
                var position = positions[index];
                var type = typeList[index];
                result.boardGame[position.LayerIndex, position.RowIndex, position.ColumnIndex] = type;
                result.boardGame[position.LayerIndex, position.RowIndex + 1, position.ColumnIndex] = type;
                result.boardGame[position.LayerIndex, position.RowIndex, position.ColumnIndex + 1] = type;
                result.boardGame[position.LayerIndex, position.RowIndex + 1, position.ColumnIndex + 1] = type;
            }
            
            //Create item
            InitSpriteMap(typeList, levelDataRaw.hasFlower == 1, levelDataRaw.hasSeason == 1);
            
            for (var index = 0; index < positions.Count; index++)
            {
                var positionData = positions[index];
                var type = typeList[index];
                var newVisualData = new VisualData(CreateMahjongSpriteIndex(type), GetMahjongType(type));

                var newMahjongData = new MahjongData(positionData, type, newVisualData);
                
                result.items.Add(newMahjongData);
            }
            
            return result;
        }


        public static LevelDataRaw GetLevelDataRaw(List<MahjongData> mahjongDatas, string id, bool hasFlower, bool hasSeason)
        {
            var positionData = mahjongDatas.Select(x => x.PositionData);
            //q
            var position = string.Join(";", positionData
                .GroupBy(t => t.LayerIndex.ToChar()) // Nhóm theo 
                .Select(g => $"{g.Key}" + string.Join(".", g
                    .GroupBy(t => t.RowIndex.ToChar()) // Nhóm tiếp theo i trong từng nhóm d
                    .Select(ig => $"{ig.Key}" + string.Join(",", ig.Select(t => t.ColumnIndex.ToChar()))))));
            
            //type:
            var sortData = mahjongDatas.OrderBy(x => x.PositionData.LayerIndex)
                .ThenBy(x => x.PositionData.RowIndex)
                .ThenBy(x => x.PositionData.ColumnIndex)
                .Select(x => x.LogicType);

            var type = string.Join("", sortData);

            var qResult = position + ":" + type;

            var newRaw = new LevelDataRaw()
            {
                id = id,
                q = qResult,
                hasFlower = hasFlower ? 1 : 0,
                hasSeason = hasSeason ? 1 : 0
            };
            
            return newRaw;
        }


        public static int ToNumber(this char input)
        {
            if (input >= '0' && input <= '9')
            {
                return input - '0';
            }
            else if (input >= 'A' && input <= 'Z')
            {
                return input - 'A' + 10;
            }
            else if (input >= 'a' && input <= 'z')
            {
                return input - 'a' + 36;
            }
            else
            {
                return (int)input;
            }
        }


        public static char ToChar(this int input)
        {
            if (input >= 0 && input <= 9)
            {
                return (char)('0' + input);
            }
            else if (input >= 10 && input <= 35)
            {
                return (char)('A' + input - 10);
            }
            else if (input >= 36 && input <= 61)
            {
                return (char)('a' + input - 36);
            }
            else
            {
                return (char)input;
            }
        }
        
        
        //Flower and Season
        public static char? FlowerType { get; set; }
        public static char? SeasonType { get; set; }

        private static int _flowerIndex = -1;
        private static int _seasonIndex = -1;
        
        //Journey
        public static char? SpecialMahjongType { get; set; }
        public static int SpecialMahjongIndex { get; set; }

        public static char? SpecialItemType { get; set; }
        public static int SpecialItemIndex { get; set; }
        
        private static Dictionary<char, int> _spriteMap = new();
        
        
        static void InitSpriteMap(List<char> typeList, bool hasFlower, bool hasSeason)
        {
            var allType = typeList.Distinct().ToList();

            var flowerTypes = typeList.GroupBy(x => x)
                .Where(group => group.Count() >= 2 && group.Count() <= 4)
                .Select(group => group.Key).ToList();

            if (hasFlower && flowerTypes.Count > 0)
            {
                FlowerType = flowerTypes.GetRandomAndRemove();
                allType.Remove(FlowerType.Value);
            }
            else
            {
                FlowerType = null;
            }

            if (hasSeason && flowerTypes.Count > 0)
            {
                SeasonType = flowerTypes.GetRandomAndRemove();
                allType.Remove(SeasonType.Value);
            }
            else
            {
                SeasonType = null;
            }
            
            // Remain
            var allSprite = new List<int>();
            for (int i = 0; i < SkinManager.Instance.GetCurrentSkinData().mahjongSpriteList.Count; i++)
            {
                allSprite.Add(i);
            }

            _spriteMap.Clear();
            for (int i = 0; i < allType.Count; i++)
            {
                _spriteMap.TryAdd(allType[i], allSprite.GetRandomAndRemove());
            }
        }

        
        static MahjongType GetMahjongType(char type)
        {
            if (FlowerType.HasValue && FlowerType.Value == type)
            {
                return MahjongType.Flower;
            }
            
            if (SeasonType.HasValue && SeasonType.Value == type)
            {
                return MahjongType.Season;
            }

            if (SpecialMahjongType.HasValue && SpecialMahjongType.Value == type)
            {
                return MahjongType.SpecialTarget;
            }
            
            if (SpecialItemType.HasValue && SpecialItemType.Value == type)
            {
                return MahjongType.SpecialItem;
            }
            
            return MahjongType.Normal;
        }
        
        static int CreateMahjongSpriteIndex(char type)
        {
            if (FlowerType.HasValue && FlowerType.Value == type)
            {
                _flowerIndex++;

                return _flowerIndex % 4;
            }
            
            if (SeasonType.HasValue && SeasonType.Value == type)
            {
                _seasonIndex++;
                
                return _seasonIndex % 4;
            }

            if (SpecialMahjongType.HasValue && SpecialMahjongType.Value == type)
            {
                return SpecialMahjongIndex;
            }
            
            if (SpecialItemType.HasValue && SpecialItemType.Value == type)
            {
                return SpecialItemIndex;
            }
            
            
            return _spriteMap[type];
        }
    }
}
