using System.Collections.Generic;
using System.Linq;
using b100SDK.Scripts.Asset;
using b100SDK.Scripts.DesignPatterns;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Extensions;
using GenerateData.Scripts;
using MahjongGame.Scripts.Gameplay.Controller;
using Sirenix.OdinInspector;
using UnityEngine;

namespace MahjongGame.Scripts.Gameplay.Data
{
    public class DataManager : Singleton<DataManager>
    {
        [SerializeField]
        private List<TextAsset> textAssetList;
        
        [SerializeField]
        private SerializedDictionary<int, string> levelMapPath = new();        
        
        [SerializeField]
        private SerializedDictionary<int, string> levelMapPathHard = new();
        
        [SerializeField]
        private TextAsset tutorialTextAsset;

        [SerializeField]
        private TextAsset levelDataDailyChallenge;
        
        [SerializeField]
        private TextAsset levelDataJourney;
        
        private SerializedDictionary<int, string> _currentLevelPathMap;


        [SerializeField]
        private LevelDataRaw _currentLevelDataRaw;
                
        private TextAsset _currentTextAsset;
        private string _currentPath = "";


        public void SetDataRaw(LevelDataRaw dataRaw)
        {
            _currentLevelDataRaw = new LevelDataRaw
            {
                id = dataRaw.id,
                q = dataRaw.q,
                hasFlower = dataRaw.hasFlower,
                hasSeason = dataRaw.hasSeason,
            };
        }


        private void Start()
        {
            SetLevelHard(false);
        }

        public LevelData GetDataFromDataRaw()
        {
            var levelData = LevelEditor.Instance.CurrentLevel.GetLevelData();

            var size =
                $"{levelData.boardGame.GetLength(0)}x{levelData.boardGame.GetLength(1) / 2}x{levelData.boardGame.GetLength(2) / 2}";
            var tileCount = levelData.items.Count;

            var typeCount = levelData.items.Select(x => x.LogicType).Distinct().Count();

            var str = size + $" - {tileCount} - {typeCount}";
            
            Evm.OnUpdateSize.Dispatch(str);
            
            
            
            //Calculate

            var result = ShuffleCountCalculate.Calculate(levelData);

            var avgStr = $"{result.Item1} - {result.Item2}";
            
            Evm.OnUpdateShuffleAvg.Dispatch(avgStr);
            

            return LevelEditor.Instance.CurrentLevel.GetLevelData();
        }

        public LevelData GetLevelData(int level)
        {
            var correctLevel = GetCorrectLevelInMap(level);
            var levelData = DataExtensions.GetLevelData(_currentTextAsset, correctLevel);
            return levelData;
        }
        
        public LevelData GetLevelDataDailyChallenge(int level)
        {
            int correctIndexLevel = level - 1;
            return DataExtensions.GetLevelData(levelDataDailyChallenge, correctIndexLevel);
        }       
        
        
        public LevelData GetLevelDataJourney(int level)
        {
            int correctIndexLevel = level - 1;
            
            return DataExtensions.GetLevelData(levelDataJourney, correctIndexLevel);
        }
        
        public LevelData GetTutorialData(int level)
        {
            var correctLevelIndex = level - 1;
            return DataExtensions.GetLevelData(tutorialTextAsset, correctLevelIndex);
        }
        
        
        int GetCorrectLevelInMap(int level)
        {
            var allDataMap = _currentLevelPathMap.Keys.ToList();
                
            allDataMap.Sort((x, y) => x.CompareTo(y));

            var dataAsset = allDataMap.FirstOrDefault(x => x >= level);

            var path = _currentLevelPathMap[dataAsset];
                
            if (string.IsNullOrEmpty(_currentPath))
            {
                _currentPath = path;
                //_currentTextAsset = Resources.Load<TextAsset>(_currentPath);
                _currentTextAsset = AssetManager.LoadAsset<TextAsset>(_currentPath);
            }
            else
            {
                if (_currentPath != path)
                {
                    if (_currentTextAsset != null)
                    {
                        AssetManager.UnloadAsset(_currentTextAsset);
                        //Resources.UnloadAsset(_currentTextAsset);
                    }
                        
                    _currentPath = path;
                    //_currentTextAsset = Resources.Load<TextAsset>(_currentPath);
                    _currentTextAsset = AssetManager.LoadAsset<TextAsset>(_currentPath);
                }
            }

            var previousLevel = allDataMap.LastOrDefault(x => x < level);

            return level - ((int) previousLevel) - 1;
        }
        
        
        
        
        
        [Button]
        private void SetLevelHard(bool isLevelHard)
        {
            if (isLevelHard)
            {
                _currentLevelPathMap = levelMapPathHard;
            }
            else
            {
                _currentLevelPathMap = levelMapPath;
            }
        }
    }
}