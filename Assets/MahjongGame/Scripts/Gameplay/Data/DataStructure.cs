using System;
using System.Collections.Generic;
using MahjongGame.Scripts.Gameplay.Item;

namespace MahjongGame.Scripts.Gameplay.Data
{
    [Serializable]
    public class LevelDataRaw
    {
        public string id;
        public string q;
        public int hasFlower;
        public int hasSeason;
    }

    [Serializable]
    public class LevelData
    {
        public List<MahjongData> items = new();
        public char[,,] boardGame;
        public bool hasFlower;
        public bool hasSeason;
    }

    [Serializable]
    public class PositionData
    {
        private int _layerIndex;
        private int _rowIndex;
        private int _columnIndex;

        public int LayerIndex => _layerIndex;
        public int RowIndex => _rowIndex;
        public int ColumnIndex => _columnIndex;

        public event Action OnUpdatePositionData;
        
        
        public PositionData(int layerIndex, int rowIndex, int columnIndex)
        {
            _layerIndex = layerIndex;
            _rowIndex = rowIndex;
            _columnIndex = columnIndex;
        }


        public void UpdatePositionData(PositionData positionData)
        {
            _layerIndex = positionData.LayerIndex;
            _rowIndex = positionData.RowIndex;
            _columnIndex = positionData.ColumnIndex;
            
            OnUpdatePositionData?.Invoke();
        }


        public bool Equals(PositionData positionData)
        {
            return LayerIndex == positionData.LayerIndex && RowIndex == positionData.RowIndex &&
                   ColumnIndex == positionData.ColumnIndex;
        }
    }
}