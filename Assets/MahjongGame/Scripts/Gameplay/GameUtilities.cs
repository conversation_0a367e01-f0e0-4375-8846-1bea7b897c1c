using b100SDK.Scripts.Base;
using MahjongGame.Scripts.Gameplay.Data;

namespace MahjongGame.Scripts.Gameplay
{
    public static class GameUtilities
    {
        public static SelectResult CheckCanSelectItem(PositionData positionToCheck, char[,,] boardGame)
        {
            var layerItem = positionToCheck.LayerIndex;
            var rowItem = positionToCheck.RowIndex;
            var colItem = positionToCheck.ColumnIndex;
            
            //Check layer
            var totalLayer = boardGame.GetLength(0);
            var totalCol = boardGame.GetLength(2);

            for (int i = layerItem + 1; i < totalLayer; i++)
            {
                if (!boardGame[i, rowItem, colItem].Equals('.'))
                {
                    return SelectResult.Layer;
                }
                
                if (!boardGame[i, rowItem + 1, colItem].Equals('.'))
                {
                    return SelectResult.Layer;
                }
                
                if (!boardGame[i, rowItem, colItem + 1].Equals('.'))
                {
                    return SelectResult.Layer;
                }
                
                if (!boardGame[i, rowItem + 1, colItem + 1].Equals('.'))
                {
                    return SelectResult.Layer;
                }
            }
            
            //Check left
            bool checkLeft = !(colItem + 2 < totalCol && !boardGame[layerItem, rowItem, colItem + 2].Equals('.'));
            
            if (colItem + 2 < totalCol && !boardGame[layerItem, rowItem + 1, colItem + 2].Equals('.'))
            {
                checkLeft = false;
            }
            
            //Check right
            bool checkRight = !(colItem - 1 > 0 && !boardGame[layerItem, rowItem, colItem - 1].Equals('.'));
            
            if (colItem - 1 > 0 && !boardGame[layerItem, rowItem + 1, colItem - 1].Equals('.'))
            {
                checkRight =  false;
            }

            if (!checkLeft && !checkRight)
            {
                
                return SelectResult.LeftRight;
            }

            
            return SelectResult.Success;
        }
        
        public static BlockLeftRightType GetTypeBlockLeftRight(PositionData positionToCheck, char[,,] boardGame)
        {
            var layerItem = positionToCheck.LayerIndex;
            var rowItem = positionToCheck.RowIndex;
            var colItem = positionToCheck.ColumnIndex;
            
            var totalCol = boardGame.GetLength(2);
            
            bool checkLeft = !(colItem + 2 < totalCol && !boardGame[layerItem, rowItem, colItem + 2].Equals('.'));
            
            if (colItem + 2 < totalCol && !boardGame[layerItem, rowItem + 1, colItem + 2].Equals('.'))
            {
                checkLeft = false;
            }

            if (!checkLeft)
            {
                return BlockLeftRightType.Left;
            }
            
            //Check right
            bool checkRight = !(colItem - 1 > 0 && !boardGame[layerItem, rowItem, colItem - 1].Equals('.'));
            
            if (colItem - 1 > 0 && !boardGame[layerItem, rowItem + 1, colItem - 1].Equals('.'))
            {
                checkRight =  false;
            }
            
            if (!checkRight)
            {
                return BlockLeftRightType.Right;
            }
            
            return BlockLeftRightType.None;
        }


        public static int GetSortingOrder(PositionData positionData)
        {
            return positionData.ColumnIndex + positionData.RowIndex + positionData.LayerIndex * 100;
        }
    }
    
    
    public enum SelectResult
    {
        Success,
        Failure,
        Layer,
        LeftRight,
    }

    public enum BlockLeftRightType
    {
        None,
        Left,
        Right,
    }
}