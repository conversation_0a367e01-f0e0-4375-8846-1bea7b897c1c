using b100SDK.Scripts.Base;
using UnityEngine;

namespace MahjongGame.Scripts.Gameplay.Controller
{
    public class CameraController : BhMonoBehavior
    {
        [Space]
        [SerializeField]
        private Camera cam;
        
        [SerializeField]
        private float tutorialCameraSize = 18f;

        [SerializeField]
        private float cameraMinSize = 8f;

        [SerializeField]
        private float rateScreenX = .9f;
        
        [SerializeField]
        private float rateScreenY = .6f;


        private ItemController _itemController;
        private GameController _gameController;

        public Camera Cam => cam;

        private void Start()
        {
            _gameController = GameController.Instance;
            _itemController = _gameController.ItemController;
        }


        /// <summary>
        /// Set camera size so that all items are contained in the camera
        /// </summary>
        public void Init()
        {
            var bounds = _itemController.GetBounds();

            float screenRatio = (float)Screen.width * rateScreenX / (Screen.height * rateScreenY);
            float targetRatio = bounds.size.x / bounds.size.y;
            float orthographicSize = bounds.size.y / 2;

            var correctOrthographicSize = orthographicSize / rateScreenY;

            if (screenRatio >= targetRatio)
            {
                cam.orthographicSize = Mathf.Max(correctOrthographicSize, cameraMinSize);
            }
            else
            {
                float differenceInSize = targetRatio / screenRatio;
                cam.orthographicSize = Mathf.Max(orthographicSize * differenceInSize / rateScreenY, cameraMinSize);
            }

            if (GameController.Instance.IsTutorialDoing)
            {
                cam.orthographicSize = tutorialCameraSize;
            }

            cam.orthographicSize = correctOrthographicSize;

            cam.transform.position = new Vector3(bounds.center.x, bounds.center.y, cam.transform.position.z);
        }
    }
}