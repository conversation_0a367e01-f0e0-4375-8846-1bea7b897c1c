using System;
using System.Collections.Generic;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.UI.Panel;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Vibration;
using MahjongGame.Scripts.Gameplay.Item;
using Sirenix.OdinInspector;

namespace MahjongGame.Scripts.Gameplay.Controller
{
    public class UndoController : BhMonoBehavior
    {
        private ItemController _itemController;
        
        private GameplayController _gameplayController;
        
        private ScoreController _scoreController;
        
        private ComboController _comboController;
        private HintController _hintController;


        
        private Stack<UndoData> _undoData = new Stack<UndoData>();

        private void OnEnable()
        {
            if (Evm)
            {
                Evm.OnUndo.AddListener(Undo);
            }
        }

        private void OnDisable()
        {
            if (Evm)
            {
                Evm.OnUndo.RemoveListener(Undo);
            }
        }

        private void Start()
        {
            _itemController = GameController.Instance.ItemController;
            _gameplayController = GameController.Instance.GameplayController;
            _scoreController = GameController.Instance.ScoreController;
            _comboController = GameController.Instance.ComboController;
            _hintController = GameController.Instance.HintController;
        }


        public void Init()
        {
            _undoData.Clear();
            Evm.OnUndoChange.Dispatch(0);
        }
        
        public void AddUndoData(MahjongItem item1, MahjongItem item2, int score)
        {
            var undoData = new UndoData()
            {
                item1 = item1.Data,
                item2 = item2.Data,
                score = score
            };

            _undoData.Push(undoData);
            Evm.OnUndoChange.Dispatch(_undoData.Count);
        }


        [Button]
        void Undo()
        {
            if (!CanUndo())
            {
                BhDebug.LogError("No item to undo");
                return;
            }
            
            _hintController.CloseHint(false);

            var data = _undoData.Pop();

            if (data == null)
            {
                BhDebug.LogError("Cant undo: data is null");
                return;
            }

            // Spawn item
            _itemController.SpawnItem(data.item1);
            _itemController.SpawnItem(data.item2);
            

            // Add data to board game
            _gameplayController.SetData(data.item1);
            _gameplayController.SetData(data.item2);

            Evm.OnUndoFinish.Dispatch();

            _scoreController.DecreaseScore(data.score);

            _comboController.ResetCombo();
            
            Evm.OnUndoChange.Dispatch(_undoData.Count);

            //TODO: Tutorial
            /*if (_isTutorialDoing || Gm.data.setting.hideTileCantSelect)
            {
                Evm.OnUpdateColorMahjongByStatus.Dispatch(true);
            }*/


            // Journey
            /*
            if (CurrentGameMode == GameMode.Journey)
            {
                /*if (_normalTargets.Contains(data.item1.type))
                {
                    if (PlayScreenJourney.Instance)
                    {
                        PlayScreenJourney.Instance.IncreaseItem(data.item1MahjongType, GetMahjongSpriteIndex(data.item1.type), 2);
                    }
                }

                if (_normalFlip.Contains(data.item1.type))
                {
                    if (PlayScreenJourney.Instance)
                    {
                        PlayScreenJourney.Instance.IncreaseItem(data.item1MahjongType, GetMahjongSpriteIndex(data.item1.type), 2);
                    }
                }

                if (SpecialMahjongType.HasValue && SpecialMahjongType.Value == data.item1.type)
                {
                    if (PlayScreenJourney.Instance)
                    {
                        PlayScreenJourney.Instance.IncreaseItem(data.item1MahjongType, GetMahjongSpriteIndex(data.item1.type), 2);
                    }
                }

                if (SpecialItemType.HasValue && SpecialItemType.Value == data.item1.type)
                {
                    if (PlayScreenJourney.Instance)
                    {
                        PlayScreenJourney.Instance.IncreaseItem(data.item1MahjongType, GetMahjongSpriteIndex(data.item1.type), 2);
                    }
                }#1#
            }*/
        }

        bool CanUndo()
        {
            return _undoData.Count > 0;
        }
    }

    [Serializable]
    public class UndoData
    {
        public MahjongData item1;
        public MahjongData item2;
        public int score;
    }
}