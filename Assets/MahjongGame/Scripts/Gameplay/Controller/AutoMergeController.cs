using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities.Extensions;
using MahjongGame.Scripts.Gameplay.Item;
using Sirenix.OdinInspector;
using UnityEngine;

namespace MahjongGame.Scripts.Gameplay.Controller
{
    public class AutoMergeController : BhMonoBehavior
    {
        [SerializeField]
        private float selectInterval = .6f;
        
        
        private ItemController _itemController;
        private GameplayController _gameplayController;

        private void Start()
        {
            _itemController = GameController.Instance.ItemController;
        }

        [Button]
        public async void StartTest()
        {
            if (_itemController.Items.Count <= 0)
                return;
            
            while (_itemController.PairCount > 0) 
            {
                var allItemLeft = _itemController.Items.Clone();

                var allItemCanSelect = new List<MahjongItem>();

                foreach (var item in allItemLeft)
                {
                    if (GameUtilities.CheckCanSelectItem(item.PositionData, _gameplayController.BoardGame) == SelectResult.Success)
                    {
                        allItemCanSelect.Add(item);
                    }
                }

                var elementCountMap = new Dictionary<char, int>();

                foreach (var item in allItemCanSelect)
                {
                    if (elementCountMap.ContainsKey(item.Data.LogicType))
                    {
                        elementCountMap[item.Data.LogicType]++;
                    }
                    else
                    {
                        elementCountMap.TryAdd(item.Data.LogicType, 1);
                    }
                }

                var randomTypeArray = elementCountMap.Where(x => x.Value >= 2).ToArray();

                if (randomTypeArray.Length <= 0)
                    return;
                
                var randomType = randomTypeArray.GetRandom().Key;

                var items = allItemCanSelect.Where(x => x.Data.LogicType == randomType).ToList();
                
                if (items.Count < 2)
                    return;
                
                var item1 = items.GetRandom();
                if (items.Contains(item1))
                {
                    items.Remove(item1);
                }
                
                var item2 = items.GetRandom();
                if (items.Contains(item2))
                {
                    items.Remove(item2);
                }

                MahjongItem selectITem1 = null;
                MahjongItem selectITem2 = null;

                /*if (item1)
                {
                    item2.SetStatusHint(true);
                    selectITem1 = item1;
                }

                if (item2)
                {
                    item2.SetStatusHint(true);
                    selectITem2 = item2;
                }*/

                if (selectITem1 && selectITem2)
                {
                    selectITem1.Select();
                    selectITem2.Select();
                }
                
                await Task.Delay((int) selectInterval * 1000);
            }
            
            /*await Task.Delay(3000);

            if (PopupLose.Instance)
            {
                PopupLose.Instance.Shuffle();
            }
            
            await Task.Delay(3000);

            
            StartTest();*/
        }
        
        
    }
}