using System;
using System.Collections.Generic;
using System.Linq;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Extensions;
using MahjongGame.Scripts.Gameplay.Data;
using MahjongGame.Scripts.Gameplay.Item;

namespace MahjongGame.Scripts.Gameplay.Controller
{
    public class ShuffleHandler
    { 
        public static List<MahjongData> Shuffle(List<MahjongData> items, char[,,] boardGame, int maxShuffleOnFail = 20)
        {
            if (items.Count <= 0)
                return null;

            var itemLeft = items.Select(x => x.PositionData).ToList();

            var itemCanSelect = GetItemCanSelect(itemLeft, boardGame);

            if (itemCanSelect.Count < 2)
            {
                return null;
            }
            else
            {
                var shuffleCount = 0;

                var shuffleResult = HandleShuffle(items, boardGame);
                
                while (shuffleResult == null && shuffleCount <= maxShuffleOnFail)
                {
                    shuffleCount++;
                    shuffleResult = HandleShuffle(items, boardGame);
                }
                
                return shuffleResult;
            }
        }

        
        
        static List<MahjongData> HandleShuffle(List<MahjongData> allMahjongDatas, char[,,] boardGame)
        {
            var mahjongDataCalculate = allMahjongDatas.Clone();
            
            var shuffleTypeMap = new List<ShuffleData>();

            foreach (var mahjongData in allMahjongDatas)
            {
                var newShuffleData = new ShuffleData()
                {
                    tye = mahjongData.LogicType,
                    visualData = new VisualData(mahjongData.VisualData.IndexSprite, mahjongData.VisualData.Type)
                };
                
                shuffleTypeMap.Add(newShuffleData);
            }
            
            //Handle type left
            var typeLeft = mahjongDataCalculate.Select(x => x.LogicType).ToList();

            if (typeLeft.Count % 2 != 0)
            {
                BhDebug.LogError("Odd type left");
                return null;
            }

            var typeDistinct = typeLeft.Distinct().ToList();

            var typeCountMap = new Dictionary<char, int>();

            foreach (var type in typeDistinct)
            {
                typeCountMap.TryAdd(type, 0);
            }

            foreach (var type in typeLeft)
            {
                typeCountMap[type]++;
            }

            var typeCouple = new List<char>();

            foreach (var typeCount in typeCountMap)
            {
                for (int i = 0; i < typeCount.Value / 2; i++)
                {
                    typeCouple.Add(typeCount.Key);
                }
            }

            typeCouple.Shuffle(null);


            var typeSortByCouple = new List<char>();

            foreach (var couple in typeCouple)
            {
                typeSortByCouple.Add(couple);
                typeSortByCouple.Add(couple);
            }


            //Handle item left
            var itemCanNotSelect = new List<PositionData>();

            //var itemInBound = GetItemInBound(itemDataCalculate);
            var itemCanSelect = GetItemCanSelect(mahjongDataCalculate.Select(x => x.PositionData).ToList(), boardGame);

            if (itemCanSelect != null && itemCanSelect.Count > 0)
            {
                itemCanNotSelect = mahjongDataCalculate.Select(x => x.PositionData)
                    .Where(x => !itemCanSelect.Contains(x)).ToList();
            }
            else
            {
                itemCanNotSelect = mahjongDataCalculate.Select(x => x.PositionData).ToList();
            }

            
            
            //Create new data
            var result = new List<MahjongData>();

            if (itemCanSelect != null && itemCanSelect.Count > 0)
            {
               // BhDebug.Log("Item in bound count: " + itemCanSelect.Count);
                
                foreach (var item in itemCanSelect)
                {
                    if (typeSortByCouple.Count > 0)
                    {
                        var type = typeSortByCouple[0];
                        typeSortByCouple.RemoveAt(0);

                        var shuffleData = shuffleTypeMap.FirstOrDefault(x => x.tye == type);

                        if (shuffleData != null)
                        {
                            var newMahjongData = new MahjongData(item, type, shuffleData.visualData);
                            result.Add(newMahjongData);
                            shuffleTypeMap.Remove(shuffleData);
                        }
                        else
                        {
                            //BhDebug.Log("null data shuffle visual");
                        }
                    }
                }
            }
            
            //BhDebug.Log("Item NOT in bound count: " + itemCanNotSelect.Count);

            typeSortByCouple.Shuffle(null);

            foreach (var item in itemCanNotSelect)
            {
                if (typeSortByCouple.Count > 0)
                {
                    var type = typeSortByCouple[0];
                    typeSortByCouple.RemoveAt(0);
                    
                    var shuffleData = shuffleTypeMap.FirstOrDefault(x => x.tye == type);

                    if (shuffleData != null)
                    {
                        var newMahjongData = new MahjongData(item, type, shuffleData.visualData);
                        result.Add(newMahjongData);
                    }
                }
            }


            if (CountPairOfTile(result, boardGame) > 0)
            {
                return result;
            }

            return null;
        }


        static List<PositionData> GetItemInBound(List<PositionData> items)
        {
            var result = new List<PositionData>();
            
            Dictionary<int, List<PositionData>> itemPerLayerMap = new();

            foreach (var item in items)
            {
                if (itemPerLayerMap.ContainsKey(item.LayerIndex))
                {
                    itemPerLayerMap[item.LayerIndex].Add(item);
                }
                else
                {
                    itemPerLayerMap.TryAdd(item.LayerIndex, new List<PositionData>()
                    {
                        item
                    });
                }
            }

            var keys = itemPerLayerMap.Keys.ToList();

            if (keys.Count <= 0)
                return null;
            
            keys.Sort((a, b) => b.CompareTo(a));


            foreach (var key in keys)
            {
                var groupLines = itemPerLayerMap[key].GroupBy(x => x.RowIndex);
                
                foreach (var groupLine in groupLines)
                {
                    if (groupLine.Count() > 1)
                    {
                        result.Add(groupLine.FirstOrDefault(x => x.ColumnIndex == groupLine.Min(item => item.ColumnIndex)));
                        result.Add(groupLine.FirstOrDefault(x => x.ColumnIndex == groupLine.Max(item => item.ColumnIndex)));
                    }
                    else if (groupLine.Count() == 1)
                    {
                        result.Add(groupLine.SingleOrDefault());
                    }
                }
            }
            
            return result;
        }

        static List<PositionData> GetItemCanSelect(List<PositionData> items, char[,,] boardGame)
        {
            var result = new List<PositionData>();

            foreach (var item in items)
            {
                if (GameUtilities.CheckCanSelectItem(item, boardGame) == SelectResult.Success)
                {
                    result.Add(item);
                }
            }
            
            return result;
        }
        
        
       static int CountPairOfTile(List<MahjongData> mahjongDatas, char [,,] boardGame)
        {
            var result = 0;

            var allItemLeft = mahjongDatas.Clone();

            var allItemCanSelect = new List<MahjongData>();

            var calculateBoard = CloneBoard(boardGame);

            foreach (var mahjongData in mahjongDatas)
            {
                var itemData = mahjongData.PositionData;
                
                calculateBoard[itemData.LayerIndex, itemData.RowIndex, itemData.ColumnIndex] = mahjongData.LogicType;
                calculateBoard[itemData.LayerIndex, itemData.RowIndex + 1, itemData.ColumnIndex] =  mahjongData.LogicType;
                calculateBoard[itemData.LayerIndex, itemData.RowIndex, itemData.ColumnIndex + 1] = mahjongData.LogicType;
                calculateBoard[itemData.LayerIndex, itemData.RowIndex + 1, itemData.ColumnIndex + 1] =  mahjongData.LogicType;
            }

            foreach (var item in allItemLeft)
            {
                if (GameUtilities.CheckCanSelectItem(item.PositionData, calculateBoard) == SelectResult.Success)
                {
                    allItemCanSelect.Add(item);
                }
            }

            var elementCountMap = new Dictionary<char, int>();

            foreach (var item in allItemCanSelect)
            {
                if (elementCountMap.ContainsKey(item.LogicType))
                {
                    elementCountMap[item.LogicType]++;
                }
                else
                {
                    elementCountMap.TryAdd(item.LogicType, 1);
                }
            }

            foreach (var pair in elementCountMap)
            {
                int count = pair.Value;
                result += count / 2;
            }

            return result;
        }
        
        
        


        
        
        static char[,,] CloneBoard(char[,,] original)
        {
            int dim1 = original.GetLength(0);
            int dim2 = original.GetLength(1);
            int dim3 = original.GetLength(2);

            char[,,] clone = new char[dim1, dim2, dim3];

            for (int i = 0; i < dim1; i++)
            {
                for (int j = 0; j < dim2; j++)
                {
                    for (int k = 0; k < dim3; k++)
                    {
                        clone[i, j, k] = original[i, j, k];
                    }
                }
            }

            return clone;
        }
    }


    [Serializable]
    public class ShuffleData
    {
        public char tye;
        public VisualData visualData;
    }
}