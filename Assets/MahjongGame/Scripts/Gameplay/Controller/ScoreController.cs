using b100SDK.Scripts.Base;
using UnityEngine;

namespace MahjongGame.Scripts.Gameplay.Controller
{
    public class ScoreController : BhMonoBehavior
    {
        [Header("Score")]
        [SerializeField]
        private int maxScorePerPair = 200;
        
        [SerializeField]
        private int minScorePair = 100;

        [SerializeField]
        private float scoreDecreaseRate = 0.14f;

        [SerializeField]
        private int scoreCombo = 20;
        
        /*[Space]
        [SerializeField]
        private ScoreItem scoreItemPrefab;*/

        [SerializeField]
        private float offsetScoreY = .5f;
        
        private int _score;
        
        int Score
        {
            get => _score;
            
            set
            {
                _score = value;
                Evm.OnScoreChange.Dispatch(_score);
                //BhDebug.Log("Current Score: " + _score);
            }
        }
        
        public int TotalScore { get; private set; }
        public int CurrentScoreAdd { get;private set; }



        public void Init(int totalPairInLevel)
        {
            Score = 0;
            TotalScore = totalPairInLevel * maxScorePerPair + totalPairInLevel *
                (scoreCombo + Mathf.Clamp(totalPairInLevel, 0, int.MaxValue) * scoreCombo / 2);

            ResetCurrentScoreAdd();
        }

        public void ResetCurrentScoreAdd()
        {
            CurrentScoreAdd = maxScorePerPair;
        }

        public void DecreaseCurrentScoreAdd()
        {
            CurrentScoreAdd -= Mathf.FloorToInt(CurrentScoreAdd * scoreDecreaseRate);
            CurrentScoreAdd = Mathf.Clamp(CurrentScoreAdd, minScorePair, int.MaxValue);
        }

        public int AddScore(int comboCount = 0)
        {
            CurrentScoreAdd += (comboCount - 1) * scoreCombo;
            Score += CurrentScoreAdd;

            var addScore = CurrentScoreAdd;
            
            //BhDebug.Log("CurrentScoreAdd: " + CurrentScoreAdd);
            
            ResetCurrentScoreAdd();
            
            return addScore;
        }

        public void SpawnScoreItem(Vector3 mergePosition)
        {
            /*var position = mergePosition + new Vector3(0, offsetScoreY, 0);
            var item = scoreItemPrefab.Spawn(position, scoreItemPrefab.transform.rotation, Pool.Score);
            item.Init(CurrentScoreAdd);*/
        }

        public void DecreaseScore(int value)
        {
            Score -= value;
            ResetCurrentScoreAdd();
        }

    }
}