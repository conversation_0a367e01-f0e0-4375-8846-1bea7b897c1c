using b100SDK.Scripts.Base;
using b100SDK.Scripts.Tutorial;

namespace MahjongGame.Scripts.Gameplay.Controller
{
    public class TutorialController : BhMonoBehavior
    {
        private bool _isTutorialDoing = false;
        private bool _isShowingHandTutorials = false;
        
        void ShowHandTutorial(int index = 0)
        {
            /*if (_isTutorialDoing && Gm.data.user.stepTutorial == 1)
            {
                var item = _items[index].transform;
                TutorialManager.Instance.ShowHandTap(cam.WorldToScreenPoint(item.position));
                _isShowingHandTutorials = true;
            }*/
        }

        void ShowHandHint()
        {
            /*var canShowHint = (bool)Gm.remoteConfigData.remoteConfigMap[RemoteConfigType.IsShowTutorialStartGame];
            
            if (!canShowHint)
                return;
            
            if (!Gm.data.user.isCompleteTutorialHint && Gm.data.user.level == 2)
            {
                TutorialManager.Instance.ShowHandTap(
                    PlayScreen.Instance
                        ? PlayScreen.Instance.HintPositionButton
                        : PlayScreenJourney.Instance.HintPositionButton, true, true);
                _isShowingHandTutorials = true;

                Gm.data.user.isCompleteTutorialHint = true;
                
                Gm.data.user.stepTutorial = 4;
                
                if (PlayScreen.Instance)
                {
                    PlayScreen.Instance.ShowTutorial();
                }

            }*/
        }

        void ShowHandShuffle()
        {
            /*var canShowHint = (bool)Gm.remoteConfigData.remoteConfigMap[RemoteConfigType.IsShowTutorialStartGame];
            
            if (!canShowHint)
                return;
            
            if (!Gm.data.user.isCompleteTutorialShuffle && Gm.data.user.level == 3)
            {
                TutorialManager.Instance.ShowHandTap(PlayScreen.Instance
                    ? PlayScreen.Instance.ShufflePositionButton
                    : PlayScreenJourney.Instance.ShufflePositionButton, true, true);
                _isShowingHandTutorials = true;
                
                Gm.data.user.isCompleteTutorialShuffle = true;
                
                Gm.data.user.stepTutorial = 5;
                
                if (PlayScreen.Instance)
                {
                    PlayScreen.Instance.ShowTutorial();
                }
            }*/
        }

        void HideHandTutorial()
        {
            if (_isTutorialDoing)
            {
                TutorialManager.Instance.Hide();
                _isShowingHandTutorials = false;
            }
        }

        void HideTutorial()
        {
            if (!_isShowingHandTutorials)
                return;
            
            TutorialManager.Instance.Hide();
            _isShowingHandTutorials = false;


        }
    }
}