using System.Collections.Generic;
using System.Linq;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities.Extensions;
using b100SDK.Scripts.Vibration;
using MahjongGame.Scripts.Gameplay.Item;
using Sirenix.OdinInspector;

namespace MahjongGame.Scripts.Gameplay.Controller
{
    public class HintController : BhMonoBehavior
    {
        private ItemController _itemController;
        private GameplayController _gameplayController;
        
        
        private MahjongItem _hintItem1;
        private MahjongItem _hintItem2;

        private void OnEnable()
        {
            if (Evm)
            {
                Evm.OnHint.AddListener(Hint);
            }
        }

        private void OnDisable()
        {
            if (Evm)
            {
                Evm.OnHint.RemoveListener(Hint);
            }
        }


        private void Start()
        {
            _itemController = GameController.Instance.ItemController;
            _gameplayController = GameController.Instance.GameplayController;
        }

        [Button]
        void Hint()
        {
            CloseHint(false);
            
            //AudioAssistant.Shot(TypeSound.HintUse);
            //BhVibrate.ContinuousHaptic();

            var allItemLeft = _itemController.Items.Clone();

            var allItemCanSelect = new List<MahjongItem>();

            foreach (var item in allItemLeft)
            {
                if (GameUtilities.CheckCanSelectItem(item.PositionData, _gameplayController.BoardGame) == SelectResult.Success)
                {
                    allItemCanSelect.Add(item);
                }
            }
            
            var elementCountMap = new Dictionary<char, int>();

            foreach (var item in allItemCanSelect)
            {
                if (elementCountMap.ContainsKey(item.Data.LogicType))
                {
                    elementCountMap[item.Data.LogicType]++;
                }
                else
                {
                    elementCountMap.TryAdd(item.Data.LogicType, 1);
                }
            }

            var randomTypeArray = elementCountMap.Where(x => x.Value >= 2).ToArray();

            if (randomTypeArray.Length <= 0)
                return;

            var randomType = randomTypeArray.GetRandom().Key;

            var items = allItemCanSelect.Where(x => x.Data.LogicType == randomType).ToList();

            if (items.Count < 2)
                return;

            _hintItem1 = items.GetRandom();
            if (items.Contains(_hintItem1))
            {
                items.Remove(_hintItem1);
            }

            _hintItem2 = items.GetRandom();
            if (items.Contains(_hintItem2))
            {
                items.Remove(_hintItem2);
            }

            if (_hintItem1)
            {
                _hintItem1.ShowHint(true);
            }

            if (_hintItem2)
            {
                _hintItem2.ShowHint(true);
            }
        }

        public void CloseHint(bool isSelect, MahjongItem item = null)
        {
            if (item != null)
            {
                if (item == _hintItem1)
                {
                    if (!isSelect)
                    {
                        _hintItem1.HideHint();
                    }
                    _hintItem1 = null;
                }
                else if (item == _hintItem2)
                {
                    if (!isSelect)
                    {
                        _hintItem2.HideHint();
                    }
                    _hintItem2 = null;
                }
            }
            else
            {
                if (_hintItem1)
                {
                    if (!isSelect)
                    {
                        _hintItem1.HideHint();
                    }
                    _hintItem1 = null;
                }

                if (_hintItem2)
                {
                    if (!isSelect)
                    {
                        _hintItem2.HideHint();
                    }
                    _hintItem2 = null;
                }
            }
        }
    }
}