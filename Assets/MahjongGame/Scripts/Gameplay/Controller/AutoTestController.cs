using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Extensions;
using MahjongGame.Scripts.Gameplay.Item;
using Sirenix.OdinInspector;

namespace MahjongGame.Scripts.Gameplay.Controller
{
    public class AutoTestController : BhMonoBehavior
    {
        private ItemController _itemController;
        private GameplayController _gameplayController;

        private void OnEnable()
        {
            if (Evm)
            {
                Evm.AutoTest.AddListener(StartTest);
            }
        }

        private void OnDisable()
        {
            if (Evm)
            {
                Evm.AutoTest.RemoveListener(StartTest);
            }
        }

        private void Start()
        {
            _itemController = GameController.Instance.ItemController;
            _gameplayController = GameController.Instance.GameplayController;
        }

        [Button]
        async void StartTest()
        {
            if (_itemController.Items.Count <= 0)
                return;
            
            while (_itemController.PairCount > 0) 
            {
                var allItemLeft = _itemController.Items.Clone();

                var allItemCanSelect = new List<MahjongItem>();

                foreach (var item in allItemLeft)
                {
                    if (_gameplayController.CheckCanSelect(item) == SelectResult.Success)
                    {
                        allItemCanSelect.Add(item);
                    }
                }

                var elementCountMap = new Dictionary<char, int>();

                foreach (var item in allItemCanSelect)
                {
                    if (elementCountMap.ContainsKey(item.Data.LogicType))
                    {
                        elementCountMap[item.Data.LogicType]++;
                    }
                    else
                    {
                        elementCountMap.TryAdd(item.Data.LogicType, 1);
                    }
                }

                var randomTypeArray = elementCountMap.Where(x => x.Value >= 2).ToArray();

                if (randomTypeArray.Length <= 0)
                    return;
                
                var randomType = randomTypeArray.GetRandom().Key;

                var items = allItemCanSelect.Where(x => x.Data.LogicType == randomType).ToList();
                
                if (items.Count < 2)
                    return;

                var _hintItem1 = items.GetRandom();
                if (items.Contains(_hintItem1))
                {
                    items.Remove(_hintItem1);
                }
                
                var _hintItem2 = items.GetRandom();
                if (items.Contains(_hintItem2))
                {
                    items.Remove(_hintItem2);
                }

                MahjongItem selectITem1 = null;
                MahjongItem selectITem2 = null;

                if (_hintItem1)
                {
                    _hintItem1.HideHint(true);
                    
                    BhDebug.Log("Select item 1");

                    selectITem1 = _hintItem1;
                    //await Task.Delay(500);

                }

                if (_hintItem2)
                {
                    _hintItem2.HideHint(true);

                    selectITem2 = _hintItem2;
                    
                }

                if (selectITem1 && selectITem2)
                {
                    selectITem1.Select();
                    selectITem2.Select();
                }



                await Task.Delay(600);
            }
            
            /*await Task.Delay(3000);

            if (PopupLose.Instance)
            {
                PopupLose.Instance.Shuffle();
            }
            
            await Task.Delay(3000);

            
            StartTest();*/
        }
    }
}