using System;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities;
using MahjongGame.Scripts.Gameplay.Data;
using MahjongGame.Scripts.Gameplay.Item;
using UnityEngine;

namespace MahjongGame.Scripts.Gameplay.Controller
{
    public class GameplayController : BhMonoBehavior
    {
        private ItemController _itemController;
        private UndoController _undoController;
        private ScoreController _scoreController;
        private ComboController _comboController;
        private MergeController _mergeController;
        private HintController _hintController;

        private Action _onWin;
        
        [SerializeField]
        private LevelData _levelData;
        
        private MahjongItem _currentSelectItem1;
        private MahjongItem _currentSelectItem2;

        public LevelData LevelData => _levelData;
        public char[,,] BoardGame => _levelData.boardGame;
        
        
        public bool IsLose { get; set; }


        private void Start()
        {
            _undoController = GameController.Instance.UndoController;
            _scoreController = GameController.Instance.ScoreController;
            _comboController = GameController.Instance.ComboController;
            _mergeController = GameController.Instance.MergeController;
            
            _itemController = GameController.Instance.ItemController;
            _hintController = GameController.Instance.HintController;
        }


        public void Init(Action onWin)
        {
            //TODO: create data for mode

            _onWin = onWin;

            _levelData = DataManager.Instance.GetDataFromDataRaw();

            _currentSelectItem1 = null;
            _currentSelectItem2 = null;
        }


        public void SetData(MahjongData data)
        {
            var layerCount = _levelData.boardGame.GetLength(0);
            int rowCount = _levelData.boardGame.GetLength(1);
            int columnCount = _levelData.boardGame.GetLength(2);
            
            int layerIndex = data.PositionData.LayerIndex;
            int rowIndex = data.PositionData.RowIndex;
            int columnIndex = data.PositionData.ColumnIndex;

            if (layerIndex >= 0 && layerIndex < layerCount && rowIndex >= 0 && rowIndex + 1 < rowCount &&
                columnIndex >= 0 && columnIndex + 1 < columnCount)
            {
                _levelData.boardGame[layerIndex, rowIndex, columnIndex] = data.LogicType;
                _levelData.boardGame[layerIndex, rowIndex + 1, columnIndex] = data.LogicType;
                _levelData.boardGame[layerIndex, rowIndex, columnIndex + 1] = data.LogicType;
                _levelData.boardGame[layerIndex, rowIndex + 1, columnIndex + 1] = data.LogicType;
                
                _levelData.items.Add(data);
            }
        }

        public void RemoveData(MahjongData data)
        {
            var layerCount = _levelData.boardGame.GetLength(0);
            int rowCount = _levelData.boardGame.GetLength(1);
            int columnCount = _levelData.boardGame.GetLength(2);
            
            int layerIndex = data.PositionData.LayerIndex;
            int rowIndex = data.PositionData.RowIndex;
            int columnIndex = data.PositionData.ColumnIndex;

            if (layerIndex >= 0 && layerIndex < layerCount && rowIndex >= 0 && rowIndex + 1 < rowCount &&
                columnIndex >= 0 && columnIndex + 1 < columnCount)
            {
                _levelData.boardGame[layerIndex, rowIndex, columnIndex] = '.';
                _levelData.boardGame[layerIndex, rowIndex + 1, columnIndex] = '.';
                _levelData.boardGame[layerIndex, rowIndex, columnIndex + 1] = '.';
                _levelData.boardGame[layerIndex, rowIndex + 1, columnIndex + 1] = '.';
                
                _levelData.items.Remove(data);
            }
        }

        public SelectResult CheckCanSelect(PositionData data)
        {
            return GameUtilities.CheckCanSelectItem(data, _levelData.boardGame);
        }

        public SelectResult CheckCanSelect(MahjongItem item)
        {
            return CheckCanSelect(item.PositionData);
        }

        public BlockLeftRightType GetBlockLeftRightType(PositionData data)
        {
            return GameUtilities.GetTypeBlockLeftRight(data, _levelData.boardGame);
        }
        
        public BlockLeftRightType GetBlockLeftRightType(MahjongItem item)
        {
            return GameUtilities.GetTypeBlockLeftRight(item.PositionData, _levelData.boardGame);
        }
        
        
        
        
        public void SelectItem(MahjongItem item)
        {
            if (!_currentSelectItem1)
            {
                _currentSelectItem1 = item;
                
                _hintController.CloseHint(true, item);
            }
            else
            {
                if (!_currentSelectItem2)
                {
                    _currentSelectItem2 = item;
                    CheckValue();   
                }
            }
        }

        public void DeselectItem(MahjongItem item)
        {
            if (_currentSelectItem1 == item)
            {
                _currentSelectItem1 = null;
            }
            
            if (_currentSelectItem2 == item)
            {
                _currentSelectItem2 = null;
            }
        }


        void CheckValue()
        {
            if (!_currentSelectItem1 || !_currentSelectItem2)
                return;
            
            if (_currentSelectItem1.Data.LogicType.Equals(_currentSelectItem2.Data.LogicType))
            {
                
                
                //Start
                
                RemoveData(_currentSelectItem1.Data);
                RemoveData(_currentSelectItem2.Data);
                
                _itemController.RemoveItem(_currentSelectItem1);
                _itemController.RemoveItem(_currentSelectItem2);

                _comboController.IncreaseCombo(); 
                
                var currentScore = _scoreController.AddScore(_comboController.CurrentComboCount);
                
                _undoController.AddUndoData(_currentSelectItem1, _currentSelectItem2, currentScore);
                
                _mergeController.MergerItem(_currentSelectItem1, _currentSelectItem2);
                


                _currentSelectItem1 = null;
                _currentSelectItem2 = null;

                _itemController.UpdatePairCount();

                if (_itemController.PairCount <= 0)
                {
                    if (_itemController.Items.Count <= 0)
                    {
                        _onWin?.Invoke();
                    }
                    else
                    {
                        Revival();
                    }
                }
            }
            else
            {
                ResetMahjongSelect();
            }
            

            void ResetMahjongSelect()
            {
                _comboController.ResetCombo();
                _scoreController.DecreaseCurrentScoreAdd();

                //var cacheItem2 = _itemController.Items.IndexOf(_currentSelectItem2);
                var cacheItem2 = _currentSelectItem2;
                
                _currentSelectItem1.Deselect();
                _currentSelectItem2.Deselect();

                /*if (cacheItem2 >= 0 && cacheItem2 < _itemController.Items.Count)
                {
                    _itemController.Items[cacheItem2].Select(true);
                }*/

                if (cacheItem2)
                {
                    cacheItem2.Select(true);
                }
            }
        }



        void Revival()
        {
            BhDebug.Log("Revival");
        }
        
    }
}