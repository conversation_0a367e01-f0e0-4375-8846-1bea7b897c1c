using System.Collections.Generic;
using System.Linq;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities.Extensions;
using GenerateData.Scripts.Configs;
using MahjongGame.Scripts.Gameplay.Data;
using MahjongGame.Scripts.Gameplay.Item;
using UnityEngine;
using CollectionExtensions = b100SDK.Scripts.Utilities.Extensions.CollectionExtensions;

namespace MahjongGame.Scripts.Gameplay.Controller
{
    public class ShuffleCountCalculate : BhMonoBehavior
    {
        public static (float, float) Calculate(LevelData levelData)
        {
            int shuffleCount = 0;
            int shuffleTotal = 0;

            for (int i = 0; i < 100; i++)
            {
                var shuffleCountInCheck = 0;

                var newItems = new List<MahjongData>();

                foreach (var mahjongData in levelData.items)
                {
                    var positionData = new PositionData(mahjongData.PositionData.LayerIndex,
                        mahjongData.PositionData.RowIndex, mahjongData.PositionData.ColumnIndex);
                    var visualData = new VisualData(mahjongData.VisualData.IndexSprite, mahjongData.VisualData.Type);
                    var newMahjongData = new MahjongData(positionData, mahjongData.LogicType, visualData);
                    
                    newItems.Add(newMahjongData);
                }
                
                var data = new LevelData()
                {
                    boardGame = CollectionExtensions.Clone(levelData.boardGame),
                    hasSeason = levelData.hasSeason,
                    hasFlower = levelData.hasFlower,
                    items = newItems
                };

                while (shuffleCountInCheck < 5)
                {
                    var allItemLeft = data.items.Clone();

                    var allDataCanSelect = new List<MahjongData>();

                    foreach (var item in allItemLeft)
                    {
                        if (GameUtilities.CheckCanSelectItem(item.PositionData, data.boardGame) ==
                            SelectResult.Success)
                        {
                            allDataCanSelect.Add(item);
                        }
                    }

                    var elementCountMap = new Dictionary<char, int>();

                    foreach (var item in allDataCanSelect)
                    {
                        if (elementCountMap.ContainsKey(item.LogicType))
                        {
                            elementCountMap[item.LogicType]++;
                        }
                        else
                        {
                            elementCountMap.TryAdd(item.LogicType, 1);
                        }
                    }

                    var randomTypeArray = elementCountMap.Where(x => x.Value >= 2).ToArray();

                    if (randomTypeArray.Length <= 0)
                    {
                        Shuffle(data);
                        shuffleCountInCheck++;
                        continue;
                    }

                    var randomType = randomTypeArray.GetRandom().Key;

                    var items = allDataCanSelect.Where(x => x.LogicType == randomType).ToList();

                    if (items.Count < 2)
                    {
                        continue;
                    }

                    var item1 = items.GetRandomAndRemove();
                    var item2 = items.GetRandomAndRemove();

                    RemoveData(data, item1);
                    RemoveData(data, item2);

                    if (data.items.Count <= 0)
                    {
                        break;
                    }
                }



                if (shuffleCountInCheck > 0)
                {
                    shuffleCount++;
                    shuffleTotal += shuffleCountInCheck;
                }
            }


            var rate = (float)shuffleCount / 100;
            var shuffleRate = (float)shuffleTotal / 100;


            
            return (rate, shuffleRate);
        }
        
        
        public static (float shuffleRate, float shuffleCountAvg, float minClearStuck, float maxClearStuck) CalculateStuck(LevelData levelData)
        {
            int shuffleCount = 0;
            int shuffleTotal = 0;

            int tileCount = levelData.items.Count;

            float minClearStuck = 0f;
            float maxClearStuck = 1f;
            

            for (int i = 0; i < 100; i++)
            {
                var shuffleCountInCheck = 0;

                var newItems = new List<MahjongData>();

                foreach (var mahjongData in levelData.items)
                {
                    var positionData = new PositionData(mahjongData.PositionData.LayerIndex,
                        mahjongData.PositionData.RowIndex, mahjongData.PositionData.ColumnIndex);
                    var visualData = new VisualData(mahjongData.VisualData.IndexSprite, mahjongData.VisualData.Type);
                    var newMahjongData = new MahjongData(positionData, mahjongData.LogicType, visualData);
                    
                    newItems.Add(newMahjongData);
                }
                
                var data = new LevelData()
                {
                    boardGame = CollectionExtensions.Clone(levelData.boardGame),
                    hasSeason = levelData.hasSeason,
                    hasFlower = levelData.hasFlower,
                    items = newItems
                };

                while (shuffleCountInCheck < 5)
                {
                    var allItemLeft = data.items.Clone();

                    var allDataCanSelect = new List<MahjongData>();

                    foreach (var item in allItemLeft)
                    {
                        if (GameUtilities.CheckCanSelectItem(item.PositionData, data.boardGame) ==
                            SelectResult.Success)
                        {
                            allDataCanSelect.Add(item);
                        }
                    }

                    var elementCountMap = new Dictionary<char, int>();

                    foreach (var item in allDataCanSelect)
                    {
                        if (elementCountMap.ContainsKey(item.LogicType))
                        {
                            elementCountMap[item.LogicType]++;
                        }
                        else
                        {
                            elementCountMap.TryAdd(item.LogicType, 1);
                        }
                    }

                    var randomTypeArray = elementCountMap.Where(x => x.Value >= 2).ToArray();

                    if (randomTypeArray.Length <= 0)
                    {
                        Shuffle(data);
                        shuffleCountInCheck++;

                        if (shuffleCountInCheck == 1)
                        {
                            var tileLeft = data.items.Count;
                            var clearRate = 1f - (float)tileLeft / tileCount;
                            minClearStuck = Mathf.Max(minClearStuck, clearRate);
                            maxClearStuck = Mathf.Min(maxClearStuck, clearRate);
                        }
                        
                        
                        continue;
                    }

                    var randomType = randomTypeArray.GetRandom().Key;

                    var items = allDataCanSelect.Where(x => x.LogicType == randomType).ToList();

                    if (items.Count < 2)
                    {
                        continue;
                    }

                    var item1 = items.GetRandomAndRemove();
                    var item2 = items.GetRandomAndRemove();

                    RemoveData(data, item1);
                    RemoveData(data, item2);

                    if (data.items.Count <= 0)
                    {
                        break;
                    }
                }



                if (shuffleCountInCheck > 0)
                {
                    shuffleCount++;
                    shuffleTotal += shuffleCountInCheck;
                }
            }


            var rate = (float)shuffleCount / 100;
            var shuffleRate = (float)shuffleTotal / 100;


            
            return (rate, shuffleRate, minClearStuck, maxClearStuck);
        }
        
        
        public static bool Evaluate(LevelData levelData, LevelGenerateInfo levelGenerateInfo)
        {
            // Check layout
            Dictionary<int, int> tileCountPerLayer = new Dictionary<int, int>();
            foreach (var item in levelData.items)
            {
                if (tileCountPerLayer.ContainsKey(item.PositionData.LayerIndex))
                {
                    tileCountPerLayer[item.PositionData.LayerIndex]++;
                }
                else
                {
                    tileCountPerLayer.TryAdd(item.PositionData.LayerIndex, 1);
                }
            }

            var size =
                levelData.boardGame.GetLength(1) / 2 * levelData.boardGame.GetLength(2) / 2;

            var maxTileOnALayer = size;
            /*Debug.Log("Max tile on a layer: " + maxTileOnALayer);
            
            Debug.Log("Tile count per layer: " + tileCountPerLayer.Values.Max());*/
            
            var maxFillRate = (float) tileCountPerLayer.Values.Max() / maxTileOnALayer;
            
            

            if (maxFillRate > levelGenerateInfo.maxFillRateLayer)
            {
                //Debug.LogError("Max fill rate layer: " + maxFillRate);
                return false;
            }
            
            var keys = tileCountPerLayer.Keys.ToList();
            keys.Sort();
            
            for (int i = 0; i < keys.Count; i++)
            {
                if (i + 1 >= keys.Count)
                    break;
                
                if (tileCountPerLayer[keys[i + 1]] < tileCountPerLayer[keys[i]] * levelGenerateInfo.maxOffsetTilePerLayer)
                {
                    //Debug.LogError("Max offset tile per layer: " + (float)tileCountPerLayer[keys[i + 1]] / tileCountPerLayer[keys[i]]);
                    return false;
                }
            }

            
            
            
            // Check stuck
            var isStuck = levelGenerateInfo.isStuck;
            var result = CalculateStuck(levelData);

            if (isStuck)
            {
                if (result.shuffleRate <= levelGenerateInfo.maxStuckRate)
                {
                    //Debug.LogError("Stuck rate: " + result.shuffleRate);
                    return false;
                }
                
                if (result.minClearStuck < levelGenerateInfo.minRateClearLevelHasStuck ||
                    result.maxClearStuck > levelGenerateInfo.maxRateClearLevelHasStuck)
                {
                    //Debug.LogError("Clear stuck: " + result.minClearStuck + " - " + result.maxClearStuck);
                    return false;
                }
                
                if (result.shuffleCountAvg < levelGenerateInfo.minShuffleCountToClear ||
                    result.shuffleCountAvg > levelGenerateInfo.maxShuffleCountToClear)
                {
                    //Debug.LogError("Shuffle count avg: " + result.shuffleCountAvg);
                    return false;
                }
            }
            else
            {
                if (result.shuffleRate >= levelGenerateInfo.minStuckRate)
                {
                    //Debug.LogError("Stuck rate: " + result.shuffleRate);
                    return false;
                }
            }
            
            
            return true;
        }
        
        
        static void Shuffle(LevelData data)
        {
            var itemLeft = data.items.Clone();
            
            if (itemLeft.Count <= 0)
                return;
            
            //Module 2

            var itemDataLeft = itemLeft.ToList();
            
            var mahjongDataShuffleList = ShuffleHandler.Shuffle(itemDataLeft, data.boardGame);

            if (mahjongDataShuffleList != null && mahjongDataShuffleList.Count == itemLeft.Count)
            {
                var typeCountOriginal = itemLeft.Select(x => x.LogicType).Distinct().Count();
                var typeCountAfterShuffle = mahjongDataShuffleList.Select(x => x.LogicType).Distinct().Count();

                //BhDebug.LogError($"original:{typeCountOriginal} - type count after shuffle:{typeCountAfterShuffle}");
                
                if (typeCountOriginal != typeCountAfterShuffle)
                {
                    //BhDebug.LogError($"Shuffle error: type count not match original:{typeCountOriginal} - type count after shuffle:{typeCountAfterShuffle}");
                    return;
                }
                
                var spriteIndexOriginal = data.items.Select(x => x.VisualData.IndexSprite).Distinct().Count();
                var spriteIndexAfterShuffle = mahjongDataShuffleList.Select(x => x.VisualData.IndexSprite).Distinct().Count();
                
                //BhDebug.LogError($"original:{spriteIndexOriginal} - sprite index after shuffle:{spriteIndexAfterShuffle}");

                if (spriteIndexOriginal != spriteIndexAfterShuffle)
                {
                    //BhDebug.LogError($"Shuffle error: sprite index not match original:{spriteIndexOriginal} - sprite index after shuffle:{spriteIndexAfterShuffle}");
                    return;
                }

                //BhDebug.Log("Shuffle");
                foreach (var mahjongData in data.items)
                {
                    //BhDebug.Log("mahjong data shuffle left: " + mahjongDataShuffleList.Count);
                    var mahjongDataShuffle =
                        mahjongDataShuffleList.FirstOrDefault(x => x.PositionData.Equals(mahjongData.PositionData));

                    if (mahjongDataShuffle != null)
                    {
                        mahjongData.UpdateLogicType(mahjongDataShuffle.LogicType);
                        mahjongData.UpdatePositionData(mahjongDataShuffle.PositionData);
                        mahjongData.UpdateVisualData(mahjongDataShuffle.VisualData);

                        mahjongDataShuffleList.Remove(mahjongDataShuffle);
                    }
                    else
                    {
                        //BhDebug.LogError("null data");
                    }
                }
            }
            else
            {
                //BhDebug.Log("Can't find data for shuffle'");
            }

        }
        
        
        static void RemoveData(LevelData levelData, MahjongData data)
        {
            var layerCount = levelData.boardGame.GetLength(0);
            int rowCount = levelData.boardGame.GetLength(1);
            int columnCount = levelData.boardGame.GetLength(2);
            
            int layerIndex = data.PositionData.LayerIndex;
            int rowIndex = data.PositionData.RowIndex;
            int columnIndex = data.PositionData.ColumnIndex;

            if (layerIndex >= 0 && layerIndex < layerCount && rowIndex >= 0 && rowIndex + 1 < rowCount &&
                columnIndex >= 0 && columnIndex + 1 < columnCount)
            {
                levelData.boardGame[layerIndex, rowIndex, columnIndex] = '.';
                levelData.boardGame[layerIndex, rowIndex + 1, columnIndex] = '.';
                levelData.boardGame[layerIndex, rowIndex, columnIndex + 1] = '.';
                levelData.boardGame[layerIndex, rowIndex + 1, columnIndex + 1] = '.';
                
                levelData.items.Remove(data);
            }
        }
    }
}