using b100SDK.Scripts.Base;

namespace MahjongGame.Scripts.Gameplay.Controller
{
    public class ComboController : BhMonoBehavior
    {
        private int _currentComboCount;

        public int CurrentComboCount
        {
            get => _currentComboCount;
            private set
            {
                _currentComboCount = value;
                //Evm.OnComboChange.Dispatch(_currentComboCount);
                
                //BhDebug.Log("Current Combo Count: " + _currentComboCount);
            }
        }


        public void Init()
        {
            ResetCombo();
        }

        public void IncreaseCombo()
        {
            CurrentComboCount++;
        }
        
        public void ResetCombo()
        {
            CurrentComboCount = 0;
        }
    }
}