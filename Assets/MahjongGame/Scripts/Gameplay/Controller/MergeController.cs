using System;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Vibration;
using DG.Tweening;
using MahjongGame.Scripts.Gameplay.Item;
using UnityEngine;

namespace MahjongGame.Scripts.Gameplay.Controller
{
    public class MergeController : BhMonoBehavior
    {
        [Header("Properties")]
        [SerializeField]
        private float minDistanceMerge = 200f;

        [SerializeField]
        private float maxDistanceMerge = 15f;
        
        [SerializeField]
        private float offsetMerge = 50f;

        [SerializeField]
        private float moveOutsideDuration = 0.05f;

        [SerializeField]
        private Ease moveOutsideEase;
        
        [SerializeField]
        private float moveInsideDuration = 0.05f;

        [SerializeField]
        private float minMoveInsideDuration = 0.1f;
        
        [SerializeField]
        private float maxMoveInsideDuration = 0.3f;
        
        [SerializeField]
        private Ease moveInsideEase;
        
        [SerializeField]
        private float mahjongSizeX = 1.35f / 2f;

        [SerializeField]
        private float scaleTarget = 0.8f;

        [SerializeField]
        private Ease mergeEase = Ease.InBounce;

        [SerializeField]
        private float delayDespawn = 0.1f;

        [SerializeField]
        private float alphaFadeTarget = 0f;
        
        [SerializeField]
        private float alphaFadeDuration = 0.5f;
        
        [Header("Particle")]
        [SerializeField]
        private ParticleSystem leafParticleSystem;
        
        [SerializeField]
        private ParticleSystem flowerParticleSystem;
        
        [SerializeField]
        private ParticleSystem butterflyParticleSystem;


        private ScoreController _scoreController;
        private ComboController _comboController;
        private Action _onMergeComplete;


        private void Start()
        {
            _comboController = GameController.Instance.ComboController;
            _scoreController = GameController.Instance.ScoreController;
        }

        public void Init(Action onMergeComplete)
        {
            _onMergeComplete = onMergeComplete;
        }
        
        
        public void MergerItem(MahjongItem item1, MahjongItem item2)
        {
            
            item1.IsMerged = true;
            item2.IsMerged = true;
            
            var distance = Mathf.Max((new Vector3(item1.transform.position.x, 0f, 0f) -
                                      new Vector3(item2.transform.position.x, 0f, 0f)).magnitude, minDistanceMerge);

            var boundCenter = (item1.transform.position + item2.transform.position) / 2f;
            
            var isItem1Right = item1.transform.position.x > boundCenter.x;
            
            MergeItem(item1, boundCenter, distance, isItem1Right);
            MergeItem(item2, boundCenter, distance,!isItem1Right, ShotVisual, () =>
            {
                _onMergeComplete?.Invoke();
            });
            

            void ShotVisual()
            {
                //TODO: Shot sound merge
                
                //BhVibrate.ContinuousHaptic();
                
                _scoreController.SpawnScoreItem(boundCenter);

                var currentComboCount = _comboController.CurrentComboCount;
                
                var particle = currentComboCount <= 1 ? leafParticleSystem :
                    currentComboCount < 10 ? flowerParticleSystem : butterflyParticleSystem;

                particle.transform.position = boundCenter;
                particle.Play();
            }
        }
            
        



        void MergeItem(MahjongItem item, Vector3 center, float horizontalDistance, bool isRight, Action onCollision = null, Action onComplete = null)
        {
            item.IsMerged = true;
            item.Deselect();
            item.transform.DOKill();
            item.DOKill();
            
            item.UpdateLayer(isRight ? 1000 : 999);
            
            var merge1Position = item.transform.position;

            var correctMoveInSideDuration = MapRange(horizontalDistance, minDistanceMerge, maxDistanceMerge,
                minMoveInsideDuration, maxMoveInsideDuration);

            var halfDistance = horizontalDistance / 2f;

            var targetOutsideItem1 = center;
            
            targetOutsideItem1.x = isRight
                ? targetOutsideItem1.x + halfDistance + offsetMerge
                : targetOutsideItem1.x - halfDistance - offsetMerge;
            
            var targetInsideItem1 = center;
            
            targetInsideItem1.x = isRight
                ? targetInsideItem1.x + mahjongSizeX
                : targetInsideItem1.x - mahjongSizeX;
            

            item.transform.position = merge1Position;

            var sequence = DOTween.Sequence();

            sequence.Append(item.transform.DOMove(targetOutsideItem1, moveOutsideDuration)
                .SetTarget(item)
                .SetEase(moveOutsideEase));

            sequence.Append(item.transform.DOMove(targetInsideItem1, correctMoveInSideDuration)
                .SetTarget(item)
                .SetEase(moveInsideEase));

            sequence.Join(item.transform.DOScale(scaleTarget, correctMoveInSideDuration)
                .SetTarget(item)
                .SetEase(moveInsideEase).OnComplete(() =>
                {
                    onCollision?.Invoke();
                }));

            sequence.AppendInterval(delayDespawn);
            
            sequence.Append(item.FadeAlphaMerge(alphaFadeTarget, alphaFadeDuration, () =>
            {
                item.Despawn(Pool.MahjongItem);
                
                onComplete?.Invoke();
            }));
        }
        
        
        
        float MapRange(float value, float xMin, float xMax, float yMin, float yMax)
        {
            if (xMax - xMin == 0)
            {
                throw new DivideByZeroException("xMax and xMin cannot be equal.");
            }
            
            float t = (value - xMin) / (xMax - xMin);
            return yMin + t * (yMax - yMin);
        }
    }
}