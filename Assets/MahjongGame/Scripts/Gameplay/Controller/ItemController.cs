using System;
using System.Collections.Generic;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Extensions;
using DG.Tweening;
using MahjongGame.Scripts.Gameplay.Data;
using MahjongGame.Scripts.Gameplay.Item;
using Sirenix.OdinInspector;
using UnityEngine;

namespace MahjongGame.Scripts.Gameplay.Controller
{
    public class ItemController : BhMonoBehavior
    {
        [SerializeField]
        private Transform mahjongItemContainer;

        [SerializeField]
        private MahjongItem mahjongItemPrefab;

        [SerializeField]
        private float noMatchVisualDuration = 0.5f;

        [SerializeField]
        private Camera cam;

        [Header("Board game")]
        [SerializeField]
        private float widthSize = 2.5f;

        [SerializeField]
        private float heightSize = 3f;

        [SerializeField, Range(0f, 1f)]
        private float offsetLayerRate = 0.1f;

        [SerializeField]
        private float offsetWidth = 0.33f;

        [SerializeField]
        private float offsetHeight = 0.305f;

        [SerializeField]
        private float showMahjongDuration = 0.3f;

        private CameraController _cameraController;
        
        private Vector3 _midPoint;

        private List<MahjongItem> _items = new();
        public List<MahjongItem> Items => _items;

        private int _pairCount;

        public int PairCount
        {
            get => _pairCount;
            set
            {
                _pairCount = value;
                Evm.OnMatchChange.Dispatch(_pairCount);

                CheckNoMove();
            }
        }
        
        public int TotalPair { get;private set; }


        private Action _onNoMatch;

        




        private void Start()
        {
            _cameraController = GameController.Instance.CameraController;
            DOTween.SetTweensCapacity(300, 10);
        }


        public void Init(LevelData data, Action onNoMatch = null)
        {
            _onNoMatch = onNoMatch;

            ClearData();

            SpawnItem(data);

            UpdatePairCount();

            _midPoint = GetBounds().center;
        }


        public void Clear()
        {
            PoolHelper.DespawnAll(Pool.MahjongItem);
        }

        public void UpdatePairCount()
        {
            PairCount = CountPairOfTile();
            UpdateStatusSelect();
            
            BhDebug.Log("Update pair count: " + PairCount);
        }


        public Bounds GetBounds()
        {
            var bounds = new Bounds();

            for (int i = 0; i < _items.Count; i++)
            {
                bounds.Encapsulate(_items[i].GetBounds());
            }
            return bounds;
        }
        

        public void SpawnItem(MahjongData data)
        {
            var newItem = mahjongItemPrefab.Spawn(GetSpawnPosition(data.PositionData), Quaternion.identity, mahjongItemContainer, Pool.MahjongItem);
            newItem.DOKill();
            
            newItem.Init(data, _cameraController.Cam);
            
            _items.Add(newItem);
        }


        public void UpdateStatusSelect()
        {
            foreach (var item in _items)
            {
                item.SelectResult = GameController.Instance.GameplayController.CheckCanSelect(item.PositionData);
            }
        }


        public void RemoveItem(MahjongItem item)
        {
            _items.Remove(item);
            //BhDebug.Log($"Removed item: {item.name}, left: {_items.Count}");
        }
        
        
        void SpawnItem(LevelData data)
        {
            foreach (var mahjongData in data.items)
            {
                SpawnItem(mahjongData);
            }

            TotalPair = _items.Count / 2;
            
            //RunAnimMahjongShow();
        }

        public void ShowMahjong()
        {
            ShowMahjong(showMahjongDuration);
        }
        
        [Button]
        public void ShowMahjong(float duration)
        {
            for (var index = 0; index < _items.Count; index++)
            {
                var item = _items[index];
                var cachePos = item.InitPosition;

                var targetItem = item.transform.position;
                targetItem.x = _midPoint.x;
                targetItem.y = _midPoint.y;

                item.transform.DOKill(true);
                
                item.transform.position = targetItem;
                
                item.transform.DOMove(cachePos, duration)
                    .SetTarget(item.transform);
            }
        }
        
        public void HideMahjong(float duration)
        {
            for (var index = 0; index < _items.Count; index++)
            {
                var item = _items[index];

                var targetItem = item.transform.position;
                targetItem.x = _midPoint.x;
                targetItem.y = _midPoint.y;

                item.transform.DOKill(true);

                item.transform.DOMove(targetItem, duration)
                    .SetTarget(item.transform);
            }
        }


        
        Vector3 GetSpawnPosition(PositionData positionData)
        {
            var position = new Vector3(positionData.ColumnIndex * widthSize / 2f,
                positionData.RowIndex * -heightSize / 2f);

            position.x -= offsetWidth * positionData.LayerIndex;
            position.y -= -offsetHeight * positionData.LayerIndex;

            return position;
        }

        void ClearData()
        {
            _items.Clear();
            PoolHelper.DespawnAll(Pool.MahjongItem);
        }

        


        void CheckNoMove()
        {
            if (_pairCount <= 0 && _items.Count > 0)
            {
                _onNoMatch?.Invoke();
            }
        }
        
        int CountPairOfTile()
        {
            var result = 0;

            var allItemLeft = _items.Clone();

            var allItemCanSelect = new List<MahjongItem>();

            foreach (var item in allItemLeft)
            {
                if (GameUtilities.CheckCanSelectItem(item.PositionData, GameController.Instance.GameplayController.BoardGame) == SelectResult.Success)
                {
                    allItemCanSelect.Add(item);
                }
            }

            var elementCountMap = new Dictionary<char, int>();

            foreach (var item in allItemCanSelect)
            {
                if (elementCountMap.ContainsKey(item.Data.LogicType))
                {
                    elementCountMap[item.Data.LogicType]++;
                }
                else
                {
                    elementCountMap.TryAdd(item.Data.LogicType, 1);
                }
            }

            foreach (var pair in elementCountMap)
            {
                int count = pair.Value;
                result += count / 2;
            }

            return result;
        }

    }
}