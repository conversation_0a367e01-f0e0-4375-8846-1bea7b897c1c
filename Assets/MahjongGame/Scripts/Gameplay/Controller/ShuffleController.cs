using System.Linq;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities.Extensions;
using b100SDK.Scripts.Vibration;
using DG.Tweening;
using Sirenix.OdinInspector;
using UnityEngine;

namespace MahjongGame.Scripts.Gameplay.Controller
{
    public class ShuffleController : BhMonoBehavior
    {
        [SerializeField]
        private float moveItemBeforeShuffleDuration = .5f;

        [SerializeField]
        private float delay = .3f;
        
        [SerializeField]
        private float moveItemAfterShuffleDuration =.5f;
        
        private ItemController _itemController;
        private GameplayController _gameplayController;
        private HintController _hintController;


        private void OnEnable()
        {
            if (Evm)
            {
                Evm.OnShuffle.AddListener(Shuffle);
                Evm.Shuffle.AddListener(Shuffle);
            }
        }

        private void OnDisable()
        {
            if (Evm)
            {
                Evm.OnShuffle.RemoveListener(Shuffle);
                Evm.Shuffle.RemoveListener(Shuffle);
            }
        }

        private void Start()
        {
            _itemController = GameController.Instance.ItemController;
            _gameplayController = GameController.Instance.GameplayController;
            _hintController = GameController.Instance.HintController;
        }

        [Button]
        void Shuffle()
        {
            
            
            var itemLeft = _itemController.Items.Clone();
            
            if (itemLeft.Count <= 0)
                return;
            
            //Module 2

            var itemDataLeft = itemLeft.Select(x => x.Data).ToList();
            
            var mahjongDataShuffleList = ShuffleHandler.Shuffle(itemDataLeft, _gameplayController.BoardGame);

            if (mahjongDataShuffleList != null && mahjongDataShuffleList.Count == itemLeft.Count)
            {
                var typeCountOriginal = _gameplayController.LevelData.items.Select(x => x.LogicType).Distinct().Count();
                var typeCountAfterShuffle = mahjongDataShuffleList.Select(x => x.LogicType).Distinct().Count();

                //BhDebug.LogError($"original:{typeCountOriginal} - type count after shuffle:{typeCountAfterShuffle}");
                
                if (typeCountOriginal != typeCountAfterShuffle)
                {
                    //BhDebug.LogError($"Shuffle error: type count not match original:{typeCountOriginal} - type count after shuffle:{typeCountAfterShuffle}");
                    return;
                }
                
                var spriteIndexOriginal = _gameplayController.LevelData.items.Select(x => x.VisualData.IndexSprite).Distinct().Count();
                var spriteIndexAfterShuffle = mahjongDataShuffleList.Select(x => x.VisualData.IndexSprite).Distinct().Count();
                
                //BhDebug.LogError($"original:{spriteIndexOriginal} - sprite index after shuffle:{spriteIndexAfterShuffle}");

                if (spriteIndexOriginal != spriteIndexAfterShuffle)
                {
                    //BhDebug.LogError($"Shuffle error: sprite index not match original:{spriteIndexOriginal} - sprite index after shuffle:{spriteIndexAfterShuffle}");
                    return;
                }

                //BhDebug.Log("Shuffle");
                foreach (var mahjongData in _gameplayController.LevelData.items)
                {
                    //BhDebug.Log("mahjong data shuffle left: " + mahjongDataShuffleList.Count);
                    var mahjongDataShuffle =
                        mahjongDataShuffleList.FirstOrDefault(x => x.PositionData.Equals(mahjongData.PositionData));

                    if (mahjongDataShuffle != null)
                    {
                        mahjongData.UpdateLogicType(mahjongDataShuffle.LogicType);
                        mahjongData.UpdatePositionData(mahjongDataShuffle.PositionData);
                        mahjongData.UpdateVisualData(mahjongDataShuffle.VisualData);

                        mahjongDataShuffleList.Remove(mahjongDataShuffle);
                    }
                    else
                    {
                        //BhDebug.LogError("null data");
                    }
                }
            }
            else
            {
                //BhDebug.Log("Can't find data for shuffle'");
            }
            
            var result = ShuffleCountCalculate.Calculate(_gameplayController.LevelData);

            var avgStr = $"{result.Item1} - {result.Item2}";
            
            Evm.OnUpdateShuffleAvg.Dispatch(avgStr);


            StartShuffle();

        }

        void StartShuffle()
        {
            this.DOKill();
            _hintController.CloseHint(false);
            _itemController.HideMahjong(moveItemBeforeShuffleDuration);

            DOVirtual.DelayedCall(delay, () => _itemController.ShowMahjong(moveItemAfterShuffleDuration))
                .SetTarget(this);
        }

    }
}