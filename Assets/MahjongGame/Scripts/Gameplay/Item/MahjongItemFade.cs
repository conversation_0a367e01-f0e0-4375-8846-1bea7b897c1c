using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities;
using DG.Tweening;
using UnityEngine;

namespace MahjongGame.Scripts.Gameplay.Item
{
    public class MahjongItemFade : BhMonoBehavior
    {
        [SerializeField]
        private SpriteRenderer coverCanNotSelect;

        [SerializeField]
        private float fadeNoMatchDuration = 0.3f;
        
        [SerializeField]
        private float fadeNoMatchTargetAlpha = 0.6f;

        [SerializeField]
        private float fadeCanSelectDuration = .2f;

        private bool _canFade;
        private int _sortLayer;


        public void UpdateCanFade()
        {
            //TODO: Update can fade
            _canFade = true;
        }

        public void UpdateSortingOrder(int sortOrder)
        {
            coverCanNotSelect.sortingOrder = sortOrder;
        }

        public void UpdateBySelectStatus(SelectResult result, bool isLose)
        {
            if (!_canFade && !isLose)
                return;

            coverCanNotSelect.DOKill(true);

            if (result == SelectResult.Success)
            {
                coverCanNotSelect.DOFade(0f, fadeCanSelectDuration)
                    .SetTarget(coverCanNotSelect).OnComplete(() =>
                    {
                        coverCanNotSelect.gameObject.SetActiveWithChecker(false);
                    });
            }
            else
            {
                coverCanNotSelect.gameObject.SetActiveWithChecker(true);
                coverCanNotSelect.DOFade(fadeNoMatchTargetAlpha, fadeNoMatchDuration)
                    .SetTarget(coverCanNotSelect);
            }

            /*if (_mahjongType == MahjongType.SpecialItem)
            {
                //coverCanNotSelect.gameObject.SetActiveWithChecker(false);
                coverCanNotSelect.DOKill();
                coverCanNotSelect.SetAlpha(0f);

                if (status == SelectResult.Success)
                {
                    //BhDebug.Log("Clear item " + _mahjongType);
                    Clear();
                }
            }*/
        }
    }
}