using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities;
using DG.Tweening;
using UnityEngine;

namespace MahjongGame.Scripts.Gameplay.Item
{
    public class MahjongItemHintController : BhMonoBehavior
    {
        [SerializeField]
        private SpriteRenderer selectChestRenderer;
        
        [SerializeField]
        private float selectChestAlpha = 160 / 255f;
        
        [SerializeField]
        private float showSelectDuration = .2f;

        [SerializeField]
        private float hideSelectDuration = .2f;




        public void ShowHint(bool instant)
        {
            selectChestRenderer.DOKill(true);
            
            selectChestRenderer.gameObject.SetActiveWithChecker(true);
            
            if (instant)
            {
                selectChestRenderer.SetAlpha(selectChestAlpha);
                return;
            }
            
            selectChestRenderer.DOFade(selectChestAlpha, hideSelectDuration)
                .SetTarget(selectChestRenderer)
                .OnComplete(() =>
                {
                    selectChestRenderer.SetAlpha(selectChestAlpha);
                });
        }
        
        public void HideHint(bool instant)
        {
            selectChestRenderer.DOKill(true);

            if (instant)
            {
                selectChestRenderer.gameObject.SetActiveWithChecker(false);
                return;
            }
            
            selectChestRenderer.DOFade(0f, hideSelectDuration)
                .SetTarget(selectChestRenderer)
                .OnComplete(() =>
                {
                    selectChestRenderer.SetAlpha(0f);
                });
        }
    }
}