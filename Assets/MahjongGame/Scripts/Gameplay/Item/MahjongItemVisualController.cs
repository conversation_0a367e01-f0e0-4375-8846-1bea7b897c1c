using System;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities;
using DG.Tweening;
using UnityEngine;

namespace MahjongGame.Scripts.Gameplay.Item
{
    public class MahjongItemVisualController : BhMonoBehavior
    {
        [SerializeField]
        private SpriteRenderer chestRenderer;
        
        [SerializeField]
        private SpriteRenderer mahjongRenderer;

        [SerializeField]
        private SpriteRenderer shadowRenderer;
        
        [SerializeField]
        private SpriteRenderer selectChestRenderer;

        [SerializeField]
        private SpriteRenderer selectRenderer;

        [SerializeField]
        private float selectChestAlpha = 160 / 255f;
        
        [SerializeField]
        private float showSelectDuration = .2f;

        [SerializeField]
        private float hideSelectDuration = .2f;

        private VisualData _visualData;


        private void OnEnable()
        {
            /*if (Evm)
            {
                Evm.OnStartChangeSkin.AddListener(UpdateSkin);
            }*/
        }

        private void OnDisable()
        {
            if (_visualData != null)
            {
                _visualData.OnUpdateVisualData -= UpdateSkin;
            }

            /*if (Evm)
            {
                Evm.OnStartChangeSkin.RemoveListener(UpdateSkin);
            }*/
        }

        public void Init(VisualData visualData, int sortingOrder)
        {
            _visualData = visualData;
            
            UpdateSortingOrder(sortingOrder);
            ShowVisual(true);
            HideSelect(true);
            
            UpdateSkin();

            if (_visualData != null)
            {
                _visualData.OnUpdateVisualData -= UpdateSkin;
                _visualData.OnUpdateVisualData += UpdateSkin;
            }
        }

        public void UpdateSortingOrder(int sortOrder)
        {
            chestRenderer.sortingOrder = sortOrder;
            mahjongRenderer.sortingOrder = sortOrder;
            shadowRenderer.sortingOrder = sortOrder;
            
            selectChestRenderer.sortingOrder = sortOrder;
            selectRenderer.sortingOrder = sortOrder;
        }

        public Tween ShowVisual(bool instant = false, float duration = .2f, Action onComplete = null)
        {
            chestRenderer.DOFade(1f, instant ? 0f : duration)
                .SetTarget(chestRenderer)
                .OnComplete(() =>
                {
                    onComplete?.Invoke();
                    shadowRenderer.gameObject.SetActiveWithChecker(true);
                });

            return mahjongRenderer.DOFade(1f, instant ? 0f : duration).SetTarget(mahjongRenderer);
        }

        public Tween HideVisual(float alphaTarget, float duration, Action onComplete = null, bool instant = false)
        {
            shadowRenderer.gameObject.SetActiveWithChecker(false);
            
            return mahjongRenderer.DOFade(alphaTarget, instant ? 0f : duration).SetTarget(mahjongRenderer).OnStart(() =>
            {

                chestRenderer.DOFade(alphaTarget, instant ? 0f : duration)
                    .SetTarget(chestRenderer)
                    .OnComplete(() =>
                    {
                        onComplete?.Invoke();
                    });
            });
        }
        
        
        

        public void ShowSelect(bool instant = false)
        {
            selectRenderer.DOKill(true);
            selectChestRenderer.DOKill(true);
            
            selectRenderer.gameObject.SetActiveWithChecker(true);
            selectChestRenderer.gameObject.SetActiveWithChecker(true);
            
            if (instant)
            {
                selectRenderer.SetAlpha(1f);
                selectChestRenderer.SetAlpha(selectChestAlpha);
                return;
            }
            
            
            selectRenderer.DOFade(1f, hideSelectDuration)
                .SetTarget(selectRenderer)
                .OnComplete(() =>
                {
                    selectRenderer.SetAlpha(1f);
                });
                
            selectChestRenderer.DOFade(selectChestAlpha, hideSelectDuration)
                .SetTarget(selectChestRenderer)
                .OnComplete(() =>
                {
                    selectChestRenderer.SetAlpha(selectChestAlpha);
                });
        }
        
        public void HideSelect(bool instant = false)
        {
            selectRenderer.DOKill(true);
            selectChestRenderer.DOKill(true);

            if (instant)
            {
                selectRenderer.gameObject.SetActiveWithChecker(false);
                selectChestRenderer.gameObject.SetActiveWithChecker(false);
                return;
            }


            selectRenderer.DOFade(0f, hideSelectDuration)
                .SetTarget(selectRenderer)
                .OnComplete(() =>
                {
                    selectRenderer.gameObject.SetActiveWithChecker(false);
                });
                
            selectChestRenderer.DOFade(0f, hideSelectDuration)
                .SetTarget(selectChestRenderer)
                .OnComplete(() =>
                {
                    selectChestRenderer.SetAlpha(0f);
                });
        }


        public Bounds GetBound()
        {
            return chestRenderer.bounds;
        }
 
        void UpdateSkin()
        {
            chestRenderer.sprite = SkinManager.Instance.GetChestSprite();
            selectChestRenderer.sprite = SkinManager.Instance.GetSelectSprite();

            switch (_visualData.Type)
            {
                case MahjongType.Normal:
                    mahjongRenderer.sprite = SkinManager.Instance.GetMahjongSprite(_visualData.IndexSprite);
                    break;

                case MahjongType.Flower:
                    mahjongRenderer.sprite = SkinManager.Instance.GetFlowerSprite(_visualData.IndexSprite);
                    break;

                case MahjongType.Season:
                    mahjongRenderer.sprite = SkinManager.Instance.GetSeasonSprite(_visualData.IndexSprite);
                    break;

                case MahjongType.SpecialTarget:
                    //mahjongRenderer.sprite = SkinManager.Instance.GetSpecialMahjongSprite(_visualData.IndexSprite);
                    break;

                case MahjongType.SpecialItem:
                    //mahjongRenderer.sprite = SkinManager.Instance.GetRandomSpecialItemSprite();
                    break;
            }
        }
    }
}