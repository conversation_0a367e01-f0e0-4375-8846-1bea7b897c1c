using System;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.UI.Panel;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Vibration;
using DG.Tweening;
using MahjongGame.Scripts.Gameplay.Data;
using UnityEngine;

namespace MahjongGame.Scripts.Gameplay.Item
{
    public class MahjongItem : BhMonoBehavior
    {
        [Header("Ref")]
        [SerializeField]
        private MahjongItemSelectHandler selectHandler;

        [SerializeField]
        private MahjongItemFade fadeHandler;
        
        [SerializeField]
        private MahjongItemVisualController visualController;
        
        [SerializeField]
        private MahjongItemHintController hintController;
        

        [SerializeField]
        private float goToInitDuration = .2f;

        [SerializeField]
        private float goToSelectDuration = .2f;
        
        [SerializeField]
        private float offsetX = .1f;
        
        [Header("Select Failures")]
        [SerializeField]
        private float shakeDuration = 0.2f;

        [SerializeField]
        private float targetShakeX = 0.1f;

        [SerializeField]
        private int vibrato = 50;

        [SerializeField]
        private AnimationCurve shakeCurve;
        
        
        private MahjongData _data;
        
        
        public MahjongData Data => _data;
        
        public PositionData PositionData => _data.PositionData;
        
        
        private bool _isRunningAnim;
        private BlockLeftRightType _blockLeftRightType;

        public MahjongType MahjongType => _data.VisualData.Type;

        private bool _isMerge;

        public bool IsMerged
        {
            get => _isMerge;
            set
            {
                _isMerge = value;

                if (_isMerge)
                {
                    selectHandler.Clear();
                }
            }
        }

        public bool IsSelected { get;private set; }
        
                
        private Vector3 _initPosition;

        public Vector3 InitPosition => _initPosition;
        public int Layer => _data.PositionData.LayerIndex;

        private int _sortingOrder;
        
        private SelectResult _selectResult;

        public SelectResult SelectResult
        {
            get => _selectResult;
            set
            {
                _selectResult = value;
                
                fadeHandler.UpdateBySelectStatus(_selectResult, GameController.Instance.GameplayController.IsLose);
            }
        }
        

        
        public void Init(MahjongData data, Camera cam)
        {
            _data = data;
            _initPosition = transform.position;

            _sortingOrder = GameUtilities.GetSortingOrder(data.PositionData);
            
            IsSelected = false;
            IsMerged = false;
            
            transform.DOKill();
            transform.localScale = Vector3.one;

            GoToInitPosition(true);
            
            selectHandler.Init(this, cam);
            visualController.Init(data.VisualData, _sortingOrder);


        }

        
        public void UpdateLayer(int sortingOrder)
        {
            visualController.UpdateSortingOrder(sortingOrder);
        }

        public void ResetLayer()
        {
            visualController.UpdateSortingOrder(_sortingOrder);
        }


        public bool CanSelect()
        {
            //TODO: Check can select
            return true;
        }


        public void Select(bool instant = false)
        {
            if (IsSelected)
            {
                Deselect();
                return;
            }
            
            if (SelectResult == SelectResult.Success)
            {
                //BhDebug.Log("Select");
                
                IsSelected = true;
                
                GoToSelectPosition(instant);
                GameController.Instance.GameplayController.SelectItem(this);
            }
            else
            {
                SelectFail(SelectResult);
            }
        }

        public void Deselect(bool instant = false)
        {
            IsSelected = false;
            GoToInitPosition(instant);
            
            GameController.Instance.GameplayController.DeselectItem(this);
        }


        public bool SelectOnDrag()
        {
            var selectResult = GameController.Instance.GameplayController.CheckCanSelect(this);

            if (selectResult == SelectResult.Success)
            {
                Select(true);
                return true;
            }
            else
            {
                //AudioAssistant.Shot(TypeSound.MahjongDeselect);

                //BhVibrate.ContinuousHaptic();
                
                SelectFail(selectResult);
                
                return false;
            }
        }


        public void GoToInitPosition(bool instant = false,Action onStart = null, Action onComplete = null)
        {
            _isRunningAnim = true;
            
            transform.DOKill(true);
            
            onStart?.Invoke();
            
            visualController.HideSelect(instant);


            if (instant)
            {
                transform.position = _initPosition;
                onComplete?.Invoke();
                _isRunningAnim = false;
                return;
            }
            
            transform.DOMove(_initPosition, goToSelectDuration)
                .SetTarget(transform)
                .OnComplete(() =>
                {
                    onComplete?.Invoke();
                    _isRunningAnim = false;
                });
        }

        public void GoToSelectPosition(bool instant = false, Action onStart = null, Action onComplete = null)
        {
            _isRunningAnim = true;
            
            transform.DOKill(true);
            
            onStart?.Invoke();
            
            visualController.ShowSelect();

            var targetPosition = _initPosition;
            targetPosition.x += _blockLeftRightType == BlockLeftRightType.Left ? offsetX :
                _blockLeftRightType == BlockLeftRightType.Right ? -offsetX : 0f;

            if (instant)
            {
                transform.position = targetPosition;
                onComplete?.Invoke();
                _isRunningAnim = false;
                return;
            }

            transform.DOMove(targetPosition, goToSelectDuration)
                .SetTarget(transform)
                .OnComplete(() =>
                {
                    onComplete?.Invoke();
                    _isRunningAnim = false;
                });
        }


        public void ShowHint(bool instant = false)
        {
            hintController.ShowHint(instant);
        }
        
        public void HideHint(bool instant = false)
        {
            hintController.HideHint(instant);
        }


        void SelectFail(SelectResult selectResult)
        {
            switch (selectResult)
            {
                case SelectResult.Layer:
                    //PopupNotification.Show(GameConst.BLOCK_TOP);
                    BhDebug.LogWarning("Block top");
                    break;

                case SelectResult.LeftRight:
                    //PopupNotification.Show(GameConst.BLOCK_SIDE);
                    BhDebug.LogWarning("Block left right");
                    break;
            }
            
            
            _isRunningAnim = true;
            
            transform.DOKill(true);

            transform.DOLocalMove(new Vector3(targetShakeX, 0f, 0f), shakeDuration)
                .SetEase(shakeCurve)
                .SetTarget(transform)
                .SetRelative(true)
                .OnComplete(() =>
                {
                    transform.position = _initPosition;
                    _isRunningAnim = false;
                });
        }




        public Bounds GetBounds()
        {
            return visualController.GetBound();
        }
        
        public Tween FadeAlphaMerge(float target, float duration, Action onComplete = null)
        {
            return visualController.HideVisual(target, duration, onComplete);
        }
    }


}