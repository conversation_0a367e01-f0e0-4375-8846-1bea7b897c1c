using System;
using b100SDK.Scripts.Base;
using MahjongGame.Scripts.Gameplay.Data;

namespace MahjongGame.Scripts.Gameplay.Item
{
    [Serializable]
    public class VisualData
    {
        private int _indexSprite;
        private MahjongType _type;

        public int IndexSprite => _indexSprite;

        public MahjongType Type => _type;

        public event Action OnUpdateVisualData;
        
        public VisualData(int indexSprite, MahjongType type)
        {
            _indexSprite = indexSprite;
            _type = type;
        }

        public void UpdateVisualData(VisualData visualData)
        {
            _indexSprite = visualData.IndexSprite;
            _type = visualData.Type;
            
            OnUpdateVisualData?.Invoke();
        }
    }



    [Serializable]
    public class MahjongData
    {
        private PositionData _positionData;
        private char _logicLogicType;
        private VisualData _visualData;


        public MahjongData(PositionData positionData, char logicType, VisualData visualData)
        {
            _positionData = positionData;
            _logicLogicType = logicType;
            _visualData = visualData;
        }

        public PositionData PositionData => _positionData;

        public char LogicType => _logicLogicType;
        public VisualData VisualData => _visualData;
        
        public void UpdateLogicType(char logicType)
        {
            _logicLogicType = logicType;
        }

        public void UpdatePositionData(PositionData positionData)
        {
            _positionData.UpdatePositionData(positionData);
        }
        public void UpdateVisualData(VisualData visualData)
        {
            _visualData.UpdateVisualData(visualData);
        }
    }
}