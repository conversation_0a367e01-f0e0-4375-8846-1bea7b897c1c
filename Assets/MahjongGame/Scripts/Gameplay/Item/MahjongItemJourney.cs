using b100SDK.Scripts.Base;
using UnityEngine;

namespace MahjongGame.Scripts.Gameplay.Item
{
    public class MahjongItemJourney : BhMonoBehavior
    {
        [Header("Journey")]
        [SerializeField]
        private SpriteRenderer journeyTarget;

        [SerializeField]
        private Sprite<PERSON><PERSON>er journeyFlip;

        [SerializeField]
        private SpriteRenderer journeyItem;

        [SerializeField]
        private float journeySpecialItemScaleDuration = .3f;
    }
}