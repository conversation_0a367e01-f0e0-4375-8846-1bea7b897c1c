using System.Collections.Generic;
using System.Linq;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.EventSystems;

namespace MahjongGame.Scripts.Gameplay.Item
{
    public class MahjongItemSelectHandler : <PERSON>h<PERSON>onoBeh<PERSON>or, IBeginDragHandler, IDragHandler, IPointerDownHandler, IPointerUpHandler
    {
        [SerializeField]
        private BoxCollider2D boxCollider2D;

        [SerializeField]
        private LayerMask mahjongLayerMask;
        
        private MahjongItem _mahjongItem;
        
        private Vector3 _offSetMouse;
        private Camera _camera;
        private Collider2D[] _overlapCollider = new Collider2D[20];
        private bool _isDragging;


        public void Init(MahjongItem mahjongItem, Camera cam)
        {
            _mahjongItem = mahjongItem;
            _camera = cam;

            boxCollider2D.enabled = true;
            _isDragging = false;
        }

        public void Clear()
        {
            boxCollider2D.enabled = false;
        }


        public void OnBeginDrag(PointerEventData eventData)
        {
            /*if (_isDragging)
                return;*/
            
            if (_mahjongItem.MahjongType == MahjongType.SpecialItem)
                return;

            if (_mahjongItem.SelectOnDrag())
            {
                _isDragging = true;
                _mahjongItem.UpdateLayer(999);
                
                var position = _camera.ScreenToWorldPoint(Input.mousePosition);
                position.z = 0f;
                transform.position = position;
            }
        }
        public void OnDrag(PointerEventData eventData)
        {
            if (!_isDragging)
                return;

            /*if (GameController.Instance.CurrentDragItem != this)
                return;*/

            var position = _camera.ScreenToWorldPoint(Input.mousePosition);
            position.z = 0f;
            transform.position = position;
        }
        
        public void OnPointerDown(PointerEventData eventData)
        {
            //BhDebug.Log("Pointer down");
        }

        public void OnPointerUp(PointerEventData eventData)
        {
            //BhDebug.Log("Pointer up");
            
            if (!_isDragging)
            {
                if (_mahjongItem.MahjongType == MahjongType.SpecialItem)
                    return;

                //BhDebug.Log("Select from pointer");
                _mahjongItem.Select();

                return;
            }
            
            //BhDebug.Log("draing");

            _isDragging = false;

            var item = CheckMahjongItemOverlap();

            if (item != null)
            {
                if (item.SelectOnDrag())
                {
                    
                }
                else
                {
                    BhDebug.Log("Fail");
                    _mahjongItem.GoToSelectPosition(false, null, () =>
                    {
                        _mahjongItem.ResetLayer();
                    });
                }
            }
            else
            {
                //BhDebug.LogError("No item, back init position");
                _mahjongItem.GoToSelectPosition(false, null, () =>
                {
                    _mahjongItem.ResetLayer();
                });
            }
        }

        [Button]
        MahjongItem CheckMahjongItemOverlap()
        {
            List<MahjongItem> allItems = new List<MahjongItem>();
            MahjongItem result = null;

            int count = boxCollider2D.Overlap(new ContactFilter2D()
            {
                layerMask = mahjongLayerMask,
            }, _overlapCollider);

            if (count > 0)
            {
                //BhDebug.Log("Collider count: " + count);
                for (int i = 0; i < count; i++)
                {
                    var item = _overlapCollider[i].GetComponent<MahjongItem>();

                    if (item != null && item != _mahjongItem && item.Data.LogicType == _mahjongItem.Data.LogicType && !item.IsMerged)
                    {
                        allItems.Add(item);
                    }
                }
            }
            else
            {
                //BhDebug.LogError("no find collider");
            }

            if (allItems.Count <= 0)
            {
            }
            else
            {
                var maxIndexLayer = allItems.Max(x => x.Layer);
                result = allItems.FirstOrDefault(x => x.Layer == maxIndexLayer);
            }

            return result;
        }


        

        private void Reset()
        {
            boxCollider2D = _mahjongItem.GetComponent<BoxCollider2D>();
        }


    }
}