using b100SDK.Scripts.Base;
using Sirenix.OdinInspector;
using UnityEngine;

namespace MahjongGame.Scripts.Gameplay
{
    public class DebugBound : BhMonoBehavior
    {
        public SpriteRenderer spriteRenderer1;
        public SpriteRenderer spriteRenderer2;

        [Button]
        public void Init(SpriteRenderer item1, SpriteRenderer item2)
        {
            spriteRenderer1 = item1;
            spriteRenderer2 = item2;
            
            // Lấy <PERSON> của từng SpriteRenderer
            Bounds bounds1 = spriteRenderer1.bounds;
            Bounds bounds2 = spriteRenderer2.bounds;

            // Kết hợp Bounds
            Bounds combinedBounds = new Bounds(bounds1.center, Vector3.zero);
            combinedBounds.Encapsulate(bounds1);
            combinedBounds.Encapsulate(bounds2);

            // Đ<PERSON> dễ nhìn, vẽ một hộp Gizmos bao quanh Bounds đã kết hợp
            Gizmos.color = Color.red;
            Gizmos.DrawWireCube(combinedBounds.center, combinedBounds.size);
        }

        void OnDrawGizmos()
        {
            if (spriteRenderer1 != null && spriteRenderer2 != null)
            {
                // L<PERSON>y <PERSON> của từng SpriteRenderer
                Bounds bounds1 = spriteRenderer1.bounds;
                Bounds bounds2 = spriteRenderer2.bounds;

                // Kết hợp Bounds
                Bounds combinedBounds = new Bounds(bounds1.center, Vector3.zero);
                combinedBounds.Encapsulate(bounds1);
                combinedBounds.Encapsulate(bounds2);

                // Vẽ Gizmos
                Gizmos.color = Color.red;
                Gizmos.DrawWireCube(combinedBounds.center, combinedBounds.size);
                
                Gizmos.color = Color.green;
                Gizmos.DrawWireCube(bounds1.center, bounds1.size);
                
                Gizmos.color = Color.blue;
                Gizmos.DrawWireCube(bounds2.center, bounds2.size);
            }
        }
    }
}