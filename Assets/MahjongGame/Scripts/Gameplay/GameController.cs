using b100SDK.Scripts.DesignPatterns;
using b100SDK.Scripts.Utilities;
using MahjongGame.Scripts.Gameplay.Controller;
using Sirenix.OdinInspector;
using UnityEngine;
namespace MahjongGame.Scripts.Gameplay
{
    public class GameController : <PERSON>ton<GameController>
    {
        [Header("Score")]
        [SerializeField]
        private ScoreController scoreController;
        
        [Header("Combo")]
        [SerializeField]
        private ComboController comboController;

        [Header("Item")]
        [SerializeField]
        private ItemController itemController;
        
        [Header("Camera")]
        [SerializeField]
        private CameraController cameraController;
        
        [Header("Merge")]
        [SerializeField]
        private MergeController mergeController;

        [Header("Gameplay")]
        [SerializeField]
        private GameplayController gameplayController;
        
        [Header("Undo")]
        [SerializeField]
        private UndoController undoController;
        
        [Head<PERSON>("Hint")]
        [SerializeField]
        private HintController hintController;


        
        public ItemController ItemController => itemController;
        public CameraController CameraController => cameraController;
        
        public MergeController MergeController => mergeController;
        public GameplayController GameplayController => gameplayController;
        
        public ScoreController ScoreController => scoreController;
        public ComboController ComboController => comboController;
        public UndoController UndoController => undoController;
        
        public HintController HintController => hintController;
        
        
        public bool IsTutorialDoing { get; set; }
        
        public bool IsPlaying { get; private set; }

        
        
        [Button]
        public void InitGame()
        {
            gameplayController.Init(OnWin);
            itemController.Init(gameplayController.LevelData, ShowNoMatch);
            cameraController.Init();
            comboController.Init();
            undoController.Init();
            mergeController.Init(OnMergeComplete);
            scoreController.Init(itemController.TotalPair);
            
            itemController.ShowMahjong();
        }



        void ShowNoMatch()
        {
            BhDebug.Log("Show no match");
        }

        void OnMergeComplete()
        {
            /*BhDebug.Log("MergeComplete");

            if (itemController.PairCount == 0)
            {
                BhDebug.LogError("WIn================");
            }*/
        }

        void OnWin()
        {
            //missionController.AddWinLevel();
            BhDebug.LogError("WIn================");
        }

    }
}