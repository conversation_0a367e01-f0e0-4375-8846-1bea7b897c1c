using b100SDK.Scripts.Base;
using b100SDK.Scripts.UI.Components.Button;

namespace MahjongGame.Scripts.Gameplay
{
    public class TestNextLevel : BhMonoBehavior
    {
        public BhButton button;


        private void OnEnable()
        {
            button.onClick.AddListener(NextLevel);
        }
        
        private void OnDisable()
        {
            button.onClick.RemoveListener(NextLevel);
        }

        void NextLevel()
        {
#if UNITY_EDITOR
            Gm.data.user.level++;
            GameController.Instance.InitGame();
            
#endif
        }
    }
}