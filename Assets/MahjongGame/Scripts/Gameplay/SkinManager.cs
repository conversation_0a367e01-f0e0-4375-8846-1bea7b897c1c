using System;
using System.Collections.Generic;
using System.Linq;
using b100SDK.Scripts.Asset;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.DesignPatterns;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Extensions;
using b100SDK.Scripts.Utilities.Tool;
using MahjongGame.Scripts.Config.Skin;
using Sirenix.OdinInspector;
using UnityEditor;
using UnityEngine;
using Object = UnityEngine.Object;
using Random = UnityEngine.Random;

namespace MahjongGame.Scripts.Gameplay
{
    public class SkinManager : Singleton<SkinManager>
    {
        [SerializeField]
        private SkinDataConfig currentSkinDataConfig;
        
        public SkinData GetCurrentSkinData()
        {
            return currentSkinDataConfig.GetSkinData();
        }
        
        public Sprite GetSelectSprite()
        {
            return GetCurrentSkinData().selectSprite;
        }

        public Sprite GetMahjongSprite(int indexSprite)
        {
            return GetCurrentSkinData().mahjongSpriteList[indexSprite];
        }
        public Sprite GetFlowerSprite(int spriteIndex)
        {
            return GetCurrentSkinData().mahjongFlowerSprites[spriteIndex];
        }
        public Sprite GetSeasonSprite(int spriteIndex)
        {
            return GetCurrentSkinData().mahjongSeasonSprites[spriteIndex];
        }
        public int GetRandomSpriteIndex()
        {
            var randomIndex = Random.Range(0, GetCurrentSkinData().mahjongSpriteList.Count);
            return randomIndex;
        }

        public int GetMahjongCount() => GetCurrentSkinData().mahjongSpriteList.Count;

        public Sprite GetChestSprite()
        {
            return GetCurrentSkinData().chestSprite;
        }


    }

    [Serializable]
    public class SkinData
    {
        [PreviewField(100)]
        public List<Sprite> mahjongSpriteList;
        
        [PreviewField(100)]
        public Sprite selectSprite;
        public Color selectColor = Color.green;
        
        [PreviewField(100)]
        public Sprite chestSprite;
        
        [PreviewField(100)]
        public Sprite iconSprite;

        [PreviewField(100)]
        public List<Sprite> mahjongFlowerSprites;
        
        [PreviewField(100)]
        public List<Sprite> mahjongSeasonSprites;
    }
}