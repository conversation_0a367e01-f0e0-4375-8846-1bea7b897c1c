using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities;
using DG.Tweening;
using Sirenix.OdinInspector;

namespace Test
{
    public class TestSequence : BhMonoBehavior
    {
        [Button]
        void Test()
        {
            var sequence = DOTween.Sequence();

            sequence.AppendInterval(1f);

            for (int i = 0; i < 5; i++)
            {
                var i1 = i;
                sequence.AppendCallback(() => { BhDebug.Log($"loop = {i1}"); });
                sequence.AppendInterval(1f);
            }

            sequence.Play();
        }
    }
}