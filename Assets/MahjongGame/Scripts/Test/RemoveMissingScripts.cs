
#if UNITY_EDITOR

using b100SDK.Scripts.Base;
using Sirenix.OdinInspector;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;

namespace Test
{
    public class RemoveMissingScripts : BhMonoBehavior
    {
        [Button]
        void RemoveMissingScriptsInScene()
        {
            GameObject[] allObjects = GameObject.FindObjectsOfType<GameObject>(true);
            foreach (GameObject obj in allObjects)
            {
                int count = GameObjectUtility.RemoveMonoBehavioursWithMissingScript(obj);
                if (count > 0)
                {
                    Debug.Log($"Removed {count} missing scripts from {obj.name}");
                }
            }
        } 
        
        
        [Button]
        
        void RemoveMissingScriptsInPrefabs()
        {
            string[] prefabGuids = AssetDatabase.FindAssets("t:Prefab");
            foreach (string guid in prefabGuids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);

                if (prefab != null)
                {
                    int count = GameObjectUtility.RemoveMonoBehavioursWithMissingScript(prefab);
                    if (count > 0)
                    {
                        Debug.Log($"Removed {count} missing scripts from {path}");
                        PrefabUtility.SavePrefabAsset(prefab);
                    }
                }
            }

            AssetDatabase.Refresh();
        }
        
        
        
            [MenuItem("b100/Remove Missing Scripts in Opened Prefab")]
            static void RemoveMissingScriptsInOpenedPrefab()
            {
                GameObject root = UnityEditor.SceneManagement.PrefabStageUtility.GetCurrentPrefabStage()?.prefabContentsRoot;
                if (root == null)
                {
                    Debug.LogWarning("No prefab is currently open in Prefab Mode.");
                    return;
                }

                int count = RemoveMissingScriptsRecursively(root);
                if (count > 0)
                {
                    Debug.Log($"Removed {count} missing scripts from {root.name}");
                    EditorSceneManager.MarkSceneDirty(root.scene);
                }
                else
                {
                    Debug.Log("No missing scripts found.");
                }
            }

            static int RemoveMissingScriptsRecursively(GameObject obj)
            {
                int count = GameObjectUtility.RemoveMonoBehavioursWithMissingScript(obj);
                foreach (Transform child in obj.transform)
                {
                    count += RemoveMissingScriptsRecursively(child.gameObject);
                }
                return count;
            }
        

    }
}

#endif
