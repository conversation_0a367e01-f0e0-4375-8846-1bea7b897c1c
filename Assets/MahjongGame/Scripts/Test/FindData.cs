using System.Collections.Generic;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities.Tool;
using MahjongGame.Scripts.Gameplay.Data;
using Newtonsoft.Json;
using Sirenix.OdinInspector;
using UnityEngine;

namespace Test
{
    public class FindData : BhMonoBehavior
    {
        #if UNITY_EDITOR
        [SerializeField]
        private List<TextAsset> levelDataTextAssets;
        
        [FolderPath]
        public string path;

        [SerializeField, TextArea]
        private string dataToFind;

        [Button]
        void FindAssetContainData()
        {
            var correctDataToFing = dataToFind.Replace("\n", "").Replace("\r", "").Replace("\t", "").Replace(" ", "");
            Debug.Log("Data to find: " + correctDataToFing);
            
            foreach (var textAsset in levelDataTextAssets)
            {
                var allData = JsonConvert.DeserializeObject<LevelDataRaw[]>(textAsset.text);

                foreach (var levelDataRaw in allData)
                {
                    if (levelDataRaw.q.Contains(correctDataToFing))
                    {
                        Debug.LogWarning("Finded data!!!" );
                        Debug.Log($"Level: {levelDataRaw.id}, Question: {levelDataRaw.q}");
                        Debug.Log($"Assets: {textAsset.name}");
                        
                        return;
                    }
                }
            }
        }       
        
        [Button]
        void FindAllAssetContainData()
        {
            var correctDataToFing = dataToFind.Replace("\n", "").Replace("\r", "").Replace("\t", "").Replace(" ", "");
            Debug.Log("Data to find: " + correctDataToFing);
            
            foreach (var textAsset in levelDataTextAssets)
            {
                var allData = JsonConvert.DeserializeObject<LevelDataRaw[]>(textAsset.text);

                foreach (var levelDataRaw in allData)
                {
                    if (levelDataRaw.q.Contains(correctDataToFing))
                    {
                        Debug.LogWarning("Finded data!!!" );
                        Debug.Log($"Level: {levelDataRaw.id}, Question: {levelDataRaw.q}");
                        Debug.Log($"Assets: {textAsset.name}");
                    }
                }
            }
        }

        [Button]
        void DebugName()
        {
            Debug.Log($"Assets: {levelDataTextAssets[0].name}");
        }



        [Button]
        void GetAllAsset()
        {
            levelDataTextAssets = UtilitiesTool.GetResources<TextAsset>(path, new List<string>() { ".txt" });
        }
        
        #endif
    }
}