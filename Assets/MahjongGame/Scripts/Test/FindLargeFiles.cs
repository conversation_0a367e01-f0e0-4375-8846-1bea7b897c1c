#if UNITY_EDITOR


using System.IO;
using UnityEditor;
using UnityEngine;

namespace MahjongGame.Scripts.Test
{
    public class FindLargeFiles : EditorWindow
    {
        [MenuItem("b100/Find Large Files")]
        public static void FindFiles()
        {
            string[] files = Directory.GetFiles(Application.dataPath, "*.*", SearchOption.AllDirectories);
            foreach (var file in files)
            {
                FileInfo fileInfo = new FileInfo(file);
                if (fileInfo.Length > 10 * 1024 * 1024) // Lọc file > 10 MB
                {
                    Debug.Log($"Large File: {fileInfo.FullName}, Size: {fileInfo.Length / (1024 * 1024)} MB");
                }
            }
        }
    }
}

#endif
